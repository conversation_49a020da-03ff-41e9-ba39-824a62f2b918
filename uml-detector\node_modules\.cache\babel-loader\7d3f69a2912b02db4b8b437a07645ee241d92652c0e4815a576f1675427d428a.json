{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\imageUp\\\\HistoryAnalysisSection.tsx\",\n  _s = $RefreshSig$();\n// HistoryAnalysisSection.tsx - Composant pour l'analyse historique et l'import de classes\nimport React, { useState, useEffect } from 'react';\nimport { useHistory } from '../../context/HistoryContext';\nimport { useAuth } from '../../context/AuthContext';\nimport { HistoryAnalysisService } from '../../services/HistoryAnalysisService';\nimport { ChevronDown, Eye, Clock, Zap, AlertTriangle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HistoryAnalysisSection = ({\n  darkMode,\n  targetClassName,\n  currentClassData,\n  onImport\n}) => {\n  _s();\n  const {\n    historyItems\n  } = useHistory();\n  const {\n    currentUser\n  } = useAuth();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [matches, setMatches] = useState([]);\n  const [selectedMatches, setSelectedMatches] = useState(new Set());\n  const [sortOption, setSortOption] = useState(HistoryAnalysisService.getSortOptions()[0]);\n  const [showPreview, setShowPreview] = useState(null);\n  const [conflicts, setConflicts] = useState({\n    attributes: [],\n    methods: []\n  });\n\n  // Rechercher les correspondances lors du changement de classe cible\n  useEffect(() => {\n    if (!targetClassName || !currentUser) return;\n    const foundMatches = HistoryAnalysisService.findMatchingDiagrams(targetClassName, historyItems, currentUser.uid);\n    const sortedMatches = HistoryAnalysisService.sortMatches(foundMatches, sortOption);\n    setMatches(sortedMatches);\n  }, [targetClassName, historyItems, currentUser, sortOption]);\n\n  // Détecter les conflits lors du changement de sélection\n  useEffect(() => {\n    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));\n    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);\n    if (selectedClasses.length > 0) {\n      const detectedConflicts = HistoryAnalysisService.detectConflicts(currentClassData, selectedClasses);\n      setConflicts(detectedConflicts);\n    } else {\n      setConflicts({\n        attributes: [],\n        methods: []\n      });\n    }\n  }, [selectedMatches, matches, currentClassData]);\n  const handleToggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n  const handleMatchSelection = (matchId, selected) => {\n    const newSelection = new Set(selectedMatches);\n    if (selected) {\n      newSelection.add(matchId);\n    } else {\n      newSelection.delete(matchId);\n    }\n    setSelectedMatches(newSelection);\n  };\n  const handleImport = () => {\n    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));\n    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);\n    if (selectedClasses.length > 0) {\n      const mergedData = HistoryAnalysisService.mergeClassData(currentClassData, selectedClasses, 'merge' // Par défaut, on fusionne sans écraser\n      );\n      onImport(mergedData);\n      setSelectedMatches(new Set()); // Reset selection\n    }\n  };\n  const handlePreview = matchId => {\n    setShowPreview(showPreview === matchId ? null : matchId);\n  };\n  const getSectionStyles = () => ({\n    container: {\n      marginBottom: '24px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '12px',\n      background: darkMode ? 'linear-gradient(135deg, rgba(10, 15, 28, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%)',\n      backdropFilter: 'blur(10px)',\n      WebkitBackdropFilter: 'blur(10px)'\n    },\n    header: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      padding: '16px 20px',\n      cursor: 'pointer',\n      borderBottom: isExpanded ? `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}` : 'none'\n    },\n    headerLeft: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px'\n    },\n    title: {\n      fontSize: '16px',\n      fontWeight: '600',\n      color: darkMode ? '#f8fafc' : '#0f172a',\n      margin: 0\n    },\n    badge: {\n      backgroundColor: matches.length > 0 ? '#3b82f6' : darkMode ? '#64748b' : '#94a3b8',\n      color: '#ffffff',\n      fontSize: '12px',\n      fontWeight: '600',\n      padding: '4px 8px',\n      borderRadius: '12px',\n      minWidth: '20px',\n      textAlign: 'center'\n    },\n    expandIcon: {\n      color: darkMode ? '#94a3b8' : '#64748b',\n      transition: 'transform 0.2s ease',\n      transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)'\n    },\n    content: {\n      padding: isExpanded ? '20px' : '0',\n      maxHeight: isExpanded ? '400px' : '0',\n      overflow: 'hidden',\n      transition: 'all 0.3s ease'\n    },\n    sortContainer: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      marginBottom: '16px'\n    },\n    sortLabel: {\n      fontSize: '14px',\n      color: darkMode ? '#cbd5e1' : '#64748b',\n      fontWeight: '500'\n    },\n    sortSelect: {\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '6px 12px',\n      fontSize: '14px',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      cursor: 'pointer'\n    },\n    matchesList: {\n      maxHeight: '250px',\n      overflowY: 'auto',\n      marginBottom: '16px'\n    },\n    matchItem: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      padding: '12px',\n      marginBottom: '8px',\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.4)' : 'rgba(248, 250, 252, 0.6)',\n      borderRadius: '8px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'}`,\n      transition: 'all 0.2s ease'\n    },\n    checkbox: {\n      width: '16px',\n      height: '16px',\n      accentColor: '#3b82f6',\n      cursor: 'pointer'\n    },\n    matchInfo: {\n      flex: 1\n    },\n    matchTitle: {\n      fontSize: '14px',\n      fontWeight: '500',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '4px'\n    },\n    matchMeta: {\n      fontSize: '12px',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    similarityBadge: {\n      backgroundColor: '#10b981',\n      color: '#ffffff',\n      fontSize: '11px',\n      fontWeight: '600',\n      padding: '2px 6px',\n      borderRadius: '8px'\n    },\n    actionButtons: {\n      display: 'flex',\n      gap: '8px'\n    },\n    actionButton: {\n      backgroundColor: 'transparent',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'}`,\n      borderRadius: '6px',\n      padding: '6px',\n      cursor: 'pointer',\n      color: darkMode ? '#60a5fa' : '#3b82f6',\n      transition: 'all 0.2s ease'\n    },\n    conflictsWarning: {\n      backgroundColor: darkMode ? 'rgba(245, 158, 11, 0.1)' : 'rgba(245, 158, 11, 0.05)',\n      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`,\n      borderRadius: '8px',\n      padding: '12px',\n      marginBottom: '16px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    conflictsText: {\n      fontSize: '13px',\n      color: darkMode ? '#fbbf24' : '#d97706',\n      fontWeight: '500'\n    },\n    importButton: {\n      backgroundColor: selectedMatches.size > 0 ? '#3b82f6' : darkMode ? '#374151' : '#9ca3af',\n      color: '#ffffff',\n      border: 'none',\n      borderRadius: '8px',\n      padding: '10px 16px',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: selectedMatches.size > 0 ? 'pointer' : 'not-allowed',\n      transition: 'all 0.2s ease',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    emptyState: {\n      textAlign: 'center',\n      color: darkMode ? '#64748b' : '#9ca3af',\n      fontSize: '14px',\n      fontStyle: 'italic',\n      padding: '20px'\n    }\n  });\n  const styles = getSectionStyles();\n  if (!currentUser) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.header,\n      onClick: handleToggleExpand,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.headerLeft,\n        children: [/*#__PURE__*/_jsxDEV(Zap, {\n          size: 18,\n          color: darkMode ? '#60a5fa' : '#3b82f6'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          style: styles.title,\n          children: \"Analyse Historique\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: styles.badge,\n          children: matches.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n        size: 20,\n        style: styles.expandIcon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), isExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.content,\n      children: matches.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.emptyState,\n        children: [\"Aucun diagramme historique trouv\\xE9 pour la classe \\\"\", targetClassName, \"\\\"\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.sortContainer,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: styles.sortLabel,\n            children: \"Trier par:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            style: styles.sortSelect,\n            value: `${sortOption.key}-${sortOption.direction}`,\n            onChange: e => {\n              const [key, direction] = e.target.value.split('-');\n              const option = HistoryAnalysisService.getSortOptions().find(opt => opt.key === key && opt.direction === direction);\n              if (option) setSortOption(option);\n            },\n            children: HistoryAnalysisService.getSortOptions().map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: `${option.key}-${option.direction}`,\n              children: option.label\n            }, `${option.key}-${option.direction}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 15\n        }, this), conflicts.attributes.length > 0 || conflicts.methods.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.conflictsWarning,\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: styles.conflictsText,\n            children: [\"Conflits d\\xE9tect\\xE9s: \", conflicts.attributes.length, \" attribut(s), \", conflicts.methods.length, \" m\\xE9thode(s)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 17\n        }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.matchesList,\n          children: matches.map(match => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.matchItem,\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              style: styles.checkbox,\n              checked: selectedMatches.has(match.historyItem.id),\n              onChange: e => handleMatchSelection(match.historyItem.id, e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.matchInfo,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.matchTitle,\n                children: match.historyItem.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.matchMeta,\n                children: [/*#__PURE__*/_jsxDEV(Clock, {\n                  size: 12\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 25\n                }, this), match.historyItem.createdAt.toLocaleDateString('fr-FR'), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: styles.similarityBadge,\n                  children: [Math.round(match.similarity), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 25\n                }, this), match.isShared && /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDD17 Partag\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 44\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.actionButtons,\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.actionButton,\n                onClick: () => handlePreview(match.historyItem.id),\n                title: \"Voir aper\\xE7u\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 21\n            }, this)]\n          }, match.historyItem.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.importButton,\n          onClick: handleImport,\n          disabled: selectedMatches.size === 0,\n          children: [/*#__PURE__*/_jsxDEV(Zap, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 17\n          }, this), \"Importer (\", selectedMatches.size, \" s\\xE9lectionn\\xE9\", selectedMatches.size > 1 ? 's' : '', \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 263,\n    columnNumber: 5\n  }, this);\n};\n_s(HistoryAnalysisSection, \"K4L3Afquyk3sZw3P1jymkS0Zf50=\", false, function () {\n  return [useHistory, useAuth];\n});\n_c = HistoryAnalysisSection;\nexport default HistoryAnalysisSection;\nvar _c;\n$RefreshReg$(_c, \"HistoryAnalysisSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useHistory", "useAuth", "HistoryAnalysisService", "ChevronDown", "Eye", "Clock", "Zap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HistoryAnalysisSection", "darkMode", "targetClassName", "currentClassData", "onImport", "_s", "historyItems", "currentUser", "isExpanded", "setIsExpanded", "matches", "setMatches", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMatches", "Set", "sortOption", "setSortOption", "getSortOptions", "showPreview", "setShowPreview", "conflicts", "setConflicts", "attributes", "methods", "foundMatches", "findMatchingDiagrams", "uid", "sortedMatches", "sortMatches", "selectedMatchObjects", "filter", "match", "has", "historyItem", "id", "selectedClasses", "flatMap", "matchingClasses", "length", "detectedConflicts", "detectConflicts", "handleToggleExpand", "handleMatchSelection", "matchId", "selected", "newSelection", "add", "delete", "handleImport", "mergedData", "mergeClassData", "handlePreview", "getSectionStyles", "container", "marginBottom", "border", "borderRadius", "background", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "header", "display", "alignItems", "justifyContent", "padding", "cursor", "borderBottom", "headerLeft", "gap", "title", "fontSize", "fontWeight", "color", "margin", "badge", "backgroundColor", "min<PERSON><PERSON><PERSON>", "textAlign", "expandIcon", "transition", "transform", "content", "maxHeight", "overflow", "sortContainer", "sortLabel", "sortSelect", "matchesList", "overflowY", "matchItem", "checkbox", "width", "height", "accentColor", "matchInfo", "flex", "matchTitle", "matchMeta", "similarityBadge", "actionButtons", "actionButton", "conflictsWarning", "conflictsText", "importButton", "size", "emptyState", "fontStyle", "styles", "style", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "key", "direction", "onChange", "e", "target", "split", "option", "find", "opt", "map", "label", "type", "checked", "createdAt", "toLocaleDateString", "Math", "round", "similarity", "isShared", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/imageUp/HistoryAnalysisSection.tsx"], "sourcesContent": ["// HistoryAnalysisSection.tsx - Composant pour l'analyse historique et l'import de classes\nimport React, { useState, useEffect } from 'react';\nimport { useHistory } from '../../context/HistoryContext';\nimport { useAuth } from '../../context/AuthContext';\nimport { HistoryAnalysisService, HistoryMatch, SortOption, ClassData } from '../../services/HistoryAnalysisService';\nimport { ChevronDown, ChevronUp, Eye, Clock, Zap, AlertTriangle } from 'lucide-react';\n\ninterface HistoryAnalysisSectionProps {\n  darkMode: boolean;\n  targetClassName: string;\n  currentClassData: ClassData;\n  onImport: (importedData: ClassData) => void;\n}\n\nconst HistoryAnalysisSection: React.FC<HistoryAnalysisSectionProps> = ({\n  darkMode,\n  targetClassName,\n  currentClassData,\n  onImport\n}) => {\n  const { historyItems } = useHistory();\n  const { currentUser } = useAuth();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [matches, setMatches] = useState<HistoryMatch[]>([]);\n  const [selectedMatches, setSelectedMatches] = useState<Set<string>>(new Set());\n  const [sortOption, setSortOption] = useState<SortOption>(HistoryAnalysisService.getSortOptions()[0]);\n  const [showPreview, setShowPreview] = useState<string | null>(null);\n  const [conflicts, setConflicts] = useState<{ attributes: string[], methods: string[] }>({ attributes: [], methods: [] });\n\n  // Rechercher les correspondances lors du changement de classe cible\n  useEffect(() => {\n    if (!targetClassName || !currentUser) return;\n    \n    const foundMatches = HistoryAnalysisService.findMatchingDiagrams(\n      targetClassName,\n      historyItems,\n      currentUser.uid\n    );\n    \n    const sortedMatches = HistoryAnalysisService.sortMatches(foundMatches, sortOption);\n    setMatches(sortedMatches);\n  }, [targetClassName, historyItems, currentUser, sortOption]);\n\n  // Détecter les conflits lors du changement de sélection\n  useEffect(() => {\n    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));\n    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);\n    \n    if (selectedClasses.length > 0) {\n      const detectedConflicts = HistoryAnalysisService.detectConflicts(currentClassData, selectedClasses);\n      setConflicts(detectedConflicts);\n    } else {\n      setConflicts({ attributes: [], methods: [] });\n    }\n  }, [selectedMatches, matches, currentClassData]);\n\n  const handleToggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n\n  const handleMatchSelection = (matchId: string, selected: boolean) => {\n    const newSelection = new Set(selectedMatches);\n    if (selected) {\n      newSelection.add(matchId);\n    } else {\n      newSelection.delete(matchId);\n    }\n    setSelectedMatches(newSelection);\n  };\n\n  const handleImport = () => {\n    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));\n    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);\n    \n    if (selectedClasses.length > 0) {\n      const mergedData = HistoryAnalysisService.mergeClassData(\n        currentClassData,\n        selectedClasses,\n        'merge' // Par défaut, on fusionne sans écraser\n      );\n      onImport(mergedData);\n      setSelectedMatches(new Set()); // Reset selection\n    }\n  };\n\n  const handlePreview = (matchId: string) => {\n    setShowPreview(showPreview === matchId ? null : matchId);\n  };\n\n  const getSectionStyles = () => ({\n    container: {\n      marginBottom: '24px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '12px',\n      background: darkMode \n        ? 'linear-gradient(135deg, rgba(10, 15, 28, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)' \n        : 'linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%)',\n      backdropFilter: 'blur(10px)',\n      WebkitBackdropFilter: 'blur(10px)',\n    },\n    header: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      padding: '16px 20px',\n      cursor: 'pointer',\n      borderBottom: isExpanded ? `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}` : 'none',\n    },\n    headerLeft: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n    },\n    title: {\n      fontSize: '16px',\n      fontWeight: '600',\n      color: darkMode ? '#f8fafc' : '#0f172a',\n      margin: 0,\n    },\n    badge: {\n      backgroundColor: matches.length > 0 ? '#3b82f6' : darkMode ? '#64748b' : '#94a3b8',\n      color: '#ffffff',\n      fontSize: '12px',\n      fontWeight: '600',\n      padding: '4px 8px',\n      borderRadius: '12px',\n      minWidth: '20px',\n      textAlign: 'center' as const,\n    },\n    expandIcon: {\n      color: darkMode ? '#94a3b8' : '#64748b',\n      transition: 'transform 0.2s ease',\n      transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',\n    },\n    content: {\n      padding: isExpanded ? '20px' : '0',\n      maxHeight: isExpanded ? '400px' : '0',\n      overflow: 'hidden' as const,\n      transition: 'all 0.3s ease',\n    },\n    sortContainer: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      marginBottom: '16px',\n    },\n    sortLabel: {\n      fontSize: '14px',\n      color: darkMode ? '#cbd5e1' : '#64748b',\n      fontWeight: '500',\n    },\n    sortSelect: {\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '6px 12px',\n      fontSize: '14px',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      cursor: 'pointer',\n    },\n    matchesList: {\n      maxHeight: '250px',\n      overflowY: 'auto' as const,\n      marginBottom: '16px',\n    },\n    matchItem: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      padding: '12px',\n      marginBottom: '8px',\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.4)' : 'rgba(248, 250, 252, 0.6)',\n      borderRadius: '8px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'}`,\n      transition: 'all 0.2s ease',\n    },\n    checkbox: {\n      width: '16px',\n      height: '16px',\n      accentColor: '#3b82f6',\n      cursor: 'pointer',\n    },\n    matchInfo: {\n      flex: 1,\n    },\n    matchTitle: {\n      fontSize: '14px',\n      fontWeight: '500',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '4px',\n    },\n    matchMeta: {\n      fontSize: '12px',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    similarityBadge: {\n      backgroundColor: '#10b981',\n      color: '#ffffff',\n      fontSize: '11px',\n      fontWeight: '600',\n      padding: '2px 6px',\n      borderRadius: '8px',\n    },\n    actionButtons: {\n      display: 'flex',\n      gap: '8px',\n    },\n    actionButton: {\n      backgroundColor: 'transparent',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'}`,\n      borderRadius: '6px',\n      padding: '6px',\n      cursor: 'pointer',\n      color: darkMode ? '#60a5fa' : '#3b82f6',\n      transition: 'all 0.2s ease',\n    },\n    conflictsWarning: {\n      backgroundColor: darkMode ? 'rgba(245, 158, 11, 0.1)' : 'rgba(245, 158, 11, 0.05)',\n      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`,\n      borderRadius: '8px',\n      padding: '12px',\n      marginBottom: '16px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    conflictsText: {\n      fontSize: '13px',\n      color: darkMode ? '#fbbf24' : '#d97706',\n      fontWeight: '500',\n    },\n    importButton: {\n      backgroundColor: selectedMatches.size > 0 ? '#3b82f6' : darkMode ? '#374151' : '#9ca3af',\n      color: '#ffffff',\n      border: 'none',\n      borderRadius: '8px',\n      padding: '10px 16px',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: selectedMatches.size > 0 ? 'pointer' : 'not-allowed',\n      transition: 'all 0.2s ease',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    emptyState: {\n      textAlign: 'center' as const,\n      color: darkMode ? '#64748b' : '#9ca3af',\n      fontSize: '14px',\n      fontStyle: 'italic',\n      padding: '20px',\n    }\n  });\n\n  const styles = getSectionStyles();\n\n  if (!currentUser) return null;\n\n  return (\n    <div style={styles.container}>\n      <div style={styles.header} onClick={handleToggleExpand}>\n        <div style={styles.headerLeft}>\n          <Zap size={18} color={darkMode ? '#60a5fa' : '#3b82f6'} />\n          <h4 style={styles.title}>Analyse Historique</h4>\n          <span style={styles.badge}>{matches.length}</span>\n        </div>\n        <ChevronDown size={20} style={styles.expandIcon} />\n      </div>\n      \n      {isExpanded && (\n        <div style={styles.content}>\n          {matches.length === 0 ? (\n            <div style={styles.emptyState}>\n              Aucun diagramme historique trouvé pour la classe \"{targetClassName}\"\n            </div>\n          ) : (\n            <>\n              <div style={styles.sortContainer}>\n                <span style={styles.sortLabel}>Trier par:</span>\n                <select \n                  style={styles.sortSelect}\n                  value={`${sortOption.key}-${sortOption.direction}`}\n                  onChange={(e) => {\n                    const [key, direction] = e.target.value.split('-');\n                    const option = HistoryAnalysisService.getSortOptions().find(\n                      opt => opt.key === key && opt.direction === direction\n                    );\n                    if (option) setSortOption(option);\n                  }}\n                >\n                  {HistoryAnalysisService.getSortOptions().map(option => (\n                    <option key={`${option.key}-${option.direction}`} value={`${option.key}-${option.direction}`}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {conflicts.attributes.length > 0 || conflicts.methods.length > 0 ? (\n                <div style={styles.conflictsWarning}>\n                  <AlertTriangle size={16} />\n                  <span style={styles.conflictsText}>\n                    Conflits détectés: {conflicts.attributes.length} attribut(s), {conflicts.methods.length} méthode(s)\n                  </span>\n                </div>\n              ) : null}\n\n              <div style={styles.matchesList}>\n                {matches.map(match => (\n                  <div key={match.historyItem.id} style={styles.matchItem}>\n                    <input\n                      type=\"checkbox\"\n                      style={styles.checkbox}\n                      checked={selectedMatches.has(match.historyItem.id)}\n                      onChange={(e) => handleMatchSelection(match.historyItem.id, e.target.checked)}\n                    />\n                    <div style={styles.matchInfo}>\n                      <div style={styles.matchTitle}>{match.historyItem.title}</div>\n                      <div style={styles.matchMeta}>\n                        <Clock size={12} />\n                        {match.historyItem.createdAt.toLocaleDateString('fr-FR')}\n                        <span style={styles.similarityBadge}>{Math.round(match.similarity)}%</span>\n                        {match.isShared && <span>🔗 Partagé</span>}\n                      </div>\n                    </div>\n                    <div style={styles.actionButtons}>\n                      <button\n                        style={styles.actionButton}\n                        onClick={() => handlePreview(match.historyItem.id)}\n                        title=\"Voir aperçu\"\n                      >\n                        <Eye size={14} />\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              <button\n                style={styles.importButton}\n                onClick={handleImport}\n                disabled={selectedMatches.size === 0}\n              >\n                <Zap size={16} />\n                Importer ({selectedMatches.size} sélectionné{selectedMatches.size > 1 ? 's' : ''})\n              </button>\n            </>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default HistoryAnalysisSection;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,sBAAsB,QAA6C,uCAAuC;AACnH,SAASC,WAAW,EAAaC,GAAG,EAAEC,KAAK,EAAEC,GAAG,EAAEC,aAAa,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAStF,MAAMC,sBAA6D,GAAGA,CAAC;EACrEC,QAAQ;EACRC,eAAe;EACfC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAa,CAAC,GAAGlB,UAAU,CAAC,CAAC;EACrC,MAAM;IAAEmB;EAAY,CAAC,GAAGlB,OAAO,CAAC,CAAC;EACjC,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAiB,EAAE,CAAC;EAC1D,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAc,IAAI4B,GAAG,CAAC,CAAC,CAAC;EAC9E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAaI,sBAAsB,CAAC2B,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAA8C;IAAEoC,UAAU,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG,CAAC,CAAC;;EAExH;EACApC,SAAS,CAAC,MAAM;IACd,IAAI,CAACe,eAAe,IAAI,CAACK,WAAW,EAAE;IAEtC,MAAMiB,YAAY,GAAGlC,sBAAsB,CAACmC,oBAAoB,CAC9DvB,eAAe,EACfI,YAAY,EACZC,WAAW,CAACmB,GACd,CAAC;IAED,MAAMC,aAAa,GAAGrC,sBAAsB,CAACsC,WAAW,CAACJ,YAAY,EAAET,UAAU,CAAC;IAClFJ,UAAU,CAACgB,aAAa,CAAC;EAC3B,CAAC,EAAE,CAACzB,eAAe,EAAEI,YAAY,EAAEC,WAAW,EAAEQ,UAAU,CAAC,CAAC;;EAE5D;EACA5B,SAAS,CAAC,MAAM;IACd,MAAM0C,oBAAoB,GAAGnB,OAAO,CAACoB,MAAM,CAACC,KAAK,IAAInB,eAAe,CAACoB,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,CAAC;IAC/F,MAAMC,eAAe,GAAGN,oBAAoB,CAACO,OAAO,CAACL,KAAK,IAAIA,KAAK,CAACM,eAAe,CAAC;IAEpF,IAAIF,eAAe,CAACG,MAAM,GAAG,CAAC,EAAE;MAC9B,MAAMC,iBAAiB,GAAGjD,sBAAsB,CAACkD,eAAe,CAACrC,gBAAgB,EAAEgC,eAAe,CAAC;MACnGd,YAAY,CAACkB,iBAAiB,CAAC;IACjC,CAAC,MAAM;MACLlB,YAAY,CAAC;QAAEC,UAAU,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACX,eAAe,EAAEF,OAAO,EAAEP,gBAAgB,CAAC,CAAC;EAEhD,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BhC,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMkC,oBAAoB,GAAGA,CAACC,OAAe,EAAEC,QAAiB,KAAK;IACnE,MAAMC,YAAY,GAAG,IAAI/B,GAAG,CAACF,eAAe,CAAC;IAC7C,IAAIgC,QAAQ,EAAE;MACZC,YAAY,CAACC,GAAG,CAACH,OAAO,CAAC;IAC3B,CAAC,MAAM;MACLE,YAAY,CAACE,MAAM,CAACJ,OAAO,CAAC;IAC9B;IACA9B,kBAAkB,CAACgC,YAAY,CAAC;EAClC,CAAC;EAED,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMnB,oBAAoB,GAAGnB,OAAO,CAACoB,MAAM,CAACC,KAAK,IAAInB,eAAe,CAACoB,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,CAAC;IAC/F,MAAMC,eAAe,GAAGN,oBAAoB,CAACO,OAAO,CAACL,KAAK,IAAIA,KAAK,CAACM,eAAe,CAAC;IAEpF,IAAIF,eAAe,CAACG,MAAM,GAAG,CAAC,EAAE;MAC9B,MAAMW,UAAU,GAAG3D,sBAAsB,CAAC4D,cAAc,CACtD/C,gBAAgB,EAChBgC,eAAe,EACf,OAAO,CAAC;MACV,CAAC;MACD/B,QAAQ,CAAC6C,UAAU,CAAC;MACpBpC,kBAAkB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMqC,aAAa,GAAIR,OAAe,IAAK;IACzCxB,cAAc,CAACD,WAAW,KAAKyB,OAAO,GAAG,IAAI,GAAGA,OAAO,CAAC;EAC1D,CAAC;EAED,MAAMS,gBAAgB,GAAGA,CAAA,MAAO;IAC9BC,SAAS,EAAE;MACTC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,aAAatD,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFuD,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAExD,QAAQ,GAChB,+EAA+E,GAC/E,qFAAqF;MACzFyD,cAAc,EAAE,YAAY;MAC5BC,oBAAoB,EAAE;IACxB,CAAC;IACDC,MAAM,EAAE;MACNC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,eAAe;MAC/BC,OAAO,EAAE,WAAW;MACpBC,MAAM,EAAE,SAAS;MACjBC,YAAY,EAAE1D,UAAU,GAAG,aAAaP,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B,EAAE,GAAG;IACjH,CAAC;IACDkE,UAAU,EAAE;MACVN,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBC,KAAK,EAAEvE,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCwE,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLC,eAAe,EAAEjE,OAAO,CAAC4B,MAAM,GAAG,CAAC,GAAG,SAAS,GAAGrC,QAAQ,GAAG,SAAS,GAAG,SAAS;MAClFuE,KAAK,EAAE,SAAS;MAChBF,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBP,OAAO,EAAE,SAAS;MAClBR,YAAY,EAAE,MAAM;MACpBoB,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE;IACb,CAAC;IACDC,UAAU,EAAE;MACVN,KAAK,EAAEvE,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC8E,UAAU,EAAE,qBAAqB;MACjCC,SAAS,EAAExE,UAAU,GAAG,gBAAgB,GAAG;IAC7C,CAAC;IACDyE,OAAO,EAAE;MACPjB,OAAO,EAAExD,UAAU,GAAG,MAAM,GAAG,GAAG;MAClC0E,SAAS,EAAE1E,UAAU,GAAG,OAAO,GAAG,GAAG;MACrC2E,QAAQ,EAAE,QAAiB;MAC3BJ,UAAU,EAAE;IACd,CAAC;IACDK,aAAa,EAAE;MACbvB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,MAAM;MACXd,YAAY,EAAE;IAChB,CAAC;IACD+B,SAAS,EAAE;MACTf,QAAQ,EAAE,MAAM;MAChBE,KAAK,EAAEvE,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCsE,UAAU,EAAE;IACd,CAAC;IACDe,UAAU,EAAE;MACVX,eAAe,EAAE1E,QAAQ,GAAG,uBAAuB,GAAG,0BAA0B;MAChFsD,MAAM,EAAE,aAAatD,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFuD,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,UAAU;MACnBM,QAAQ,EAAE,MAAM;MAChBE,KAAK,EAAEvE,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCgE,MAAM,EAAE;IACV,CAAC;IACDsB,WAAW,EAAE;MACXL,SAAS,EAAE,OAAO;MAClBM,SAAS,EAAE,MAAe;MAC1BlC,YAAY,EAAE;IAChB,CAAC;IACDmC,SAAS,EAAE;MACT5B,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,MAAM;MACXJ,OAAO,EAAE,MAAM;MACfV,YAAY,EAAE,KAAK;MACnBqB,eAAe,EAAE1E,QAAQ,GAAG,uBAAuB,GAAG,0BAA0B;MAChFuD,YAAY,EAAE,KAAK;MACnBD,MAAM,EAAE,aAAatD,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B,EAAE;MACxF8E,UAAU,EAAE;IACd,CAAC;IACDW,QAAQ,EAAE;MACRC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,WAAW,EAAE,SAAS;MACtB5B,MAAM,EAAE;IACV,CAAC;IACD6B,SAAS,EAAE;MACTC,IAAI,EAAE;IACR,CAAC;IACDC,UAAU,EAAE;MACV1B,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBC,KAAK,EAAEvE,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCqD,YAAY,EAAE;IAChB,CAAC;IACD2C,SAAS,EAAE;MACT3B,QAAQ,EAAE,MAAM;MAChBE,KAAK,EAAEvE,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC4D,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACD8B,eAAe,EAAE;MACfvB,eAAe,EAAE,SAAS;MAC1BH,KAAK,EAAE,SAAS;MAChBF,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBP,OAAO,EAAE,SAAS;MAClBR,YAAY,EAAE;IAChB,CAAC;IACD2C,aAAa,EAAE;MACbtC,OAAO,EAAE,MAAM;MACfO,GAAG,EAAE;IACP,CAAC;IACDgC,YAAY,EAAE;MACZzB,eAAe,EAAE,aAAa;MAC9BpB,MAAM,EAAE,aAAatD,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFuD,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,SAAS;MACjBO,KAAK,EAAEvE,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC8E,UAAU,EAAE;IACd,CAAC;IACDsB,gBAAgB,EAAE;MAChB1B,eAAe,EAAE1E,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B;MAClFsD,MAAM,EAAE,aAAatD,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFuD,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,MAAM;MACfV,YAAY,EAAE,MAAM;MACpBO,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACDkC,aAAa,EAAE;MACbhC,QAAQ,EAAE,MAAM;MAChBE,KAAK,EAAEvE,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCsE,UAAU,EAAE;IACd,CAAC;IACDgC,YAAY,EAAE;MACZ5B,eAAe,EAAE/D,eAAe,CAAC4F,IAAI,GAAG,CAAC,GAAG,SAAS,GAAGvG,QAAQ,GAAG,SAAS,GAAG,SAAS;MACxFuE,KAAK,EAAE,SAAS;MAChBjB,MAAM,EAAE,MAAM;MACdC,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,WAAW;MACpBM,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBN,MAAM,EAAErD,eAAe,CAAC4F,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG,aAAa;MAC5DzB,UAAU,EAAE,eAAe;MAC3BlB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACDqC,UAAU,EAAE;MACV5B,SAAS,EAAE,QAAiB;MAC5BL,KAAK,EAAEvE,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCqE,QAAQ,EAAE,MAAM;MAChBoC,SAAS,EAAE,QAAQ;MACnB1C,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EAEF,MAAM2C,MAAM,GAAGvD,gBAAgB,CAAC,CAAC;EAEjC,IAAI,CAAC7C,WAAW,EAAE,OAAO,IAAI;EAE7B,oBACEV,OAAA;IAAK+G,KAAK,EAAED,MAAM,CAACtD,SAAU;IAAAwD,QAAA,gBAC3BhH,OAAA;MAAK+G,KAAK,EAAED,MAAM,CAAC/C,MAAO;MAACkD,OAAO,EAAErE,kBAAmB;MAAAoE,QAAA,gBACrDhH,OAAA;QAAK+G,KAAK,EAAED,MAAM,CAACxC,UAAW;QAAA0C,QAAA,gBAC5BhH,OAAA,CAACH,GAAG;UAAC8G,IAAI,EAAE,EAAG;UAAChC,KAAK,EAAEvE,QAAQ,GAAG,SAAS,GAAG;QAAU;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DrH,OAAA;UAAI+G,KAAK,EAAED,MAAM,CAACtC,KAAM;UAAAwC,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChDrH,OAAA;UAAM+G,KAAK,EAAED,MAAM,CAACjC,KAAM;UAAAmC,QAAA,EAAEnG,OAAO,CAAC4B;QAAM;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNrH,OAAA,CAACN,WAAW;QAACiH,IAAI,EAAE,EAAG;QAACI,KAAK,EAAED,MAAM,CAAC7B;MAAW;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,EAEL1G,UAAU,iBACTX,OAAA;MAAK+G,KAAK,EAAED,MAAM,CAAC1B,OAAQ;MAAA4B,QAAA,EACxBnG,OAAO,CAAC4B,MAAM,KAAK,CAAC,gBACnBzC,OAAA;QAAK+G,KAAK,EAAED,MAAM,CAACF,UAAW;QAAAI,QAAA,GAAC,wDACqB,EAAC3G,eAAe,EAAC,IACrE;MAAA;QAAA6G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAENrH,OAAA,CAAAE,SAAA;QAAA8G,QAAA,gBACEhH,OAAA;UAAK+G,KAAK,EAAED,MAAM,CAACvB,aAAc;UAAAyB,QAAA,gBAC/BhH,OAAA;YAAM+G,KAAK,EAAED,MAAM,CAACtB,SAAU;YAAAwB,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDrH,OAAA;YACE+G,KAAK,EAAED,MAAM,CAACrB,UAAW;YACzB6B,KAAK,EAAE,GAAGpG,UAAU,CAACqG,GAAG,IAAIrG,UAAU,CAACsG,SAAS,EAAG;YACnDC,QAAQ,EAAGC,CAAC,IAAK;cACf,MAAM,CAACH,GAAG,EAAEC,SAAS,CAAC,GAAGE,CAAC,CAACC,MAAM,CAACL,KAAK,CAACM,KAAK,CAAC,GAAG,CAAC;cAClD,MAAMC,MAAM,GAAGpI,sBAAsB,CAAC2B,cAAc,CAAC,CAAC,CAAC0G,IAAI,CACzDC,GAAG,IAAIA,GAAG,CAACR,GAAG,KAAKA,GAAG,IAAIQ,GAAG,CAACP,SAAS,KAAKA,SAC9C,CAAC;cACD,IAAIK,MAAM,EAAE1G,aAAa,CAAC0G,MAAM,CAAC;YACnC,CAAE;YAAAb,QAAA,EAEDvH,sBAAsB,CAAC2B,cAAc,CAAC,CAAC,CAAC4G,GAAG,CAACH,MAAM,iBACjD7H,OAAA;cAAkDsH,KAAK,EAAE,GAAGO,MAAM,CAACN,GAAG,IAAIM,MAAM,CAACL,SAAS,EAAG;cAAAR,QAAA,EAC1Fa,MAAM,CAACI;YAAK,GADF,GAAGJ,MAAM,CAACN,GAAG,IAAIM,MAAM,CAACL,SAAS,EAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAExC,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL9F,SAAS,CAACE,UAAU,CAACgB,MAAM,GAAG,CAAC,IAAIlB,SAAS,CAACG,OAAO,CAACe,MAAM,GAAG,CAAC,gBAC9DzC,OAAA;UAAK+G,KAAK,EAAED,MAAM,CAACN,gBAAiB;UAAAQ,QAAA,gBAClChH,OAAA,CAACF,aAAa;YAAC6G,IAAI,EAAE;UAAG;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3BrH,OAAA;YAAM+G,KAAK,EAAED,MAAM,CAACL,aAAc;YAAAO,QAAA,GAAC,2BACd,EAACzF,SAAS,CAACE,UAAU,CAACgB,MAAM,EAAC,gBAAc,EAAClB,SAAS,CAACG,OAAO,CAACe,MAAM,EAAC,gBAC1F;UAAA;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,GACJ,IAAI,eAERrH,OAAA;UAAK+G,KAAK,EAAED,MAAM,CAACpB,WAAY;UAAAsB,QAAA,EAC5BnG,OAAO,CAACmH,GAAG,CAAC9F,KAAK,iBAChBlC,OAAA;YAAgC+G,KAAK,EAAED,MAAM,CAAClB,SAAU;YAAAoB,QAAA,gBACtDhH,OAAA;cACEkI,IAAI,EAAC,UAAU;cACfnB,KAAK,EAAED,MAAM,CAACjB,QAAS;cACvBsC,OAAO,EAAEpH,eAAe,CAACoB,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAE;cACnDoF,QAAQ,EAAGC,CAAC,IAAK7E,oBAAoB,CAACX,KAAK,CAACE,WAAW,CAACC,EAAE,EAAEqF,CAAC,CAACC,MAAM,CAACQ,OAAO;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACFrH,OAAA;cAAK+G,KAAK,EAAED,MAAM,CAACb,SAAU;cAAAe,QAAA,gBAC3BhH,OAAA;gBAAK+G,KAAK,EAAED,MAAM,CAACX,UAAW;gBAAAa,QAAA,EAAE9E,KAAK,CAACE,WAAW,CAACoC;cAAK;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9DrH,OAAA;gBAAK+G,KAAK,EAAED,MAAM,CAACV,SAAU;gBAAAY,QAAA,gBAC3BhH,OAAA,CAACJ,KAAK;kBAAC+G,IAAI,EAAE;gBAAG;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAClBnF,KAAK,CAACE,WAAW,CAACgG,SAAS,CAACC,kBAAkB,CAAC,OAAO,CAAC,eACxDrI,OAAA;kBAAM+G,KAAK,EAAED,MAAM,CAACT,eAAgB;kBAAAW,QAAA,GAAEsB,IAAI,CAACC,KAAK,CAACrG,KAAK,CAACsG,UAAU,CAAC,EAAC,GAAC;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC1EnF,KAAK,CAACuG,QAAQ,iBAAIzI,OAAA;kBAAAgH,QAAA,EAAM;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrH,OAAA;cAAK+G,KAAK,EAAED,MAAM,CAACR,aAAc;cAAAU,QAAA,eAC/BhH,OAAA;gBACE+G,KAAK,EAAED,MAAM,CAACP,YAAa;gBAC3BU,OAAO,EAAEA,CAAA,KAAM3D,aAAa,CAACpB,KAAK,CAACE,WAAW,CAACC,EAAE,CAAE;gBACnDmC,KAAK,EAAC,gBAAa;gBAAAwC,QAAA,eAEnBhH,OAAA,CAACL,GAAG;kBAACgH,IAAI,EAAE;gBAAG;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GAxBEnF,KAAK,CAACE,WAAW,CAACC,EAAE;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBzB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrH,OAAA;UACE+G,KAAK,EAAED,MAAM,CAACJ,YAAa;UAC3BO,OAAO,EAAE9D,YAAa;UACtBuF,QAAQ,EAAE3H,eAAe,CAAC4F,IAAI,KAAK,CAAE;UAAAK,QAAA,gBAErChH,OAAA,CAACH,GAAG;YAAC8G,IAAI,EAAE;UAAG;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cACP,EAACtG,eAAe,CAAC4F,IAAI,EAAC,oBAAY,EAAC5F,eAAe,CAAC4F,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,GACnF;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACT;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7G,EAAA,CArVIL,sBAA6D;EAAA,QAMxCZ,UAAU,EACXC,OAAO;AAAA;AAAAmJ,EAAA,GAP3BxI,sBAA6D;AAuVnE,eAAeA,sBAAsB;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}