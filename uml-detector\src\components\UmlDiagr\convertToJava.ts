import JSZip from 'jszip';
import { saveAs } from 'file-saver';

export const generateJavaFromMermaid = async (umlText: string): Promise<void> => {
  const zip = new JSZip();
  
  const parseMermaid = (umlText: string) => {
    const lines = umlText.split('\n').map(l => l.trim()).filter(l => l && !l.startsWith('classDiagram'));

    interface ClassMember {
      name: string;
      type?: string;
      visibility: string;
      params?: string;
      returnType?: string;
    }

    interface ParsedClass {
      name: string;
      type: 'class' | 'abstract' | 'interface';
      attributes: ClassMember[];
      methods: ClassMember[];
      extends?: string;
      implements?: string[];
    }

    const classes: Record<string, ParsedClass> = {};
    let currentClass: ParsedClass | null = null;

    for (const line of lines) {
      if (line.startsWith('class ')) {
        const name = line.replace('class ', '').split(' ')[0];
        currentClass = { name, attributes: [], methods: [], type: 'class' };
        if (line.includes('abstract')) currentClass.type = 'abstract';
        if (line.includes('interface')) currentClass.type = 'interface';
        classes[name] = currentClass;
      } else if (currentClass) {
        // Handle lines with colons (attributes with types)
        if (line.includes(':')) {
          const [left, right] = line.split(':').map(x => x.trim());
          const visibilityChar = left[0];
          let visibility = 'private';
          if (visibilityChar === '+') visibility = 'public';
          else if (visibilityChar === '-') visibility = 'private';
          else if (visibilityChar === '#') visibility = 'protected';
          else if (visibilityChar === '~') visibility = 'package-private';

          const memberName = left.slice(1).trim();

          // Check if it's a method (contains parentheses)
          if (memberName.includes('(')) {
            // Extract method name and parameters
            const methodNameMatch = memberName.match(/^([^(]+)\(([^)]*)\)/);
            if (methodNameMatch) {
              const methodName = methodNameMatch[1].trim();
              const params = methodNameMatch[2].trim();
              const returnType = right.trim() || 'void';
              
              currentClass.methods.push({ 
                name: methodName, 
                params, 
                returnType, 
                visibility 
              });
            }
          } else {
            // It's an attribute
            currentClass.attributes.push({ 
              name: memberName, 
              type: right, 
              visibility 
            });
          }
        } 
        // Handle method lines without explicit types (just method names with parentheses)
        else if (line.includes('(') && line.includes(')')) {
          const visibilityChar = line[0];
          let visibility = 'public'; // Default visibility for methods without explicit visibility
          let memberName = line;
          
          if (['+', '-', '#', '~'].includes(visibilityChar)) {
            if (visibilityChar === '+') visibility = 'public';
            else if (visibilityChar === '-') visibility = 'private';
            else if (visibilityChar === '#') visibility = 'protected';
            else if (visibilityChar === '~') visibility = 'package-private';
            memberName = line.slice(1).trim();
          }

          // Extract method name and parameters
          const methodNameMatch = memberName.match(/^([^(]+)\(([^)]*)\)/);
          if (methodNameMatch) {
            const methodName = methodNameMatch[1].trim();
            const params = methodNameMatch[2].trim();
            const returnType = 'void'; // Default return type for methods without explicit return type
            
            currentClass.methods.push({ 
              name: methodName, 
              params, 
              returnType, 
              visibility 
            });
          }
        }
      }
    }

    // Handle inheritance and implementation relationships
    lines.forEach(line => {
      if (line.includes('<|--') || line.includes('<|..')) {
        const [parent, rest] = line.split(/<\|--|<\|\.\./);
        const child = rest.trim().split(' ')[0];
        const relation = line.includes('<|..') ? 'implements' : 'extends';

        if (classes[child]) {
          if (relation === 'extends') {
            classes[child].extends = parent.trim();
          } else {
            classes[child].implements = [...(classes[child].implements || []), parent.trim()];
          }
        }
      }

      // Optional: treat A *-- B or A o-- B as composition/aggregation attributes
      if (line.includes('*--') || line.includes('o--')) {
        const [from, to] = line.split(/[*|o]--/).map(x => x.trim());
        if (classes[from]) {
          classes[from].attributes.push({
            name: to.toLowerCase(),
            type: to,
            visibility: 'private'
          });
        }
      }
    });

    return Object.values(classes);
  };

  const javaTypeMap: Record<string, string> = {
    string: 'String',
    String: 'String',
    int: 'int',
    Integer: 'Integer',
    float: 'float',
    Float: 'Float',
    double: 'double',
    Double: 'Double',
    bool: 'boolean',
    boolean: 'boolean',
    Boolean: 'Boolean',
    void: 'void',
    Void: 'void'
  };

  const capitalize = (s: string) => s.charAt(0).toUpperCase() + s.slice(1);

  const generateJavaClass = (cls: any) => {
    const packageLine = `package com.generated;`;
    const imports = new Set<string>();
    
    // Generate fields
    const fields = cls.attributes.length > 0 
      ? cls.attributes.map((a: any) => 
          `    ${a.visibility} ${javaTypeMap[a.type] || a.type} ${a.name};`
        ).join('\n')
      : '';

    // Generate constructor (only for classes, not interfaces)
    const constructor = cls.type !== 'interface' && cls.attributes.length > 0
      ? `    public ${cls.name}(${cls.attributes.map((a: any) => 
          `${javaTypeMap[a.type] || a.type} ${a.name}`
        ).join(', ')}) {\n` +
        cls.attributes.map((a: any) => `        this.${a.name} = ${a.name};`).join('\n') + 
        `\n    }`
      : cls.type !== 'interface' 
        ? `    public ${cls.name}() {\n        // Default constructor\n    }`
        : '';

    // Generate getters and setters (only for classes with attributes)
    const gettersSetters = cls.type !== 'interface' && cls.attributes.length > 0
      ? cls.attributes.flatMap((a: any) => [
          `    public ${javaTypeMap[a.type] || a.type} get${capitalize(a.name)}() {\n        return ${a.name};\n    }`,
          `    public void set${capitalize(a.name)}(${javaTypeMap[a.type] || a.type} ${a.name}) {\n        this.${a.name} = ${a.name};\n    }`
        ]).join('\n\n')
      : '';

    // Generate methods with proper implementation
    const methods = cls.methods.length > 0
      ? cls.methods.map((m: any) => {
          const javaReturnType = javaTypeMap[m.returnType] || m.returnType;
          const javaVisibility = m.visibility === 'package-private' ? '' : m.visibility;
          
          if (cls.type === 'interface') {
            // Interface methods are abstract by default
            return `    ${javaReturnType} ${m.name}(${m.params});`;
          } else if (cls.type === 'abstract') {
            // Abstract class can have abstract methods or concrete methods
            // For now, we'll make them concrete with not implemented exception
            return `    ${javaVisibility} ${javaReturnType} ${m.name}(${m.params}) {\n        throw new UnsupportedOperationException("Method '${m.name}' is not implemented yet.");\n    }`;
          } else {
            // Regular class with concrete implementation
            return `    ${javaVisibility} ${javaReturnType} ${m.name}(${m.params}) {\n        throw new UnsupportedOperationException("Method '${m.name}' is not implemented yet.");\n    }`;
          }
        }).join('\n\n')
      : '';

    // Determine class type keyword
    const classKeyword = cls.type === 'interface' ? 'interface' : 
                        cls.type === 'abstract' ? 'abstract class' : 'class';

    // Build inheritance/implementation clause
    const extendsClause = cls.extends ? ` extends ${cls.extends}` : '';
    const implementsClause = cls.implements?.length ? ` implements ${cls.implements.join(', ')}` : '';

    // Combine all parts
    const parts = [
      packageLine,
      Array.from(imports).join('\n'),
      `public ${classKeyword} ${cls.name}${extendsClause}${implementsClause} {`,
      fields,
      constructor,
      gettersSetters,
      methods,
      '}'
    ].filter(part => part.trim().length > 0);

    return parts.join('\n\n');
  };

  const uml = umlText;
  const classes = parseMermaid(uml);

  classes.forEach((cls: any) => {
    const code = generateJavaClass(cls);
    const path = `com/generated/${cls.name}.java`;
    zip.file(path, code);
  });

  zip.generateAsync({ type: 'blob' }).then(content => {
    saveAs(content, 'java_project.zip');
  });
};