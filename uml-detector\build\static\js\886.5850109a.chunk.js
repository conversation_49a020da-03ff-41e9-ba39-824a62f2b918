"use strict";(self.webpackChunkuml_detector=self.webpackChunkuml_detector||[]).push([[886],{2396:(t,e,a)=>{a.d(e,{CP:()=>l,HT:()=>h,PB:()=>d,aC:()=>c,lC:()=>n,m:()=>o,tk:()=>i});var s=a(7551),r=a(4137),i=(0,s.K2)(((t,e)=>{const a=t.append("rect");if(a.attr("x",e.x),a.attr("y",e.y),a.attr("fill",e.fill),a.attr("stroke",e.stroke),a.attr("width",e.width),a.attr("height",e.height),e.name&&a.attr("name",e.name),e.rx&&a.attr("rx",e.rx),e.ry&&a.attr("ry",e.ry),void 0!==e.attrs)for(const s in e.attrs)a.attr(s,e.attrs[s]);return e.class&&a.attr("class",e.class),a}),"drawRect"),n=(0,s.K2)(((t,e)=>{const a={x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,stroke:e.stroke,class:"rect"};i(t,a).lower()}),"drawBackgroundRect"),o=(0,s.K2)(((t,e)=>{const a=e.text.replace(s.H1," "),r=t.append("text");r.attr("x",e.x),r.attr("y",e.y),r.attr("class","legend"),r.style("text-anchor",e.anchor),e.class&&r.attr("class",e.class);const i=r.append("tspan");return i.attr("x",e.x+2*e.textMargin),i.text(a),r}),"drawText"),c=(0,s.K2)(((t,e,a,s)=>{const i=t.append("image");i.attr("x",e),i.attr("y",a);const n=(0,r.J)(s);i.attr("xlink:href",n)}),"drawImage"),l=(0,s.K2)(((t,e,a,s)=>{const i=t.append("use");i.attr("x",e),i.attr("y",a);const n=(0,r.J)(s);i.attr("xlink:href",`#${n}`)}),"drawEmbeddedImage"),d=(0,s.K2)((()=>({x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0})),"getNoteRect"),h=(0,s.K2)((()=>({x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0})),"getTextObj")},5609:(t,e,a)=>{a.d(e,{m:()=>r});var s=a(7551),r=class{constructor(t){this.init=t,this.records=this.init()}static#t=(()=>(0,s.K2)(this,"ImperativeState"))();reset(){this.records=this.init()}}},9886:(t,e,a)=>{a.d(e,{diagram:()=>mt});var s=a(2396),r=a(5609),i=a(6102),n=a(7551),o=a(1804),c=a(4137),l=function(){var t=(0,n.K2)((function(t,e,a,s){for(a=a||{},s=t.length;s--;a[t[s]]=e);return a}),"o"),e=[1,2],a=[1,3],s=[1,4],r=[2,4],i=[1,9],o=[1,11],c=[1,13],l=[1,14],d=[1,16],h=[1,17],p=[1,18],g=[1,24],u=[1,25],x=[1,26],y=[1,27],m=[1,28],b=[1,29],T=[1,30],E=[1,31],f=[1,32],w=[1,33],I=[1,34],L=[1,35],_=[1,36],P=[1,37],k=[1,38],v=[1,39],A=[1,41],N=[1,42],M=[1,43],D=[1,44],O=[1,45],S=[1,46],R=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],Y=[4,5,16,50,52,53],K=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],C=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],B=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],$=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],V=[68,69,70],F=[1,122],W={trace:(0,n.K2)((function(){}),"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,BIDIRECTIONAL_SOLID_ARROW:74,DOTTED_ARROW:75,BIDIRECTIONAL_DOTTED_ARROW:76,SOLID_CROSS:77,DOTTED_CROSS:78,SOLID_POINT:79,DOTTED_POINT:80,TXT:81,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"BIDIRECTIONAL_SOLID_ARROW",75:"DOTTED_ARROW",76:"BIDIRECTIONAL_DOTTED_ARROW",77:"SOLID_CROSS",78:"DOTTED_CROSS",79:"SOLID_POINT",80:"DOTTED_POINT",81:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:(0,n.K2)((function(t,e,a,s,r,i,n){var o=i.length-1;switch(r){case 3:return s.apply(i[o]),i[o];case 4:case 9:case 8:case 13:this.$=[];break;case 5:case 10:i[o-1].push(i[o]),this.$=i[o-1];break;case 6:case 7:case 11:case 12:case 62:this.$=i[o];break;case 15:i[o].type="createParticipant",this.$=i[o];break;case 16:i[o-1].unshift({type:"boxStart",boxData:s.parseBoxData(i[o-2])}),i[o-1].push({type:"boxEnd",boxText:i[o-2]}),this.$=i[o-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(i[o-2]),sequenceIndexStep:Number(i[o-1]),sequenceVisible:!0,signalType:s.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(i[o-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:s.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:s.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:s.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:s.LINETYPE.ACTIVE_START,actor:i[o-1].actor};break;case 23:this.$={type:"activeEnd",signalType:s.LINETYPE.ACTIVE_END,actor:i[o-1].actor};break;case 29:s.setDiagramTitle(i[o].substring(6)),this.$=i[o].substring(6);break;case 30:s.setDiagramTitle(i[o].substring(7)),this.$=i[o].substring(7);break;case 31:this.$=i[o].trim(),s.setAccTitle(this.$);break;case 32:case 33:this.$=i[o].trim(),s.setAccDescription(this.$);break;case 34:i[o-1].unshift({type:"loopStart",loopText:s.parseMessage(i[o-2]),signalType:s.LINETYPE.LOOP_START}),i[o-1].push({type:"loopEnd",loopText:i[o-2],signalType:s.LINETYPE.LOOP_END}),this.$=i[o-1];break;case 35:i[o-1].unshift({type:"rectStart",color:s.parseMessage(i[o-2]),signalType:s.LINETYPE.RECT_START}),i[o-1].push({type:"rectEnd",color:s.parseMessage(i[o-2]),signalType:s.LINETYPE.RECT_END}),this.$=i[o-1];break;case 36:i[o-1].unshift({type:"optStart",optText:s.parseMessage(i[o-2]),signalType:s.LINETYPE.OPT_START}),i[o-1].push({type:"optEnd",optText:s.parseMessage(i[o-2]),signalType:s.LINETYPE.OPT_END}),this.$=i[o-1];break;case 37:i[o-1].unshift({type:"altStart",altText:s.parseMessage(i[o-2]),signalType:s.LINETYPE.ALT_START}),i[o-1].push({type:"altEnd",signalType:s.LINETYPE.ALT_END}),this.$=i[o-1];break;case 38:i[o-1].unshift({type:"parStart",parText:s.parseMessage(i[o-2]),signalType:s.LINETYPE.PAR_START}),i[o-1].push({type:"parEnd",signalType:s.LINETYPE.PAR_END}),this.$=i[o-1];break;case 39:i[o-1].unshift({type:"parStart",parText:s.parseMessage(i[o-2]),signalType:s.LINETYPE.PAR_OVER_START}),i[o-1].push({type:"parEnd",signalType:s.LINETYPE.PAR_END}),this.$=i[o-1];break;case 40:i[o-1].unshift({type:"criticalStart",criticalText:s.parseMessage(i[o-2]),signalType:s.LINETYPE.CRITICAL_START}),i[o-1].push({type:"criticalEnd",signalType:s.LINETYPE.CRITICAL_END}),this.$=i[o-1];break;case 41:i[o-1].unshift({type:"breakStart",breakText:s.parseMessage(i[o-2]),signalType:s.LINETYPE.BREAK_START}),i[o-1].push({type:"breakEnd",optText:s.parseMessage(i[o-2]),signalType:s.LINETYPE.BREAK_END}),this.$=i[o-1];break;case 43:this.$=i[o-3].concat([{type:"option",optionText:s.parseMessage(i[o-1]),signalType:s.LINETYPE.CRITICAL_OPTION},i[o]]);break;case 45:this.$=i[o-3].concat([{type:"and",parText:s.parseMessage(i[o-1]),signalType:s.LINETYPE.PAR_AND},i[o]]);break;case 47:this.$=i[o-3].concat([{type:"else",altText:s.parseMessage(i[o-1]),signalType:s.LINETYPE.ALT_ELSE},i[o]]);break;case 48:i[o-3].draw="participant",i[o-3].type="addParticipant",i[o-3].description=s.parseMessage(i[o-1]),this.$=i[o-3];break;case 49:i[o-1].draw="participant",i[o-1].type="addParticipant",this.$=i[o-1];break;case 50:i[o-3].draw="actor",i[o-3].type="addParticipant",i[o-3].description=s.parseMessage(i[o-1]),this.$=i[o-3];break;case 51:i[o-1].draw="actor",i[o-1].type="addParticipant",this.$=i[o-1];break;case 52:i[o-1].type="destroyParticipant",this.$=i[o-1];break;case 53:this.$=[i[o-1],{type:"addNote",placement:i[o-2],actor:i[o-1].actor,text:i[o]}];break;case 54:i[o-2]=[].concat(i[o-1],i[o-1]).slice(0,2),i[o-2][0]=i[o-2][0].actor,i[o-2][1]=i[o-2][1].actor,this.$=[i[o-1],{type:"addNote",placement:s.PLACEMENT.OVER,actor:i[o-2].slice(0,2),text:i[o]}];break;case 55:this.$=[i[o-1],{type:"addLinks",actor:i[o-1].actor,text:i[o]}];break;case 56:this.$=[i[o-1],{type:"addALink",actor:i[o-1].actor,text:i[o]}];break;case 57:this.$=[i[o-1],{type:"addProperties",actor:i[o-1].actor,text:i[o]}];break;case 58:this.$=[i[o-1],{type:"addDetails",actor:i[o-1].actor,text:i[o]}];break;case 61:this.$=[i[o-2],i[o]];break;case 63:this.$=s.PLACEMENT.LEFTOF;break;case 64:this.$=s.PLACEMENT.RIGHTOF;break;case 65:this.$=[i[o-4],i[o-1],{type:"addMessage",from:i[o-4].actor,to:i[o-1].actor,signalType:i[o-3],msg:i[o],activate:!0},{type:"activeStart",signalType:s.LINETYPE.ACTIVE_START,actor:i[o-1].actor}];break;case 66:this.$=[i[o-4],i[o-1],{type:"addMessage",from:i[o-4].actor,to:i[o-1].actor,signalType:i[o-3],msg:i[o]},{type:"activeEnd",signalType:s.LINETYPE.ACTIVE_END,actor:i[o-4].actor}];break;case 67:this.$=[i[o-3],i[o-1],{type:"addMessage",from:i[o-3].actor,to:i[o-1].actor,signalType:i[o-2],msg:i[o]}];break;case 68:this.$={type:"addParticipant",actor:i[o]};break;case 69:this.$=s.LINETYPE.SOLID_OPEN;break;case 70:this.$=s.LINETYPE.DOTTED_OPEN;break;case 71:this.$=s.LINETYPE.SOLID;break;case 72:this.$=s.LINETYPE.BIDIRECTIONAL_SOLID;break;case 73:this.$=s.LINETYPE.DOTTED;break;case 74:this.$=s.LINETYPE.BIDIRECTIONAL_DOTTED;break;case 75:this.$=s.LINETYPE.SOLID_CROSS;break;case 76:this.$=s.LINETYPE.DOTTED_CROSS;break;case 77:this.$=s.LINETYPE.SOLID_POINT;break;case 78:this.$=s.LINETYPE.DOTTED_POINT;break;case 79:this.$=s.parseMessage(i[o].trim().substring(1))}}),"anonymous"),table:[{3:1,4:e,5:a,6:s},{1:[3]},{3:5,4:e,5:a,6:s},{3:6,4:e,5:a,6:s},t([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],r,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:i,5:o,8:8,9:10,12:12,13:c,14:l,17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:_,50:P,52:k,53:v,54:A,59:N,60:M,61:D,62:O,70:S},t(R,[2,5]),{9:47,12:12,13:c,14:l,17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:_,50:P,52:k,53:v,54:A,59:N,60:M,61:D,62:O,70:S},t(R,[2,7]),t(R,[2,8]),t(R,[2,14]),{12:48,50:P,52:k,53:v},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:S},{22:55,70:S},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},t(R,[2,29]),t(R,[2,30]),{32:[1,61]},{34:[1,62]},t(R,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:S},{22:72,70:S},{22:73,70:S},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:S},{22:90,70:S},{22:91,70:S},{22:92,70:S},t([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),t(R,[2,6]),t(R,[2,15]),t(Y,[2,9],{10:93}),t(R,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},t(R,[2,21]),{5:[1,97]},{5:[1,98]},t(R,[2,24]),t(R,[2,25]),t(R,[2,26]),t(R,[2,27]),t(R,[2,28]),t(R,[2,31]),t(R,[2,32]),t(K,r,{7:99}),t(K,r,{7:100}),t(K,r,{7:101}),t(C,r,{40:102,7:103}),t(B,r,{42:104,7:105}),t(B,r,{7:105,42:106}),t($,r,{45:107,7:108}),t(K,r,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:S},t(V,[2,69]),t(V,[2,70]),t(V,[2,71]),t(V,[2,72]),t(V,[2,73]),t(V,[2,74]),t(V,[2,75]),t(V,[2,76]),t(V,[2,77]),t(V,[2,78]),{22:118,70:S},{22:120,58:119,70:S},{70:[2,63]},{70:[2,64]},{56:121,81:F},{56:123,81:F},{56:124,81:F},{56:125,81:F},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:P,52:k,53:v},{5:[1,131]},t(R,[2,19]),t(R,[2,20]),t(R,[2,22]),t(R,[2,23]),{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,132],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:_,50:P,52:k,53:v,54:A,59:N,60:M,61:D,62:O,70:S},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,133],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:_,50:P,52:k,53:v,54:A,59:N,60:M,61:D,62:O,70:S},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,134],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:_,50:P,52:k,53:v,54:A,59:N,60:M,61:D,62:O,70:S},{16:[1,135]},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[2,46],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:_,49:[1,136],50:P,52:k,53:v,54:A,59:N,60:M,61:D,62:O,70:S},{16:[1,137]},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[2,44],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:_,48:[1,138],50:P,52:k,53:v,54:A,59:N,60:M,61:D,62:O,70:S},{16:[1,139]},{16:[1,140]},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[2,42],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:_,47:[1,141],50:P,52:k,53:v,54:A,59:N,60:M,61:D,62:O,70:S},{4:i,5:o,8:8,9:10,12:12,13:c,14:l,16:[1,142],17:15,18:d,21:h,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:_,50:P,52:k,53:v,54:A,59:N,60:M,61:D,62:O,70:S},{15:[1,143]},t(R,[2,49]),{15:[1,144]},t(R,[2,51]),t(R,[2,52]),{22:145,70:S},{22:146,70:S},{56:147,81:F},{56:148,81:F},{56:149,81:F},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},t(R,[2,16]),t(Y,[2,10]),{12:151,50:P,52:k,53:v},t(Y,[2,12]),t(Y,[2,13]),t(R,[2,18]),t(R,[2,34]),t(R,[2,35]),t(R,[2,36]),t(R,[2,37]),{15:[1,152]},t(R,[2,38]),{15:[1,153]},t(R,[2,39]),t(R,[2,40]),{15:[1,154]},t(R,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:F},{56:158,81:F},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:S},t(Y,[2,11]),t(C,r,{7:103,40:160}),t(B,r,{7:105,42:161}),t($,r,{7:108,45:162}),t(R,[2,48]),t(R,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},parseError:(0,n.K2)((function(t,e){if(!e.recoverable){var a=new Error(t);throw a.hash=e,a}this.trace(t)}),"parseError"),parse:(0,n.K2)((function(t){var e=this,a=[0],s=[],r=[null],i=[],o=this.table,c="",l=0,d=0,h=0,p=i.slice.call(arguments,1),g=Object.create(this.lexer),u={yy:{}};for(var x in this.yy)Object.prototype.hasOwnProperty.call(this.yy,x)&&(u.yy[x]=this.yy[x]);g.setInput(t,u.yy),u.yy.lexer=g,u.yy.parser=this,"undefined"==typeof g.yylloc&&(g.yylloc={});var y=g.yylloc;i.push(y);var m=g.options&&g.options.ranges;function b(){var t;return"number"!==typeof(t=s.pop()||g.lex()||1)&&(t instanceof Array&&(t=(s=t).pop()),t=e.symbols_[t]||t),t}"function"===typeof u.yy.parseError?this.parseError=u.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,(0,n.K2)((function(t){a.length=a.length-2*t,r.length=r.length-t,i.length=i.length-t}),"popStack"),(0,n.K2)(b,"lex");for(var T,E,f,w,I,L,_,P,k,v={};;){if(f=a[a.length-1],this.defaultActions[f]?w=this.defaultActions[f]:(null!==T&&"undefined"!=typeof T||(T=b()),w=o[f]&&o[f][T]),"undefined"===typeof w||!w.length||!w[0]){var A="";for(L in k=[],o[f])this.terminals_[L]&&L>2&&k.push("'"+this.terminals_[L]+"'");A=g.showPosition?"Parse error on line "+(l+1)+":\n"+g.showPosition()+"\nExpecting "+k.join(", ")+", got '"+(this.terminals_[T]||T)+"'":"Parse error on line "+(l+1)+": Unexpected "+(1==T?"end of input":"'"+(this.terminals_[T]||T)+"'"),this.parseError(A,{text:g.match,token:this.terminals_[T]||T,line:g.yylineno,loc:y,expected:k})}if(w[0]instanceof Array&&w.length>1)throw new Error("Parse Error: multiple actions possible at state: "+f+", token: "+T);switch(w[0]){case 1:a.push(T),r.push(g.yytext),i.push(g.yylloc),a.push(w[1]),T=null,E?(T=E,E=null):(d=g.yyleng,c=g.yytext,l=g.yylineno,y=g.yylloc,h>0&&h--);break;case 2:if(_=this.productions_[w[1]][1],v.$=r[r.length-_],v._$={first_line:i[i.length-(_||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-(_||1)].first_column,last_column:i[i.length-1].last_column},m&&(v._$.range=[i[i.length-(_||1)].range[0],i[i.length-1].range[1]]),"undefined"!==typeof(I=this.performAction.apply(v,[c,d,l,u.yy,w[1],r,i].concat(p))))return I;_&&(a=a.slice(0,-1*_*2),r=r.slice(0,-1*_),i=i.slice(0,-1*_)),a.push(this.productions_[w[1]][0]),r.push(v.$),i.push(v._$),P=o[a[a.length-2]][a[a.length-1]],a.push(P);break;case 3:return!0}}return!0}),"parse")},q=function(){return{EOF:1,parseError:(0,n.K2)((function(t,e){if(!this.yy.parser)throw new Error(t);this.yy.parser.parseError(t,e)}),"parseError"),setInput:(0,n.K2)((function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this}),"setInput"),input:(0,n.K2)((function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t}),"input"),unput:(0,n.K2)((function(t){var e=t.length,a=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var s=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),a.length-1&&(this.yylineno-=a.length-1);var r=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===s.length?this.yylloc.first_column:0)+s[s.length-a.length].length-a[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[r[0],r[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this}),"unput"),more:(0,n.K2)((function(){return this._more=!0,this}),"more"),reject:(0,n.K2)((function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}),"reject"),less:(0,n.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,n.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,n.K2)((function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,n.K2)((function(){var t=this.pastInput(),e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,n.K2)((function(t,e){var a,s,r;if(this.options.backtrack_lexer&&(r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(r.yylloc.range=this.yylloc.range.slice(0))),(s=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=s.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:s?s[s.length-1].length-s[s.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],a=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),a)return a;if(this._backtrack){for(var i in r)this[i]=r[i];return!1}return!1}),"test_match"),next:(0,n.K2)((function(){if(this.done)return this.EOF;var t,e,a,s;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var r=this._currentRules(),i=0;i<r.length;i++)if((a=this._input.match(this.rules[r[i]]))&&(!e||a[0].length>e[0].length)){if(e=a,s=i,this.options.backtrack_lexer){if(!1!==(t=this.test_match(a,r[i])))return t;if(this._backtrack){e=!1;continue}return!1}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,r[s]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}),"next"),lex:(0,n.K2)((function(){var t=this.next();return t||this.lex()}),"lex"),begin:(0,n.K2)((function(t){this.conditionStack.push(t)}),"begin"),popState:(0,n.K2)((function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]}),"popState"),_currentRules:(0,n.K2)((function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules}),"_currentRules"),topState:(0,n.K2)((function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"}),"topState"),pushState:(0,n.K2)((function(t){this.begin(t)}),"pushState"),stateStackSize:(0,n.K2)((function(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,n.K2)((function(t,e,a,s){switch(a){case 0:case 51:case 66:return 5;case 1:case 2:case 3:case 4:case 5:break;case 6:return 19;case 7:return this.begin("LINE"),14;case 8:return this.begin("ID"),50;case 9:return this.begin("ID"),52;case 10:return 13;case 11:return this.begin("ID"),53;case 12:return e.yytext=e.yytext.trim(),this.begin("ALIAS"),70;case 13:return this.popState(),this.popState(),this.begin("LINE"),51;case 14:return this.popState(),this.popState(),5;case 15:return this.begin("LINE"),36;case 16:return this.begin("LINE"),37;case 17:return this.begin("LINE"),38;case 18:return this.begin("LINE"),39;case 19:return this.begin("LINE"),49;case 20:return this.begin("LINE"),41;case 21:return this.begin("LINE"),43;case 22:return this.begin("LINE"),48;case 23:return this.begin("LINE"),44;case 24:return this.begin("LINE"),47;case 25:return this.begin("LINE"),46;case 26:return this.popState(),15;case 27:return 16;case 28:return 65;case 29:return 66;case 30:return 59;case 31:return 60;case 32:return 61;case 33:return 62;case 34:return 57;case 35:return 54;case 36:return this.begin("ID"),21;case 37:return this.begin("ID"),23;case 38:return 29;case 39:return 30;case 40:return this.begin("acc_title"),31;case 41:return this.popState(),"acc_title_value";case 42:return this.begin("acc_descr"),33;case 43:return this.popState(),"acc_descr_value";case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";case 47:return 6;case 48:return 18;case 49:return 20;case 50:return 64;case 52:return e.yytext=e.yytext.trim(),70;case 53:return 73;case 54:return 74;case 55:return 75;case 56:return 76;case 57:return 71;case 58:return 72;case 59:return 77;case 60:return 78;case 61:return 79;case 62:return 80;case 63:return 81;case 64:return 68;case 65:return 69;case 67:return"INVALID"}}),"anonymous"),rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\<->\->:\n,;]+?([\-]*[^\<->\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\<->\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\<->\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:!1},acc_descr:{rules:[43],inclusive:!1},acc_title:{rules:[41],inclusive:!1},ID:{rules:[2,3,12],inclusive:!1},ALIAS:{rules:[2,3,13,14],inclusive:!1},LINE:{rules:[2,3,26],inclusive:!1},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67],inclusive:!0}}}}();function z(){this.yy={}}return W.lexer=q,(0,n.K2)(z,"Parser"),z.prototype=W,W.Parser=z,new z}();l.parser=l;var d=l,h={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32,BIDIRECTIONAL_SOLID:33,BIDIRECTIONAL_DOTTED:34},p={FILLED:0,OPEN:1},g={LEFTOF:0,RIGHTOF:1,OVER:2},u=class{constructor(){this.state=new r.m((()=>({prevActor:void 0,actors:new Map,createdActors:new Map,destroyedActors:new Map,boxes:[],messages:[],notes:[],sequenceNumbersEnabled:!1,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0}))),this.setAccTitle=n.SV,this.setAccDescription=n.EI,this.setDiagramTitle=n.ke,this.getAccTitle=n.iN,this.getAccDescription=n.m7,this.getDiagramTitle=n.ab,this.apply=this.apply.bind(this),this.parseBoxData=this.parseBoxData.bind(this),this.parseMessage=this.parseMessage.bind(this),this.clear(),this.setWrap((0,n.D7)().wrap),this.LINETYPE=h,this.ARROWTYPE=p,this.PLACEMENT=g}static#t=(()=>(0,n.K2)(this,"SequenceDB"))();addBox(t){this.state.records.boxes.push({name:t.text,wrap:t.wrap??this.autoWrap(),fill:t.color,actorKeys:[]}),this.state.records.currentBox=this.state.records.boxes.slice(-1)[0]}addActor(t,e,a,s){let r=this.state.records.currentBox;const i=this.state.records.actors.get(t);if(i){if(this.state.records.currentBox&&i.box&&this.state.records.currentBox!==i.box)throw new Error(`A same participant should only be defined in one Box: ${i.name} can't be in '${i.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`);if(r=i.box?i.box:this.state.records.currentBox,i.box=r,i&&e===i.name&&null==a)return}if(null==a?.text&&(a={text:e,type:s}),null!=s&&null!=a.text||(a={text:e,type:s}),this.state.records.actors.set(t,{box:r,name:e,description:a.text,wrap:a.wrap??this.autoWrap(),prevActor:this.state.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:s??"participant"}),this.state.records.prevActor){const e=this.state.records.actors.get(this.state.records.prevActor);e&&(e.nextActor=t)}this.state.records.currentBox&&this.state.records.currentBox.actorKeys.push(t),this.state.records.prevActor=t}activationCount(t){let e,a=0;if(!t)return 0;for(e=0;e<this.state.records.messages.length;e++)this.state.records.messages[e].type===this.LINETYPE.ACTIVE_START&&this.state.records.messages[e].from===t&&a++,this.state.records.messages[e].type===this.LINETYPE.ACTIVE_END&&this.state.records.messages[e].from===t&&a--;return a}addMessage(t,e,a,s){this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:e,message:a.text,wrap:a.wrap??this.autoWrap(),answer:s})}addSignal(t,e,a,s){let r=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(s===this.LINETYPE.ACTIVE_END){if(this.activationCount(t??"")<1){const e=new Error("Trying to inactivate an inactive participant ("+t+")");throw e.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},e}}return this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:e,message:a?.text??"",wrap:a?.wrap??this.autoWrap(),type:s,activate:r}),!0}hasAtLeastOneBox(){return this.state.records.boxes.length>0}hasAtLeastOneBoxWithTitle(){return this.state.records.boxes.some((t=>t.name))}getMessages(){return this.state.records.messages}getBoxes(){return this.state.records.boxes}getActors(){return this.state.records.actors}getCreatedActors(){return this.state.records.createdActors}getDestroyedActors(){return this.state.records.destroyedActors}getActor(t){return this.state.records.actors.get(t)}getActorKeys(){return[...this.state.records.actors.keys()]}enableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!0}disableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!1}showSequenceNumbers(){return this.state.records.sequenceNumbersEnabled}setWrap(t){this.state.records.wrapEnabled=t}extractWrap(t){if(void 0===t)return{};t=t.trim();const e=null!==/^:?wrap:/.exec(t)||null===/^:?nowrap:/.exec(t)&&void 0;return{cleanedText:(void 0===e?t:t.replace(/^:?(?:no)?wrap:/,"")).trim(),wrap:e}}autoWrap(){return void 0!==this.state.records.wrapEnabled?this.state.records.wrapEnabled:(0,n.D7)().sequence?.wrap??!1}clear(){this.state.reset(),(0,n.IU)()}parseMessage(t){const e=t.trim(),{wrap:a,cleanedText:s}=this.extractWrap(e),r={text:s,wrap:a};return n.Rm.debug(`parseMessage: ${JSON.stringify(r)}`),r}parseBoxData(t){const e=/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/.exec(t);let a=e?.[1]?e[1].trim():"transparent",s=e?.[2]?e[2].trim():void 0;if(window?.CSS)window.CSS.supports("color",a)||(a="transparent",s=t.trim());else{const e=(new Option).style;e.color=a,e.color!==a&&(a="transparent",s=t.trim())}const{wrap:r,cleanedText:i}=this.extractWrap(s);return{text:i?(0,n.jZ)(i,(0,n.D7)()):void 0,color:a,wrap:r}}addNote(t,e,a){const s={actor:t,placement:e,message:a.text,wrap:a.wrap??this.autoWrap()},r=[].concat(t,t);this.state.records.notes.push(s),this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:r[0],to:r[1],message:a.text,wrap:a.wrap??this.autoWrap(),type:this.LINETYPE.NOTE,placement:e})}addLinks(t,e){const a=this.getActor(t);try{let t=(0,n.jZ)(e.text,(0,n.D7)());t=t.replace(/&equals;/g,"="),t=t.replace(/&amp;/g,"&");const s=JSON.parse(t);this.insertLinks(a,s)}catch(s){n.Rm.error("error while parsing actor link text",s)}}addALink(t,e){const a=this.getActor(t);try{const t={};let s=(0,n.jZ)(e.text,(0,n.D7)());const r=s.indexOf("@");s=s.replace(/&equals;/g,"="),s=s.replace(/&amp;/g,"&");const i=s.slice(0,r-1).trim(),o=s.slice(r+1).trim();t[i]=o,this.insertLinks(a,t)}catch(s){n.Rm.error("error while parsing actor link text",s)}}insertLinks(t,e){if(null==t.links)t.links=e;else for(const a in e)t.links[a]=e[a]}addProperties(t,e){const a=this.getActor(t);try{const t=(0,n.jZ)(e.text,(0,n.D7)()),s=JSON.parse(t);this.insertProperties(a,s)}catch(s){n.Rm.error("error while parsing actor properties text",s)}}insertProperties(t,e){if(null==t.properties)t.properties=e;else for(const a in e)t.properties[a]=e[a]}boxEnd(){this.state.records.currentBox=void 0}addDetails(t,e){const a=this.getActor(t),s=document.getElementById(e.text);try{const t=s.innerHTML,e=JSON.parse(t);e.properties&&this.insertProperties(a,e.properties),e.links&&this.insertLinks(a,e.links)}catch(r){n.Rm.error("error while parsing actor details text",r)}}getActorProperty(t,e){if(void 0!==t?.properties)return t.properties[e]}apply(t){if(Array.isArray(t))t.forEach((t=>{this.apply(t)}));else switch(t.type){case"sequenceIndex":this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":this.addActor(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(this.state.records.actors.has(t.actor))throw new Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior");this.state.records.lastCreated=t.actor,this.addActor(t.actor,t.actor,t.description,t.draw),this.state.records.createdActors.set(t.actor,this.state.records.messages.length);break;case"destroyParticipant":this.state.records.lastDestroyed=t.actor,this.state.records.destroyedActors.set(t.actor,this.state.records.messages.length);break;case"activeStart":case"activeEnd":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"addNote":this.addNote(t.actor,t.placement,t.text);break;case"addLinks":this.addLinks(t.actor,t.text);break;case"addALink":this.addALink(t.actor,t.text);break;case"addProperties":this.addProperties(t.actor,t.text);break;case"addDetails":this.addDetails(t.actor,t.text);break;case"addMessage":if(this.state.records.lastCreated){if(t.to!==this.state.records.lastCreated)throw new Error("The created participant "+this.state.records.lastCreated.name+" does not have an associated creating message after its declaration. Please check the sequence diagram.");this.state.records.lastCreated=void 0}else if(this.state.records.lastDestroyed){if(t.to!==this.state.records.lastDestroyed&&t.from!==this.state.records.lastDestroyed)throw new Error("The destroyed participant "+this.state.records.lastDestroyed.name+" does not have an associated destroying message after its declaration. Please check the sequence diagram.");this.state.records.lastDestroyed=void 0}this.addSignal(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":this.addBox(t.boxData);break;case"boxEnd":this.boxEnd();break;case"loopStart":this.addSignal(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":case"rectEnd":case"optEnd":case"altEnd":case"parEnd":case"criticalEnd":case"breakEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"rectStart":this.addSignal(void 0,void 0,t.color,t.signalType);break;case"optStart":this.addSignal(void 0,void 0,t.optText,t.signalType);break;case"altStart":case"else":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"setAccTitle":(0,n.SV)(t.text);break;case"parStart":case"and":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"criticalStart":this.addSignal(void 0,void 0,t.criticalText,t.signalType);break;case"option":this.addSignal(void 0,void 0,t.optionText,t.signalType);break;case"breakStart":this.addSignal(void 0,void 0,t.breakText,t.signalType)}}getConfig(){return(0,n.D7)().sequence}},x=(0,n.K2)((t=>`.actor {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n  }\n\n  text.actor > tspan {\n    fill: ${t.actorTextColor};\n    stroke: none;\n  }\n\n  .actor-line {\n    stroke: ${t.actorLineColor};\n  }\n\n  .messageLine0 {\n    stroke-width: 1.5;\n    stroke-dasharray: none;\n    stroke: ${t.signalColor};\n  }\n\n  .messageLine1 {\n    stroke-width: 1.5;\n    stroke-dasharray: 2, 2;\n    stroke: ${t.signalColor};\n  }\n\n  #arrowhead path {\n    fill: ${t.signalColor};\n    stroke: ${t.signalColor};\n  }\n\n  .sequenceNumber {\n    fill: ${t.sequenceNumberColor};\n  }\n\n  #sequencenumber {\n    fill: ${t.signalColor};\n  }\n\n  #crosshead path {\n    fill: ${t.signalColor};\n    stroke: ${t.signalColor};\n  }\n\n  .messageText {\n    fill: ${t.signalTextColor};\n    stroke: none;\n  }\n\n  .labelBox {\n    stroke: ${t.labelBoxBorderColor};\n    fill: ${t.labelBoxBkgColor};\n  }\n\n  .labelText, .labelText > tspan {\n    fill: ${t.labelTextColor};\n    stroke: none;\n  }\n\n  .loopText, .loopText > tspan {\n    fill: ${t.loopTextColor};\n    stroke: none;\n  }\n\n  .loopLine {\n    stroke-width: 2px;\n    stroke-dasharray: 2, 2;\n    stroke: ${t.labelBoxBorderColor};\n    fill: ${t.labelBoxBorderColor};\n  }\n\n  .note {\n    //stroke: #decc93;\n    stroke: ${t.noteBorderColor};\n    fill: ${t.noteBkgColor};\n  }\n\n  .noteText, .noteText > tspan {\n    fill: ${t.noteTextColor};\n    stroke: none;\n  }\n\n  .activation0 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .activation1 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .activation2 {\n    fill: ${t.activationBkgColor};\n    stroke: ${t.activationBorderColor};\n  }\n\n  .actorPopupMenu {\n    position: absolute;\n  }\n\n  .actorPopupMenuPanel {\n    position: absolute;\n    fill: ${t.actorBkg};\n    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));\n}\n  .actor-man line {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n  }\n  .actor-man circle, line {\n    stroke: ${t.actorBorder};\n    fill: ${t.actorBkg};\n    stroke-width: 2px;\n  }\n`),"getStyles"),y="actor-top",m="actor-bottom",b="actor-man",T=(0,n.K2)((function(t,e){return(0,s.tk)(t,e)}),"drawRect"),E=(0,n.K2)((function(t,e,a,s,r){if(void 0===e.links||null===e.links||0===Object.keys(e.links).length)return{height:0,width:0};const i=e.links,n=e.actorCnt,o=e.rectData;var l="none";r&&(l="block !important");const d=t.append("g");d.attr("id","actor"+n+"_popup"),d.attr("class","actorPopupMenu"),d.attr("display",l);var h="";void 0!==o.class&&(h=" "+o.class);let p=o.width>a?o.width:a;const g=d.append("rect");if(g.attr("class","actorPopupMenuPanel"+h),g.attr("x",o.x),g.attr("y",o.height),g.attr("fill",o.fill),g.attr("stroke",o.stroke),g.attr("width",p),g.attr("height",o.height),g.attr("rx",o.rx),g.attr("ry",o.ry),null!=i){var u=20;for(let t in i){var x=d.append("a"),y=(0,c.J)(i[t]);x.attr("xlink:href",y),x.attr("target","_blank"),z(s)(t,x,o.x+10,o.height+u,p,20,{class:"actor"},s),u+=30}}return g.attr("height",u),{height:o.height+u,width:p}}),"drawPopup"),f=(0,n.K2)((function(t){return"var pu = document.getElementById('"+t+"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }"}),"popupMenuToggle"),w=(0,n.K2)((async function(t,e){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,s=t.append("foreignObject");const r=await(0,n.VJ)(e.text,(0,n.zj)()),i=s.append("xhtml:div").attr("style","width: fit-content;").attr("xmlns","http://www.w3.org/1999/xhtml").html(r).node().getBoundingClientRect();if(s.attr("height",Math.round(i.height)).attr("width",Math.round(i.width)),"noteText"===e.class){const a=t.node().firstChild;a.setAttribute("height",i.height+2*e.textMargin);const r=a.getBBox();s.attr("x",Math.round(r.x+r.width/2-i.width/2)).attr("y",Math.round(r.y+r.height/2-i.height/2))}else if(a){let{startx:t,stopx:r,starty:n}=a;if(t>r){const e=t;t=r,r=e}s.attr("x",Math.round(t+Math.abs(t-r)/2-i.width/2)),"loopText"===e.class?s.attr("y",Math.round(n)):s.attr("y",Math.round(n-i.height))}return[s]}),"drawKatex"),I=(0,n.K2)((function(t,e){let a=0,s=0;const r=e.text.split(n.Y2.lineBreakRegex),[o,c]=(0,i.I5)(e.fontSize);let l=[],d=0,h=(0,n.K2)((()=>e.y),"yfunc");if(void 0!==e.valign&&void 0!==e.textMargin&&e.textMargin>0)switch(e.valign){case"top":case"start":h=(0,n.K2)((()=>Math.round(e.y+e.textMargin)),"yfunc");break;case"middle":case"center":h=(0,n.K2)((()=>Math.round(e.y+(a+s+e.textMargin)/2)),"yfunc");break;case"bottom":case"end":h=(0,n.K2)((()=>Math.round(e.y+(a+s+2*e.textMargin)-e.textMargin)),"yfunc")}if(void 0!==e.anchor&&void 0!==e.textMargin&&void 0!==e.width)switch(e.anchor){case"left":case"start":e.x=Math.round(e.x+e.textMargin),e.anchor="start",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"middle":case"center":e.x=Math.round(e.x+e.width/2),e.anchor="middle",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"right":case"end":e.x=Math.round(e.x+e.width-e.textMargin),e.anchor="end",e.dominantBaseline="middle",e.alignmentBaseline="middle"}for(let[n,p]of r.entries()){void 0!==e.textMargin&&0===e.textMargin&&void 0!==o&&(d=n*o);const r=t.append("text");r.attr("x",e.x),r.attr("y",h()),void 0!==e.anchor&&r.attr("text-anchor",e.anchor).attr("dominant-baseline",e.dominantBaseline).attr("alignment-baseline",e.alignmentBaseline),void 0!==e.fontFamily&&r.style("font-family",e.fontFamily),void 0!==c&&r.style("font-size",c),void 0!==e.fontWeight&&r.style("font-weight",e.fontWeight),void 0!==e.fill&&r.attr("fill",e.fill),void 0!==e.class&&r.attr("class",e.class),void 0!==e.dy?r.attr("dy",e.dy):0!==d&&r.attr("dy",d);const g=p||i.pe;if(e.tspan){const t=r.append("tspan");t.attr("x",e.x),void 0!==e.fill&&t.attr("fill",e.fill),t.text(g)}else r.text(g);void 0!==e.valign&&void 0!==e.textMargin&&e.textMargin>0&&(s+=(r._groups||r)[0][0].getBBox().height,a=s),l.push(r)}return l}),"drawText"),L=(0,n.K2)((function(t,e){function a(t,e,a,s,r){return t+","+e+" "+(t+a)+","+e+" "+(t+a)+","+(e+s-r)+" "+(t+a-1.2*r)+","+(e+s)+" "+t+","+(e+s)}(0,n.K2)(a,"genPoints");const s=t.append("polygon");return s.attr("points",a(e.x,e.y,e.width,e.height,7)),s.attr("class","labelBox"),e.y=e.y+e.height/2,I(t,e),s}),"drawLabel"),_=-1,P=(0,n.K2)(((t,e,a,s)=>{t.select&&a.forEach((a=>{const r=e.get(a),i=t.select("#actor"+r.actorCnt);!s.mirrorActors&&r.stopy?i.attr("y2",r.stopy+r.height/2):s.mirrorActors&&i.attr("y2",r.stopy)}))}),"fixLifeLineHeights"),k=(0,n.K2)((function(t,e,a,r){const i=r?e.stopy:e.starty,o=e.x+e.width/2,c=i+e.height,l=t.append("g").lower();var d=l;r||(_++,Object.keys(e.links||{}).length&&!a.forceMenus&&d.attr("onclick",f(`actor${_}_popup`)).attr("cursor","pointer"),d.append("line").attr("id","actor"+_).attr("x1",o).attr("y1",c).attr("x2",o).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),d=l.append("g"),e.actorCnt=_,null!=e.links&&d.attr("id","root-"+_));const h=(0,s.PB)();var p="actor";e.properties?.class?p=e.properties.class:h.fill="#eaeaea",p+=r?` ${m}`:` ${y}`,h.x=e.x,h.y=i,h.width=e.width,h.height=e.height,h.class=p,h.rx=3,h.ry=3,h.name=e.name;const g=T(d,h);if(e.rectData=h,e.properties?.icon){const t=e.properties.icon.trim();"@"===t.charAt(0)?(0,s.CP)(d,h.x+h.width-20,h.y+10,t.substr(1)):(0,s.aC)(d,h.x+h.width-20,h.y+10,t)}q(a,(0,n.Wi)(e.description))(e.description,d,h.x,h.y,h.width,h.height,{class:"actor actor-box"},a);let u=e.height;if(g.node){const t=g.node().getBBox();e.height=t.height,u=t.height}return u}),"drawActorTypeParticipant"),v=(0,n.K2)((function(t,e,a,r){const i=r?e.stopy:e.starty,o=e.x+e.width/2,c=i+80,l=t.append("g").lower();r||(_++,l.append("line").attr("id","actor"+_).attr("x1",o).attr("y1",c).attr("x2",o).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),e.actorCnt=_);const d=t.append("g");let h=b;h+=r?` ${m}`:` ${y}`,d.attr("class",h),d.attr("name",e.name);const p=(0,s.PB)();p.x=e.x,p.y=i,p.fill="#eaeaea",p.width=e.width,p.height=e.height,p.class="actor",p.rx=3,p.ry=3,d.append("line").attr("id","actor-man-torso"+_).attr("x1",o).attr("y1",i+25).attr("x2",o).attr("y2",i+45),d.append("line").attr("id","actor-man-arms"+_).attr("x1",o-18).attr("y1",i+33).attr("x2",o+18).attr("y2",i+33),d.append("line").attr("x1",o-18).attr("y1",i+60).attr("x2",o).attr("y2",i+45),d.append("line").attr("x1",o).attr("y1",i+45).attr("x2",o+18-2).attr("y2",i+60);const g=d.append("circle");g.attr("cx",e.x+e.width/2),g.attr("cy",i+10),g.attr("r",15),g.attr("width",e.width),g.attr("height",e.height);const u=d.node().getBBox();return e.height=u.height,q(a,(0,n.Wi)(e.description))(e.description,d,p.x,p.y+35,p.width,p.height,{class:`actor ${b}`},a),e.height}),"drawActorTypeActor"),A=(0,n.K2)((async function(t,e,a,s){switch(e.type){case"actor":return await v(t,e,a,s);case"participant":return await k(t,e,a,s)}}),"drawActor"),N=(0,n.K2)((function(t,e,a){const s=t.append("g");S(s,e),e.name&&q(a)(e.name,s,e.x,e.y+(e.textMaxHeight||0)/2,e.width,0,{class:"text"},a),s.lower()}),"drawBox"),M=(0,n.K2)((function(t){return t.append("g")}),"anchorElement"),D=(0,n.K2)((function(t,e,a,r,i){const n=(0,s.PB)(),o=e.anchored;n.x=e.startx,n.y=e.starty,n.class="activation"+i%3,n.width=e.stopx-e.startx,n.height=a-e.starty,T(o,n)}),"drawActivation"),O=(0,n.K2)((async function(t,e,a,r){const{boxMargin:i,boxTextMargin:o,labelBoxHeight:c,labelBoxWidth:l,messageFontFamily:d,messageFontSize:h,messageFontWeight:p}=r,g=t.append("g"),u=(0,n.K2)((function(t,e,a,s){return g.append("line").attr("x1",t).attr("y1",e).attr("x2",a).attr("y2",s).attr("class","loopLine")}),"drawLoopLine");u(e.startx,e.starty,e.stopx,e.starty),u(e.stopx,e.starty,e.stopx,e.stopy),u(e.startx,e.stopy,e.stopx,e.stopy),u(e.startx,e.starty,e.startx,e.stopy),void 0!==e.sections&&e.sections.forEach((function(t){u(e.startx,t.y,e.stopx,t.y).style("stroke-dasharray","3, 3")}));let x=(0,s.HT)();x.text=a,x.x=e.startx,x.y=e.starty,x.fontFamily=d,x.fontSize=h,x.fontWeight=p,x.anchor="middle",x.valign="middle",x.tspan=!1,x.width=l||50,x.height=c||20,x.textMargin=o,x.class="labelText",L(g,x),x=F(),x.text=e.title,x.x=e.startx+l/2+(e.stopx-e.startx)/2,x.y=e.starty+i+o,x.anchor="middle",x.valign="middle",x.textMargin=o,x.class="loopText",x.fontFamily=d,x.fontSize=h,x.fontWeight=p,x.wrap=!0;let y=(0,n.Wi)(x.text)?await w(g,x,e):I(g,x);if(void 0!==e.sectionTitles)for(const[s,m]of Object.entries(e.sectionTitles))if(m.message){x.text=m.message,x.x=e.startx+(e.stopx-e.startx)/2,x.y=e.sections[s].y+i+o,x.class="loopText",x.anchor="middle",x.valign="middle",x.tspan=!1,x.fontFamily=d,x.fontSize=h,x.fontWeight=p,x.wrap=e.wrap,(0,n.Wi)(x.text)?(e.starty=e.sections[s].y,await w(g,x,e)):I(g,x);let t=Math.round(y.map((t=>(t._groups||t)[0][0].getBBox().height)).reduce(((t,e)=>t+e)));e.sections[s].height+=t-(i+o)}return e.height=Math.round(e.stopy-e.starty),g}),"drawLoop"),S=(0,n.K2)((function(t,e){(0,s.lC)(t,e)}),"drawBackgroundRect"),R=(0,n.K2)((function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")}),"insertDatabaseIcon"),Y=(0,n.K2)((function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")}),"insertComputerIcon"),K=(0,n.K2)((function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")}),"insertClockIcon"),C=(0,n.K2)((function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto-start-reverse").append("path").attr("d","M -1 0 L 10 5 L 0 10 z")}),"insertArrowHead"),B=(0,n.K2)((function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")}),"insertArrowFilledHead"),$=(0,n.K2)((function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)}),"insertSequenceNumber"),V=(0,n.K2)((function(t){t.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5).append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")}),"insertArrowCrossHead"),F=(0,n.K2)((function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}}),"getTextObj"),W=(0,n.K2)((function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}}),"getNoteRect"),q=function(){function t(t,e,a,s,i,n,o){r(e.append("text").attr("x",a+i/2).attr("y",s+n/2+5).style("text-anchor","middle").text(t),o)}function e(t,e,a,s,o,c,l,d){const{actorFontSize:h,actorFontFamily:p,actorFontWeight:g}=d,[u,x]=(0,i.I5)(h),y=t.split(n.Y2.lineBreakRegex);for(let i=0;i<y.length;i++){const t=i*u-u*(y.length-1)/2,n=e.append("text").attr("x",a+o/2).attr("y",s).style("text-anchor","middle").style("font-size",x).style("font-weight",g).style("font-family",p);n.append("tspan").attr("x",a+o/2).attr("dy",t).text(y[i]),n.attr("y",s+c/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),r(n,l)}}function a(t,a,s,i,n,o,c,l){const d=a.append("switch"),h=d.append("foreignObject").attr("x",s).attr("y",i).attr("width",n).attr("height",o).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");h.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,d,s,i,n,o,c,l),r(h,c)}async function s(t,a,s,i,o,c,l,d){const h=await(0,n.Dl)(t,(0,n.zj)()),p=a.append("switch"),g=p.append("foreignObject").attr("x",s+o/2-h.width/2).attr("y",i+c/2-h.height/2).attr("width",h.width).attr("height",h.height).append("xhtml:div").style("height","100%").style("width","100%");g.append("div").style("text-align","center").style("vertical-align","middle").html(await(0,n.VJ)(t,(0,n.zj)())),e(t,p,s,i,o,c,l,d),r(g,l)}function r(t,e){for(const a in e)e.hasOwnProperty(a)&&t.attr(a,e[a])}return(0,n.K2)(t,"byText"),(0,n.K2)(e,"byTspan"),(0,n.K2)(a,"byFo"),(0,n.K2)(s,"byKatex"),(0,n.K2)(r,"_setTextAttrs"),function(r){return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?s:"fo"===r.textPlacement?a:"old"===r.textPlacement?t:e}}(),z=function(){function t(t,e,a,r,i,n,o){s(e.append("text").attr("x",a).attr("y",r).style("text-anchor","start").text(t),o)}function e(t,e,a,r,i,o,c,l){const{actorFontSize:d,actorFontFamily:h,actorFontWeight:p}=l,g=t.split(n.Y2.lineBreakRegex);for(let n=0;n<g.length;n++){const t=n*d-d*(g.length-1)/2,i=e.append("text").attr("x",a).attr("y",r).style("text-anchor","start").style("font-size",d).style("font-weight",p).style("font-family",h);i.append("tspan").attr("x",a).attr("dy",t).text(g[n]),i.attr("y",r+o/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),s(i,c)}}function a(t,a,r,i,n,o,c,l){const d=a.append("switch"),h=d.append("foreignObject").attr("x",r).attr("y",i).attr("width",n).attr("height",o).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");h.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,d,r,i,0,o,c,l),s(h,c)}function s(t,e){for(const a in e)e.hasOwnProperty(a)&&t.attr(a,e[a])}return(0,n.K2)(t,"byText"),(0,n.K2)(e,"byTspan"),(0,n.K2)(a,"byFo"),(0,n.K2)(s,"_setTextAttrs"),function(s){return"fo"===s.textPlacement?a:"old"===s.textPlacement?t:e}}(),H={drawRect:T,drawText:I,drawLabel:L,drawActor:A,drawBox:N,drawPopup:E,anchorElement:M,drawActivation:D,drawLoop:O,drawBackgroundRect:S,insertArrowHead:C,insertArrowFilledHead:B,insertSequenceNumber:$,insertArrowCrossHead:V,insertDatabaseIcon:R,insertComputerIcon:Y,insertClockIcon:K,getTextObj:F,getNoteRect:W,fixLifeLineHeights:P,sanitizeUrl:c.J},j={},U={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:(0,n.K2)((function(){return Math.max.apply(null,0===this.actors.length?[0]:this.actors.map((t=>t.height||0)))+(0===this.loops.length?0:this.loops.map((t=>t.height||0)).reduce(((t,e)=>t+e)))+(0===this.messages.length?0:this.messages.map((t=>t.height||0)).reduce(((t,e)=>t+e)))+(0===this.notes.length?0:this.notes.map((t=>t.height||0)).reduce(((t,e)=>t+e)))}),"getHeight"),clear:(0,n.K2)((function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]}),"clear"),addBox:(0,n.K2)((function(t){this.boxes.push(t)}),"addBox"),addActor:(0,n.K2)((function(t){this.actors.push(t)}),"addActor"),addLoop:(0,n.K2)((function(t){this.loops.push(t)}),"addLoop"),addMessage:(0,n.K2)((function(t){this.messages.push(t)}),"addMessage"),addNote:(0,n.K2)((function(t){this.notes.push(t)}),"addNote"),lastActor:(0,n.K2)((function(){return this.actors[this.actors.length-1]}),"lastActor"),lastLoop:(0,n.K2)((function(){return this.loops[this.loops.length-1]}),"lastLoop"),lastMessage:(0,n.K2)((function(){return this.messages[this.messages.length-1]}),"lastMessage"),lastNote:(0,n.K2)((function(){return this.notes[this.notes.length-1]}),"lastNote"),actors:[],boxes:[],loops:[],messages:[],notes:[]},init:(0,n.K2)((function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,rt((0,n.D7)())}),"init"),updateVal:(0,n.K2)((function(t,e,a,s){void 0===t[e]?t[e]=a:t[e]=s(a,t[e])}),"updateVal"),updateBounds:(0,n.K2)((function(t,e,a,s){const r=this;let i=0;function o(o){return(0,n.K2)((function(n){i++;const c=r.sequenceItems.length-i+1;r.updateVal(n,"starty",e-c*j.boxMargin,Math.min),r.updateVal(n,"stopy",s+c*j.boxMargin,Math.max),r.updateVal(U.data,"startx",t-c*j.boxMargin,Math.min),r.updateVal(U.data,"stopx",a+c*j.boxMargin,Math.max),"activation"!==o&&(r.updateVal(n,"startx",t-c*j.boxMargin,Math.min),r.updateVal(n,"stopx",a+c*j.boxMargin,Math.max),r.updateVal(U.data,"starty",e-c*j.boxMargin,Math.min),r.updateVal(U.data,"stopy",s+c*j.boxMargin,Math.max))}),"updateItemBounds")}(0,n.K2)(o,"updateFn"),this.sequenceItems.forEach(o()),this.activations.forEach(o("activation"))}),"updateBounds"),insert:(0,n.K2)((function(t,e,a,s){const r=n.Y2.getMin(t,a),i=n.Y2.getMax(t,a),o=n.Y2.getMin(e,s),c=n.Y2.getMax(e,s);this.updateVal(U.data,"startx",r,Math.min),this.updateVal(U.data,"starty",o,Math.min),this.updateVal(U.data,"stopx",i,Math.max),this.updateVal(U.data,"stopy",c,Math.max),this.updateBounds(r,o,i,c)}),"insert"),newActivation:(0,n.K2)((function(t,e,a){const s=a.get(t.from),r=it(t.from).length||0,i=s.x+s.width/2+(r-1)*j.activationWidth/2;this.activations.push({startx:i,starty:this.verticalPos+2,stopx:i+j.activationWidth,stopy:void 0,actor:t.from,anchored:H.anchorElement(e)})}),"newActivation"),endActivation:(0,n.K2)((function(t){const e=this.activations.map((function(t){return t.actor})).lastIndexOf(t.from);return this.activations.splice(e,1)[0]}),"endActivation"),createLoop:(0,n.K2)((function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{message:void 0,wrap:!1,width:void 0},e=arguments.length>1?arguments[1]:void 0;return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:t.message,wrap:t.wrap,width:t.width,height:0,fill:e}}),"createLoop"),newLoop:(0,n.K2)((function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{message:void 0,wrap:!1,width:void 0},e=arguments.length>1?arguments[1]:void 0;this.sequenceItems.push(this.createLoop(t,e))}),"newLoop"),endLoop:(0,n.K2)((function(){return this.sequenceItems.pop()}),"endLoop"),isLoopOverlap:(0,n.K2)((function(){return!!this.sequenceItems.length&&this.sequenceItems[this.sequenceItems.length-1].overlap}),"isLoopOverlap"),addSectionToLoop:(0,n.K2)((function(t){const e=this.sequenceItems.pop();e.sections=e.sections||[],e.sectionTitles=e.sectionTitles||[],e.sections.push({y:U.getVerticalPos(),height:0}),e.sectionTitles.push(t),this.sequenceItems.push(e)}),"addSectionToLoop"),saveVerticalPos:(0,n.K2)((function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)}),"saveVerticalPos"),resetVerticalPos:(0,n.K2)((function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)}),"resetVerticalPos"),bumpVerticalPos:(0,n.K2)((function(t){this.verticalPos=this.verticalPos+t,this.data.stopy=n.Y2.getMax(this.data.stopy,this.verticalPos)}),"bumpVerticalPos"),getVerticalPos:(0,n.K2)((function(){return this.verticalPos}),"getVerticalPos"),getBounds:(0,n.K2)((function(){return{bounds:this.data,models:this.models}}),"getBounds")},X=(0,n.K2)((async function(t,e){U.bumpVerticalPos(j.boxMargin),e.height=j.boxMargin,e.starty=U.getVerticalPos();const a=(0,s.PB)();a.x=e.startx,a.y=e.starty,a.width=e.width||j.width,a.class="note";const r=t.append("g"),i=H.drawRect(r,a),o=(0,s.HT)();o.x=e.startx,o.y=e.starty,o.width=a.width,o.dy="1em",o.text=e.message,o.class="noteText",o.fontFamily=j.noteFontFamily,o.fontSize=j.noteFontSize,o.fontWeight=j.noteFontWeight,o.anchor=j.noteAlign,o.textMargin=j.noteMargin,o.valign="center";const c=(0,n.Wi)(o.text)?await w(r,o):I(r,o),l=Math.round(c.map((t=>(t._groups||t)[0][0].getBBox().height)).reduce(((t,e)=>t+e)));i.attr("height",l+2*j.noteMargin),e.height+=l+2*j.noteMargin,U.bumpVerticalPos(l+2*j.noteMargin),e.stopy=e.starty+l+2*j.noteMargin,e.stopx=e.startx+a.width,U.insert(e.startx,e.starty,e.stopx,e.stopy),U.models.addNote(e)}),"drawNote"),J=(0,n.K2)((t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight})),"messageFont"),G=(0,n.K2)((t=>({fontFamily:t.noteFontFamily,fontSize:t.noteFontSize,fontWeight:t.noteFontWeight})),"noteFont"),Z=(0,n.K2)((t=>({fontFamily:t.actorFontFamily,fontSize:t.actorFontSize,fontWeight:t.actorFontWeight})),"actorFont");async function Q(t,e){U.bumpVerticalPos(10);const{startx:a,stopx:s,message:r}=e,o=n.Y2.splitBreaks(r).length,c=(0,n.Wi)(r),l=c?await(0,n.Dl)(r,(0,n.D7)()):i._K.calculateTextDimensions(r,J(j));if(!c){const t=l.height/o;e.height+=t,U.bumpVerticalPos(t)}let d,h=l.height-10;const p=l.width;if(a===s){d=U.getVerticalPos()+h,j.rightAngles||(h+=j.boxMargin,d=U.getVerticalPos()+h),h+=30;const t=n.Y2.getMax(p/2,j.width/2);U.insert(a-t,U.getVerticalPos()-10+h,s+t,U.getVerticalPos()+30+h)}else h+=j.boxMargin,d=U.getVerticalPos()+h,U.insert(a,d-10,s,d);return U.bumpVerticalPos(h),e.height+=h,e.stopy=e.starty+e.height,U.insert(e.fromBounds,e.starty,e.toBounds,e.stopy),d}(0,n.K2)(Q,"boundMessage");var tt=(0,n.K2)((async function(t,e,a,r){const{startx:o,stopx:c,starty:l,message:d,type:h,sequenceIndex:p,sequenceVisible:g}=e,u=i._K.calculateTextDimensions(d,J(j)),x=(0,s.HT)();x.x=o,x.y=l+10,x.width=c-o,x.class="messageText",x.dy="1em",x.text=d,x.fontFamily=j.messageFontFamily,x.fontSize=j.messageFontSize,x.fontWeight=j.messageFontWeight,x.anchor=j.messageAlign,x.valign="center",x.textMargin=j.wrapPadding,x.tspan=!1,(0,n.Wi)(x.text)?await w(t,x,{startx:o,stopx:c,starty:a}):I(t,x);const y=u.width;let m;o===c?m=j.rightAngles?t.append("path").attr("d",`M  ${o},${a} H ${o+n.Y2.getMax(j.width/2,y/2)} V ${a+25} H ${o}`):t.append("path").attr("d","M "+o+","+a+" C "+(o+60)+","+(a-10)+" "+(o+60)+","+(a+30)+" "+o+","+(a+20)):(m=t.append("line"),m.attr("x1",o),m.attr("y1",a),m.attr("x2",c),m.attr("y2",a)),h===r.db.LINETYPE.DOTTED||h===r.db.LINETYPE.DOTTED_CROSS||h===r.db.LINETYPE.DOTTED_POINT||h===r.db.LINETYPE.DOTTED_OPEN||h===r.db.LINETYPE.BIDIRECTIONAL_DOTTED?(m.style("stroke-dasharray","3, 3"),m.attr("class","messageLine1")):m.attr("class","messageLine0");let b="";j.arrowMarkerAbsolute&&(b=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,b=b.replace(/\(/g,"\\("),b=b.replace(/\)/g,"\\)")),m.attr("stroke-width",2),m.attr("stroke","none"),m.style("fill","none"),h!==r.db.LINETYPE.SOLID&&h!==r.db.LINETYPE.DOTTED||m.attr("marker-end","url("+b+"#arrowhead)"),h!==r.db.LINETYPE.BIDIRECTIONAL_SOLID&&h!==r.db.LINETYPE.BIDIRECTIONAL_DOTTED||(m.attr("marker-start","url("+b+"#arrowhead)"),m.attr("marker-end","url("+b+"#arrowhead)")),h!==r.db.LINETYPE.SOLID_POINT&&h!==r.db.LINETYPE.DOTTED_POINT||m.attr("marker-end","url("+b+"#filled-head)"),h!==r.db.LINETYPE.SOLID_CROSS&&h!==r.db.LINETYPE.DOTTED_CROSS||m.attr("marker-end","url("+b+"#crosshead)"),(g||j.showSequenceNumbers)&&(m.attr("marker-start","url("+b+"#sequencenumber)"),t.append("text").attr("x",o).attr("y",a+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(p))}),"drawMessage"),et=(0,n.K2)((function(t,e,a,s,r,i,o){let c,l=0,d=0,h=0;for(const p of s){const t=e.get(p),s=t.box;c&&c!=s&&(o||U.models.addBox(c),d+=j.boxMargin+c.margin),s&&s!=c&&(o||(s.x=l+d,s.y=r),d+=s.margin),t.width=t.width||j.width,t.height=n.Y2.getMax(t.height||j.height,j.height),t.margin=t.margin||j.actorMargin,h=n.Y2.getMax(h,t.height),a.get(t.name)&&(d+=t.width/2),t.x=l+d,t.starty=U.getVerticalPos(),U.insert(t.x,r,t.x+t.width,t.height),l+=t.width+d,t.box&&(t.box.width=l+s.margin-t.box.x),d=t.margin,c=t.box,U.models.addActor(t)}c&&!o&&U.models.addBox(c),U.bumpVerticalPos(h)}),"addActorRenderingData"),at=(0,n.K2)((async function(t,e,a,s){if(s){let s=0;U.bumpVerticalPos(2*j.boxMargin);for(const r of a){const a=e.get(r);a.stopy||(a.stopy=U.getVerticalPos());const i=await H.drawActor(t,a,j,!0);s=n.Y2.getMax(s,i)}U.bumpVerticalPos(s+j.boxMargin)}else for(const r of a){const a=e.get(r);await H.drawActor(t,a,j,!1)}}),"drawActors"),st=(0,n.K2)((function(t,e,a,s){let r=0,i=0;for(const n of a){const a=e.get(n),o=ht(a),c=H.drawPopup(t,a,o,j,j.forceMenus,s);c.height>r&&(r=c.height),c.width+a.x>i&&(i=c.width+a.x)}return{maxHeight:r,maxWidth:i}}),"drawActorsPopup"),rt=(0,n.K2)((function(t){(0,n.hH)(j,t),t.fontFamily&&(j.actorFontFamily=j.noteFontFamily=j.messageFontFamily=t.fontFamily),t.fontSize&&(j.actorFontSize=j.noteFontSize=j.messageFontSize=t.fontSize),t.fontWeight&&(j.actorFontWeight=j.noteFontWeight=j.messageFontWeight=t.fontWeight)}),"setConf"),it=(0,n.K2)((function(t){return U.activations.filter((function(e){return e.actor===t}))}),"actorActivations"),nt=(0,n.K2)((function(t,e){const a=e.get(t),s=it(t);return[s.reduce((function(t,e){return n.Y2.getMin(t,e.startx)}),a.x+a.width/2-1),s.reduce((function(t,e){return n.Y2.getMax(t,e.stopx)}),a.x+a.width/2+1)]}),"activationBounds");function ot(t,e,a,s,r){U.bumpVerticalPos(a);let o=s;if(e.id&&e.message&&t[e.id]){const a=t[e.id].width,r=J(j);e.message=i._K.wrapLabel(`[${e.message}]`,a-2*j.wrapPadding,r),e.width=a,e.wrap=!0;const c=i._K.calculateTextDimensions(e.message,r),l=n.Y2.getMax(c.height,j.labelBoxHeight);o=s+l,n.Rm.debug(`${l} - ${e.message}`)}r(e),U.bumpVerticalPos(o)}function ct(t,e,a,s,r,i,o){function c(a,s){a.x<r.get(t.from).x?(U.insert(e.stopx-s,e.starty,e.startx,e.stopy+a.height/2+j.noteMargin),e.stopx=e.stopx+s):(U.insert(e.startx,e.starty,e.stopx+s,e.stopy+a.height/2+j.noteMargin),e.stopx=e.stopx-s)}function l(a,s){a.x<r.get(t.to).x?(U.insert(e.startx-s,e.starty,e.stopx,e.stopy+a.height/2+j.noteMargin),e.startx=e.startx+s):(U.insert(e.stopx,e.starty,e.startx+s,e.stopy+a.height/2+j.noteMargin),e.startx=e.startx-s)}if((0,n.K2)(c,"receiverAdjustment"),(0,n.K2)(l,"senderAdjustment"),i.get(t.to)==s){const e=r.get(t.to);c(e,"actor"==e.type?21:e.width/2+3),e.starty=a-e.height/2,U.bumpVerticalPos(e.height/2)}else if(o.get(t.from)==s){const e=r.get(t.from);if(j.mirrorActors){l(e,"actor"==e.type?18:e.width/2)}e.stopy=a-e.height/2,U.bumpVerticalPos(e.height/2)}else if(o.get(t.to)==s){const e=r.get(t.to);if(j.mirrorActors){c(e,"actor"==e.type?21:e.width/2+3)}e.stopy=a-e.height/2,U.bumpVerticalPos(e.height/2)}}(0,n.K2)(ot,"adjustLoopHeightForWrap"),(0,n.K2)(ct,"adjustCreatedDestroyedData");var lt=(0,n.K2)((async function(t,e,a,s){const{securityLevel:r,sequence:i}=(0,n.D7)();let c;j=i,"sandbox"===r&&(c=(0,o.Ltv)("#i"+e));const l="sandbox"===r?(0,o.Ltv)(c.nodes()[0].contentDocument.body):(0,o.Ltv)("body"),d="sandbox"===r?c.nodes()[0].contentDocument:document;U.init(),n.Rm.debug(s.db);const h="sandbox"===r?l.select(`[id="${e}"]`):(0,o.Ltv)(`[id="${e}"]`),p=s.db.getActors(),g=s.db.getCreatedActors(),u=s.db.getDestroyedActors(),x=s.db.getBoxes();let y=s.db.getActorKeys();const m=s.db.getMessages(),b=s.db.getDiagramTitle(),T=s.db.hasAtLeastOneBox(),E=s.db.hasAtLeastOneBoxWithTitle(),f=await dt(p,m,s);if(j.height=await pt(p,f,x),H.insertComputerIcon(h),H.insertDatabaseIcon(h),H.insertClockIcon(h),T&&(U.bumpVerticalPos(j.boxMargin),E&&U.bumpVerticalPos(x[0].textMaxHeight)),!0===j.hideUnusedParticipants){const t=new Set;m.forEach((e=>{t.add(e.from),t.add(e.to)})),y=y.filter((e=>t.has(e)))}et(h,p,g,y,0,m,!1);const w=await xt(m,p,f,s);function I(t,e){const a=U.endActivation(t);a.starty+18>e&&(a.starty=e-6,e+=12),H.drawActivation(h,a,e,j,it(t.from).length),U.insert(a.startx,e-10,a.stopx,e)}H.insertArrowHead(h),H.insertArrowCrossHead(h),H.insertArrowFilledHead(h),H.insertSequenceNumber(h),(0,n.K2)(I,"activeEnd");let L=1,_=1;const k=[],v=[];let A=0;for(const o of m){let t,e,a;switch(o.type){case s.db.LINETYPE.NOTE:U.resetVerticalPos(),e=o.noteModel,await X(h,e);break;case s.db.LINETYPE.ACTIVE_START:U.newActivation(o,h,p);break;case s.db.LINETYPE.ACTIVE_END:I(o,U.getVerticalPos());break;case s.db.LINETYPE.LOOP_START:ot(w,o,j.boxMargin,j.boxMargin+j.boxTextMargin,(t=>U.newLoop(t)));break;case s.db.LINETYPE.LOOP_END:t=U.endLoop(),await H.drawLoop(h,t,"loop",j),U.bumpVerticalPos(t.stopy-U.getVerticalPos()),U.models.addLoop(t);break;case s.db.LINETYPE.RECT_START:ot(w,o,j.boxMargin,j.boxMargin,(t=>U.newLoop(void 0,t.message)));break;case s.db.LINETYPE.RECT_END:t=U.endLoop(),v.push(t),U.models.addLoop(t),U.bumpVerticalPos(t.stopy-U.getVerticalPos());break;case s.db.LINETYPE.OPT_START:ot(w,o,j.boxMargin,j.boxMargin+j.boxTextMargin,(t=>U.newLoop(t)));break;case s.db.LINETYPE.OPT_END:t=U.endLoop(),await H.drawLoop(h,t,"opt",j),U.bumpVerticalPos(t.stopy-U.getVerticalPos()),U.models.addLoop(t);break;case s.db.LINETYPE.ALT_START:ot(w,o,j.boxMargin,j.boxMargin+j.boxTextMargin,(t=>U.newLoop(t)));break;case s.db.LINETYPE.ALT_ELSE:ot(w,o,j.boxMargin+j.boxTextMargin,j.boxMargin,(t=>U.addSectionToLoop(t)));break;case s.db.LINETYPE.ALT_END:t=U.endLoop(),await H.drawLoop(h,t,"alt",j),U.bumpVerticalPos(t.stopy-U.getVerticalPos()),U.models.addLoop(t);break;case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:ot(w,o,j.boxMargin,j.boxMargin+j.boxTextMargin,(t=>U.newLoop(t))),U.saveVerticalPos();break;case s.db.LINETYPE.PAR_AND:ot(w,o,j.boxMargin+j.boxTextMargin,j.boxMargin,(t=>U.addSectionToLoop(t)));break;case s.db.LINETYPE.PAR_END:t=U.endLoop(),await H.drawLoop(h,t,"par",j),U.bumpVerticalPos(t.stopy-U.getVerticalPos()),U.models.addLoop(t);break;case s.db.LINETYPE.AUTONUMBER:L=o.message.start||L,_=o.message.step||_,o.message.visible?s.db.enableSequenceNumbers():s.db.disableSequenceNumbers();break;case s.db.LINETYPE.CRITICAL_START:ot(w,o,j.boxMargin,j.boxMargin+j.boxTextMargin,(t=>U.newLoop(t)));break;case s.db.LINETYPE.CRITICAL_OPTION:ot(w,o,j.boxMargin+j.boxTextMargin,j.boxMargin,(t=>U.addSectionToLoop(t)));break;case s.db.LINETYPE.CRITICAL_END:t=U.endLoop(),await H.drawLoop(h,t,"critical",j),U.bumpVerticalPos(t.stopy-U.getVerticalPos()),U.models.addLoop(t);break;case s.db.LINETYPE.BREAK_START:ot(w,o,j.boxMargin,j.boxMargin+j.boxTextMargin,(t=>U.newLoop(t)));break;case s.db.LINETYPE.BREAK_END:t=U.endLoop(),await H.drawLoop(h,t,"break",j),U.bumpVerticalPos(t.stopy-U.getVerticalPos()),U.models.addLoop(t);break;default:try{a=o.msgModel,a.starty=U.getVerticalPos(),a.sequenceIndex=L,a.sequenceVisible=s.db.showSequenceNumbers();const t=await Q(0,a);ct(o,a,t,A,p,g,u),k.push({messageModel:a,lineStartY:t}),U.models.addMessage(a)}catch(K){n.Rm.error("error while drawing message",K)}}[s.db.LINETYPE.SOLID_OPEN,s.db.LINETYPE.DOTTED_OPEN,s.db.LINETYPE.SOLID,s.db.LINETYPE.DOTTED,s.db.LINETYPE.SOLID_CROSS,s.db.LINETYPE.DOTTED_CROSS,s.db.LINETYPE.SOLID_POINT,s.db.LINETYPE.DOTTED_POINT,s.db.LINETYPE.BIDIRECTIONAL_SOLID,s.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(o.type)&&(L+=_),A++}n.Rm.debug("createdActors",g),n.Rm.debug("destroyedActors",u),await at(h,p,y,!1);for(const n of k)await tt(h,n.messageModel,n.lineStartY,s);j.mirrorActors&&await at(h,p,y,!0),v.forEach((t=>H.drawBackgroundRect(h,t))),P(h,p,y,j);for(const n of U.models.boxes)n.height=U.getVerticalPos()-n.y,U.insert(n.x,n.y,n.x+n.width,n.height),n.startx=n.x,n.starty=n.y,n.stopx=n.startx+n.width,n.stopy=n.starty+n.height,n.stroke="rgb(0,0,0, 0.5)",H.drawBox(h,n,j);T&&U.bumpVerticalPos(j.boxMargin);const N=st(h,p,y,d),{bounds:M}=U.getBounds();void 0===M.startx&&(M.startx=0),void 0===M.starty&&(M.starty=0),void 0===M.stopx&&(M.stopx=0),void 0===M.stopy&&(M.stopy=0);let D=M.stopy-M.starty;D<N.maxHeight&&(D=N.maxHeight);let O=D+2*j.diagramMarginY;j.mirrorActors&&(O=O-j.boxMargin+j.bottomMarginAdj);let S=M.stopx-M.startx;S<N.maxWidth&&(S=N.maxWidth);const R=S+2*j.diagramMarginX;b&&h.append("text").text(b).attr("x",(M.stopx-M.startx)/2-2*j.diagramMarginX).attr("y",-25),(0,n.a$)(h,O,R,j.useMaxWidth);const Y=b?40:0;h.attr("viewBox",M.startx-j.diagramMarginX+" -"+(j.diagramMarginY+Y)+" "+R+" "+(O+Y)),n.Rm.debug("models:",U.models)}),"draw");async function dt(t,e,a){const s={};for(const r of e)if(t.get(r.to)&&t.get(r.from)){const e=t.get(r.to);if(r.placement===a.db.PLACEMENT.LEFTOF&&!e.prevActor)continue;if(r.placement===a.db.PLACEMENT.RIGHTOF&&!e.nextActor)continue;const o=void 0!==r.placement,c=!o,l=o?G(j):J(j),d=r.wrap?i._K.wrapLabel(r.message,j.width-2*j.wrapPadding,l):r.message,h=((0,n.Wi)(d)?await(0,n.Dl)(r.message,(0,n.D7)()):i._K.calculateTextDimensions(d,l)).width+2*j.wrapPadding;c&&r.from===e.nextActor?s[r.to]=n.Y2.getMax(s[r.to]||0,h):c&&r.from===e.prevActor?s[r.from]=n.Y2.getMax(s[r.from]||0,h):c&&r.from===r.to?(s[r.from]=n.Y2.getMax(s[r.from]||0,h/2),s[r.to]=n.Y2.getMax(s[r.to]||0,h/2)):r.placement===a.db.PLACEMENT.RIGHTOF?s[r.from]=n.Y2.getMax(s[r.from]||0,h):r.placement===a.db.PLACEMENT.LEFTOF?s[e.prevActor]=n.Y2.getMax(s[e.prevActor]||0,h):r.placement===a.db.PLACEMENT.OVER&&(e.prevActor&&(s[e.prevActor]=n.Y2.getMax(s[e.prevActor]||0,h/2)),e.nextActor&&(s[r.from]=n.Y2.getMax(s[r.from]||0,h/2)))}return n.Rm.debug("maxMessageWidthPerActor:",s),s}(0,n.K2)(dt,"getMaxMessageWidthPerActor");var ht=(0,n.K2)((function(t){let e=0;const a=Z(j);for(const s in t.links){const t=i._K.calculateTextDimensions(s,a).width+2*j.wrapPadding+2*j.boxMargin;e<t&&(e=t)}return e}),"getRequiredPopupWidth");async function pt(t,e,a){let s=0;for(const o of t.keys()){const e=t.get(o);e.wrap&&(e.description=i._K.wrapLabel(e.description,j.width-2*j.wrapPadding,Z(j)));const a=(0,n.Wi)(e.description)?await(0,n.Dl)(e.description,(0,n.D7)()):i._K.calculateTextDimensions(e.description,Z(j));e.width=e.wrap?j.width:n.Y2.getMax(j.width,a.width+2*j.wrapPadding),e.height=e.wrap?n.Y2.getMax(a.height,j.height):j.height,s=n.Y2.getMax(s,e.height)}for(const i in e){const a=t.get(i);if(!a)continue;const s=t.get(a.nextActor);if(!s){const t=e[i]+j.actorMargin-a.width/2;a.margin=n.Y2.getMax(t,j.actorMargin);continue}const r=e[i]+j.actorMargin-a.width/2-s.width/2;a.margin=n.Y2.getMax(r,j.actorMargin)}let r=0;return a.forEach((e=>{const a=J(j);let s=e.actorKeys.reduce(((e,a)=>e+(t.get(a).width+(t.get(a).margin||0))),0);s-=2*j.boxTextMargin,e.wrap&&(e.name=i._K.wrapLabel(e.name,s-2*j.wrapPadding,a));const o=i._K.calculateTextDimensions(e.name,a);r=n.Y2.getMax(o.height,r);const c=n.Y2.getMax(s,o.width+2*j.wrapPadding);if(e.margin=j.boxTextMargin,s<c){const t=(c-s)/2;e.margin+=t}})),a.forEach((t=>t.textMaxHeight=r)),n.Y2.getMax(s,j.height)}(0,n.K2)(pt,"calculateActorMargins");var gt=(0,n.K2)((async function(t,e,a){const s=e.get(t.from),r=e.get(t.to),o=s.x,c=r.x,l=t.wrap&&t.message;let d=(0,n.Wi)(t.message)?await(0,n.Dl)(t.message,(0,n.D7)()):i._K.calculateTextDimensions(l?i._K.wrapLabel(t.message,j.width,G(j)):t.message,G(j));const h={width:l?j.width:n.Y2.getMax(j.width,d.width+2*j.noteMargin),height:0,startx:s.x,stopx:0,starty:0,stopy:0,message:t.message};return t.placement===a.db.PLACEMENT.RIGHTOF?(h.width=l?n.Y2.getMax(j.width,d.width):n.Y2.getMax(s.width/2+r.width/2,d.width+2*j.noteMargin),h.startx=o+(s.width+j.actorMargin)/2):t.placement===a.db.PLACEMENT.LEFTOF?(h.width=l?n.Y2.getMax(j.width,d.width+2*j.noteMargin):n.Y2.getMax(s.width/2+r.width/2,d.width+2*j.noteMargin),h.startx=o-h.width+(s.width-j.actorMargin)/2):t.to===t.from?(d=i._K.calculateTextDimensions(l?i._K.wrapLabel(t.message,n.Y2.getMax(j.width,s.width),G(j)):t.message,G(j)),h.width=l?n.Y2.getMax(j.width,s.width):n.Y2.getMax(s.width,j.width,d.width+2*j.noteMargin),h.startx=o+(s.width-h.width)/2):(h.width=Math.abs(o+s.width/2-(c+r.width/2))+j.actorMargin,h.startx=o<c?o+s.width/2-j.actorMargin/2:c+r.width/2-j.actorMargin/2),l&&(h.message=i._K.wrapLabel(t.message,h.width-2*j.wrapPadding,G(j))),n.Rm.debug(`NM:[${h.startx},${h.stopx},${h.starty},${h.stopy}:${h.width},${h.height}=${t.message}]`),h}),"buildNoteModel"),ut=(0,n.K2)((function(t,e,a){if(![a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN,a.db.LINETYPE.SOLID,a.db.LINETYPE.DOTTED,a.db.LINETYPE.SOLID_CROSS,a.db.LINETYPE.DOTTED_CROSS,a.db.LINETYPE.SOLID_POINT,a.db.LINETYPE.DOTTED_POINT,a.db.LINETYPE.BIDIRECTIONAL_SOLID,a.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type))return{};const[s,r]=nt(t.from,e),[o,c]=nt(t.to,e),l=s<=o;let d=l?r:s,h=l?o:c;const p=Math.abs(o-c)>2,g=(0,n.K2)((t=>l?-t:t),"adjustValue");t.from===t.to?h=d:(t.activate&&!p&&(h+=g(j.activationWidth/2-1)),[a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN].includes(t.type)||(h+=g(3)),[a.db.LINETYPE.BIDIRECTIONAL_SOLID,a.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type)&&(d-=g(3)));const u=[s,r,o,c],x=Math.abs(d-h);t.wrap&&t.message&&(t.message=i._K.wrapLabel(t.message,n.Y2.getMax(x+2*j.wrapPadding,j.width),J(j)));const y=i._K.calculateTextDimensions(t.message,J(j));return{width:n.Y2.getMax(t.wrap?0:y.width+2*j.wrapPadding,x+2*j.wrapPadding,j.width),height:0,startx:d,stopx:h,starty:0,stopy:0,message:t.message,type:t.type,wrap:t.wrap,fromBounds:Math.min.apply(null,u),toBounds:Math.max.apply(null,u)}}),"buildMessageModel"),xt=(0,n.K2)((async function(t,e,a,s){const r={},i=[];let o,c,l;for(const d of t){switch(d.type){case s.db.LINETYPE.LOOP_START:case s.db.LINETYPE.ALT_START:case s.db.LINETYPE.OPT_START:case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:case s.db.LINETYPE.CRITICAL_START:case s.db.LINETYPE.BREAK_START:i.push({id:d.id,msg:d.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case s.db.LINETYPE.ALT_ELSE:case s.db.LINETYPE.PAR_AND:case s.db.LINETYPE.CRITICAL_OPTION:d.message&&(o=i.pop(),r[o.id]=o,r[d.id]=o,i.push(o));break;case s.db.LINETYPE.LOOP_END:case s.db.LINETYPE.ALT_END:case s.db.LINETYPE.OPT_END:case s.db.LINETYPE.PAR_END:case s.db.LINETYPE.CRITICAL_END:case s.db.LINETYPE.BREAK_END:o=i.pop(),r[o.id]=o;break;case s.db.LINETYPE.ACTIVE_START:{const t=e.get(d.from?d.from:d.to.actor),a=it(d.from?d.from:d.to.actor).length,s=t.x+t.width/2+(a-1)*j.activationWidth/2,r={startx:s,stopx:s+j.activationWidth,actor:d.from,enabled:!0};U.activations.push(r)}break;case s.db.LINETYPE.ACTIVE_END:{const t=U.activations.map((t=>t.actor)).lastIndexOf(d.from);U.activations.splice(t,1).splice(0,1)}}void 0!==d.placement?(c=await gt(d,e,s),d.noteModel=c,i.forEach((t=>{o=t,o.from=n.Y2.getMin(o.from,c.startx),o.to=n.Y2.getMax(o.to,c.startx+c.width),o.width=n.Y2.getMax(o.width,Math.abs(o.from-o.to))-j.labelBoxWidth}))):(l=ut(d,e,s),d.msgModel=l,l.startx&&l.stopx&&i.length>0&&i.forEach((t=>{if(o=t,l.startx===l.stopx){const t=e.get(d.from),a=e.get(d.to);o.from=n.Y2.getMin(t.x-l.width/2,t.x-t.width/2,o.from),o.to=n.Y2.getMax(a.x+l.width/2,a.x+t.width/2,o.to),o.width=n.Y2.getMax(o.width,Math.abs(o.to-o.from))-j.labelBoxWidth}else o.from=n.Y2.getMin(l.startx,o.from),o.to=n.Y2.getMax(l.stopx,o.to),o.width=n.Y2.getMax(o.width,l.width)-j.labelBoxWidth})))}return U.activations=[],n.Rm.debug("Loop type widths:",r),r}),"calculateLoopBounds"),yt={bounds:U,drawActors:at,drawActorsPopup:st,setConf:rt,draw:lt},mt={parser:d,get db(){return new u},renderer:yt,styles:x,init:(0,n.K2)((t=>{t.sequence||(t.sequence={}),t.wrap&&(t.sequence.wrap=t.wrap,(0,n.XV)({sequence:{wrap:t.wrap}}))}),"init")}}}]);
//# sourceMappingURL=886.5850109a.chunk.js.map