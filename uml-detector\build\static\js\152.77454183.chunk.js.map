{"version": 3, "file": "static/js/152.77454183.chunk.js", "mappings": "sQAKA,SAASA,EAAMC,GACb,IAAIC,EAAO,CACTC,QAAS,CACPC,SAAUH,EAAEI,aACZC,WAAYL,EAAEM,eACdC,SAAUP,EAAEQ,cAEdC,MAAOC,EAAWV,GAClBW,MAAOC,EAAWZ,IAKpB,OAHKa,EAAAA,EAAcb,EAAEc,WACnBb,EAAKc,MAAQF,EAAAA,EAAQb,EAAEc,UAElBb,CACT,CAEA,SAASS,EAAWV,GAClB,OAAOa,EAAAA,EAAMb,EAAES,SAAS,SAAUO,GAChC,IAAIC,EAAYjB,EAAEkB,KAAKF,GACnBG,EAASnB,EAAEmB,OAAOH,GAClBE,EAAO,CAAEF,EAAGA,GAOhB,OANKH,EAAAA,EAAcI,KACjBC,EAAKH,MAAQE,GAEVJ,EAAAA,EAAcM,KACjBD,EAAKC,OAASA,GAETD,CACT,GACF,CAEA,SAASN,EAAWZ,GAClB,OAAOa,EAAAA,EAAMb,EAAEW,SAAS,SAAUS,GAChC,IAAIC,EAAYrB,EAAEsB,KAAKF,GACnBE,EAAO,CAAEN,EAAGI,EAAEJ,EAAGO,EAAGH,EAAEG,GAO1B,OANKV,EAAAA,EAAcO,EAAEI,QACnBF,EAAKE,KAAOJ,EAAEI,MAEXX,EAAAA,EAAcQ,KACjBC,EAAKP,MAAQM,GAERC,CACT,GACF,C,aCXIG,EAA4B,IAAIC,IAChCC,EAA8B,IAAID,IAClCE,EAA0B,IAAIF,IAC9BG,GAAyBC,EAAAA,EAAAA,KAAO,KAClCH,EAAYI,QACZH,EAAQG,QACRN,EAAUM,OAAO,GAChB,SACCC,GAA+BF,EAAAA,EAAAA,KAAO,CAACG,EAAIC,KAC7C,MAAMC,EAAsBR,EAAYS,IAAIF,IAAe,GAE3D,OADAG,EAAAA,GAAIC,MAAM,kBAAmBJ,EAAY,IAAKD,EAAI,MAAOE,EAAoBI,SAASN,IAC/EE,EAAoBI,SAASN,EAAG,GACtC,gBACCO,GAAgCV,EAAAA,EAAAA,KAAO,CAACR,EAAMmB,KAChD,MAAMC,EAAqBf,EAAYS,IAAIK,IAAc,GAGzD,OAFAJ,EAAAA,GAAIM,KAAK,kBAAmBF,EAAW,OAAQC,GAC/CL,EAAAA,GAAIM,KAAK,WAAYrB,GACjBA,EAAKN,IAAMyB,GAAanB,EAAKC,IAAMkB,IAGlCC,EAIEA,EAAmBH,SAASjB,EAAKN,IAAMgB,EAAaV,EAAKN,EAAGyB,IAAcT,EAAaV,EAAKC,EAAGkB,IAAcC,EAAmBH,SAASjB,EAAKC,IAHnJc,EAAAA,GAAIO,MAAM,SAAUH,EAAW,wBACxB,GAE8I,GACtJ,iBACCI,GAAuBf,EAAAA,EAAAA,KAAO,CAACW,EAAW3B,EAAOgC,EAAUC,KAC7DV,EAAAA,GAAIW,KACF,uBACAP,EACA,OACAM,EACA,OACAjC,EAAMI,KAAKuB,GACXM,GAEF,MAAMtC,EAAQK,EAAMmC,SAASR,IAAc,GACvCA,IAAcM,GAChBtC,EAAMyC,KAAKT,GAEbJ,EAAAA,GAAIW,KAAK,4BAA6BP,EAAW,QAAShC,GAC1DA,EAAM0C,SAASjC,IACb,GAAIJ,EAAMmC,SAAS/B,GAAMkC,OAAS,EAChCP,EAAK3B,EAAMJ,EAAOgC,EAAUC,OACvB,CACL,MAAMM,EAAOvC,EAAMI,KAAKA,GACxBmB,EAAAA,GAAIM,KAAK,MAAOzB,EAAM,OAAQ6B,EAAQ,gBAAiBN,GACvDK,EAASQ,QAAQpC,EAAMmC,GACnBN,IAAWjC,EAAMK,OAAOD,KAC1BmB,EAAAA,GAAIW,KAAK,iBAAkB9B,EAAMJ,EAAMK,OAAOD,IAC9C4B,EAASS,UAAUrC,EAAMJ,EAAMK,OAAOD,KAEpCuB,IAAcM,GAAU7B,IAASuB,GACnCJ,EAAAA,GAAIO,MAAM,iBAAkB1B,EAAMuB,GAClCK,EAASS,UAAUrC,EAAMuB,KAEzBJ,EAAAA,GAAIM,KAAK,WAAYF,EAAW,OAAQM,EAAQ,OAAQjC,EAAMI,KAAKuB,GAAYM,GAC/EV,EAAAA,GAAIO,MACF,+BACA1B,EACA,mBACAuB,IAAcM,EACd,mBACA7B,IAASuB,IAGb,MAAM9B,EAAQG,EAAMH,MAAMO,GAC1BmB,EAAAA,GAAIO,MAAM,gBAAiBjC,GAC3BA,EAAMwC,SAAS7B,IACbe,EAAAA,GAAIM,KAAK,OAAQrB,GACjB,MAAMkC,EAAQ1C,EAAMQ,KAAKA,EAAKN,EAAGM,EAAKC,EAAGD,EAAKE,MAC9Ca,EAAAA,GAAIM,KAAK,YAAaa,EAAOT,GAC7B,IACMP,EAAclB,EAAMyB,IACtBV,EAAAA,GAAIM,KAAK,cAAerB,EAAKN,EAAGM,EAAKC,EAAGiC,EAAOlC,EAAKE,MACpDsB,EAASW,QAAQnC,EAAKN,EAAGM,EAAKC,EAAGiC,EAAOlC,EAAKE,MAC7Ca,EAAAA,GAAIM,KAAK,kBAAmBG,EAASnC,QAASmC,EAASxB,KAAKwB,EAASnC,QAAQ,MAE7E0B,EAAAA,GAAIM,KACF,yBACArB,EAAKN,EACL,SACAM,EAAKC,EACL,YACAwB,EACA,cACAN,EAGN,CAAE,MAAOrB,GACPiB,EAAAA,GAAIqB,MAAMtC,EACZ,IAEJ,CACAiB,EAAAA,GAAIO,MAAM,gBAAiB1B,GAC3BJ,EAAM6C,WAAWzC,EAAK,GACtB,GACD,QACC0C,GAAqC9B,EAAAA,EAAAA,KAAO,CAACG,EAAInB,KACnD,MAAMmC,EAAWnC,EAAMmC,SAAShB,GAChC,IAAI4B,EAAM,IAAIZ,GACd,IAAK,MAAMa,KAASb,EAClBrB,EAAQmC,IAAID,EAAO7B,GACnB4B,EAAM,IAAIA,KAAQD,EAAmBE,EAAOhD,IAE9C,OAAO+C,CAAG,GACT,sBACCG,GAAkClC,EAAAA,EAAAA,KAAO,CAAChB,EAAOmD,EAAKC,KACxD,MAAMC,EAASrD,EAAMH,QAAQyD,QAAQ9C,GAASA,EAAKN,IAAMiD,GAAO3C,EAAKC,IAAM0C,IACrEI,EAASvD,EAAMH,QAAQyD,QAAQ9C,GAASA,EAAKN,IAAMkD,GAAO5C,EAAKC,IAAM2C,IACrEI,EAAaH,EAAOI,KAAKjD,IACtB,CAAEN,EAAGM,EAAKN,IAAMiD,EAAMC,EAAM5C,EAAKN,EAAGO,EAAGD,EAAKC,IAAM0C,EAAMA,EAAM3C,EAAKC,MAEtEiD,EAAaH,EAAOE,KAAKjD,IACtB,CAAEN,EAAGM,EAAKN,EAAGO,EAAGD,EAAKC,MAK9B,OAHe+C,EAAWF,QAAQK,GACzBD,EAAWE,MAAMpD,GAASmD,EAAQzD,IAAMM,EAAKN,GAAKyD,EAAQlD,IAAMD,EAAKC,KAEjE,GACZ,mBACCoD,GAAsC7C,EAAAA,EAAAA,KAAO,CAACG,EAAInB,EAAO2B,KAC3D,MAAMQ,EAAWnC,EAAMmC,SAAShB,GAEhC,GADAI,EAAAA,GAAIC,MAAM,4BAA6BL,EAAIgB,GACvCA,EAASG,OAAS,EACpB,OAAOnB,EAET,IAAI2C,EACJ,IAAK,MAAMd,KAASb,EAAU,CAC5B,MAAM4B,EAAMF,EAAoBb,EAAOhD,EAAO2B,GACxCqC,EAAcd,EAAgBlD,EAAO2B,EAAWoC,GACtD,GAAIA,EAAK,CACP,KAAIC,EAAY1B,OAAS,GAGvB,OAAOyB,EAFPD,EAAUC,CAId,CACF,CACA,OAAOD,CAAO,GACb,uBACCG,GAA8BjD,EAAAA,EAAAA,KAAQG,GACnCR,EAAUuD,IAAI/C,IAGdR,EAAUW,IAAIH,GAAIgD,qBAGnBxD,EAAUuD,IAAI/C,GACTR,EAAUW,IAAIH,GAAIA,GANlBA,GASR,eACCiD,GAAyCpD,EAAAA,EAAAA,KAAO,CAAChB,EAAOqE,KAC1D,IAAKrE,GAASqE,EAAQ,GACpB9C,EAAAA,GAAIO,MAAM,6BADZ,CAIEP,EAAAA,GAAIO,MAAM,qBAEZ9B,EAAML,QAAQ0C,SAAQ,SAASlB,GACZnB,EAAMmC,SAAShB,GACnBmB,OAAS,IACpBf,EAAAA,GAAIW,KACF,qBACAf,EACA,6BACA0C,EAAoB1C,EAAInB,EAAOmB,IAEjCN,EAAYoC,IAAI9B,EAAI2B,EAAmB3B,EAAInB,IAC3CW,EAAUsC,IAAI9B,EAAI,CAAEA,GAAI0C,EAAoB1C,EAAInB,EAAOmB,GAAKmD,YAAatE,EAAMI,KAAKe,KAExF,IACAnB,EAAML,QAAQ0C,SAAQ,SAASlB,GAC7B,MAAMgB,EAAWnC,EAAMmC,SAAShB,GAC1BtB,EAAQG,EAAMH,QAChBsC,EAASG,OAAS,GACpBf,EAAAA,GAAIO,MAAM,qBAAsBX,EAAIN,GACpChB,EAAMwC,SAAS7B,IACFU,EAAaV,EAAKN,EAAGiB,GACrBD,EAAaV,EAAKC,EAAGU,KAE9BI,EAAAA,GAAIW,KAAK,SAAU1B,EAAM,mBAAoBW,GAC7CI,EAAAA,GAAIW,KAAK,sBAAuBf,EAAI,KAAMN,EAAYS,IAAIH,IAC1DR,EAAUW,IAAIH,GAAIgD,qBAAsB,EAC1C,KAGF5C,EAAAA,GAAIO,MAAM,iBAAkBX,EAAIN,EAEpC,IACA,IAAK,IAAIM,KAAMR,EAAU4D,OAAQ,CAC/B,MAAMC,EAAkB7D,EAAUW,IAAIH,GAAIA,GACpCd,EAASL,EAAMK,OAAOmE,GACxBnE,IAAWc,GAAMR,EAAUuD,IAAI7D,KAAYM,EAAUW,IAAIjB,GAAQ8D,sBACnExD,EAAUW,IAAIH,GAAIA,GAAKd,EAE3B,CACAL,EAAMH,QAAQwC,SAAQ,SAAS/B,GAC7B,MAAME,EAAOR,EAAMQ,KAAKF,GACxBiB,EAAAA,GAAIW,KAAK,QAAU5B,EAAEJ,EAAI,OAASI,EAAEG,EAAI,KAAOgE,KAAKC,UAAUpE,IAC9DiB,EAAAA,GAAIW,KAAK,QAAU5B,EAAEJ,EAAI,OAASI,EAAEG,EAAI,KAAOgE,KAAKC,UAAU1E,EAAMQ,KAAKF,KACzE,IAAIJ,EAAII,EAAEJ,EACNO,EAAIH,EAAEG,EAYV,GAXAc,EAAAA,GAAIW,KACF,UACAvB,EACA,OACAL,EAAEJ,EACFI,EAAEG,EACF,gBACAE,EAAUW,IAAIhB,EAAEJ,GAChB,QACAS,EAAUW,IAAIhB,EAAEG,IAEdE,EAAUW,IAAIhB,EAAEJ,IAAMS,EAAUW,IAAIhB,EAAEG,GAAI,CAK5C,GAJAc,EAAAA,GAAIW,KAAK,mCAAoC5B,EAAEJ,EAAGI,EAAEG,EAAGH,EAAEI,MACzDR,EAAI+D,EAAY3D,EAAEJ,GAClBO,EAAIwD,EAAY3D,EAAEG,GAClBT,EAAM2E,WAAWrE,EAAEJ,EAAGI,EAAEG,EAAGH,EAAEI,MACzBR,IAAMI,EAAEJ,EAAG,CACb,MAAMG,EAASL,EAAMK,OAAOH,GAC5BS,EAAUW,IAAIjB,GAAQ8D,qBAAsB,EAC5C3D,EAAKoE,YAActE,EAAEJ,CACvB,CACA,GAAIO,IAAMH,EAAEG,EAAG,CACb,MAAMJ,EAASL,EAAMK,OAAOI,GAC5BE,EAAUW,IAAIjB,GAAQ8D,qBAAsB,EAC5C3D,EAAKqE,UAAYvE,EAAEG,CACrB,CACAc,EAAAA,GAAIW,KAAK,yBAA0BhC,EAAGO,EAAGH,EAAEI,MAC3CV,EAAM2C,QAAQzC,EAAGO,EAAGD,EAAMF,EAAEI,KAC9B,CACF,IACAa,EAAAA,GAAIW,KAAK,iBAAkB4C,EAAmB9E,IAC9C+E,EAAU/E,EAAO,GACjBuB,EAAAA,GAAIC,MAAMb,EA7EV,CA6EoB,GACnB,0BACCoE,GAA4B/D,EAAAA,EAAAA,KAAO,CAAChB,EAAOqE,KAE7C,GADA9C,EAAAA,GAAIW,KAAK,eAAgBmC,EAAOS,EAAmB9E,GAAQA,EAAMmC,SAAS,MACtEkC,EAAQ,GAEV,YADA9C,EAAAA,GAAIqB,MAAM,eAGZ,IAAIjD,EAAQK,EAAML,QACdqF,GAAc,EAClB,IAAK,MAAM5E,KAAQT,EAAO,CACxB,MAAMwC,EAAWnC,EAAMmC,SAAS/B,GAChC4E,EAAcA,GAAe7C,EAASG,OAAS,CACjD,CACA,GAAK0C,EAAL,CAIAzD,EAAAA,GAAIO,MAAM,WAAYnC,EAAO0E,GAC7B,IAAK,MAAMjE,KAAQT,EAYjB,GAXA4B,EAAAA,GAAIO,MACF,kBACA1B,EACAO,EACAA,EAAUuD,IAAI9D,KAAUO,EAAUW,IAAIlB,GAAM+D,qBAC3CnE,EAAMK,OAAOD,GACdJ,EAAMI,KAAKA,GACXJ,EAAMmC,SAAS,KACf,UACAkC,GAEG1D,EAAUuD,IAAI9D,GAEZ,IAAKO,EAAUW,IAAIlB,GAAM+D,qBAAuBnE,EAAMmC,SAAS/B,IAASJ,EAAMmC,SAAS/B,GAAMkC,OAAS,EAAG,CAC9Gf,EAAAA,GAAIW,KACF,2EACA9B,EACAiE,GAGF,IAAIY,EAAgC,OADdjF,EAAMA,QACJkF,QAAmB,KAAO,KAC9CvE,EAAUW,IAAIlB,IAAOkE,aAAaW,MACpCA,EAAMtE,EAAUW,IAAIlB,GAAMkE,YAAYW,IACtC1D,EAAAA,GAAIW,KAAK,aAAcvB,EAAUW,IAAIlB,GAAMkE,YAAYW,IAAKA,IAE9D,MAAME,EAAe,IAAIC,EAAAA,EAAe,CACtC7F,YAAY,EACZE,UAAU,IACT4F,SAAS,CACVH,QAASD,EACTK,QAAS,GACTC,QAAS,GACTC,QAAS,EACTC,QAAS,IACRC,qBAAoB,WACrB,MAAO,CAAC,CACV,IACAnE,EAAAA,GAAIW,KAAK,wBAAyB4C,EAAmB9E,IACrD+B,EAAK3B,EAAMJ,EAAOmF,EAAc/E,GAChCJ,EAAMwC,QAAQpC,EAAM,CAClBuF,aAAa,EACbxE,GAAIf,EACJkE,YAAa3D,EAAUW,IAAIlB,GAAMkE,YACjCsB,MAAOjF,EAAUW,IAAIlB,GAAMwF,MAC3B5F,MAAOmF,IAET5D,EAAAA,GAAIW,KAAK,+BAAgC9B,EAAM,IAAK0E,EAAmBK,IACvE5D,EAAAA,GAAIO,MAAM,uBAAwBgD,EAAmB9E,GACvD,MACEuB,EAAAA,GAAIW,KACF,cACA9B,EACA,qDACCO,EAAUW,IAAIlB,GAAM+D,oBACrB,gBACCnE,EAAMK,OAAOD,GACd,aACAJ,EAAMmC,SAAS/B,IAASJ,EAAMmC,SAAS/B,GAAMkC,OAAS,EACtDtC,EAAMmC,SAAS,KACfkC,GAEF9C,EAAAA,GAAIO,MAAMnB,QAjDVY,EAAAA,GAAIO,MAAM,gBAAiB1B,EAAMiE,GAoDrC1E,EAAQK,EAAML,QACd4B,EAAAA,GAAIW,KAAK,oBAAqBvC,GAC9B,IAAK,MAAMS,KAAQT,EAAO,CACxB,MAAM4C,EAAOvC,EAAMI,KAAKA,GACxBmB,EAAAA,GAAIW,KAAK,kBAAmB9B,EAAMmC,GAC9BA,GAAMoD,aACRZ,EAAUxC,EAAKvC,MAAOqE,EAAQ,EAElC,CA3EA,MAFE9C,EAAAA,GAAIO,MAAM,6BAA8B9B,EAAML,QA6EhD,GACC,aACCkG,GAAyB7E,EAAAA,EAAAA,KAAO,CAAChB,EAAOL,KAC1C,GAAqB,IAAjBA,EAAM2C,OACR,MAAO,GAET,IAAIwD,EAASC,OAAOC,OAAO,GAAIrG,GAM/B,OALAA,EAAM0C,SAASjC,IACb,MAAM+B,EAAWnC,EAAMmC,SAAS/B,GAC1B6F,EAASJ,EAAO7F,EAAOmC,GAC7B2D,EAAS,IAAIA,KAAWG,EAAO,IAE1BH,CAAM,GACZ,UACCI,GAAuClF,EAAAA,EAAAA,KAAQhB,GAAU6F,EAAO7F,EAAOA,EAAMmC,aAAa,wBAG1FgE,GAAkCnF,EAAAA,EAAAA,KAAOoF,MAAOC,EAAOrG,EAAOsG,EAAanF,EAAIoF,EAAeC,KAChGjF,EAAAA,GAAIW,KAAK,gCAAiCuE,EAAoBzG,GAAQuG,GACtE,MAAMtB,EAAMjF,EAAMA,QAAQkF,QAC1B3D,EAAAA,GAAIC,MAAM,iCAAkCyD,GAC5C,MAAMyB,EAAOL,EAAMM,OAAO,KAAKC,KAAK,QAAS,QACxC5G,EAAML,QAGT4B,EAAAA,GAAIM,KAAK,uBAAwB7B,EAAML,SAFvC4B,EAAAA,GAAIM,KAAK,qBAAsB7B,GAI7BA,EAAMH,QAAQyC,OAAS,GACzBf,EAAAA,GAAIM,KAAK,kBAAmB7B,EAAMQ,KAAKR,EAAMH,QAAQ,KAEvD,MAAMgH,EAAWH,EAAKC,OAAO,KAAKC,KAAK,QAAS,YAC1CE,EAAYJ,EAAKC,OAAO,KAAKC,KAAK,QAAS,aAC3CG,EAAaL,EAAKC,OAAO,KAAKC,KAAK,QAAS,cAC5CjH,EAAQ+G,EAAKC,OAAO,KAAKC,KAAK,QAAS,eACvCI,QAAQC,IACZjH,EAAML,QAAQ8D,KAAI2C,eAAelG,GAC/B,MAAME,EAAOJ,EAAMI,KAAKF,GACxB,QAAsB,IAAlBqG,EAA0B,CAC5B,MAAMhE,EAAOkC,KAAKyC,MAAMzC,KAAKC,UAAU6B,EAAcjC,cACrD/C,EAAAA,GAAIC,MACF,mDACAtB,EACA,WACAqC,EAAK4E,OACL,mBACAZ,EAAcY,QAEhBnH,EAAMwC,QAAQ+D,EAAcpF,GAAIoB,GAC3BvC,EAAMK,OAAOH,KAChBqB,EAAAA,GAAIC,MAAM,iBAAkBtB,EAAGqG,EAAcpF,IAC7CnB,EAAMyC,UAAUvC,EAAGqG,EAAcpF,GAAIoB,GAEzC,CAEA,GADAhB,EAAAA,GAAIM,KAAK,oBAAsB3B,EAAI,KAAOuE,KAAKC,UAAU1E,EAAMI,KAAKF,KAChEE,GAAMuF,YAAa,CACrBpE,EAAAA,GAAIM,KAAK,yBAA0B3B,EAAGE,EAAKgH,MAAOpH,EAAMI,KAAKF,IAC7D,MAAM,QAAEqF,EAAO,QAAED,GAAYtF,EAAMA,QACnCI,EAAKJ,MAAMqF,SAAS,IACfjF,EAAKJ,MAAMA,QACduF,QAASA,EAAU,GACnBD,YAEF,MAAM+B,QAAUlB,EACdxG,EACAS,EAAKJ,MACLsG,EACAnF,EACAnB,EAAMI,KAAKF,GACXsG,GAEIc,EAAQD,EAAEX,MAChBa,EAAAA,EAAAA,IAAiBnH,EAAMkH,GACvBlH,EAAKoH,KAAOH,EAAEG,MAAQ,EACtBjG,EAAAA,GAAIM,KACF,+CACA3B,EACA,QAEAE,EAAKgH,MACL,SACAhH,EAAK+G,SAIPM,EAAAA,EAAAA,IAAYH,EAAOlH,EACrB,MACMJ,EAAMmC,SAASjC,GAAGoC,OAAS,GAC7Bf,EAAAA,GAAIC,MACF,uCACAtB,EACAE,EAAKe,GACLf,EACAA,EAAKgH,MACL,SACApH,GAEFuB,EAAAA,GAAIC,MAAMqC,EAAoBzD,EAAKe,GAAInB,IACvCW,EAAUsC,IAAI7C,EAAKe,GAAI,CAAEA,GAAI0C,EAAoBzD,EAAKe,GAAInB,GAAQI,WAElEmB,EAAAA,GAAIC,MAAM,oCAAqCtB,EAAGP,EAAOK,EAAMI,KAAKF,GAAI+E,SAClEyC,EAAAA,EAAAA,IAAW/H,EAAOK,EAAMI,KAAKF,GAAI,CAAEyH,OAAQnB,EAAYvB,QAGnE,KAEF,MAAM2C,GAA+B5G,EAAAA,EAAAA,KAAOoF,UAC1C,MAAMyB,EAAe7H,EAAMH,QAAQ4D,KAAI2C,eAAe9F,GACpD,MAAME,EAAOR,EAAMQ,KAAKF,EAAEJ,EAAGI,EAAEG,EAAGH,EAAEI,MACpCa,EAAAA,GAAIM,KAAK,QAAUvB,EAAEJ,EAAI,OAASI,EAAEG,EAAI,KAAOgE,KAAKC,UAAUpE,IAC9DiB,EAAAA,GAAIM,KAAK,QAAUvB,EAAEJ,EAAI,OAASI,EAAEG,EAAI,KAAMH,EAAG,IAAKmE,KAAKC,UAAU1E,EAAMQ,KAAKF,KAChFiB,EAAAA,GAAIM,KACF,MACAlB,EACA,OACAL,EAAEJ,EACFI,EAAEG,EACF,gBACAE,EAAUW,IAAIhB,EAAEJ,GAChBS,EAAUW,IAAIhB,EAAEG,UAEZqH,EAAAA,EAAAA,IAAgBf,EAAYvG,EACpC,UACMwG,QAAQC,IAAIY,EAAa,GAC9B,sBACGD,IACNrG,EAAAA,GAAIM,KAAK,uBAAwB4C,KAAKC,UAAU+B,EAAoBzG,KACpEuB,EAAAA,GAAIM,KAAK,qDACTN,EAAAA,GAAIM,KAAK,qDACTN,EAAAA,GAAIM,KAAK,sDACTkG,EAAAA,EAAAA,IAAY/H,GACZuB,EAAAA,GAAIM,KAAK,sBAAuB4C,KAAKC,UAAU+B,EAAoBzG,KACnE,IAAIwH,EAAO,GACP,yBAAEQ,IAA6BC,EAAAA,EAAAA,GAAwBzB,GAuF3D,aAtFMQ,QAAQC,IACZf,EAAqBlG,GAAOyD,KAAI2C,eAAelG,GAC7C,MAAME,EAAOJ,EAAMI,KAAKF,GASxB,GARAqB,EAAAA,GAAIM,KACF,mBAAqB3B,EAAI,MAAQE,EAAK8H,EACtC,IAAM9H,EAAK+H,EACX,YACA/H,EAAKgH,MACL,YACAhH,EAAK+G,QAEH/G,GAAMuF,YACRvF,EAAK+H,GAAKH,EACVzG,EAAAA,GAAIM,KACF,8BACA3B,EACAE,EAAKe,GACLf,EAAKgH,MACLhH,EAAK+G,OACL/G,EAAK8H,EACL9H,EAAK+H,EACLnI,EAAMK,OAAOH,IAEfS,EAAUW,IAAIlB,EAAKe,IAAIf,KAAOA,GAC9BgI,EAAAA,EAAAA,IAAahI,QAEb,GAAIJ,EAAMmC,SAASjC,GAAGoC,OAAS,EAAG,CAChCf,EAAAA,GAAIM,KACF,2BACA3B,EACAE,EAAKe,GACLf,EAAK8H,EACL9H,EAAK+H,EACL/H,EAAKgH,MACLhH,EAAK+G,OACLnH,EAAMK,OAAOH,IAEfE,EAAK+G,QAAUa,EACfhI,EAAMI,KAAKA,EAAKiI,UAChB,MAAMC,EAAclI,GAAMmI,QAAU,GAAK,EACnCC,EAAcpI,GAAMqI,WAAWtB,QAAU,EACzCuB,EAAUF,EAAcF,GAAe,EAC7C/G,EAAAA,GAAIO,MAAM,UAAW4G,EAAS,cAAeF,EAAa,cAAeF,SACnEK,EAAAA,EAAAA,GAAc9B,EAAUzG,GAC9BO,EAAUW,IAAIlB,EAAKe,IAAIf,KAAOA,CAChC,KAAO,CACL,MAAMC,EAASL,EAAMI,KAAKA,EAAKiI,UAC/BjI,EAAK+H,GAAKH,EAA2B,EACrCzG,EAAAA,GAAIM,KACF,0CACAzB,EAAKe,GACL,SACAf,EAAKiI,SACLjI,EAAKgH,MACLhH,EAAK+G,OACL/G,EAAK8H,EACL9H,EAAK+H,EACL,UACA/H,EAAKsI,QACL,SACArI,EACAA,GAAQqI,QACRtI,IAEFgI,EAAAA,EAAAA,IAAahI,EACf,CAEJ,KAEFJ,EAAMH,QAAQwC,SAAQ,SAAS/B,GAC7B,MAAME,EAAOR,EAAMQ,KAAKF,GACxBiB,EAAAA,GAAIM,KAAK,QAAUvB,EAAEJ,EAAI,OAASI,EAAEG,EAAI,KAAOgE,KAAKC,UAAUlE,GAAOA,GACrEA,EAAKoI,OAAOvG,SAASwG,GAAUA,EAAMV,GAAKH,EAA2B,IACrE,MAAMc,EAAY9I,EAAMI,KAAKE,EAAEJ,GAC/B,IAAI6I,EAAU/I,EAAMI,KAAKE,EAAEG,GAC3B,MAAMuI,GAAQC,EAAAA,EAAAA,IAAWnC,EAAWtG,EAAMG,EAAW2F,EAAawC,EAAWC,EAAS5H,IACtF+H,EAAAA,EAAAA,IAAkB1I,EAAMwI,EAC1B,IACAhJ,EAAML,QAAQ0C,SAAQ,SAASnC,GAC7B,MAAMiJ,EAAInJ,EAAMI,KAAKF,GACrBqB,EAAAA,GAAIM,KAAK3B,EAAGiJ,EAAEC,KAAMD,EAAE3B,MAClB2B,EAAEE,UACJ7B,EAAO2B,EAAE3B,KAEb,IACAjG,EAAAA,GAAIW,KAAK,sCAAuCwE,EAAMc,GAC/C,CAAEd,OAAMc,OAAM,GACpB,mBACC8B,GAAyBtI,EAAAA,EAAAA,KAAOoF,MAAOmD,EAAaC,KACtD,MAAMxJ,EAAQ,IAAIyJ,EAAAA,EAAgB,CAChClK,YAAY,EACZE,UAAU,IACT4F,SAAS,CACVH,QAASqE,EAAYG,UACrBpE,QAASiE,EAAY5B,QAAQgC,aAAeJ,EAAY5B,QAAQiC,WAAWD,aAAeJ,EAAYI,YACtGpE,QAASgE,EAAY5B,QAAQkC,aAAeN,EAAY5B,QAAQiC,WAAWC,aAAeN,EAAYM,YACtGrE,QAAS,EACTC,QAAS,IACRC,qBAAoB,WACrB,MAAO,CAAC,CACV,IACMoE,EAAUN,EAAIO,OAAO,MAC3BC,EAAAA,EAAAA,IAAgBF,EAASP,EAAYU,QAASV,EAAYH,KAAMG,EAAYW,YAC5EC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,OACAnJ,EAAAA,EAAAA,MACAF,IACAwI,EAAY5J,MAAM0C,SAASjC,IACzBJ,EAAMwC,QAAQpC,EAAKe,GAAI,IAAKf,IACxBA,EAAKiI,UACPrI,EAAMyC,UAAUrC,EAAKe,GAAIf,EAAKiI,SAChC,IAEF9G,EAAAA,GAAIO,MAAM,SAAUyH,EAAY1J,OAChC0J,EAAY1J,MAAMwC,SAAS7B,IACzB,GAAIA,EAAK6J,QAAU7J,EAAK8J,IAAK,CAC3B,MAAMC,EAAS/J,EAAK6J,MACdG,EAAaD,EAAS,MAAQA,EAAS,OACvCE,EAAaF,EAAS,MAAQA,EAAS,OACvCnK,EAAOJ,EAAMI,KAAKmK,GACxBvK,EAAMwC,QAAQgI,EAAY,CACxBE,MAAOF,EACPrJ,GAAIqJ,EACJnC,SAAUjI,EAAKiI,SACfsC,WAAY,GACZ/E,MAAO,GACP2C,QAAS,EACTqC,MAAO,YAEPC,MAAO,GACPzD,MAAO,GACPD,OAAQ,KAEVnH,EAAMyC,UAAU+H,EAAYpK,EAAKiI,UACjCrI,EAAMwC,QAAQiI,EAAY,CACxBC,MAAOD,EACPtJ,GAAIsJ,EACJpC,SAAUjI,EAAKiI,SACfsC,WAAY,GACZpC,QAAS,EAETqC,MAAO,YACPhF,MAAO,GACPiF,MAAO,GACPzD,MAAO,GACPD,OAAQ,KAEVnH,EAAMyC,UAAUgI,EAAYrK,EAAKiI,UACjC,MAAMyC,EAAQC,gBAAgBvK,GACxBwK,EAAUD,gBAAgBvK,GAC1ByK,EAAQF,gBAAgBvK,GAC9BsK,EAAMlF,MAAQ,GACdkF,EAAMI,aAAe,OACrBJ,EAAM3J,GAAKoJ,EAAS,oBACpBS,EAAQG,eAAiB,OACzBH,EAAQE,aAAe,OACvBF,EAAQ7J,GAAKoJ,EAAS,sBACtBU,EAAMrF,MAAQ,GACVxF,EAAKiJ,UACPyB,EAAMlG,YAAc2F,EACpBU,EAAMpG,UAAY0F,GAEpBU,EAAM9J,GAAKoJ,EAAS,oBACpBU,EAAME,eAAiB,OACvBnL,EAAM2C,QAAQ4H,EAAQC,EAAYM,EAAOP,EAAS,qBAClDvK,EAAM2C,QAAQ6H,EAAYC,EAAYO,EAAST,EAAS,qBACxDvK,EAAM2C,QAAQ8H,EAAYF,EAAQU,EAAOV,EAAS,qBACpD,MACEvK,EAAM2C,QAAQnC,EAAK6J,MAAO7J,EAAK8J,IAAK,IAAK9J,GAAQA,EAAKW,GACxD,IAEFI,EAAAA,GAAIW,KAAK,kBAAmBuC,KAAKC,UAAU+B,EAAoBzG,KAC/DoE,EAAuBpE,GACvBuB,EAAAA,GAAIW,KAAK,mBAAoBuC,KAAKC,UAAU+B,EAAoBzG,KAChE,MAAMwG,GAAa4E,EAAAA,EAAAA,YACbjF,EACJ2D,EACA9J,EACAuJ,EAAYH,KACZG,EAAYW,eACZ,EACA1D,EACD,GACA,S,gDCtoBH,QAJA,SAAevG,GACb,OAAOoL,EAAAA,EAAAA,GAAUpL,EA7BM,EA8BzB,C", "sources": ["../../node_modules/dagre-d3-es/src/graphlib/json.js", "../../node_modules/mermaid/dist/chunks/mermaid.core/dagre-OKDRZEBW.mjs", "../../node_modules/lodash-es/clone.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from './graph.js';\n\nexport { write, read };\n\nfunction write(g) {\n  var json = {\n    options: {\n      directed: g.isDirected(),\n      multigraph: g.isMultigraph(),\n      compound: g.isCompound(),\n    },\n    nodes: writeNodes(g),\n    edges: writeEdges(g),\n  };\n  if (!_.isUndefined(g.graph())) {\n    json.value = _.clone(g.graph());\n  }\n  return json;\n}\n\nfunction writeNodes(g) {\n  return _.map(g.nodes(), function (v) {\n    var nodeValue = g.node(v);\n    var parent = g.parent(v);\n    var node = { v: v };\n    if (!_.isUndefined(nodeValue)) {\n      node.value = nodeValue;\n    }\n    if (!_.isUndefined(parent)) {\n      node.parent = parent;\n    }\n    return node;\n  });\n}\n\nfunction writeEdges(g) {\n  return _.map(g.edges(), function (e) {\n    var edgeValue = g.edge(e);\n    var edge = { v: e.v, w: e.w };\n    if (!_.isUndefined(e.name)) {\n      edge.name = e.name;\n    }\n    if (!_.isUndefined(edgeValue)) {\n      edge.value = edgeValue;\n    }\n    return edge;\n  });\n}\n\nfunction read(json) {\n  var g = new Graph(json.options).setGraph(json.value);\n  _.each(json.nodes, function (entry) {\n    g.setNode(entry.v, entry.value);\n    if (entry.parent) {\n      g.setParent(entry.v, entry.parent);\n    }\n  });\n  _.each(json.edges, function (entry) {\n    g.setEdge({ v: entry.v, w: entry.w, name: entry.name }, entry.value);\n  });\n  return g;\n}\n", "import {\n  clear as clear2,\n  insertEdge,\n  insertEdgeLabel,\n  markers_default,\n  positionEdgeLabel\n} from \"./chunk-IIMUDSI4.mjs\";\nimport \"./chunk-VV3M67IP.mjs\";\nimport {\n  clear,\n  clear2 as clear3,\n  insertCluster,\n  insertNode,\n  positionNode,\n  setNodeElem,\n  updateNodeBounds\n} from \"./chunk-HRU6DDCH.mjs\";\nimport {\n  getSubGraphTitleMargins\n} from \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  getConfig2 as getConfig,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/layout-algorithms/dagre/index.js\nimport { layout as dagreLayout } from \"dagre-d3-es/src/dagre/index.js\";\nimport * as graphlibJson2 from \"dagre-d3-es/src/graphlib/json.js\";\nimport * as graphlib2 from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/rendering-util/layout-algorithms/dagre/mermaid-graphlib.js\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\nimport * as graphlibJson from \"dagre-d3-es/src/graphlib/json.js\";\nvar clusterDb = /* @__PURE__ */ new Map();\nvar descendants = /* @__PURE__ */ new Map();\nvar parents = /* @__PURE__ */ new Map();\nvar clear4 = /* @__PURE__ */ __name(() => {\n  descendants.clear();\n  parents.clear();\n  clusterDb.clear();\n}, \"clear\");\nvar isDescendant = /* @__PURE__ */ __name((id, ancestorId) => {\n  const ancestorDescendants = descendants.get(ancestorId) || [];\n  log.trace(\"In isDescendant\", ancestorId, \" \", id, \" = \", ancestorDescendants.includes(id));\n  return ancestorDescendants.includes(id);\n}, \"isDescendant\");\nvar edgeInCluster = /* @__PURE__ */ __name((edge, clusterId) => {\n  const clusterDescendants = descendants.get(clusterId) || [];\n  log.info(\"Descendants of \", clusterId, \" is \", clusterDescendants);\n  log.info(\"Edge is \", edge);\n  if (edge.v === clusterId || edge.w === clusterId) {\n    return false;\n  }\n  if (!clusterDescendants) {\n    log.debug(\"Tilt, \", clusterId, \",not in descendants\");\n    return false;\n  }\n  return clusterDescendants.includes(edge.v) || isDescendant(edge.v, clusterId) || isDescendant(edge.w, clusterId) || clusterDescendants.includes(edge.w);\n}, \"edgeInCluster\");\nvar copy = /* @__PURE__ */ __name((clusterId, graph, newGraph, rootId) => {\n  log.warn(\n    \"Copying children of \",\n    clusterId,\n    \"root\",\n    rootId,\n    \"data\",\n    graph.node(clusterId),\n    rootId\n  );\n  const nodes = graph.children(clusterId) || [];\n  if (clusterId !== rootId) {\n    nodes.push(clusterId);\n  }\n  log.warn(\"Copying (nodes) clusterId\", clusterId, \"nodes\", nodes);\n  nodes.forEach((node) => {\n    if (graph.children(node).length > 0) {\n      copy(node, graph, newGraph, rootId);\n    } else {\n      const data = graph.node(node);\n      log.info(\"cp \", node, \" to \", rootId, \" with parent \", clusterId);\n      newGraph.setNode(node, data);\n      if (rootId !== graph.parent(node)) {\n        log.warn(\"Setting parent\", node, graph.parent(node));\n        newGraph.setParent(node, graph.parent(node));\n      }\n      if (clusterId !== rootId && node !== clusterId) {\n        log.debug(\"Setting parent\", node, clusterId);\n        newGraph.setParent(node, clusterId);\n      } else {\n        log.info(\"In copy \", clusterId, \"root\", rootId, \"data\", graph.node(clusterId), rootId);\n        log.debug(\n          \"Not Setting parent for node=\",\n          node,\n          \"cluster!==rootId\",\n          clusterId !== rootId,\n          \"node!==clusterId\",\n          node !== clusterId\n        );\n      }\n      const edges = graph.edges(node);\n      log.debug(\"Copying Edges\", edges);\n      edges.forEach((edge) => {\n        log.info(\"Edge\", edge);\n        const data2 = graph.edge(edge.v, edge.w, edge.name);\n        log.info(\"Edge data\", data2, rootId);\n        try {\n          if (edgeInCluster(edge, rootId)) {\n            log.info(\"Copying as \", edge.v, edge.w, data2, edge.name);\n            newGraph.setEdge(edge.v, edge.w, data2, edge.name);\n            log.info(\"newGraph edges \", newGraph.edges(), newGraph.edge(newGraph.edges()[0]));\n          } else {\n            log.info(\n              \"Skipping copy of edge \",\n              edge.v,\n              \"-->\",\n              edge.w,\n              \" rootId: \",\n              rootId,\n              \" clusterId:\",\n              clusterId\n            );\n          }\n        } catch (e) {\n          log.error(e);\n        }\n      });\n    }\n    log.debug(\"Removing node\", node);\n    graph.removeNode(node);\n  });\n}, \"copy\");\nvar extractDescendants = /* @__PURE__ */ __name((id, graph) => {\n  const children = graph.children(id);\n  let res = [...children];\n  for (const child of children) {\n    parents.set(child, id);\n    res = [...res, ...extractDescendants(child, graph)];\n  }\n  return res;\n}, \"extractDescendants\");\nvar findCommonEdges = /* @__PURE__ */ __name((graph, id1, id2) => {\n  const edges1 = graph.edges().filter((edge) => edge.v === id1 || edge.w === id1);\n  const edges2 = graph.edges().filter((edge) => edge.v === id2 || edge.w === id2);\n  const edges1Prim = edges1.map((edge) => {\n    return { v: edge.v === id1 ? id2 : edge.v, w: edge.w === id1 ? id1 : edge.w };\n  });\n  const edges2Prim = edges2.map((edge) => {\n    return { v: edge.v, w: edge.w };\n  });\n  const result = edges1Prim.filter((edgeIn1) => {\n    return edges2Prim.some((edge) => edgeIn1.v === edge.v && edgeIn1.w === edge.w);\n  });\n  return result;\n}, \"findCommonEdges\");\nvar findNonClusterChild = /* @__PURE__ */ __name((id, graph, clusterId) => {\n  const children = graph.children(id);\n  log.trace(\"Searching children of id \", id, children);\n  if (children.length < 1) {\n    return id;\n  }\n  let reserve;\n  for (const child of children) {\n    const _id = findNonClusterChild(child, graph, clusterId);\n    const commonEdges = findCommonEdges(graph, clusterId, _id);\n    if (_id) {\n      if (commonEdges.length > 0) {\n        reserve = _id;\n      } else {\n        return _id;\n      }\n    }\n  }\n  return reserve;\n}, \"findNonClusterChild\");\nvar getAnchorId = /* @__PURE__ */ __name((id) => {\n  if (!clusterDb.has(id)) {\n    return id;\n  }\n  if (!clusterDb.get(id).externalConnections) {\n    return id;\n  }\n  if (clusterDb.has(id)) {\n    return clusterDb.get(id).id;\n  }\n  return id;\n}, \"getAnchorId\");\nvar adjustClustersAndEdges = /* @__PURE__ */ __name((graph, depth) => {\n  if (!graph || depth > 10) {\n    log.debug(\"Opting out, no graph \");\n    return;\n  } else {\n    log.debug(\"Opting in, graph \");\n  }\n  graph.nodes().forEach(function(id) {\n    const children = graph.children(id);\n    if (children.length > 0) {\n      log.warn(\n        \"Cluster identified\",\n        id,\n        \" Replacement id in edges: \",\n        findNonClusterChild(id, graph, id)\n      );\n      descendants.set(id, extractDescendants(id, graph));\n      clusterDb.set(id, { id: findNonClusterChild(id, graph, id), clusterData: graph.node(id) });\n    }\n  });\n  graph.nodes().forEach(function(id) {\n    const children = graph.children(id);\n    const edges = graph.edges();\n    if (children.length > 0) {\n      log.debug(\"Cluster identified\", id, descendants);\n      edges.forEach((edge) => {\n        const d1 = isDescendant(edge.v, id);\n        const d2 = isDescendant(edge.w, id);\n        if (d1 ^ d2) {\n          log.warn(\"Edge: \", edge, \" leaves cluster \", id);\n          log.warn(\"Descendants of XXX \", id, \": \", descendants.get(id));\n          clusterDb.get(id).externalConnections = true;\n        }\n      });\n    } else {\n      log.debug(\"Not a cluster \", id, descendants);\n    }\n  });\n  for (let id of clusterDb.keys()) {\n    const nonClusterChild = clusterDb.get(id).id;\n    const parent = graph.parent(nonClusterChild);\n    if (parent !== id && clusterDb.has(parent) && !clusterDb.get(parent).externalConnections) {\n      clusterDb.get(id).id = parent;\n    }\n  }\n  graph.edges().forEach(function(e) {\n    const edge = graph.edge(e);\n    log.warn(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(e));\n    log.warn(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(graph.edge(e)));\n    let v = e.v;\n    let w = e.w;\n    log.warn(\n      \"Fix XXX\",\n      clusterDb,\n      \"ids:\",\n      e.v,\n      e.w,\n      \"Translating: \",\n      clusterDb.get(e.v),\n      \" --- \",\n      clusterDb.get(e.w)\n    );\n    if (clusterDb.get(e.v) || clusterDb.get(e.w)) {\n      log.warn(\"Fixing and trying - removing XXX\", e.v, e.w, e.name);\n      v = getAnchorId(e.v);\n      w = getAnchorId(e.w);\n      graph.removeEdge(e.v, e.w, e.name);\n      if (v !== e.v) {\n        const parent = graph.parent(v);\n        clusterDb.get(parent).externalConnections = true;\n        edge.fromCluster = e.v;\n      }\n      if (w !== e.w) {\n        const parent = graph.parent(w);\n        clusterDb.get(parent).externalConnections = true;\n        edge.toCluster = e.w;\n      }\n      log.warn(\"Fix Replacing with XXX\", v, w, e.name);\n      graph.setEdge(v, w, edge, e.name);\n    }\n  });\n  log.warn(\"Adjusted Graph\", graphlibJson.write(graph));\n  extractor(graph, 0);\n  log.trace(clusterDb);\n}, \"adjustClustersAndEdges\");\nvar extractor = /* @__PURE__ */ __name((graph, depth) => {\n  log.warn(\"extractor - \", depth, graphlibJson.write(graph), graph.children(\"D\"));\n  if (depth > 10) {\n    log.error(\"Bailing out\");\n    return;\n  }\n  let nodes = graph.nodes();\n  let hasChildren = false;\n  for (const node of nodes) {\n    const children = graph.children(node);\n    hasChildren = hasChildren || children.length > 0;\n  }\n  if (!hasChildren) {\n    log.debug(\"Done, no node has children\", graph.nodes());\n    return;\n  }\n  log.debug(\"Nodes = \", nodes, depth);\n  for (const node of nodes) {\n    log.debug(\n      \"Extracting node\",\n      node,\n      clusterDb,\n      clusterDb.has(node) && !clusterDb.get(node).externalConnections,\n      !graph.parent(node),\n      graph.node(node),\n      graph.children(\"D\"),\n      \" Depth \",\n      depth\n    );\n    if (!clusterDb.has(node)) {\n      log.debug(\"Not a cluster\", node, depth);\n    } else if (!clusterDb.get(node).externalConnections && graph.children(node) && graph.children(node).length > 0) {\n      log.warn(\n        \"Cluster without external connections, without a parent and with children\",\n        node,\n        depth\n      );\n      const graphSettings = graph.graph();\n      let dir = graphSettings.rankdir === \"TB\" ? \"LR\" : \"TB\";\n      if (clusterDb.get(node)?.clusterData?.dir) {\n        dir = clusterDb.get(node).clusterData.dir;\n        log.warn(\"Fixing dir\", clusterDb.get(node).clusterData.dir, dir);\n      }\n      const clusterGraph = new graphlib.Graph({\n        multigraph: true,\n        compound: true\n      }).setGraph({\n        rankdir: dir,\n        nodesep: 50,\n        ranksep: 50,\n        marginx: 8,\n        marginy: 8\n      }).setDefaultEdgeLabel(function() {\n        return {};\n      });\n      log.warn(\"Old graph before copy\", graphlibJson.write(graph));\n      copy(node, graph, clusterGraph, node);\n      graph.setNode(node, {\n        clusterNode: true,\n        id: node,\n        clusterData: clusterDb.get(node).clusterData,\n        label: clusterDb.get(node).label,\n        graph: clusterGraph\n      });\n      log.warn(\"New graph after copy node: (\", node, \")\", graphlibJson.write(clusterGraph));\n      log.debug(\"Old graph after copy\", graphlibJson.write(graph));\n    } else {\n      log.warn(\n        \"Cluster ** \",\n        node,\n        \" **not meeting the criteria !externalConnections:\",\n        !clusterDb.get(node).externalConnections,\n        \" no parent: \",\n        !graph.parent(node),\n        \" children \",\n        graph.children(node) && graph.children(node).length > 0,\n        graph.children(\"D\"),\n        depth\n      );\n      log.debug(clusterDb);\n    }\n  }\n  nodes = graph.nodes();\n  log.warn(\"New list of nodes\", nodes);\n  for (const node of nodes) {\n    const data = graph.node(node);\n    log.warn(\" Now next level\", node, data);\n    if (data?.clusterNode) {\n      extractor(data.graph, depth + 1);\n    }\n  }\n}, \"extractor\");\nvar sorter = /* @__PURE__ */ __name((graph, nodes) => {\n  if (nodes.length === 0) {\n    return [];\n  }\n  let result = Object.assign([], nodes);\n  nodes.forEach((node) => {\n    const children = graph.children(node);\n    const sorted = sorter(graph, children);\n    result = [...result, ...sorted];\n  });\n  return result;\n}, \"sorter\");\nvar sortNodesByHierarchy = /* @__PURE__ */ __name((graph) => sorter(graph, graph.children()), \"sortNodesByHierarchy\");\n\n// src/rendering-util/layout-algorithms/dagre/index.js\nvar recursiveRender = /* @__PURE__ */ __name(async (_elem, graph, diagramType, id, parentCluster, siteConfig) => {\n  log.warn(\"Graph in recursive render:XAX\", graphlibJson2.write(graph), parentCluster);\n  const dir = graph.graph().rankdir;\n  log.trace(\"Dir in recursive render - dir:\", dir);\n  const elem = _elem.insert(\"g\").attr(\"class\", \"root\");\n  if (!graph.nodes()) {\n    log.info(\"No nodes found for\", graph);\n  } else {\n    log.info(\"Recursive render XXX\", graph.nodes());\n  }\n  if (graph.edges().length > 0) {\n    log.info(\"Recursive edges\", graph.edge(graph.edges()[0]));\n  }\n  const clusters = elem.insert(\"g\").attr(\"class\", \"clusters\");\n  const edgePaths = elem.insert(\"g\").attr(\"class\", \"edgePaths\");\n  const edgeLabels = elem.insert(\"g\").attr(\"class\", \"edgeLabels\");\n  const nodes = elem.insert(\"g\").attr(\"class\", \"nodes\");\n  await Promise.all(\n    graph.nodes().map(async function(v) {\n      const node = graph.node(v);\n      if (parentCluster !== void 0) {\n        const data = JSON.parse(JSON.stringify(parentCluster.clusterData));\n        log.trace(\n          \"Setting data for parent cluster XXX\\n Node.id = \",\n          v,\n          \"\\n data=\",\n          data.height,\n          \"\\nParent cluster\",\n          parentCluster.height\n        );\n        graph.setNode(parentCluster.id, data);\n        if (!graph.parent(v)) {\n          log.trace(\"Setting parent\", v, parentCluster.id);\n          graph.setParent(v, parentCluster.id, data);\n        }\n      }\n      log.info(\"(Insert) Node XXX\" + v + \": \" + JSON.stringify(graph.node(v)));\n      if (node?.clusterNode) {\n        log.info(\"Cluster identified XBX\", v, node.width, graph.node(v));\n        const { ranksep, nodesep } = graph.graph();\n        node.graph.setGraph({\n          ...node.graph.graph(),\n          ranksep: ranksep + 25,\n          nodesep\n        });\n        const o = await recursiveRender(\n          nodes,\n          node.graph,\n          diagramType,\n          id,\n          graph.node(v),\n          siteConfig\n        );\n        const newEl = o.elem;\n        updateNodeBounds(node, newEl);\n        node.diff = o.diff || 0;\n        log.info(\n          \"New compound node after recursive render XAX\",\n          v,\n          \"width\",\n          // node,\n          node.width,\n          \"height\",\n          node.height\n          // node.x,\n          // node.y\n        );\n        setNodeElem(newEl, node);\n      } else {\n        if (graph.children(v).length > 0) {\n          log.trace(\n            \"Cluster - the non recursive path XBX\",\n            v,\n            node.id,\n            node,\n            node.width,\n            \"Graph:\",\n            graph\n          );\n          log.trace(findNonClusterChild(node.id, graph));\n          clusterDb.set(node.id, { id: findNonClusterChild(node.id, graph), node });\n        } else {\n          log.trace(\"Node - the non recursive path XAX\", v, nodes, graph.node(v), dir);\n          await insertNode(nodes, graph.node(v), { config: siteConfig, dir });\n        }\n      }\n    })\n  );\n  const processEdges = /* @__PURE__ */ __name(async () => {\n    const edgePromises = graph.edges().map(async function(e) {\n      const edge = graph.edge(e.v, e.w, e.name);\n      log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(e));\n      log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \", e, \" \", JSON.stringify(graph.edge(e)));\n      log.info(\n        \"Fix\",\n        clusterDb,\n        \"ids:\",\n        e.v,\n        e.w,\n        \"Translating: \",\n        clusterDb.get(e.v),\n        clusterDb.get(e.w)\n      );\n      await insertEdgeLabel(edgeLabels, edge);\n    });\n    await Promise.all(edgePromises);\n  }, \"processEdges\");\n  await processEdges();\n  log.info(\"Graph before layout:\", JSON.stringify(graphlibJson2.write(graph)));\n  log.info(\"############################################# XXX\");\n  log.info(\"###                Layout                 ### XXX\");\n  log.info(\"############################################# XXX\");\n  dagreLayout(graph);\n  log.info(\"Graph after layout:\", JSON.stringify(graphlibJson2.write(graph)));\n  let diff = 0;\n  let { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);\n  await Promise.all(\n    sortNodesByHierarchy(graph).map(async function(v) {\n      const node = graph.node(v);\n      log.info(\n        \"Position XBX => \" + v + \": (\" + node.x,\n        \",\" + node.y,\n        \") width: \",\n        node.width,\n        \" height: \",\n        node.height\n      );\n      if (node?.clusterNode) {\n        node.y += subGraphTitleTotalMargin;\n        log.info(\n          \"A tainted cluster node XBX1\",\n          v,\n          node.id,\n          node.width,\n          node.height,\n          node.x,\n          node.y,\n          graph.parent(v)\n        );\n        clusterDb.get(node.id).node = node;\n        positionNode(node);\n      } else {\n        if (graph.children(v).length > 0) {\n          log.info(\n            \"A pure cluster node XBX1\",\n            v,\n            node.id,\n            node.x,\n            node.y,\n            node.width,\n            node.height,\n            graph.parent(v)\n          );\n          node.height += subGraphTitleTotalMargin;\n          graph.node(node.parentId);\n          const halfPadding = node?.padding / 2 || 0;\n          const labelHeight = node?.labelBBox?.height || 0;\n          const offsetY = labelHeight - halfPadding || 0;\n          log.debug(\"OffsetY\", offsetY, \"labelHeight\", labelHeight, \"halfPadding\", halfPadding);\n          await insertCluster(clusters, node);\n          clusterDb.get(node.id).node = node;\n        } else {\n          const parent = graph.node(node.parentId);\n          node.y += subGraphTitleTotalMargin / 2;\n          log.info(\n            \"A regular node XBX1 - using the padding\",\n            node.id,\n            \"parent\",\n            node.parentId,\n            node.width,\n            node.height,\n            node.x,\n            node.y,\n            \"offsetY\",\n            node.offsetY,\n            \"parent\",\n            parent,\n            parent?.offsetY,\n            node\n          );\n          positionNode(node);\n        }\n      }\n    })\n  );\n  graph.edges().forEach(function(e) {\n    const edge = graph.edge(e);\n    log.info(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(edge), edge);\n    edge.points.forEach((point) => point.y += subGraphTitleTotalMargin / 2);\n    const startNode = graph.node(e.v);\n    var endNode = graph.node(e.w);\n    const paths = insertEdge(edgePaths, edge, clusterDb, diagramType, startNode, endNode, id);\n    positionEdgeLabel(edge, paths);\n  });\n  graph.nodes().forEach(function(v) {\n    const n = graph.node(v);\n    log.info(v, n.type, n.diff);\n    if (n.isGroup) {\n      diff = n.diff;\n    }\n  });\n  log.warn(\"Returning from recursive render XAX\", elem, diff);\n  return { elem, diff };\n}, \"recursiveRender\");\nvar render = /* @__PURE__ */ __name(async (data4Layout, svg) => {\n  const graph = new graphlib2.Graph({\n    multigraph: true,\n    compound: true\n  }).setGraph({\n    rankdir: data4Layout.direction,\n    nodesep: data4Layout.config?.nodeSpacing || data4Layout.config?.flowchart?.nodeSpacing || data4Layout.nodeSpacing,\n    ranksep: data4Layout.config?.rankSpacing || data4Layout.config?.flowchart?.rankSpacing || data4Layout.rankSpacing,\n    marginx: 8,\n    marginy: 8\n  }).setDefaultEdgeLabel(function() {\n    return {};\n  });\n  const element = svg.select(\"g\");\n  markers_default(element, data4Layout.markers, data4Layout.type, data4Layout.diagramId);\n  clear3();\n  clear2();\n  clear();\n  clear4();\n  data4Layout.nodes.forEach((node) => {\n    graph.setNode(node.id, { ...node });\n    if (node.parentId) {\n      graph.setParent(node.id, node.parentId);\n    }\n  });\n  log.debug(\"Edges:\", data4Layout.edges);\n  data4Layout.edges.forEach((edge) => {\n    if (edge.start === edge.end) {\n      const nodeId = edge.start;\n      const specialId1 = nodeId + \"---\" + nodeId + \"---1\";\n      const specialId2 = nodeId + \"---\" + nodeId + \"---2\";\n      const node = graph.node(nodeId);\n      graph.setNode(specialId1, {\n        domId: specialId1,\n        id: specialId1,\n        parentId: node.parentId,\n        labelStyle: \"\",\n        label: \"\",\n        padding: 0,\n        shape: \"labelRect\",\n        // shape: 'rect',\n        style: \"\",\n        width: 10,\n        height: 10\n      });\n      graph.setParent(specialId1, node.parentId);\n      graph.setNode(specialId2, {\n        domId: specialId2,\n        id: specialId2,\n        parentId: node.parentId,\n        labelStyle: \"\",\n        padding: 0,\n        // shape: 'rect',\n        shape: \"labelRect\",\n        label: \"\",\n        style: \"\",\n        width: 10,\n        height: 10\n      });\n      graph.setParent(specialId2, node.parentId);\n      const edge1 = structuredClone(edge);\n      const edgeMid = structuredClone(edge);\n      const edge2 = structuredClone(edge);\n      edge1.label = \"\";\n      edge1.arrowTypeEnd = \"none\";\n      edge1.id = nodeId + \"-cyclic-special-1\";\n      edgeMid.arrowTypeStart = \"none\";\n      edgeMid.arrowTypeEnd = \"none\";\n      edgeMid.id = nodeId + \"-cyclic-special-mid\";\n      edge2.label = \"\";\n      if (node.isGroup) {\n        edge1.fromCluster = nodeId;\n        edge2.toCluster = nodeId;\n      }\n      edge2.id = nodeId + \"-cyclic-special-2\";\n      edge2.arrowTypeStart = \"none\";\n      graph.setEdge(nodeId, specialId1, edge1, nodeId + \"-cyclic-special-0\");\n      graph.setEdge(specialId1, specialId2, edgeMid, nodeId + \"-cyclic-special-1\");\n      graph.setEdge(specialId2, nodeId, edge2, nodeId + \"-cyc<lic-special-2\");\n    } else {\n      graph.setEdge(edge.start, edge.end, { ...edge }, edge.id);\n    }\n  });\n  log.warn(\"Graph at first:\", JSON.stringify(graphlibJson2.write(graph)));\n  adjustClustersAndEdges(graph);\n  log.warn(\"Graph after XAX:\", JSON.stringify(graphlibJson2.write(graph)));\n  const siteConfig = getConfig();\n  await recursiveRender(\n    element,\n    graph,\n    data4Layout.type,\n    data4Layout.diagramId,\n    void 0,\n    siteConfig\n  );\n}, \"render\");\nexport {\n  render\n};\n", "import baseClone from './_baseClone.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * Creates a shallow clone of `value`.\n *\n * **Note:** This method is loosely based on the\n * [structured clone algorithm](https://mdn.io/Structured_clone_algorithm)\n * and supports cloning arrays, array buffers, booleans, date objects, maps,\n * numbers, `Object` objects, regexes, sets, strings, symbols, and typed\n * arrays. The own enumerable properties of `arguments` objects are cloned\n * as plain objects. An empty object is returned for uncloneable values such\n * as error objects, functions, DOM nodes, and WeakMaps.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to clone.\n * @returns {*} Returns the cloned value.\n * @see _.cloneDeep\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var shallow = _.clone(objects);\n * console.log(shallow[0] === objects[0]);\n * // => true\n */\nfunction clone(value) {\n  return baseClone(value, CLONE_SYMBOLS_FLAG);\n}\n\nexport default clone;\n"], "names": ["write", "g", "json", "options", "directed", "isDirected", "multigraph", "isMultigraph", "compound", "isCompound", "nodes", "writeNodes", "edges", "writeEdges", "_", "graph", "value", "v", "nodeValue", "node", "parent", "e", "edgeValue", "edge", "w", "name", "clusterDb", "Map", "descendants", "parents", "clear4", "__name", "clear", "isDescendant", "id", "ancestorId", "ancestorDescendants", "get", "log", "trace", "includes", "edgeInCluster", "clusterId", "clusterDescendants", "info", "debug", "copy", "newGraph", "rootId", "warn", "children", "push", "for<PERSON>ach", "length", "data", "setNode", "setParent", "data2", "setEdge", "error", "removeNode", "extractDescendants", "res", "child", "set", "find<PERSON><PERSON><PERSON><PERSON>dges", "id1", "id2", "edges1", "filter", "edges2", "edges1Prim", "map", "edges2Prim", "edgeIn1", "some", "findNonClusterChild", "reserve", "_id", "commonEdges", "getAnchorId", "has", "externalConnections", "adjustClustersAndEdges", "depth", "clusterData", "keys", "nonClusterChild", "JSON", "stringify", "removeEdge", "fromCluster", "toCluster", "graphlibJson", "extractor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dir", "rankdir", "clusterGraph", "graphlib", "setGraph", "nodesep", "ranksep", "marginx", "marginy", "setDefaultEdgeLabel", "clusterNode", "label", "sorter", "result", "Object", "assign", "sorted", "sortNodesByHierarchy", "recursiveRender", "async", "_elem", "diagramType", "parentCluster", "siteConfig", "graphlibJson2", "elem", "insert", "attr", "clusters", "edgePaths", "edgeLabels", "Promise", "all", "parse", "height", "width", "o", "newEl", "updateNodeBounds", "diff", "setNodeElem", "insertNode", "config", "processEdges", "edgePromises", "insertEdgeLabel", "dagreLayout", "subGraphTitleTotalMargin", "getSubGraphTitleMargins", "x", "y", "positionNode", "parentId", "halfPadding", "padding", "labelHeight", "labelBBox", "offsetY", "insertCluster", "points", "point", "startNode", "endNode", "paths", "insertEdge", "positionEdgeLabel", "n", "type", "isGroup", "render", "data4Layout", "svg", "graphlib2", "direction", "nodeSpacing", "flowchart", "rankSpacing", "element", "select", "markers_default", "markers", "diagramId", "clear3", "clear2", "start", "end", "nodeId", "specialId1", "specialId2", "domId", "labelStyle", "shape", "style", "edge1", "structuredClone", "edgeMid", "edge2", "arrowTypeEnd", "arrowTypeStart", "getConfig", "baseClone"], "sourceRoot": ""}