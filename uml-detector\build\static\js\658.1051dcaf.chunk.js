"use strict";(self.webpackChunkuml_detector=self.webpackChunkuml_detector||[]).push([[658],{658:(e,r,s)=>{s.d(r,{diagram:()=>l});var t=s(295),a=(s(7731),s(89),s(5616),s(594),s(8546),s(4190),s(590),s(1984),s(6102),s(7551)),l={parser:t._$,get db(){return new t.NM},renderer:t.Lh,styles:t.tM,init:(0,a.K2)((e=>{e.class||(e.class={}),e.class.arrowMarkerAbsolute=e.arrowMarkerAbsolute}),"init")}}}]);
//# sourceMappingURL=658.1051dcaf.chunk.js.map