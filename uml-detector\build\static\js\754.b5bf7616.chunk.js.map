{"version": 3, "file": "static/js/754.b5bf7616.chunk.js", "mappings": "0IAKIA,EAAkB,MAIpBC,WAAAA,CAAYC,GACVC,KAAKD,KAAOA,EACZC,KAAKC,QAAUD,KAAKD,MACtB,CAAC,eAECG,EAAAA,EAAAA,IAAOF,KAAM,mBAFd,GAIDG,KAAAA,GACEH,KAAKC,QAAUD,KAAKD,MACtB,E,iBCbF,SAASK,EAAiBC,EAAKC,GACzBD,EAAIE,UACND,EAAGE,oBAAoBH,EAAIE,UAEzBF,EAAII,UACNH,EAAGI,cAAcL,EAAII,UAEnBJ,EAAIM,OACNL,EAAGM,kBAAkBP,EAAIM,MAE7B,C,kBACAT,E,QAAAA,IAAOE,EAAkB,mB,yGCgBrBS,EAAa,CACfC,OAAQ,EACRC,QAAS,EACTC,UAAW,EACXC,MAAO,EACPC,YAAa,GAIXC,EAA0BC,EAAAA,GAAsBC,SAChDC,GAA6BpB,EAAAA,EAAAA,KAAO,KACvBqB,EAAAA,EAAAA,IAAc,IACxBJ,MACAK,EAAAA,EAAAA,MAAYH,YAGhB,aACCI,EAAQ,IAAI5B,EAAAA,GAAgB,KAC9B,MAAM6B,EAASJ,IACTK,EAAiBD,EAAOC,eACxBC,EAAkBF,EAAOE,gBAC/B,MAAO,CACLD,iBACAE,QAAyB,IAAIC,IAC7BC,KAAM,KACNC,aAA8B,IAAIF,IAAI,CAAC,CAACH,EAAgB,CAAEM,KAAMN,EAAgBO,MAAON,MACvFO,SAA0B,IAAIL,IAAI,CAAC,CAACH,EAAgB,QACpDS,WAAYT,EACZU,UAAW,KACXC,IAAK,EACLC,QAAS,CAAC,EACX,IAEH,SAASC,IACP,OAAOC,EAAAA,EAAAA,IAAO,CAAEC,OAAQ,GAC1B,CAEA,SAASC,EAAOC,EAAMC,GACpB,MAAMC,EAA4BC,OAAOC,OAAO,MAChD,OAAOJ,EAAKK,QAAO,CAACC,EAAKC,KACvB,MAAMC,EAAMP,EAAGM,GAKf,OAJKL,EAAUM,KACbN,EAAUM,IAAO,EACjBF,EAAIG,KAAKF,IAEJD,CAAG,GACT,GACL,EAXAhD,EAAAA,EAAAA,IAAOsC,EAAO,UAYdtC,EAAAA,EAAAA,IAAOyC,EAAQ,UACf,IAAIW,GAA+BpD,EAAAA,EAAAA,KAAO,SAASqD,GACjD9B,EAAMxB,QAAQoC,UAAYkB,CAC5B,GAAG,gBACCC,GAA6BtD,EAAAA,EAAAA,KAAO,SAASuD,GAC/CC,EAAAA,GAAIC,MAAM,cAAeF,GACzBA,EAAeA,GAAcG,OAC7BH,EAAeA,GAAgB,KAC/B,IACEhC,EAAMxB,QAAQsC,QAAUsB,KAAKC,MAAML,EACrC,CAAE,MAAOM,GACPL,EAAAA,GAAIM,MAAM,uCAAwCD,EAAEE,QACtD,CACF,GAAG,cACCC,GAA6BhE,EAAAA,EAAAA,KAAO,WACtC,OAAOuB,EAAMxB,QAAQsC,OACvB,GAAG,cACC4B,GAAyBjE,EAAAA,EAAAA,KAAO,SAASkE,GAC3C,IAAIC,EAAMD,EAASC,IACfC,EAAKF,EAASE,GAClB,MAAMC,EAAOH,EAASG,KACtB,IAAIC,EAAOJ,EAASI,KACpBd,EAAAA,GAAIe,KAAK,SAAUJ,EAAKC,EAAIC,EAAMC,GAClCd,EAAAA,GAAIC,MAAM,mBAAoBU,EAAKC,EAAIC,EAAMC,GAC7C,MAAM9C,EAASJ,IACfgD,EAAKI,EAAAA,GAAeC,aAAaL,EAAI5C,GACrC2C,EAAMK,EAAAA,GAAeC,aAAaN,EAAK3C,GACvC8C,EAAOA,GAAMI,KAAKC,GAAQH,EAAAA,GAAeC,aAAaE,EAAKnD,KAC3D,MAAMoD,EAAY,CAChBR,GAAIA,GAAU7C,EAAMxB,QAAQqC,IAAM,IAAME,IACxCyB,QAASI,EACT/B,IAAKb,EAAMxB,QAAQqC,MACnBiC,KAAMA,GAAQ1D,EAAWC,OACzB0D,KAAMA,GAAQ,GACdO,QAA+B,MAAtBtD,EAAMxB,QAAQ8B,KAAe,GAAK,CAACN,EAAMxB,QAAQ8B,KAAKuC,IAC/DU,OAAQvD,EAAMxB,QAAQmC,YAExBX,EAAMxB,QAAQ8B,KAAO+C,EACrBpB,EAAAA,GAAIe,KAAK,cAAe/C,EAAOC,gBAC/BF,EAAMxB,QAAQ4B,QAAQoD,IAAIH,EAAUR,GAAIQ,GACxCrD,EAAMxB,QAAQkC,SAAS8C,IAAIxD,EAAMxB,QAAQmC,WAAY0C,EAAUR,IAC/DZ,EAAAA,GAAIC,MAAM,iBAAmBmB,EAAUR,GACzC,GAAG,UACCU,GAAyB9E,EAAAA,EAAAA,KAAO,SAASgF,GAC3C,IAAIjD,EAAOiD,EAASjD,KACpB,MAAMC,EAAQgD,EAAShD,MAEvB,GADAD,EAAOyC,EAAAA,GAAeC,aAAa1C,EAAMX,KACrCG,EAAMxB,QAAQkC,SAASgD,IAAIlD,GAC7B,MAAM,IAAImD,MACR,4HAA4HnD,OAGhIR,EAAMxB,QAAQkC,SAAS8C,IAAIhD,EAA4B,MAAtBR,EAAMxB,QAAQ8B,KAAeN,EAAMxB,QAAQ8B,KAAKuC,GAAK,MACtF7C,EAAMxB,QAAQ+B,aAAaiD,IAAIhD,EAAM,CAAEA,OAAMC,UAC7CmD,EAASpD,GACTyB,EAAAA,GAAIC,MAAM,kBACZ,GAAG,UACC2B,GAAwBpF,EAAAA,EAAAA,KAAQqF,IAClC,IAAIC,EAAcD,EAAQP,OACtBS,EAAWF,EAAQjB,GACvB,MAAMoB,EAAeH,EAAQhB,KACvBoB,EAAaJ,EAAQf,KACrB9C,EAASJ,IACfkE,EAAcd,EAAAA,GAAeC,aAAaa,EAAa9D,GACnD+D,IACFA,EAAWf,EAAAA,GAAeC,aAAac,EAAU/D,IAEnD,MAAMkE,EAAqBnE,EAAMxB,QAAQkC,SAAS0D,IAAIpE,EAAMxB,QAAQmC,YAC9D0D,EAAmBrE,EAAMxB,QAAQkC,SAAS0D,IAAIL,GAC9CO,EAAgBH,EAAqBnE,EAAMxB,QAAQ4B,QAAQgE,IAAID,QAAsB,EACrFI,EAAcF,EAAmBrE,EAAMxB,QAAQ4B,QAAQgE,IAAIC,QAAoB,EACrF,GAAIC,GAAiBC,GAAeD,EAAcf,SAAWQ,EAC3D,MAAM,IAAIJ,MAAM,wBAAwBI,mBAE1C,GAAI/D,EAAMxB,QAAQmC,aAAeoD,EAAa,CAC5C,MAAMxB,EAAQ,IAAIoB,MAAM,+DAMxB,MALApB,EAAMiC,KAAO,CACXC,KAAM,SAASV,IACfW,MAAO,SAASX,IAChBY,SAAU,CAAC,eAEPpC,CACR,CACA,QAAsB,IAAlB+B,IAA6BA,EAAe,CAC9C,MAAM/B,EAAQ,IAAIoB,MAChB,+CAA+C3D,EAAMxB,QAAQmC,6BAO/D,MALA4B,EAAMiC,KAAO,CACXC,KAAM,SAASV,IACfW,MAAO,SAASX,IAChBY,SAAU,CAAC,WAEPpC,CACR,CACA,IAAKvC,EAAMxB,QAAQkC,SAASgD,IAAIK,GAAc,CAC5C,MAAMxB,EAAQ,IAAIoB,MAChB,oDAAsDI,EAAc,oBAOtE,MALAxB,EAAMiC,KAAO,CACXC,KAAM,SAASV,IACfW,MAAO,SAASX,IAChBY,SAAU,CAAC,UAAUZ,MAEjBxB,CACR,CACA,QAAoB,IAAhBgC,IAA2BA,EAAa,CAC1C,MAAMhC,EAAQ,IAAIoB,MAChB,oDAAsDI,EAAc,oBAOtE,MALAxB,EAAMiC,KAAO,CACXC,KAAM,SAASV,IACfW,MAAO,SAASX,IAChBY,SAAU,CAAC,aAEPpC,CACR,CACA,GAAI+B,IAAkBC,EAAa,CACjC,MAAMhC,EAAQ,IAAIoB,MAAM,4DAMxB,MALApB,EAAMiC,KAAO,CACXC,KAAM,SAASV,IACfW,MAAO,SAASX,IAChBY,SAAU,CAAC,eAEPpC,CACR,CACA,GAAIyB,GAAYhE,EAAMxB,QAAQ4B,QAAQsD,IAAIM,GAAW,CACnD,MAAMzB,EAAQ,IAAIoB,MAChB,8CAAgDK,EAAW,4CAS7D,MAPAzB,EAAMiC,KAAO,CACXC,KAAM,SAASV,KAAeC,KAAYC,KAAgBC,GAAYU,KAAK,OAC3EF,MAAO,SAASX,KAAeC,KAAYC,KAAgBC,GAAYU,KAAK,OAC5ED,SAAU,CACR,SAASZ,KAAeC,YAAmBC,KAAgBC,GAAYU,KAAK,SAG1ErC,CACR,CACA,MAAMsC,EAAiBR,GAAsC,GACvDS,EAAU,CACdjC,GAAImB,GAAY,GAAGhE,EAAMxB,QAAQqC,OAAOE,MACxCyB,QAAS,iBAAiBuB,UAAoB/D,EAAMxB,QAAQmC,aAC5DE,IAAKb,EAAMxB,QAAQqC,MACnByC,QAA+B,MAAtBtD,EAAMxB,QAAQ8B,KAAe,GAAK,CAACN,EAAMxB,QAAQ8B,KAAKuC,GAAIgC,GACnEtB,OAAQvD,EAAMxB,QAAQmC,WACtBmC,KAAM1D,EAAWI,MACjBuF,WAAYd,EACZD,WAAUA,EACVjB,KAAMmB,GAAc,IAEtBlE,EAAMxB,QAAQ8B,KAAOwE,EACrB9E,EAAMxB,QAAQ4B,QAAQoD,IAAIsB,EAAQjC,GAAIiC,GACtC9E,EAAMxB,QAAQkC,SAAS8C,IAAIxD,EAAMxB,QAAQmC,WAAYmE,EAAQjC,IAC7DZ,EAAAA,GAAIC,MAAMlC,EAAMxB,QAAQkC,UACxBuB,EAAAA,GAAIC,MAAM,iBAAiB,GAC1B,SACC8C,GAA6BvG,EAAAA,EAAAA,KAAO,SAASwG,GAC/C,IAAIC,EAAWD,EAAapC,GACxBsC,EAAWF,EAAaE,SACxBpC,EAAOkC,EAAalC,KACpBqC,EAAiBH,EAAaI,OAClCpD,EAAAA,GAAIC,MAAM,uBAAwBgD,EAAUC,EAAUpC,GACtD,MAAM9C,EAASJ,IAKf,GAJAqF,EAAWjC,EAAAA,GAAeC,aAAagC,EAAUjF,GACjDkF,EAAWlC,EAAAA,GAAeC,aAAaiC,EAAUlF,GACjD8C,EAAOA,GAAMI,KAAKC,GAAQH,EAAAA,GAAeC,aAAaE,EAAKnD,KAC3DmF,EAAiBnC,EAAAA,GAAeC,aAAakC,EAAgBnF,IACxDiF,IAAalF,EAAMxB,QAAQ4B,QAAQsD,IAAIwB,GAAW,CACrD,MAAM3C,EAAQ,IAAIoB,MAChB,+EAOF,MALApB,EAAMiC,KAAO,CACXC,KAAM,cAAcS,KAAYC,IAChCT,MAAO,cAAcQ,KAAYC,IACjCR,SAAU,CAAC,oBAEPpC,CACR,CACA,MAAM+C,EAAetF,EAAMxB,QAAQ4B,QAAQgE,IAAIc,GAC/C,QAAqB,IAAjBI,IAA4BA,EAC9B,MAAM,IAAI3B,MAAM,+EAElB,GAAIyB,KAAoBG,MAAMC,QAAQF,EAAahC,WAAYgC,EAAahC,QAAQmC,SAASL,IAAkB,CAI7G,MAHc,IAAIzB,MAChB,yGAGJ,CACA,MAAM+B,EAAqBJ,EAAa/B,OACxC,GAAI+B,EAAaxC,OAAS1D,EAAWI,QAAU4F,EAAgB,CAI7D,MAHc,IAAIzB,MAChB,wHAGJ,CACA,IAAKwB,IAAanF,EAAMxB,QAAQ4B,QAAQsD,IAAIyB,GAAW,CACrD,GAAIO,IAAuB1F,EAAMxB,QAAQmC,WAAY,CACnD,MAAM4B,EAAQ,IAAIoB,MAChB,+EAOF,MALApB,EAAMiC,KAAO,CACXC,KAAM,cAAcS,KAAYC,IAChCT,MAAO,cAAcQ,KAAYC,IACjCR,SAAU,CAAC,oBAEPpC,CACR,CACA,MAAMoD,EAAkB3F,EAAMxB,QAAQkC,SAAS0D,IAAIpE,EAAMxB,QAAQmC,YACjE,QAAwB,IAApBgF,IAA+BA,EAAiB,CAClD,MAAMpD,EAAQ,IAAIoB,MAChB,qDAAqD3D,EAAMxB,QAAQmC,6BAOrE,MALA4B,EAAMiC,KAAO,CACXC,KAAM,cAAcS,KAAYC,IAChCT,MAAO,cAAcQ,KAAYC,IACjCR,SAAU,CAAC,oBAEPpC,CACR,CACA,MAAM+B,EAAgBtE,EAAMxB,QAAQ4B,QAAQgE,IAAIuB,GAChD,QAAsB,IAAlBrB,IAA6BA,EAAe,CAC9C,MAAM/B,EAAQ,IAAIoB,MAChB,qDAAqD3D,EAAMxB,QAAQmC,6BAOrE,MALA4B,EAAMiC,KAAO,CACXC,KAAM,cAAcS,KAAYC,IAChCT,MAAO,cAAcQ,KAAYC,IACjCR,SAAU,CAAC,oBAEPpC,CACR,CACA,MAAMuC,EAAU,CACdjC,GAAI7C,EAAMxB,QAAQqC,IAAM,IAAME,IAC9ByB,QAAS,iBAAiB8C,GAAc9C,gBAAgBxC,EAAMxB,QAAQmC,aACtEE,IAAKb,EAAMxB,QAAQqC,MACnByC,QAA+B,MAAtBtD,EAAMxB,QAAQ8B,KAAe,GAAK,CAACN,EAAMxB,QAAQ8B,KAAKuC,GAAIyC,EAAazC,IAChFU,OAAQvD,EAAMxB,QAAQmC,WACtBmC,KAAM1D,EAAWK,YACjBsD,KAAMA,EAAOA,EAAK6C,OAAOC,SAAW,CAClC,eAAeP,EAAazC,KAAKyC,EAAaxC,OAAS1D,EAAWI,MAAQ,WAAW4F,IAAmB,OAG5GpF,EAAMxB,QAAQ8B,KAAOwE,EACrB9E,EAAMxB,QAAQ4B,QAAQoD,IAAIsB,EAAQjC,GAAIiC,GACtC9E,EAAMxB,QAAQkC,SAAS8C,IAAIxD,EAAMxB,QAAQmC,WAAYmE,EAAQjC,IAC7DZ,EAAAA,GAAIC,MAAMlC,EAAMxB,QAAQkC,UACxBuB,EAAAA,GAAIC,MAAM,gBACZ,CACF,GAAG,cACC0B,GAA2BnF,EAAAA,EAAAA,KAAO,SAASqH,GAE7C,GADAA,EAAU7C,EAAAA,GAAeC,aAAa4C,EAASjG,MAC1CG,EAAMxB,QAAQkC,SAASgD,IAAIoC,GAAU,CACxC,MAAMvD,EAAQ,IAAIoB,MAChB,+EAA+EmC,OAOjF,MALAvD,EAAMiC,KAAO,CACXC,KAAM,YAAYqB,IAClBpB,MAAO,YAAYoB,IACnBnB,SAAU,CAAC,UAAUmB,MAEjBvD,CACR,CAAO,CACLvC,EAAMxB,QAAQmC,WAAamF,EAC3B,MAAMjD,EAAK7C,EAAMxB,QAAQkC,SAAS0D,IAAIpE,EAAMxB,QAAQmC,YAIlDX,EAAMxB,QAAQ8B,UAHL,IAAPuC,GAAkBA,EAGC7C,EAAMxB,QAAQ4B,QAAQgE,IAAIvB,IAAO,KAFjC,IAIzB,CACF,GAAG,YACH,SAASkD,EAAOC,EAAKrE,EAAKsE,GACxB,MAAMC,EAAQF,EAAIG,QAAQxE,IACX,IAAXuE,EACFF,EAAIpE,KAAKqE,GAETD,EAAII,OAAOF,EAAO,EAAGD,EAEzB,CAEA,SAASI,EAAyBC,GAChC,MAAMxB,EAAUwB,EAAU9E,QAAO,CAACC,EAAK8E,IACjC9E,EAAIZ,IAAM0F,EAAQ1F,IACbY,EAEF8E,GACND,EAAU,IACb,IAAIE,EAAO,GACXF,EAAUG,SAAQ,SAASC,GAEvBF,GADEE,IAAM5B,EACA,MAEA,KAEZ,IACA,MAAM6B,EAAQ,CAACH,EAAM1B,EAAQjC,GAAIiC,EAAQjE,KACzC,IAAK,MAAMiF,KAAW9F,EAAMxB,QAAQkC,SAC9BV,EAAMxB,QAAQkC,SAAS0D,IAAI0B,KAAahB,EAAQjC,IAClD8D,EAAM/E,KAAKkE,GAIf,GADA7D,EAAAA,GAAIC,MAAMyE,EAAM/B,KAAK,MACjBE,EAAQxB,SAAqC,GAA1BwB,EAAQxB,QAAQrC,QAAe6D,EAAQxB,QAAQ,IAAMwB,EAAQxB,QAAQ,GAAI,CAC9F,MAAMD,EAAYrD,EAAMxB,QAAQ4B,QAAQgE,IAAIU,EAAQxB,QAAQ,IAC5DyC,EAAOO,EAAWxB,EAASzB,GACvByB,EAAQxB,QAAQ,IAClBgD,EAAU1E,KAAK5B,EAAMxB,QAAQ4B,QAAQgE,IAAIU,EAAQxB,QAAQ,IAE7D,KAAO,IAA8B,GAA1BwB,EAAQxB,QAAQrC,OACzB,OAEA,GAAI6D,EAAQxB,QAAQ,GAAI,CACtB,MAAMD,EAAYrD,EAAMxB,QAAQ4B,QAAQgE,IAAIU,EAAQxB,QAAQ,IAC5DyC,EAAOO,EAAWxB,EAASzB,EAC7B,CACF,CAEAgD,EADAC,EAAYpF,EAAOoF,GAAYI,GAAMA,EAAE7D,KAEzC,EAvCApE,EAAAA,EAAAA,IAAOsH,EAAQ,WAwCftH,EAAAA,EAAAA,IAAO4H,EAA0B,4BACjC,IAAIO,GAA8BnI,EAAAA,EAAAA,KAAO,WACvCwD,EAAAA,GAAIC,MAAMlC,EAAMxB,QAAQ4B,SAExBiG,EAAyB,CADZQ,IAAkB,IAEjC,GAAG,eACCC,GAAyBrI,EAAAA,EAAAA,KAAO,WAClCuB,EAAMtB,SACNqI,EAAAA,EAAAA,KACF,GAAG,SACCC,GAAwCvI,EAAAA,EAAAA,KAAO,WAUjD,MATsB,IAAIuB,EAAMxB,QAAQ+B,aAAa0G,UAAU9D,KAAI,CAAC5C,EAAc2G,IACrD,OAAvB3G,EAAaE,YAAyC,IAAvBF,EAAaE,MACvCF,EAEF,IACFA,EACHE,MAAO0G,WAAW,KAAKD,QAExBE,MAAK,CAACC,EAAGC,KAAOD,EAAE5G,OAAS,IAAM6G,EAAE7G,OAAS,KAAI0C,KAAIoE,IAAA,IAAC,KAAE/G,GAAM+G,EAAA,MAAM,CAAE/G,OAAM,GAEhF,GAAG,yBACCgH,GAA8B/I,EAAAA,EAAAA,KAAO,WACvC,OAAOuB,EAAMxB,QAAQkC,QACvB,GAAG,eACC+G,GAA6BhJ,EAAAA,EAAAA,KAAO,WACtC,OAAOuB,EAAMxB,QAAQ4B,OACvB,GAAG,cACCyG,GAAkCpI,EAAAA,EAAAA,KAAO,WAC3C,MAAM6H,EAAY,IAAItG,EAAMxB,QAAQ4B,QAAQ6G,UAK5C,OAJAX,EAAUG,SAAQ,SAASiB,GACzBzF,EAAAA,GAAIC,MAAMwF,EAAE7E,GACd,IACAyD,EAAUc,MAAK,CAACC,EAAGC,IAAMD,EAAExG,IAAMyG,EAAEzG,MAC5ByF,CACT,GAAG,mBAUCzH,EAAK,CACPO,aACAW,UAAWF,EACXgC,eACAE,aACAU,aACAC,SACAa,SACAM,QACAmB,aACApB,WAEAgD,cACAG,MAAOD,EACPE,wBACAQ,cACAC,aACAZ,kBACAc,kBA3BqClJ,EAAAA,EAAAA,KAAO,WAC5C,OAAOuB,EAAMxB,QAAQmC,UACvB,GAAG,oBA0BDiH,cAzBiCnJ,EAAAA,EAAAA,KAAO,WACxC,OAAOuB,EAAMxB,QAAQoC,SACvB,GAAG,gBAwBDiH,SAvB4BpJ,EAAAA,EAAAA,KAAO,WACnC,OAAOuB,EAAMxB,QAAQ8B,IACvB,GAAG,WAsBDrB,YAAW,KACX6I,YAAW,KACXC,kBAAiB,KACjBhJ,kBAAiB,KACjBI,gBAAe,KACf6I,gBAAeA,EAAAA,IAIbC,GAA2BxJ,EAAAA,EAAAA,KAAO,CAACG,EAAKsJ,MAC1CvJ,EAAAA,EAAAA,GAAiBC,EAAKsJ,GAClBtJ,EAAIuJ,KACND,EAAIrG,aAAajD,EAAIuJ,KAEvB,IAAK,MAAMC,KAAaxJ,EAAIyJ,WAC1BC,EAAeF,EAAWF,EAC5B,GACC,YACCI,GAAiC7J,EAAAA,EAAAA,KAAO,CAAC2J,EAAWF,KACtD,MAOMK,EAPU,CACdC,QAAwB/J,EAAAA,EAAAA,KAAQgK,GAASP,EAAIxF,OAAOgG,EAAYD,KAAQ,UACxEE,QAAwBlK,EAAAA,EAAAA,KAAQgK,GAASP,EAAI3E,OAAOqF,EAAYH,KAAQ,UACxEI,OAAuBpK,EAAAA,EAAAA,KAAQgK,GAASP,EAAIrE,MAAMiF,EAAWL,KAAQ,SACrEM,UAA0BtK,EAAAA,EAAAA,KAAQgK,GAASP,EAAItE,SAASoF,EAAcP,KAAQ,YAC9EQ,eAA+BxK,EAAAA,EAAAA,KAAQgK,GAASP,EAAIlD,WAAWkE,EAAmBT,KAAQ,kBAEpEL,EAAUe,OAC9BZ,EACFA,EAAQH,GAERnG,EAAAA,GAAIM,MAAM,2BAA2B6F,EAAUe,QACjD,GACC,kBACCT,GAA8BjK,EAAAA,EAAAA,KAAQqG,IACvB,CACfjC,GAAIiC,EAAQjC,GACZD,IAAKkC,EAAQtC,SAAW,GACxBM,UAAuB,IAAjBgC,EAAQhC,KAAkB1D,EAAW0F,EAAQhC,MAAQ1D,EAAWC,OACtE0D,KAAM+B,EAAQ/B,WAAQ,KAGvB,eACC6F,GAA8BnK,EAAAA,EAAAA,KAAQqH,IACvB,CACftF,KAAMsF,EAAQtF,KACdC,MAAOqF,EAAQrF,OAAS,KAGzB,eACCqI,GAA6BrK,EAAAA,EAAAA,KAAQ2K,IACvB,CACd7F,OAAQ6F,EAAO7F,OACfV,GAAIuG,EAAOvG,IAAM,GACjBC,UAAsB,IAAhBsG,EAAOtG,KAAkB1D,EAAWgK,EAAOtG,WAAQ,EACzDC,KAAMqG,EAAOrG,WAAQ,KAGtB,cACCiG,GAAgCvK,EAAAA,EAAAA,KAAQ4K,GAC1BA,EAAU9F,QAEzB,iBACC2F,GAAqCzK,EAAAA,EAAAA,KAAQ6K,IAC1B,CACnBzG,GAAIyG,EAAczG,GAClBsC,SAAU,GACVpC,KAAqC,IAA/BuG,EAAcvG,MAAM9B,YAAe,EAASqI,EAAcvG,KAChEsC,OAAQiE,EAAcjE,UAGvB,sBACCkE,EAAS,CACXlH,OAAuB5D,EAAAA,EAAAA,KAAO+K,UAC5B,MAAM5K,QAAYyD,EAAAA,EAAAA,IAAM,WAAYoH,GACpCxH,EAAAA,GAAIC,MAAMtD,GACVqJ,EAASrJ,EAAKC,EAAG,GAChB,UA+IL,IAAI6K,GAAiBC,EAAAA,EAAAA,MACjBC,EAA2BF,GAAgB9J,SAC3CiK,EAAgB,GAChBC,EAAc,GAIdC,EAA4B,IAAI1J,IAChC2J,EAA4B,IAAI3J,IAEhC4J,EAAiC,IAAI5J,IACrC6J,EAAQ,GACRC,EAAS,EACThC,EAAM,KACNiC,GAAyB3L,EAAAA,EAAAA,KAAO,KAClCsL,EAAUhD,QACViD,EAAUjD,QACVkD,EAAelD,QACfoD,EAAS,EACTD,EAAQ,GACR/B,EAAM,IAAI,GACT,SACCkC,GAA2B5L,EAAAA,EAAAA,KAAQ6L,IACrC,MAAMC,EAAWC,SAASC,gBAAgB,6BAA8B,QAWxE,OAV4B,kBAARH,EAAmBA,EAAII,MAAM,uBAAyBJ,GACrE7D,SAASkE,IACZ,MAAMC,EAAQJ,SAASC,gBAAgB,6BAA8B,SACrEG,EAAMC,eAAe,uCAAwC,YAAa,YAC1ED,EAAME,aAAa,KAAM,OACzBF,EAAME,aAAa,IAAK,KACxBF,EAAME,aAAa,QAAS,OAC5BF,EAAMG,YAAcJ,EAAIxI,OACxBoI,EAASS,YAAYJ,EAAM,IAEtBL,CAAQ,GACd,YACCU,GAAoCxM,EAAAA,EAAAA,KAAQ6E,IAC9C,IAAI4H,EACAC,EACAC,EAeJ,MAdY,OAARjD,GACFgD,GAAiC1M,EAAAA,EAAAA,KAAO,CAAC4I,EAAGC,IAAMD,GAAKC,GAAG,kBAC1D8D,EAAiBC,MAEjBF,GAAiC1M,EAAAA,EAAAA,KAAO,CAAC4I,EAAGC,IAAMD,GAAKC,GAAG,kBAC1D8D,EAAiB,GAEnB9H,EAAQmD,SAASpB,IACf,MAAMiG,EAAyB,OAARnD,GAAuB,MAAPA,EAAc6B,EAAU5F,IAAIiB,IAASkG,EAAIvB,EAAU5F,IAAIiB,IAASmG,OAChF,IAAnBF,GAA6BH,EAAeG,EAAgBF,KAC9DF,EAAgB7F,EAChB+F,EAAiBE,EACnB,IAEKJ,CAAa,GACnB,qBACCO,GAAsChN,EAAAA,EAAAA,KAAQ6E,IAChD,IAAI4H,EAAgB,GAChBQ,EAAcL,IAQlB,OAPA/H,EAAQmD,SAASpB,IACf,MAAMiG,EAAiBtB,EAAU5F,IAAIiB,GAAQkG,EACzCD,GAAkBI,IACpBR,EAAgB7F,EAChBqG,EAAcJ,EAChB,IAEKJ,QAAiB,CAAM,GAC7B,uBACCS,IAAmClN,EAAAA,EAAAA,KAAO,CAACmN,EAAYxL,EAASyL,KAClE,IAAIC,EAASD,EACTH,EAAcG,EAClB,MAAME,EAAQ,GACdH,EAAWnF,SAAS9E,IAClB,MAAMmD,EAAU1E,EAAQgE,IAAIzC,GAC5B,IAAKmD,EACH,MAAM,IAAInB,MAAM,4BAA4BhC,KAE1CmD,EAAQxB,QAAQrC,QAClB6K,EAASE,GAAwBlH,GACjC4G,EAAcO,KAAKC,IAAIJ,EAAQJ,IAE/BK,EAAMnK,KAAKkD,GAEbqH,GAAkBrH,EAASgH,EAAO,IAEpCA,EAASJ,EACTK,EAAMtF,SAAS3B,IACbsH,GAAgBtH,EAASgH,EAAQD,EAAY,IAE/CD,EAAWnF,SAAS9E,IAClB,MAAMmD,EAAU1E,EAAQgE,IAAIzC,GAC5B,GAAImD,GAASxB,QAAQrC,OAAQ,CAC3B,MAAMiK,EAAgBO,EAAoB3G,EAAQxB,SAClDwI,EAAS9B,EAAU5F,IAAI8G,GAAeK,EAAIzB,EACtCgC,GAAUJ,IACZA,EAAcI,GAEhB,MAAMN,EAAIzB,EAAU3F,IAAIU,EAAQvB,QAAQ8I,IAClCd,EAAIO,EAASjC,EACnBG,EAAUxG,IAAIsB,EAAQjC,GAAI,CAAE2I,IAAGD,KACjC,IACA,GACD,oBACCe,IAAuC7N,EAAAA,EAAAA,KAAQqG,IACjD,MAAMoG,EAAgBD,EAAkBnG,EAAQxB,QAAQsC,QAAQ2G,GAAY,OAANA,KACtE,IAAKrB,EACH,MAAM,IAAIvH,MAAM,uCAAuCmB,EAAQjC,MAEjE,MAAM2J,EAAmBxC,EAAU5F,IAAI8G,IAAgBK,EACvD,QAAyB,IAArBiB,EACF,MAAM,IAAI7I,MAAM,gDAAgDmB,EAAQjC,MAE1E,OAAO2J,CAAgB,GACtB,wBACCR,IAA0CvN,EAAAA,EAAAA,KAAQqG,GAC3BwH,GAAqBxH,GACpBgF,GACzB,2BACCqC,IAAoC1N,EAAAA,EAAAA,KAAO,CAACqG,EAASgH,KACvD,MAAMhG,EAAUiE,EAAU3F,IAAIU,EAAQvB,QACtC,IAAKuC,EACH,MAAM,IAAInC,MAAM,+BAA+BmB,EAAQjC,MAEzD,MAAM2I,EAAI1F,EAAQuG,IACZd,EAAIO,EAASjC,EAEnB,OADAG,EAAUxG,IAAIsB,EAAQjC,GAAI,CAAE2I,IAAGD,MACxB,CAAEC,IAAGD,IAAG,GACd,qBACCa,IAAkC3N,EAAAA,EAAAA,KAAO,CAACqG,EAASgH,EAAQD,KAC7D,MAAM/F,EAAUiE,EAAU3F,IAAIU,EAAQvB,QACtC,IAAKuC,EACH,MAAM,IAAInC,MAAM,+BAA+BmB,EAAQjC,MAEzD,MAAM0I,EAAIO,EAASD,EACbL,EAAI1F,EAAQuG,IAClBrC,EAAUxG,IAAIsB,EAAQjC,GAAI,CAAE2I,IAAGD,KAAI,GAClC,mBACCkB,IAAmChO,EAAAA,EAAAA,KAAO,CAACiO,EAAU5H,EAAS6H,EAAgBC,EAAWC,EAAaC,KACxG,GAAIA,IAAqB1N,EAAWG,UAClCmN,EAASK,OAAO,QAAQC,KAAK,IAAKL,EAAenB,EAAI,IAAIwB,KAAK,IAAKL,EAAepB,EAAI,IAAIyB,KAAK,QAAS,IAAIA,KAAK,SAAU,IAAIA,KAC7H,QACA,UAAUlI,EAAQjC,sBAAsBgK,EAvItB,KAuIyDD,WAE7EF,EAASK,OAAO,QAAQC,KAAK,IAAKL,EAAenB,EAAI,GAAGwB,KAAK,IAAKL,EAAepB,EAAI,GAAGyB,KAAK,QAAS,IAAIA,KAAK,SAAU,IAAIA,KAC3H,QACA,UAAUlI,EAAQjC,YAAYgK,EA3IZ,KA2I+CD,gBAE9D,GAAIE,IAAqB1N,EAAWK,YACzCiN,EAASK,OAAO,UAAUC,KAAK,KAAML,EAAenB,GAAGwB,KAAK,KAAML,EAAepB,GAAGyB,KAAK,IAAK,IAAIA,KAAK,QAAS,UAAUlI,EAAQjC,MAAM+J,KACxIF,EAASK,OAAO,UAAUC,KAAK,KAAML,EAAenB,EAAI,GAAGwB,KAAK,KAAML,EAAepB,EAAI,GAAGyB,KAAK,IAAK,MAAMA,KAAK,OAAQ,QAAQA,KAAK,QAAS,UAAUlI,EAAQjC,MAAM+J,KACvKF,EAASK,OAAO,UAAUC,KAAK,KAAML,EAAenB,EAAI,GAAGwB,KAAK,KAAML,EAAepB,EAAI,GAAGyB,KAAK,IAAK,MAAMA,KAAK,OAAQ,QAAQA,KAAK,QAAS,UAAUlI,EAAQjC,MAAM+J,KACvKF,EAASK,OAAO,QAAQC,KAAK,KAAML,EAAenB,EAAI,GAAGwB,KAAK,KAAML,EAAepB,EAAI,GAAGyB,KAAK,KAAML,EAAenB,GAAGwB,KAAK,KAAML,EAAepB,EAAI,GAAGyB,KAAK,SAAU,QAAQA,KAAK,QAAS,UAAUlI,EAAQjC,MAAM+J,KACrNF,EAASK,OAAO,QAAQC,KAAK,KAAML,EAAenB,EAAI,GAAGwB,KAAK,KAAML,EAAepB,EAAI,GAAGyB,KAAK,KAAML,EAAenB,GAAGwB,KAAK,KAAML,EAAepB,EAAI,GAAGyB,KAAK,SAAU,QAAQA,KAAK,QAAS,UAAUlI,EAAQjC,MAAM+J,SAChN,CACL,MAAMK,EAASP,EAASK,OAAO,UAK/B,GAJAE,EAAOD,KAAK,KAAML,EAAenB,GACjCyB,EAAOD,KAAK,KAAML,EAAepB,GACjC0B,EAAOD,KAAK,IAAKlI,EAAQhC,OAAS1D,EAAWI,MAAQ,EAAI,IACzDyN,EAAOD,KAAK,QAAS,UAAUlI,EAAQjC,YAAYgK,EAxJ/B,KAyJhBC,IAAqB1N,EAAWI,MAAO,CACzC,MAAM0N,EAAUR,EAASK,OAAO,UAChCG,EAAQF,KAAK,KAAML,EAAenB,GAClC0B,EAAQF,KAAK,KAAML,EAAepB,GAClC2B,EAAQF,KAAK,IAAK,GAClBE,EAAQF,KACN,QACA,UAAUJ,KAAa9H,EAAQjC,YAAYgK,EAhK3B,IAkKpB,CACA,GAAIC,IAAqB1N,EAAWE,QAAS,CAC7BoN,EAASK,OAAO,QACxBC,KACJ,IACA,KAAKL,EAAenB,EAAI,KAAKmB,EAAepB,EAAI,KAAKoB,EAAenB,EAAI,KAAKmB,EAAepB,EAAI,KAAKoB,EAAenB,EAAI,KAAKmB,EAAepB,EAAI,KAAKoB,EAAenB,EAAI,KAAKmB,EAAepB,EAAI,KAChMyB,KAAK,QAAS,UAAUJ,KAAa9H,EAAQjC,YAAYgK,EAxKzC,IAyKpB,CACF,IACC,oBACCM,IAAkC1O,EAAAA,EAAAA,KAAO,CAAC2O,EAAStI,EAAS6H,EAAgBN,KAC9E,GAAIvH,EAAQhC,OAAS1D,EAAWK,cAAgBqF,EAAQd,UAAYc,EAAQhC,OAAS1D,EAAWI,OAASsF,EAAQhC,OAAS1D,EAAWI,QAAUoK,GAA0ByD,gBAAiB,CACxL,MAAMC,EAAUF,EAAQL,OAAO,KACzBQ,EAAWD,EAAQE,OAAO,QAAQR,KAAK,QAAS,oBAChDvI,EAAO6I,EAAQP,OAAO,QAAQC,KAAK,IAAKX,GAAKW,KAAK,IAAKL,EAAepB,EAAI,IAAIyB,KAAK,QAAS,gBAAgBvI,KAAKK,EAAQjC,IACzH4K,EAAOhJ,EAAKiJ,QAAQC,UAC1B,GAAIF,IACFF,EAASP,KAAK,IAAKL,EAAeiB,cAAgBH,EAAKI,MAAQ,EApL5D,GAoLoEb,KAAK,IAAKL,EAAepB,EAAI,MAAMyB,KAAK,QAASS,EAAKI,MAAQ,GAAQb,KAAK,SAAUS,EAAKK,OAAS,GAC9J,OAAR3F,GAAwB,OAARA,GAClBoF,EAASP,KAAK,IAAKL,EAAenB,GAAKiC,EAAKI,MAAQ,GAAS,IAAIb,KAAK,IAAKL,EAAepB,EAAI,IAC9F9G,EAAKuI,KAAK,IAAKL,EAAenB,GAAKiC,EAAKI,MAAQ,KAASb,KAAK,IAAKL,EAAepB,EAAIkC,EAAKK,OAAS,KAEpGrJ,EAAKuI,KAAK,IAAKL,EAAeiB,cAAgBH,EAAKI,MAAQ,GAEzDjE,EAAyBmE,mBAC3B,GAAY,OAAR5F,GAAwB,OAARA,EAClB1D,EAAKuI,KACH,YACA,eAAiBL,EAAenB,EAAI,KAAOmB,EAAepB,EAAI,KAEhEgC,EAASP,KACP,YACA,eAAiBL,EAAenB,EAAI,KAAOmB,EAAepB,EAAI,SAE3D,CACL,MAAMyC,GAAO,KAAOP,EAAKI,MAAQ,IAAM,GAAK,IACtCI,EAAM,GAAKR,EAAKI,MAAQ,GAAK,IACnCP,EAAQN,KACN,YACA,aAAegB,EAAM,KAAOC,EAAM,iBAAmB5B,EAAM,KAAOM,EAAepB,EAAI,IAEzF,CAGN,IACC,mBACC2C,IAAiCzP,EAAAA,EAAAA,KAAO,CAAC2O,EAAStI,EAAS6H,EAAgBN,KAC7E,GAAIvH,EAAQ/B,KAAK9B,OAAS,EAAG,CAC3B,IAAIkN,EAAU,EACVC,EAAkB,EAClBC,EAAmB,EACvB,MAAMC,EAAc,GACpB,IAAK,MAAMC,KAAYzJ,EAAQ/B,KAAKyL,UAAW,CAC7C,MAAMC,EAAOrB,EAAQI,OAAO,WACtBkB,EAAOtB,EAAQL,OAAO,UACtB3J,EAAMgK,EAAQL,OAAO,QAAQC,KAAK,IAAKL,EAAepB,EAAI,GAAK4C,GAASnB,KAAK,QAAS,aAAavI,KAAK8J,GACxGI,EAAUvL,EAAIsK,QAAQC,UAC5B,IAAKgB,EACH,MAAM,IAAIhL,MAAM,sBAElByK,EAAkBnC,KAAKC,IAAIkC,EAAiBO,EAAQd,OACpDQ,EAAmBpC,KAAKC,IAAImC,EAAkBM,EAAQb,QACtD1K,EAAI4J,KAAK,IAAKL,EAAeiB,cAAgBe,EAAQd,MAAQ,GAC7DS,EAAY1M,KAAK,CACfwB,MACAsL,OACAD,OACAN,YAEFA,GAAW,EACb,CACA,IAAK,MAAM,IAAE/K,EAAG,KAAEsL,EAAI,KAAED,EAAMN,QAASS,KAAcN,EAAa,CAChE,MAAMO,EAAKR,EAAmB,EACxBS,EAAKnC,EAAepB,EAAI,KAAOqD,EAYrC,GAXAH,EAAKzB,KAAK,QAAS,iBAAiBA,KAClC,SACA,WACAX,EAAM+B,EAAkB,EAAIW,KAAUD,EAhPrC,cAiPDzC,EAAM+B,EAAkB,EAAIW,KAAUD,EAjPrC,YAkPDnC,EAAeiB,cAAgBQ,EAAkB,EAnPhD,KAmP0DU,EAAKD,EAlP/D,YAmPDlC,EAAeiB,cAAgBQ,EAAkB,EApPhD,KAoP0DU,EAAKD,EAnP/D,YAoPDlC,EAAeiB,cAAgBQ,EAAkB,EArPhD,KAqP0DU,EAAKD,EApP/D,YAqPDlC,EAAeiB,cAAgBQ,EAAkB,EAtPhD,KAsP0DU,EAAKD,EArP/D,KAuPHH,EAAK1B,KAAK,KAAM8B,GAAI9B,KAAK,KAAMX,EAAM+B,EAAkB,EAAIW,GAAQ/B,KAAK,IAAK,KAAKA,KAAK,QAAS,YACpF,OAAR7E,GAAwB,OAARA,EAAc,CAChC,MAAM6G,EAAU3C,EAAMuC,EACtBH,EAAKzB,KAAK,QAAS,iBAAiBA,KAClC,SACA,aACAL,EAAenB,KAAKwD,EAAU,cAC9BrC,EAAenB,KAAKwD,EAAU,cAC9BrC,EAAenB,EAAI3B,KAAiBmF,EAAUH,EAAK,cACnDlC,EAAenB,EAAI3B,EAAgBuE,EAAkB,KAAKY,EAAUH,EAAK,cACzElC,EAAenB,EAAI3B,EAAgBuE,EAAkB,KAAKY,EAAUH,EAAK,cACzElC,EAAenB,EAAI3B,KAAiBmF,EAAUH,EAAK,KACnD7B,KAAK,YAAa,+BAAiCL,EAAenB,EAAI,IAAMa,EAAM,KACpFqC,EAAK1B,KAAK,KAAML,EAAenB,EAAIuD,GAAQ/B,KAAK,KAAMgC,GAAShC,KAAK,YAAa,+BAAiCL,EAAenB,EAAI,IAAMa,EAAM,KACjJjJ,EAAI4J,KAAK,IAAKL,EAAenB,EAAI,GAAGwB,KAAK,IAAKgC,EAAU,GAAGhC,KAAK,YAAa,+BAAiCL,EAAenB,EAAI,IAAMa,EAAM,IAC/I,CACF,CACF,IACC,kBACC4C,IAAqCxQ,EAAAA,EAAAA,KAAQqG,IAE/C,OADyBA,EAAQC,YAAcD,EAAQhC,MAErD,KAAK1D,EAAWC,OACd,MAAO,gBACT,KAAKD,EAAWE,QACd,MAAO,iBACT,KAAKF,EAAWG,UACd,MAAO,mBACT,KAAKH,EAAWI,MACd,MAAO,eACT,KAAKJ,EAAWK,YACd,MAAO,qBACT,QACE,MAAO,gBACX,GACC,sBACCyP,IAAoCzQ,EAAAA,EAAAA,KAAO,CAACqG,EAAShD,EAAMuK,EAAK8C,KAClE,MAAMC,EAAwB,CAAE5D,EAAG,EAAGD,EAAG,GACzC,KAAIzG,EAAQxB,QAAQrC,OAAS,GAatB,CACL,GAAa,OAATa,EACF,OAxSW,GAySN,GAAa,OAATA,EAAe,CAExB,OADwBqN,EAAW/K,IAAIU,EAAQjC,KAAOuM,GAC/B7D,EAAIzB,CAC7B,CACE,OAAO,CAEX,CAtBgC,CAC9B,MAAMoB,EAAgBD,EAAkBnG,EAAQxB,SAChD,GAAI4H,EAAe,CACjB,MAAMI,EAAiB6D,EAAW/K,IAAI8G,IAAkBkE,EACxD,GAAa,OAATtN,EACF,OAAOwJ,EAAeC,EAAIzB,EACrB,GAAa,OAAThI,EAAe,CAExB,OADwBqN,EAAW/K,IAAIU,EAAQjC,KAAOuM,GAC/B7D,EAAIzB,CAC7B,CACE,OAAOwB,EAAeE,EAAI1B,CAE9B,CACF,CAUA,OAAO,CAAC,GACP,qBACCuF,IAAoC5Q,EAAAA,EAAAA,KAAO,CAACqG,EAASuH,EAAKiD,KAC5D,MAAM1B,EAAwB,OAARzF,GAAgBmH,EAAoBjD,EAAMA,EAAMxC,EAChE0B,EAAY,OAARpD,GAAwB,OAARA,EAAeyF,EAAgB7D,EAAU3F,IAAIU,EAAQvB,SAAS8I,IAClFb,EAAY,OAARrD,GAAwB,OAARA,EAAe4B,EAAU3F,IAAIU,EAAQvB,SAAS8I,IAAMuB,EAC9E,QAAU,IAANpC,QAAsB,IAAND,EAClB,MAAM,IAAI5H,MAAM,sCAAsCmB,EAAQjC,MAEhE,MAAO,CAAE2I,IAAGD,IAAGqC,gBAAe,GAC7B,qBACC2B,IAA8B9Q,EAAAA,EAAAA,KAAO,CAAC+Q,EAAKpP,EAASqP,KACtD,IAAK7F,EACH,MAAM,IAAIjG,MAAM,6BAElB,MAAM+I,EAAW8C,EAAIzC,OAAO,KAAKC,KAAK,QAAS,kBACzCI,EAAUoC,EAAIzC,OAAO,KAAKC,KAAK,QAAS,iBAC9C,IAAIX,EAAc,OAARlE,GAAwB,OAARA,EAjUX,GAiUuC,EACtD,MAAMuH,EAAO,IAAItP,EAAQsP,QACnBJ,EAAoB1F,GAA0B+F,kBAAmB,EACjEC,GAA2BnR,EAAAA,EAAAA,KAAO,CAAC4I,EAAGC,KAC1C,MAAMuI,EAAOzP,EAAQgE,IAAIiD,IAAIxG,IACvBiP,EAAO1P,EAAQgE,IAAIkD,IAAIzG,IAC7B,YAAgB,IAATgP,QAA4B,IAATC,EAAkBD,EAAOC,EAAO,CAAC,GAC1D,YACH,IAAIlE,EAAa8D,EAAKtI,KAAKwI,GACf,OAARzH,IACEmH,GACF3D,GAAiBC,EAAYxL,EAASiM,GAExCT,EAAaA,EAAW4C,WAE1B5C,EAAWnF,SAAS9E,IAClB,MAAMmD,EAAU1E,EAAQgE,IAAIzC,GAC5B,IAAKmD,EACH,MAAM,IAAInB,MAAM,4BAA4BhC,KAE1C2N,IACFjD,EAAM6C,GAAkBpK,EAASqD,EAAKkE,EAAKrC,IAE7C,MAAM2C,EAAiB0C,GAAkBvK,EAASuH,EAAKiD,GACvD,GAAIG,EAAa,CACf,MAAM7C,EAAYqC,GAAmBnK,GAC/BgI,EAAmBhI,EAAQC,YAAcD,EAAQhC,KACjD+J,EAAc9C,EAAU3F,IAAIU,EAAQvB,SAAS2C,OAAS,EAC5DuG,GAAiBC,EAAU5H,EAAS6H,EAAgBC,EAAWC,EAAaC,GAC5EK,GAAgBC,EAAStI,EAAS6H,EAAgBN,GAClD6B,GAAed,EAAStI,EAAS6H,EAAgBN,EACnD,CACY,OAARlE,GAAwB,OAARA,EAClB6B,EAAUxG,IAAIsB,EAAQjC,GAAI,CAAE2I,EAAGmB,EAAenB,EAAGD,EAAGoB,EAAeiB,gBAEnE5D,EAAUxG,IAAIsB,EAAQjC,GAAI,CAAE2I,EAAGmB,EAAeiB,cAAerC,EAAGoB,EAAepB,IAEjFc,EAAc,OAARlE,GAAgBmH,EAAoBjD,EAAMvC,EAAcuC,EAAMvC,EAAcD,EAC9EwC,EAAMlC,IACRA,EAASkC,EACX,GACA,GACD,eACC0D,IAAqCtR,EAAAA,EAAAA,KAAO,CAACuR,EAASC,EAASC,EAAIC,EAAIC,KACzE,MACMC,GAD4B,OAARlI,GAAwB,OAARA,EAAe+H,EAAG1E,EAAI2E,EAAG3E,EAAI0E,EAAG3E,EAAI4E,EAAG5E,GACpC0E,EAAQ1M,OAASyM,EAAQzM,OAChE+M,GAAuC7R,EAAAA,EAAAA,KAAQ+M,GAAMA,EAAEjI,SAAW8M,GAAkB,wBACpFE,GAAmC9R,EAAAA,EAAAA,KAAQ+M,GAAMA,EAAE3K,IAAMmP,EAAQnP,KAAO2K,EAAE3K,IAAMoP,EAAQpP,KAAK,oBACnG,MAAO,IAAIuP,EAAWnJ,UAAUuJ,MAAMC,GAC7BF,EAAiBE,IAAYH,EAAqBG,IACzD,GACD,sBACCC,IAA2BjS,EAAAA,EAAAA,KAAO,SAACkS,EAAIC,GAAkB,IAAdC,EAAKC,UAAA7P,OAAA,QAAA8P,IAAAD,UAAA,GAAAA,UAAA,GAAG,EACrD,MAAME,EAAYL,EAAK1E,KAAKgF,IAAIN,EAAKC,GAAM,EAC3C,GAAIC,EAAQ,EACV,OAAOG,EAGT,GADW9G,EAAMgH,OAAOC,GAASlF,KAAKgF,IAAIE,EAAOH,IAAc,KAG7D,OADA9G,EAAMtI,KAAKoP,GACJA,EAET,MAAMI,EAAOnF,KAAKgF,IAAIN,EAAKC,GAC3B,OAAOF,GAASC,EAAIC,EAAKQ,EAAO,EAAGP,EAAQ,EAC7C,GAAG,YACCQ,IAA4B5S,EAAAA,EAAAA,KAAO,CAAC+Q,EAAKQ,EAASC,EAASG,KAC7D,MAAMF,EAAKlG,EAAU5F,IAAI4L,EAAQnN,IAC3BsN,EAAKnG,EAAU5F,IAAI6L,EAAQpN,IACjC,QAAW,IAAPqN,QAAwB,IAAPC,EACnB,MAAM,IAAIxM,MAAM,0CAA0CqM,EAAQnN,UAAUoN,EAAQpN,MAEtF,MAAMyO,EAAsBvB,GAAmBC,EAASC,EAASC,EAAIC,EAAIC,GACzE,IAQImB,EARAC,EAAM,GACNC,EAAO,GACPC,EAAS,EACTC,EAAS,EACTC,EAAgB7H,EAAU3F,IAAI6L,EAAQ1M,SAAS2C,MAKnD,GAJI+J,EAAQnN,OAAS1D,EAAWI,OAASwQ,EAAQnN,KAAOoN,EAAQ3M,QAAQ,KACtEsO,EAAgB7H,EAAU3F,IAAI4L,EAAQzM,SAAS2C,OAG7CoL,EAAqB,CACvBE,EAAM,oBACNC,EAAO,oBACPC,EAAS,GACTC,EAAS,GACT,MAAME,EAAQ3B,EAAG3E,EAAI4E,EAAG5E,EAAImF,GAASR,EAAG3E,EAAG4E,EAAG5E,GAAKmF,GAASP,EAAG5E,EAAG2E,EAAG3E,GAC/DuG,EAAQ5B,EAAG1E,EAAI2E,EAAG3E,EAAIkF,GAASR,EAAG1E,EAAG2E,EAAG3E,GAAKkF,GAASP,EAAG3E,EAAG0E,EAAG1E,GACzD,OAARrD,EACE+H,EAAG1E,EAAI2E,EAAG3E,EACZ+F,EAAU,KAAKrB,EAAG1E,KAAK0E,EAAG3E,OAAOuG,EAAQJ,KAAUxB,EAAG3E,KAAKkG,KAAQK,KAAS5B,EAAG3E,EAAIoG,OAAYG,KAAS3B,EAAG5E,EAAImG,KAAUF,KAAOM,EAAQH,KAAUxB,EAAG5E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,KAEvKqG,EAAgB7H,EAAU3F,IAAI4L,EAAQzM,SAAS2C,MAC/CqL,EAAU,KAAKrB,EAAG1E,KAAK0E,EAAG3E,OAAOuG,EAAQJ,KAAUxB,EAAG3E,KAAKiG,KAAOM,KAAS5B,EAAG3E,EAAIoG,OAAYG,KAAS3B,EAAG5E,EAAImG,KAAUD,KAAQK,EAAQH,KAAUxB,EAAG5E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,KAExJ,OAARpD,EACL+H,EAAG1E,EAAI2E,EAAG3E,EACZ+F,EAAU,KAAKrB,EAAG1E,KAAK0E,EAAG3E,OAAOuG,EAAQJ,KAAUxB,EAAG3E,KAAKiG,KAAOM,KAAS5B,EAAG3E,EAAIoG,OAAYG,KAAS3B,EAAG5E,EAAImG,KAAUD,KAAQK,EAAQH,KAAUxB,EAAG5E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,KAEvKqG,EAAgB7H,EAAU3F,IAAI4L,EAAQzM,SAAS2C,MAC/CqL,EAAU,KAAKrB,EAAG1E,KAAK0E,EAAG3E,OAAOuG,EAAQJ,KAAUxB,EAAG3E,KAAKkG,KAAQK,KAAS5B,EAAG3E,EAAIoG,OAAYG,KAAS3B,EAAG5E,EAAImG,KAAUF,KAAOM,EAAQH,KAAUxB,EAAG5E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,KAGrK2E,EAAG3E,EAAI4E,EAAG5E,EACZgG,EAAU,KAAKrB,EAAG1E,KAAK0E,EAAG3E,OAAO2E,EAAG1E,KAAKqG,EAAQH,KAAUF,KAAOtB,EAAG1E,EAAImG,KAAUE,OAAW1B,EAAG3E,EAAIkG,KAAUG,KAASJ,KAAQtB,EAAG3E,KAAKqG,EAAQF,OAAYxB,EAAG3E,KAAK2E,EAAG5E,KAEvKqG,EAAgB7H,EAAU3F,IAAI4L,EAAQzM,SAAS2C,MAC/CqL,EAAU,KAAKrB,EAAG1E,KAAK0E,EAAG3E,OAAO2E,EAAG1E,KAAKqG,EAAQH,KAAUD,KAAQvB,EAAG1E,EAAImG,KAAUE,OAAW1B,EAAG3E,EAAIkG,KAAUG,KAASL,KAAOrB,EAAG3E,KAAKqG,EAAQF,OAAYxB,EAAG3E,KAAK2E,EAAG5E,IAG7K,MACEiG,EAAM,oBACNC,EAAO,oBACPC,EAAS,GACTC,EAAS,GACG,OAARxJ,GACE+H,EAAG1E,EAAI2E,EAAG3E,IAEV+F,EADEtB,EAAQnN,OAAS1D,EAAWI,OAASwQ,EAAQnN,KAAOoN,EAAQ3M,QAAQ,GAC5D,KAAK4M,EAAG1E,KAAK0E,EAAG3E,OAAO2E,EAAG1E,KAAK2E,EAAG5E,EAAImG,KAAUF,KAAOtB,EAAG1E,EAAImG,KAAUxB,EAAG5E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,IAE7F,KAAK2E,EAAG1E,KAAK0E,EAAG3E,OAAO4E,EAAG3E,EAAIkG,KAAUxB,EAAG3E,KAAKkG,KAAQtB,EAAG3E,KAAK0E,EAAG3E,EAAIoG,OAAYxB,EAAG3E,KAAK2E,EAAG5E,KAGxG2E,EAAG1E,EAAI2E,EAAG3E,IACZgG,EAAM,oBACNC,EAAO,oBACPC,EAAS,GACTC,EAAS,GAEPJ,EADEtB,EAAQnN,OAAS1D,EAAWI,OAASwQ,EAAQnN,KAAOoN,EAAQ3M,QAAQ,GAC5D,KAAK4M,EAAG1E,KAAK0E,EAAG3E,OAAO2E,EAAG1E,KAAK2E,EAAG5E,EAAImG,KAAUD,KAAQvB,EAAG1E,EAAImG,KAAUxB,EAAG5E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,IAE9F,KAAK2E,EAAG1E,KAAK0E,EAAG3E,OAAO4E,EAAG3E,EAAIkG,KAAUxB,EAAG3E,KAAKiG,KAAOrB,EAAG3E,KAAK0E,EAAG3E,EAAIoG,OAAYxB,EAAG3E,KAAK2E,EAAG5E,KAGvG2E,EAAG1E,IAAM2E,EAAG3E,IACd+F,EAAU,KAAKrB,EAAG1E,KAAK0E,EAAG3E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,MAE7B,OAARpD,GACL+H,EAAG1E,EAAI2E,EAAG3E,IAEV+F,EADEtB,EAAQnN,OAAS1D,EAAWI,OAASwQ,EAAQnN,KAAOoN,EAAQ3M,QAAQ,GAC5D,KAAK4M,EAAG1E,KAAK0E,EAAG3E,OAAO2E,EAAG1E,KAAK2E,EAAG5E,EAAImG,KAAUD,KAAQvB,EAAG1E,EAAImG,KAAUxB,EAAG5E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,IAE9F,KAAK2E,EAAG1E,KAAK0E,EAAG3E,OAAO4E,EAAG3E,EAAIkG,KAAUxB,EAAG3E,KAAKiG,KAAOrB,EAAG3E,KAAK0E,EAAG3E,EAAIoG,OAAYxB,EAAG3E,KAAK2E,EAAG5E,KAGvG2E,EAAG1E,EAAI2E,EAAG3E,IACZgG,EAAM,oBACNC,EAAO,oBACPC,EAAS,GACTC,EAAS,GAEPJ,EADEtB,EAAQnN,OAAS1D,EAAWI,OAASwQ,EAAQnN,KAAOoN,EAAQ3M,QAAQ,GAC5D,KAAK4M,EAAG1E,KAAK0E,EAAG3E,OAAO2E,EAAG1E,KAAK2E,EAAG5E,EAAImG,KAAUF,KAAOtB,EAAG1E,EAAImG,KAAUxB,EAAG5E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,IAE7F,KAAK2E,EAAG1E,KAAK0E,EAAG3E,OAAO4E,EAAG3E,EAAIkG,KAAUxB,EAAG3E,KAAKiG,KAAOrB,EAAG3E,KAAK0E,EAAG3E,EAAIoG,OAAYxB,EAAG3E,KAAK2E,EAAG5E,KAGvG2E,EAAG1E,IAAM2E,EAAG3E,IACd+F,EAAU,KAAKrB,EAAG1E,KAAK0E,EAAG3E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,OAG1C2E,EAAG3E,EAAI4E,EAAG5E,IAEVgG,EADEtB,EAAQnN,OAAS1D,EAAWI,OAASwQ,EAAQnN,KAAOoN,EAAQ3M,QAAQ,GAC5D,KAAK4M,EAAG1E,KAAK0E,EAAG3E,OAAO4E,EAAG3E,EAAIkG,KAAUxB,EAAG3E,KAAKkG,KAAQtB,EAAG3E,KAAK0E,EAAG3E,EAAIoG,OAAYxB,EAAG3E,KAAK2E,EAAG5E,IAE9F,KAAK2E,EAAG1E,KAAK0E,EAAG3E,OAAO2E,EAAG1E,KAAK2E,EAAG5E,EAAImG,KAAUF,KAAOtB,EAAG1E,EAAImG,KAAUxB,EAAG5E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,KAGvG2E,EAAG3E,EAAI4E,EAAG5E,IAEVgG,EADEtB,EAAQnN,OAAS1D,EAAWI,OAASwQ,EAAQnN,KAAOoN,EAAQ3M,QAAQ,GAC5D,KAAK4M,EAAG1E,KAAK0E,EAAG3E,OAAO4E,EAAG3E,EAAIkG,KAAUxB,EAAG3E,KAAKiG,KAAOrB,EAAG3E,KAAK0E,EAAG3E,EAAIoG,OAAYxB,EAAG3E,KAAK2E,EAAG5E,IAE7F,KAAK2E,EAAG1E,KAAK0E,EAAG3E,OAAO2E,EAAG1E,KAAK2E,EAAG5E,EAAImG,KAAUD,KAAQvB,EAAG1E,EAAImG,KAAUxB,EAAG5E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,KAGxG2E,EAAG3E,IAAM4E,EAAG5E,IACdgG,EAAU,KAAKrB,EAAG1E,KAAK0E,EAAG3E,OAAO4E,EAAG3E,KAAK2E,EAAG5E,MAIlD,QAAgB,IAAZgG,EACF,MAAM,IAAI5N,MAAM,6BAElB6L,EAAIzC,OAAO,QAAQC,KAAK,IAAKuE,GAASvE,KAAK,QAAS,cAAgB4E,EA1f9C,EA0fgF,GACrG,aACCG,IAA6BtT,EAAAA,EAAAA,KAAO,CAAC+Q,EAAKpP,KAC5C,MAAM4R,EAAUxC,EAAIzC,OAAO,KAAKC,KAAK,QAAS,iBAC9C,IAAI5M,EAAQsP,QAAQjJ,SAAS9E,IAC3B,MAAMmD,EAAU1E,EAAQgE,IAAIzC,GACxBmD,EAAQxB,SAAWwB,EAAQxB,QAAQrC,OAAS,GAC9C6D,EAAQxB,QAAQmD,SAASpB,IACvBgM,GAAUW,EAAS5R,EAAQgE,IAAIiB,GAASP,EAAS1E,EAAQ,GAE7D,GACA,GACD,cACC6R,IAA+BxT,EAAAA,EAAAA,KAAO,CAAC+Q,EAAK9O,KAC9C,MAAMwR,EAAI1C,EAAIzC,OAAO,KACrBrM,EAAS+F,SAAQ,CAACX,EAASI,KACzB,MAAMiM,EAAsBjM,EA1gBR,EA2gBdmG,EAAMtC,EAAU3F,IAAI0B,EAAQtF,OAAO6L,IACzC,QAAY,IAARA,EACF,MAAM,IAAI1I,MAAM,iCAAiCmC,EAAQtF,QAE3D,MAAMgG,EAAO0L,EAAEnF,OAAO,QACtBvG,EAAKwG,KAAK,KAAM,GAChBxG,EAAKwG,KAAK,KAAMX,GAChB7F,EAAKwG,KAAK,KAAM7C,GAChB3D,EAAKwG,KAAK,KAAMX,GAChB7F,EAAKwG,KAAK,QAAS,gBAAkBmF,GACzB,OAARhK,GACF3B,EAAKwG,KAAK,KAnhBC,IAohBXxG,EAAKwG,KAAK,KAAMX,GAChB7F,EAAKwG,KAAK,KAAM7C,GAChB3D,EAAKwG,KAAK,KAAMX,IACC,OAARlE,IACT3B,EAAKwG,KAAK,KAAM7C,GAChB3D,EAAKwG,KAAK,KAAMX,GAChB7F,EAAKwG,KAAK,KA1hBC,IA2hBXxG,EAAKwG,KAAK,KAAMX,IAElBnC,EAAMtI,KAAKyK,GACX,MAAM7L,EAAOsF,EAAQtF,KACf4R,EAAe/H,EAAS7J,GACxB6R,EAAMH,EAAE1E,OAAO,QAEf7G,EADcuL,EAAE1E,OAAO,KAAKR,KAAK,QAAS,eACtBQ,OAAO,KAAKR,KAAK,QAAS,qBAAuBmF,GAC3ExL,EAAM+G,OAAO1C,YAAYoH,GACzB,MAAM3E,EAAO2E,EAAazE,UAC1B0E,EAAIrF,KAAK,QAAS,uBAAyBmF,GAAqBnF,KAAK,KAAM,GAAGA,KAAK,KAAM,GAAGA,KAAK,KAAMS,EAAKI,MAAQ,IAAqD,IAAhDjE,GAA0BmE,kBAA6B,GAAK,IAAIf,KAAK,KAAMS,EAAKK,OAAS,EAAI,GAAGd,KAAK,QAASS,EAAKI,MAAQ,IAAIb,KAAK,SAAUS,EAAKK,OAAS,GACrRnH,EAAMqG,KACJ,YACA,eAAiBS,EAAKI,MAAQ,KAAsD,IAAhDjE,GAA0BmE,kBAA6B,GAAK,IAAM,MAAQ1B,EAAMoB,EAAKK,OAAS,EAAI,GAAK,KAEjI,OAAR3F,GACFkK,EAAIrF,KAAK,IAAKX,EAAMoB,EAAKI,MAAQ,EAAI,IAAIb,KAAK,IAAK,GACnDrG,EAAMqG,KAAK,YAAa,cAAgBX,EAAMoB,EAAKI,MAAQ,EAAI,GAAK,SACnD,OAAR1F,GACTkK,EAAIrF,KAAK,IAAKX,EAAMoB,EAAKI,MAAQ,EAAI,IAAIb,KAAK,IAAK7C,GACnDxD,EAAMqG,KAAK,YAAa,cAAgBX,EAAMoB,EAAKI,MAAQ,EAAI,GAAK,KAAO1D,EAAS,MAEpFkI,EAAIrF,KAAK,YAAa,mBAAqBX,EAAMoB,EAAKK,OAAS,GAAK,IACtE,GACA,GACD,gBACCwE,IAAoC7T,EAAAA,EAAAA,KAAO,SAAS+B,EAAM6L,EAAKnG,EAAOuH,EAAMM,GAG9E,OAFAhE,EAAUvG,IAAIhD,EAAM,CAAE6L,MAAKnG,UAC3BmG,GAAO,IAAM0B,EAAoB,GAAK,IAAc,OAAR5F,GAAwB,OAARA,EAAesF,EAAKI,MAAQ,EAAI,EAE9F,GAAG,qBAkbH,IAwDI0E,GAAU,CACZhJ,SACA1K,KACA2T,SAhc6B,CAC7BC,MA7CyBhU,EAAAA,EAAAA,KAAO,SAAS6L,EAAKzH,EAAI6P,EAAKC,GAGvD,GAFAvI,IACAnI,EAAAA,GAAIC,MAAM,uBAAwBoI,EAAM,KAAM,MAAOzH,EAAI6P,IACpD9I,EACH,MAAM,IAAIjG,MAAM,6BAElB,MAAMoK,EAAoBnE,EAAyBmE,oBAAqB,EAClE7F,EAAMyK,EAAQ9T,GACpBoL,EAAiB/B,EAAIT,aACrB,MAAM/G,EAAWwH,EAAIlB,wBACrBmB,EAAMD,EAAIN,eACV,MAAMgL,GAAWC,EAAAA,EAAAA,KAAO,QAAQhQ,OAChC,IAAIwJ,EAAM,EACV3L,EAAS+F,SAAQ,CAACX,EAASI,KACzB,MAAMkM,EAAe/H,EAASvE,EAAQtF,MAChC0R,EAAIU,EAAS7F,OAAO,KACpB+F,EAAcZ,EAAE1E,OAAO,KAAKR,KAAK,QAAS,eAC1CrG,EAAQmM,EAAYtF,OAAO,KAAKR,KAAK,QAAS,sBACpDrG,EAAM+G,QAAQ1C,YAAYoH,GAC1B,MAAM3E,EAAO2E,EAAazE,UAC1BtB,EAAMiG,GAAkBxM,EAAQtF,KAAM6L,EAAKnG,EAAOuH,EAAMM,GACxDpH,EAAMoM,SACND,EAAYC,SACZb,EAAEa,QAAQ,IAEZxD,GAAYqD,EAAU3I,GAAgB,GAClCL,EAAyBoJ,cAC3Bf,GAAaW,EAAUlS,GAEzBqR,GAAWa,EAAU3I,GACrBsF,GAAYqD,EAAU3I,GAAgB,GACtCgJ,EAAAA,GAAcC,YACZN,EACA,eACAhJ,EAAyBuJ,gBAAkB,EAC3CjL,EAAIF,oBAENoL,EAAAA,EAAAA,SACE,EACAR,EACAhJ,EAAyByJ,eACzBzJ,EAAyB0J,YAE7B,GAAG,SAkcDC,QA5D8B9U,EAAAA,EAAAA,KAAQqC,GAAY,uNAShD,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAGqC,KAC1B+D,GAAM,0BACcA,aAAapG,EAAQ,iBAAmBoG,yBAC9CA,eAAepG,EAAQ,MAAQoG,aAAapG,EAAQ,MAAQoG,mCAClDA,eAAepG,EAAQ,SAAWoG,aAAapG,EAAQ,SAAWoG,wBAC7EA,cAAcpG,EAAQ,MAAQoG,wBAC9BA,eAAepG,EAAQ,MAAQoG,oBAE7CtC,KAAK,2DAIO9D,EAAQ0S,2EAGS1S,EAAQ2S,8BAA8B3S,EAAQ4S,wDAC1C5S,EAAQ2S,8BAA8B3S,EAAQ6S,qEACrD7S,EAAQ8S,2BAA2B9S,EAAQ+S,6CAC5C/S,EAAQgT,+BAA+BhT,EAAQiT,0CACpDjT,EAAQkT,kDAGhBlT,EAAQmT,4BACVnT,EAAQmT,wDAGNnT,EAAQmT,4BACVnT,EAAQmT,wHAMNnT,EAAQmT,4BACVnT,EAAQmT,sKAORnT,EAAQkT,qBAEjB,a", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-XZIHB7SX.mjs", "../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-4BMEZGHF.mjs", "../../node_modules/mermaid/dist/chunks/mermaid.core/gitGraphDiagram-7IBYFJ6S.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/utils/imperativeState.ts\nvar ImperativeState = class {\n  /**\n   * @param init - Function that creates the default state.\n   */\n  constructor(init) {\n    this.init = init;\n    this.records = this.init();\n  }\n  static {\n    __name(this, \"ImperativeState\");\n  }\n  reset() {\n    this.records = this.init();\n  }\n};\n\nexport {\n  ImperativeState\n};\n", "import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/common/populateCommonDb.ts\nfunction populateCommonDb(ast, db) {\n  if (ast.accDescr) {\n    db.setAccDescription?.(ast.accDescr);\n  }\n  if (ast.accTitle) {\n    db.setAccTitle?.(ast.accTitle);\n  }\n  if (ast.title) {\n    db.setDiagramTitle?.(ast.title);\n  }\n}\n__name(populateCommonDb, \"populateCommonDb\");\n\nexport {\n  populateCommonDb\n};\n", "import {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  ImperativeState\n} from \"./chunk-XZIHB7SX.mjs\";\nimport {\n  cleanAndMerge,\n  random,\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  clear,\n  common_default,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getConfig2,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n  setupGraphViewbox2 as setupGraphViewbox\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/git/gitGraphParser.ts\nimport { parse } from \"@mermaid-js/parser\";\n\n// src/diagrams/git/gitGraphTypes.ts\nvar commitType = {\n  NORMAL: 0,\n  REVERSE: 1,\n  HIGHLIGHT: 2,\n  MERGE: 3,\n  CHERRY_PICK: 4\n};\n\n// src/diagrams/git/gitGraphAst.ts\nvar DEFAULT_GITGRAPH_CONFIG = defaultConfig_default.gitGraph;\nvar getConfig3 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_GITGRAPH_CONFIG,\n    ...getConfig().gitGraph\n  });\n  return config;\n}, \"getConfig\");\nvar state = new ImperativeState(() => {\n  const config = getConfig3();\n  const mainBranchName = config.mainBranchName;\n  const mainBranchOrder = config.mainBranchOrder;\n  return {\n    mainBranchName,\n    commits: /* @__PURE__ */ new Map(),\n    head: null,\n    branchConfig: /* @__PURE__ */ new Map([[mainBranchName, { name: mainBranchName, order: mainBranchOrder }]]),\n    branches: /* @__PURE__ */ new Map([[mainBranchName, null]]),\n    currBranch: mainBranchName,\n    direction: \"LR\",\n    seq: 0,\n    options: {}\n  };\n});\nfunction getID() {\n  return random({ length: 7 });\n}\n__name(getID, \"getID\");\nfunction uniqBy(list, fn) {\n  const recordMap = /* @__PURE__ */ Object.create(null);\n  return list.reduce((out, item) => {\n    const key = fn(item);\n    if (!recordMap[key]) {\n      recordMap[key] = true;\n      out.push(item);\n    }\n    return out;\n  }, []);\n}\n__name(uniqBy, \"uniqBy\");\nvar setDirection = /* @__PURE__ */ __name(function(dir2) {\n  state.records.direction = dir2;\n}, \"setDirection\");\nvar setOptions = /* @__PURE__ */ __name(function(rawOptString) {\n  log.debug(\"options str\", rawOptString);\n  rawOptString = rawOptString?.trim();\n  rawOptString = rawOptString || \"{}\";\n  try {\n    state.records.options = JSON.parse(rawOptString);\n  } catch (e) {\n    log.error(\"error while parsing gitGraph options\", e.message);\n  }\n}, \"setOptions\");\nvar getOptions = /* @__PURE__ */ __name(function() {\n  return state.records.options;\n}, \"getOptions\");\nvar commit = /* @__PURE__ */ __name(function(commitDB) {\n  let msg = commitDB.msg;\n  let id = commitDB.id;\n  const type = commitDB.type;\n  let tags = commitDB.tags;\n  log.info(\"commit\", msg, id, type, tags);\n  log.debug(\"Entering commit:\", msg, id, type, tags);\n  const config = getConfig3();\n  id = common_default.sanitizeText(id, config);\n  msg = common_default.sanitizeText(msg, config);\n  tags = tags?.map((tag) => common_default.sanitizeText(tag, config));\n  const newCommit = {\n    id: id ? id : state.records.seq + \"-\" + getID(),\n    message: msg,\n    seq: state.records.seq++,\n    type: type ?? commitType.NORMAL,\n    tags: tags ?? [],\n    parents: state.records.head == null ? [] : [state.records.head.id],\n    branch: state.records.currBranch\n  };\n  state.records.head = newCommit;\n  log.info(\"main branch\", config.mainBranchName);\n  state.records.commits.set(newCommit.id, newCommit);\n  state.records.branches.set(state.records.currBranch, newCommit.id);\n  log.debug(\"in pushCommit \" + newCommit.id);\n}, \"commit\");\nvar branch = /* @__PURE__ */ __name(function(branchDB) {\n  let name = branchDB.name;\n  const order = branchDB.order;\n  name = common_default.sanitizeText(name, getConfig3());\n  if (state.records.branches.has(name)) {\n    throw new Error(\n      `Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using \"checkout ${name}\")`\n    );\n  }\n  state.records.branches.set(name, state.records.head != null ? state.records.head.id : null);\n  state.records.branchConfig.set(name, { name, order });\n  checkout(name);\n  log.debug(\"in createBranch\");\n}, \"branch\");\nvar merge = /* @__PURE__ */ __name((mergeDB) => {\n  let otherBranch = mergeDB.branch;\n  let customId = mergeDB.id;\n  const overrideType = mergeDB.type;\n  const customTags = mergeDB.tags;\n  const config = getConfig3();\n  otherBranch = common_default.sanitizeText(otherBranch, config);\n  if (customId) {\n    customId = common_default.sanitizeText(customId, config);\n  }\n  const currentBranchCheck = state.records.branches.get(state.records.currBranch);\n  const otherBranchCheck = state.records.branches.get(otherBranch);\n  const currentCommit = currentBranchCheck ? state.records.commits.get(currentBranchCheck) : void 0;\n  const otherCommit = otherBranchCheck ? state.records.commits.get(otherBranchCheck) : void 0;\n  if (currentCommit && otherCommit && currentCommit.branch === otherBranch) {\n    throw new Error(`Cannot merge branch '${otherBranch}' into itself.`);\n  }\n  if (state.records.currBranch === otherBranch) {\n    const error = new Error('Incorrect usage of \"merge\". Cannot merge a branch to itself');\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"branch abc\"]\n    };\n    throw error;\n  }\n  if (currentCommit === void 0 || !currentCommit) {\n    const error = new Error(\n      `Incorrect usage of \"merge\". Current branch (${state.records.currBranch})has no commits`\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"commit\"]\n    };\n    throw error;\n  }\n  if (!state.records.branches.has(otherBranch)) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + \") does not exist\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [`branch ${otherBranch}`]\n    };\n    throw error;\n  }\n  if (otherCommit === void 0 || !otherCommit) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + \") has no commits\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: ['\"commit\"']\n    };\n    throw error;\n  }\n  if (currentCommit === otherCommit) {\n    const error = new Error('Incorrect usage of \"merge\". Both branches have same head');\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"branch abc\"]\n    };\n    throw error;\n  }\n  if (customId && state.records.commits.has(customId)) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Commit with id:' + customId + \" already exists, use different custom Id\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch} ${customId} ${overrideType} ${customTags?.join(\" \")}`,\n      token: `merge ${otherBranch} ${customId} ${overrideType} ${customTags?.join(\" \")}`,\n      expected: [\n        `merge ${otherBranch} ${customId}_UNIQUE ${overrideType} ${customTags?.join(\" \")}`\n      ]\n    };\n    throw error;\n  }\n  const verifiedBranch = otherBranchCheck ? otherBranchCheck : \"\";\n  const commit2 = {\n    id: customId || `${state.records.seq}-${getID()}`,\n    message: `merged branch ${otherBranch} into ${state.records.currBranch}`,\n    seq: state.records.seq++,\n    parents: state.records.head == null ? [] : [state.records.head.id, verifiedBranch],\n    branch: state.records.currBranch,\n    type: commitType.MERGE,\n    customType: overrideType,\n    customId: customId ? true : false,\n    tags: customTags ?? []\n  };\n  state.records.head = commit2;\n  state.records.commits.set(commit2.id, commit2);\n  state.records.branches.set(state.records.currBranch, commit2.id);\n  log.debug(state.records.branches);\n  log.debug(\"in mergeBranch\");\n}, \"merge\");\nvar cherryPick = /* @__PURE__ */ __name(function(cherryPickDB) {\n  let sourceId = cherryPickDB.id;\n  let targetId = cherryPickDB.targetId;\n  let tags = cherryPickDB.tags;\n  let parentCommitId = cherryPickDB.parent;\n  log.debug(\"Entering cherryPick:\", sourceId, targetId, tags);\n  const config = getConfig3();\n  sourceId = common_default.sanitizeText(sourceId, config);\n  targetId = common_default.sanitizeText(targetId, config);\n  tags = tags?.map((tag) => common_default.sanitizeText(tag, config));\n  parentCommitId = common_default.sanitizeText(parentCommitId, config);\n  if (!sourceId || !state.records.commits.has(sourceId)) {\n    const error = new Error(\n      'Incorrect usage of \"cherryPick\". Source commit id should exist and provided'\n    );\n    error.hash = {\n      text: `cherryPick ${sourceId} ${targetId}`,\n      token: `cherryPick ${sourceId} ${targetId}`,\n      expected: [\"cherry-pick abc\"]\n    };\n    throw error;\n  }\n  const sourceCommit = state.records.commits.get(sourceId);\n  if (sourceCommit === void 0 || !sourceCommit) {\n    throw new Error('Incorrect usage of \"cherryPick\". Source commit id should exist and provided');\n  }\n  if (parentCommitId && !(Array.isArray(sourceCommit.parents) && sourceCommit.parents.includes(parentCommitId))) {\n    const error = new Error(\n      \"Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.\"\n    );\n    throw error;\n  }\n  const sourceCommitBranch = sourceCommit.branch;\n  if (sourceCommit.type === commitType.MERGE && !parentCommitId) {\n    const error = new Error(\n      \"Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.\"\n    );\n    throw error;\n  }\n  if (!targetId || !state.records.commits.has(targetId)) {\n    if (sourceCommitBranch === state.records.currBranch) {\n      const error = new Error(\n        'Incorrect usage of \"cherryPick\". Source commit is already on current branch'\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const currentCommitId = state.records.branches.get(state.records.currBranch);\n    if (currentCommitId === void 0 || !currentCommitId) {\n      const error = new Error(\n        `Incorrect usage of \"cherry-pick\". Current branch (${state.records.currBranch})has no commits`\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const currentCommit = state.records.commits.get(currentCommitId);\n    if (currentCommit === void 0 || !currentCommit) {\n      const error = new Error(\n        `Incorrect usage of \"cherry-pick\". Current branch (${state.records.currBranch})has no commits`\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const commit2 = {\n      id: state.records.seq + \"-\" + getID(),\n      message: `cherry-picked ${sourceCommit?.message} into ${state.records.currBranch}`,\n      seq: state.records.seq++,\n      parents: state.records.head == null ? [] : [state.records.head.id, sourceCommit.id],\n      branch: state.records.currBranch,\n      type: commitType.CHERRY_PICK,\n      tags: tags ? tags.filter(Boolean) : [\n        `cherry-pick:${sourceCommit.id}${sourceCommit.type === commitType.MERGE ? `|parent:${parentCommitId}` : \"\"}`\n      ]\n    };\n    state.records.head = commit2;\n    state.records.commits.set(commit2.id, commit2);\n    state.records.branches.set(state.records.currBranch, commit2.id);\n    log.debug(state.records.branches);\n    log.debug(\"in cherryPick\");\n  }\n}, \"cherryPick\");\nvar checkout = /* @__PURE__ */ __name(function(branch2) {\n  branch2 = common_default.sanitizeText(branch2, getConfig3());\n  if (!state.records.branches.has(branch2)) {\n    const error = new Error(\n      `Trying to checkout branch which is not yet created. (Help try using \"branch ${branch2}\")`\n    );\n    error.hash = {\n      text: `checkout ${branch2}`,\n      token: `checkout ${branch2}`,\n      expected: [`branch ${branch2}`]\n    };\n    throw error;\n  } else {\n    state.records.currBranch = branch2;\n    const id = state.records.branches.get(state.records.currBranch);\n    if (id === void 0 || !id) {\n      state.records.head = null;\n    } else {\n      state.records.head = state.records.commits.get(id) ?? null;\n    }\n  }\n}, \"checkout\");\nfunction upsert(arr, key, newVal) {\n  const index = arr.indexOf(key);\n  if (index === -1) {\n    arr.push(newVal);\n  } else {\n    arr.splice(index, 1, newVal);\n  }\n}\n__name(upsert, \"upsert\");\nfunction prettyPrintCommitHistory(commitArr) {\n  const commit2 = commitArr.reduce((out, commit3) => {\n    if (out.seq > commit3.seq) {\n      return out;\n    }\n    return commit3;\n  }, commitArr[0]);\n  let line = \"\";\n  commitArr.forEach(function(c) {\n    if (c === commit2) {\n      line += \"\t*\";\n    } else {\n      line += \"\t|\";\n    }\n  });\n  const label = [line, commit2.id, commit2.seq];\n  for (const branch2 in state.records.branches) {\n    if (state.records.branches.get(branch2) === commit2.id) {\n      label.push(branch2);\n    }\n  }\n  log.debug(label.join(\" \"));\n  if (commit2.parents && commit2.parents.length == 2 && commit2.parents[0] && commit2.parents[1]) {\n    const newCommit = state.records.commits.get(commit2.parents[0]);\n    upsert(commitArr, commit2, newCommit);\n    if (commit2.parents[1]) {\n      commitArr.push(state.records.commits.get(commit2.parents[1]));\n    }\n  } else if (commit2.parents.length == 0) {\n    return;\n  } else {\n    if (commit2.parents[0]) {\n      const newCommit = state.records.commits.get(commit2.parents[0]);\n      upsert(commitArr, commit2, newCommit);\n    }\n  }\n  commitArr = uniqBy(commitArr, (c) => c.id);\n  prettyPrintCommitHistory(commitArr);\n}\n__name(prettyPrintCommitHistory, \"prettyPrintCommitHistory\");\nvar prettyPrint = /* @__PURE__ */ __name(function() {\n  log.debug(state.records.commits);\n  const node = getCommitsArray()[0];\n  prettyPrintCommitHistory([node]);\n}, \"prettyPrint\");\nvar clear2 = /* @__PURE__ */ __name(function() {\n  state.reset();\n  clear();\n}, \"clear\");\nvar getBranchesAsObjArray = /* @__PURE__ */ __name(function() {\n  const branchesArray = [...state.records.branchConfig.values()].map((branchConfig, i) => {\n    if (branchConfig.order !== null && branchConfig.order !== void 0) {\n      return branchConfig;\n    }\n    return {\n      ...branchConfig,\n      order: parseFloat(`0.${i}`)\n    };\n  }).sort((a, b) => (a.order ?? 0) - (b.order ?? 0)).map(({ name }) => ({ name }));\n  return branchesArray;\n}, \"getBranchesAsObjArray\");\nvar getBranches = /* @__PURE__ */ __name(function() {\n  return state.records.branches;\n}, \"getBranches\");\nvar getCommits = /* @__PURE__ */ __name(function() {\n  return state.records.commits;\n}, \"getCommits\");\nvar getCommitsArray = /* @__PURE__ */ __name(function() {\n  const commitArr = [...state.records.commits.values()];\n  commitArr.forEach(function(o) {\n    log.debug(o.id);\n  });\n  commitArr.sort((a, b) => a.seq - b.seq);\n  return commitArr;\n}, \"getCommitsArray\");\nvar getCurrentBranch = /* @__PURE__ */ __name(function() {\n  return state.records.currBranch;\n}, \"getCurrentBranch\");\nvar getDirection = /* @__PURE__ */ __name(function() {\n  return state.records.direction;\n}, \"getDirection\");\nvar getHead = /* @__PURE__ */ __name(function() {\n  return state.records.head;\n}, \"getHead\");\nvar db = {\n  commitType,\n  getConfig: getConfig3,\n  setDirection,\n  setOptions,\n  getOptions,\n  commit,\n  branch,\n  merge,\n  cherryPick,\n  checkout,\n  //reset,\n  prettyPrint,\n  clear: clear2,\n  getBranchesAsObjArray,\n  getBranches,\n  getCommits,\n  getCommitsArray,\n  getCurrentBranch,\n  getDirection,\n  getHead,\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  setDiagramTitle,\n  getDiagramTitle\n};\n\n// src/diagrams/git/gitGraphParser.ts\nvar populate = /* @__PURE__ */ __name((ast, db2) => {\n  populateCommonDb(ast, db2);\n  if (ast.dir) {\n    db2.setDirection(ast.dir);\n  }\n  for (const statement of ast.statements) {\n    parseStatement(statement, db2);\n  }\n}, \"populate\");\nvar parseStatement = /* @__PURE__ */ __name((statement, db2) => {\n  const parsers = {\n    Commit: /* @__PURE__ */ __name((stmt) => db2.commit(parseCommit(stmt)), \"Commit\"),\n    Branch: /* @__PURE__ */ __name((stmt) => db2.branch(parseBranch(stmt)), \"Branch\"),\n    Merge: /* @__PURE__ */ __name((stmt) => db2.merge(parseMerge(stmt)), \"Merge\"),\n    Checkout: /* @__PURE__ */ __name((stmt) => db2.checkout(parseCheckout(stmt)), \"Checkout\"),\n    CherryPicking: /* @__PURE__ */ __name((stmt) => db2.cherryPick(parseCherryPicking(stmt)), \"CherryPicking\")\n  };\n  const parser2 = parsers[statement.$type];\n  if (parser2) {\n    parser2(statement);\n  } else {\n    log.error(`Unknown statement type: ${statement.$type}`);\n  }\n}, \"parseStatement\");\nvar parseCommit = /* @__PURE__ */ __name((commit2) => {\n  const commitDB = {\n    id: commit2.id,\n    msg: commit2.message ?? \"\",\n    type: commit2.type !== void 0 ? commitType[commit2.type] : commitType.NORMAL,\n    tags: commit2.tags ?? void 0\n  };\n  return commitDB;\n}, \"parseCommit\");\nvar parseBranch = /* @__PURE__ */ __name((branch2) => {\n  const branchDB = {\n    name: branch2.name,\n    order: branch2.order ?? 0\n  };\n  return branchDB;\n}, \"parseBranch\");\nvar parseMerge = /* @__PURE__ */ __name((merge2) => {\n  const mergeDB = {\n    branch: merge2.branch,\n    id: merge2.id ?? \"\",\n    type: merge2.type !== void 0 ? commitType[merge2.type] : void 0,\n    tags: merge2.tags ?? void 0\n  };\n  return mergeDB;\n}, \"parseMerge\");\nvar parseCheckout = /* @__PURE__ */ __name((checkout2) => {\n  const branch2 = checkout2.branch;\n  return branch2;\n}, \"parseCheckout\");\nvar parseCherryPicking = /* @__PURE__ */ __name((cherryPicking) => {\n  const cherryPickDB = {\n    id: cherryPicking.id,\n    targetId: \"\",\n    tags: cherryPicking.tags?.length === 0 ? void 0 : cherryPicking.tags,\n    parent: cherryPicking.parent\n  };\n  return cherryPickDB;\n}, \"parseCherryPicking\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"gitGraph\", input);\n    log.debug(ast);\n    populate(ast, db);\n  }, \"parse\")\n};\nif (void 0) {\n  const { it, expect, describe } = void 0;\n  const mockDB = {\n    commitType,\n    setDirection: vi.fn(),\n    commit: vi.fn(),\n    branch: vi.fn(),\n    merge: vi.fn(),\n    cherryPick: vi.fn(),\n    checkout: vi.fn()\n  };\n  describe(\"GitGraph Parser\", () => {\n    it(\"should parse a commit statement\", () => {\n      const commit2 = {\n        $type: \"Commit\",\n        id: \"1\",\n        message: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: \"NORMAL\"\n      };\n      parseStatement(commit2, mockDB);\n      expect(mockDB.commit).toHaveBeenCalledWith({\n        id: \"1\",\n        msg: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n    });\n    it(\"should parse a branch statement\", () => {\n      const branch2 = {\n        $type: \"Branch\",\n        name: \"newBranch\",\n        order: 1\n      };\n      parseStatement(branch2, mockDB);\n      expect(mockDB.branch).toHaveBeenCalledWith({ name: \"newBranch\", order: 1 });\n    });\n    it(\"should parse a checkout statement\", () => {\n      const checkout2 = {\n        $type: \"Checkout\",\n        branch: \"newBranch\"\n      };\n      parseStatement(checkout2, mockDB);\n      expect(mockDB.checkout).toHaveBeenCalledWith(\"newBranch\");\n    });\n    it(\"should parse a merge statement\", () => {\n      const merge2 = {\n        $type: \"Merge\",\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: \"NORMAL\"\n      };\n      parseStatement(merge2, mockDB);\n      expect(mockDB.merge).toHaveBeenCalledWith({\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n    });\n    it(\"should parse a cherry picking statement\", () => {\n      const cherryPick2 = {\n        $type: \"CherryPicking\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        parent: \"2\"\n      };\n      parseStatement(cherryPick2, mockDB);\n      expect(mockDB.cherryPick).toHaveBeenCalledWith({\n        id: \"1\",\n        targetId: \"\",\n        parent: \"2\",\n        tags: [\"tag1\", \"tag2\"]\n      });\n    });\n    it(\"should parse a langium generated gitGraph ast\", () => {\n      const dummy = {\n        $type: \"GitGraph\",\n        statements: []\n      };\n      const gitGraphAst = {\n        $type: \"GitGraph\",\n        statements: [\n          {\n            $container: dummy,\n            $type: \"Commit\",\n            id: \"1\",\n            message: \"test\",\n            tags: [\"tag1\", \"tag2\"],\n            type: \"NORMAL\"\n          },\n          {\n            $container: dummy,\n            $type: \"Branch\",\n            name: \"newBranch\",\n            order: 1\n          },\n          {\n            $container: dummy,\n            $type: \"Merge\",\n            branch: \"newBranch\",\n            id: \"1\",\n            tags: [\"tag1\", \"tag2\"],\n            type: \"NORMAL\"\n          },\n          {\n            $container: dummy,\n            $type: \"Checkout\",\n            branch: \"newBranch\"\n          },\n          {\n            $container: dummy,\n            $type: \"CherryPicking\",\n            id: \"1\",\n            tags: [\"tag1\", \"tag2\"],\n            parent: \"2\"\n          }\n        ]\n      };\n      populate(gitGraphAst, mockDB);\n      expect(mockDB.commit).toHaveBeenCalledWith({\n        id: \"1\",\n        msg: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n      expect(mockDB.branch).toHaveBeenCalledWith({ name: \"newBranch\", order: 1 });\n      expect(mockDB.merge).toHaveBeenCalledWith({\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n      expect(mockDB.checkout).toHaveBeenCalledWith(\"newBranch\");\n    });\n  });\n}\n\n// src/diagrams/git/gitGraphRenderer.ts\nimport { select } from \"d3\";\nvar DEFAULT_CONFIG = getConfig2();\nvar DEFAULT_GITGRAPH_CONFIG2 = DEFAULT_CONFIG?.gitGraph;\nvar LAYOUT_OFFSET = 10;\nvar COMMIT_STEP = 40;\nvar PX = 4;\nvar PY = 2;\nvar THEME_COLOR_LIMIT = 8;\nvar branchPos = /* @__PURE__ */ new Map();\nvar commitPos = /* @__PURE__ */ new Map();\nvar defaultPos = 30;\nvar allCommitsDict = /* @__PURE__ */ new Map();\nvar lanes = [];\nvar maxPos = 0;\nvar dir = \"LR\";\nvar clear3 = /* @__PURE__ */ __name(() => {\n  branchPos.clear();\n  commitPos.clear();\n  allCommitsDict.clear();\n  maxPos = 0;\n  lanes = [];\n  dir = \"LR\";\n}, \"clear\");\nvar drawText = /* @__PURE__ */ __name((txt) => {\n  const svgLabel = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n  const rows = typeof txt === \"string\" ? txt.split(/\\\\n|\\n|<br\\s*\\/?>/gi) : txt;\n  rows.forEach((row) => {\n    const tspan = document.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n    tspan.setAttributeNS(\"http://www.w3.org/XML/1998/namespace\", \"xml:space\", \"preserve\");\n    tspan.setAttribute(\"dy\", \"1em\");\n    tspan.setAttribute(\"x\", \"0\");\n    tspan.setAttribute(\"class\", \"row\");\n    tspan.textContent = row.trim();\n    svgLabel.appendChild(tspan);\n  });\n  return svgLabel;\n}, \"drawText\");\nvar findClosestParent = /* @__PURE__ */ __name((parents) => {\n  let closestParent;\n  let comparisonFunc;\n  let targetPosition;\n  if (dir === \"BT\") {\n    comparisonFunc = /* @__PURE__ */ __name((a, b) => a <= b, \"comparisonFunc\");\n    targetPosition = Infinity;\n  } else {\n    comparisonFunc = /* @__PURE__ */ __name((a, b) => a >= b, \"comparisonFunc\");\n    targetPosition = 0;\n  }\n  parents.forEach((parent) => {\n    const parentPosition = dir === \"TB\" || dir == \"BT\" ? commitPos.get(parent)?.y : commitPos.get(parent)?.x;\n    if (parentPosition !== void 0 && comparisonFunc(parentPosition, targetPosition)) {\n      closestParent = parent;\n      targetPosition = parentPosition;\n    }\n  });\n  return closestParent;\n}, \"findClosestParent\");\nvar findClosestParentBT = /* @__PURE__ */ __name((parents) => {\n  let closestParent = \"\";\n  let maxPosition = Infinity;\n  parents.forEach((parent) => {\n    const parentPosition = commitPos.get(parent).y;\n    if (parentPosition <= maxPosition) {\n      closestParent = parent;\n      maxPosition = parentPosition;\n    }\n  });\n  return closestParent || void 0;\n}, \"findClosestParentBT\");\nvar setParallelBTPos = /* @__PURE__ */ __name((sortedKeys, commits, defaultPos2) => {\n  let curPos = defaultPos2;\n  let maxPosition = defaultPos2;\n  const roots = [];\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (!commit2) {\n      throw new Error(`Commit not found for key ${key}`);\n    }\n    if (commit2.parents.length) {\n      curPos = calculateCommitPosition(commit2);\n      maxPosition = Math.max(curPos, maxPosition);\n    } else {\n      roots.push(commit2);\n    }\n    setCommitPosition(commit2, curPos);\n  });\n  curPos = maxPosition;\n  roots.forEach((commit2) => {\n    setRootPosition(commit2, curPos, defaultPos2);\n  });\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (commit2?.parents.length) {\n      const closestParent = findClosestParentBT(commit2.parents);\n      curPos = commitPos.get(closestParent).y - COMMIT_STEP;\n      if (curPos <= maxPosition) {\n        maxPosition = curPos;\n      }\n      const x = branchPos.get(commit2.branch).pos;\n      const y = curPos - LAYOUT_OFFSET;\n      commitPos.set(commit2.id, { x, y });\n    }\n  });\n}, \"setParallelBTPos\");\nvar findClosestParentPos = /* @__PURE__ */ __name((commit2) => {\n  const closestParent = findClosestParent(commit2.parents.filter((p) => p !== null));\n  if (!closestParent) {\n    throw new Error(`Closest parent not found for commit ${commit2.id}`);\n  }\n  const closestParentPos = commitPos.get(closestParent)?.y;\n  if (closestParentPos === void 0) {\n    throw new Error(`Closest parent position not found for commit ${commit2.id}`);\n  }\n  return closestParentPos;\n}, \"findClosestParentPos\");\nvar calculateCommitPosition = /* @__PURE__ */ __name((commit2) => {\n  const closestParentPos = findClosestParentPos(commit2);\n  return closestParentPos + COMMIT_STEP;\n}, \"calculateCommitPosition\");\nvar setCommitPosition = /* @__PURE__ */ __name((commit2, curPos) => {\n  const branch2 = branchPos.get(commit2.branch);\n  if (!branch2) {\n    throw new Error(`Branch not found for commit ${commit2.id}`);\n  }\n  const x = branch2.pos;\n  const y = curPos + LAYOUT_OFFSET;\n  commitPos.set(commit2.id, { x, y });\n  return { x, y };\n}, \"setCommitPosition\");\nvar setRootPosition = /* @__PURE__ */ __name((commit2, curPos, defaultPos2) => {\n  const branch2 = branchPos.get(commit2.branch);\n  if (!branch2) {\n    throw new Error(`Branch not found for commit ${commit2.id}`);\n  }\n  const y = curPos + defaultPos2;\n  const x = branch2.pos;\n  commitPos.set(commit2.id, { x, y });\n}, \"setRootPosition\");\nvar drawCommitBullet = /* @__PURE__ */ __name((gBullets, commit2, commitPosition, typeClass, branchIndex, commitSymbolType) => {\n  if (commitSymbolType === commitType.HIGHLIGHT) {\n    gBullets.append(\"rect\").attr(\"x\", commitPosition.x - 10).attr(\"y\", commitPosition.y - 10).attr(\"width\", 20).attr(\"height\", 20).attr(\n      \"class\",\n      `commit ${commit2.id} commit-highlight${branchIndex % THEME_COLOR_LIMIT} ${typeClass}-outer`\n    );\n    gBullets.append(\"rect\").attr(\"x\", commitPosition.x - 6).attr(\"y\", commitPosition.y - 6).attr(\"width\", 12).attr(\"height\", 12).attr(\n      \"class\",\n      `commit ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT} ${typeClass}-inner`\n    );\n  } else if (commitSymbolType === commitType.CHERRY_PICK) {\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x).attr(\"cy\", commitPosition.y).attr(\"r\", 10).attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x - 3).attr(\"cy\", commitPosition.y + 2).attr(\"r\", 2.75).attr(\"fill\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x + 3).attr(\"cy\", commitPosition.y + 2).attr(\"r\", 2.75).attr(\"fill\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"line\").attr(\"x1\", commitPosition.x + 3).attr(\"y1\", commitPosition.y + 1).attr(\"x2\", commitPosition.x).attr(\"y2\", commitPosition.y - 5).attr(\"stroke\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"line\").attr(\"x1\", commitPosition.x - 3).attr(\"y1\", commitPosition.y + 1).attr(\"x2\", commitPosition.x).attr(\"y2\", commitPosition.y - 5).attr(\"stroke\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n  } else {\n    const circle = gBullets.append(\"circle\");\n    circle.attr(\"cx\", commitPosition.x);\n    circle.attr(\"cy\", commitPosition.y);\n    circle.attr(\"r\", commit2.type === commitType.MERGE ? 9 : 10);\n    circle.attr(\"class\", `commit ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`);\n    if (commitSymbolType === commitType.MERGE) {\n      const circle2 = gBullets.append(\"circle\");\n      circle2.attr(\"cx\", commitPosition.x);\n      circle2.attr(\"cy\", commitPosition.y);\n      circle2.attr(\"r\", 6);\n      circle2.attr(\n        \"class\",\n        `commit ${typeClass} ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`\n      );\n    }\n    if (commitSymbolType === commitType.REVERSE) {\n      const cross = gBullets.append(\"path\");\n      cross.attr(\n        \"d\",\n        `M ${commitPosition.x - 5},${commitPosition.y - 5}L${commitPosition.x + 5},${commitPosition.y + 5}M${commitPosition.x - 5},${commitPosition.y + 5}L${commitPosition.x + 5},${commitPosition.y - 5}`\n      ).attr(\"class\", `commit ${typeClass} ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`);\n    }\n  }\n}, \"drawCommitBullet\");\nvar drawCommitLabel = /* @__PURE__ */ __name((gLabels, commit2, commitPosition, pos) => {\n  if (commit2.type !== commitType.CHERRY_PICK && (commit2.customId && commit2.type === commitType.MERGE || commit2.type !== commitType.MERGE) && DEFAULT_GITGRAPH_CONFIG2?.showCommitLabel) {\n    const wrapper = gLabels.append(\"g\");\n    const labelBkg = wrapper.insert(\"rect\").attr(\"class\", \"commit-label-bkg\");\n    const text = wrapper.append(\"text\").attr(\"x\", pos).attr(\"y\", commitPosition.y + 25).attr(\"class\", \"commit-label\").text(commit2.id);\n    const bbox = text.node()?.getBBox();\n    if (bbox) {\n      labelBkg.attr(\"x\", commitPosition.posWithOffset - bbox.width / 2 - PY).attr(\"y\", commitPosition.y + 13.5).attr(\"width\", bbox.width + 2 * PY).attr(\"height\", bbox.height + 2 * PY);\n      if (dir === \"TB\" || dir === \"BT\") {\n        labelBkg.attr(\"x\", commitPosition.x - (bbox.width + 4 * PX + 5)).attr(\"y\", commitPosition.y - 12);\n        text.attr(\"x\", commitPosition.x - (bbox.width + 4 * PX)).attr(\"y\", commitPosition.y + bbox.height - 12);\n      } else {\n        text.attr(\"x\", commitPosition.posWithOffset - bbox.width / 2);\n      }\n      if (DEFAULT_GITGRAPH_CONFIG2.rotateCommitLabel) {\n        if (dir === \"TB\" || dir === \"BT\") {\n          text.attr(\n            \"transform\",\n            \"rotate(-45, \" + commitPosition.x + \", \" + commitPosition.y + \")\"\n          );\n          labelBkg.attr(\n            \"transform\",\n            \"rotate(-45, \" + commitPosition.x + \", \" + commitPosition.y + \")\"\n          );\n        } else {\n          const r_x = -7.5 - (bbox.width + 10) / 25 * 9.5;\n          const r_y = 10 + bbox.width / 25 * 8.5;\n          wrapper.attr(\n            \"transform\",\n            \"translate(\" + r_x + \", \" + r_y + \") rotate(-45, \" + pos + \", \" + commitPosition.y + \")\"\n          );\n        }\n      }\n    }\n  }\n}, \"drawCommitLabel\");\nvar drawCommitTags = /* @__PURE__ */ __name((gLabels, commit2, commitPosition, pos) => {\n  if (commit2.tags.length > 0) {\n    let yOffset = 0;\n    let maxTagBboxWidth = 0;\n    let maxTagBboxHeight = 0;\n    const tagElements = [];\n    for (const tagValue of commit2.tags.reverse()) {\n      const rect = gLabels.insert(\"polygon\");\n      const hole = gLabels.append(\"circle\");\n      const tag = gLabels.append(\"text\").attr(\"y\", commitPosition.y - 16 - yOffset).attr(\"class\", \"tag-label\").text(tagValue);\n      const tagBbox = tag.node()?.getBBox();\n      if (!tagBbox) {\n        throw new Error(\"Tag bbox not found\");\n      }\n      maxTagBboxWidth = Math.max(maxTagBboxWidth, tagBbox.width);\n      maxTagBboxHeight = Math.max(maxTagBboxHeight, tagBbox.height);\n      tag.attr(\"x\", commitPosition.posWithOffset - tagBbox.width / 2);\n      tagElements.push({\n        tag,\n        hole,\n        rect,\n        yOffset\n      });\n      yOffset += 20;\n    }\n    for (const { tag, hole, rect, yOffset: yOffset2 } of tagElements) {\n      const h2 = maxTagBboxHeight / 2;\n      const ly = commitPosition.y - 19.2 - yOffset2;\n      rect.attr(\"class\", \"tag-label-bkg\").attr(\n        \"points\",\n        `\n      ${pos - maxTagBboxWidth / 2 - PX / 2},${ly + PY}  \n      ${pos - maxTagBboxWidth / 2 - PX / 2},${ly - PY}\n      ${commitPosition.posWithOffset - maxTagBboxWidth / 2 - PX},${ly - h2 - PY}\n      ${commitPosition.posWithOffset + maxTagBboxWidth / 2 + PX},${ly - h2 - PY}\n      ${commitPosition.posWithOffset + maxTagBboxWidth / 2 + PX},${ly + h2 + PY}\n      ${commitPosition.posWithOffset - maxTagBboxWidth / 2 - PX},${ly + h2 + PY}`\n      );\n      hole.attr(\"cy\", ly).attr(\"cx\", pos - maxTagBboxWidth / 2 + PX / 2).attr(\"r\", 1.5).attr(\"class\", \"tag-hole\");\n      if (dir === \"TB\" || dir === \"BT\") {\n        const yOrigin = pos + yOffset2;\n        rect.attr(\"class\", \"tag-label-bkg\").attr(\n          \"points\",\n          `\n        ${commitPosition.x},${yOrigin + 2}\n        ${commitPosition.x},${yOrigin - 2}\n        ${commitPosition.x + LAYOUT_OFFSET},${yOrigin - h2 - 2}\n        ${commitPosition.x + LAYOUT_OFFSET + maxTagBboxWidth + 4},${yOrigin - h2 - 2}\n        ${commitPosition.x + LAYOUT_OFFSET + maxTagBboxWidth + 4},${yOrigin + h2 + 2}\n        ${commitPosition.x + LAYOUT_OFFSET},${yOrigin + h2 + 2}`\n        ).attr(\"transform\", \"translate(12,12) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n        hole.attr(\"cx\", commitPosition.x + PX / 2).attr(\"cy\", yOrigin).attr(\"transform\", \"translate(12,12) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n        tag.attr(\"x\", commitPosition.x + 5).attr(\"y\", yOrigin + 3).attr(\"transform\", \"translate(14,14) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n      }\n    }\n  }\n}, \"drawCommitTags\");\nvar getCommitClassType = /* @__PURE__ */ __name((commit2) => {\n  const commitSymbolType = commit2.customType ?? commit2.type;\n  switch (commitSymbolType) {\n    case commitType.NORMAL:\n      return \"commit-normal\";\n    case commitType.REVERSE:\n      return \"commit-reverse\";\n    case commitType.HIGHLIGHT:\n      return \"commit-highlight\";\n    case commitType.MERGE:\n      return \"commit-merge\";\n    case commitType.CHERRY_PICK:\n      return \"commit-cherry-pick\";\n    default:\n      return \"commit-normal\";\n  }\n}, \"getCommitClassType\");\nvar calculatePosition = /* @__PURE__ */ __name((commit2, dir2, pos, commitPos2) => {\n  const defaultCommitPosition = { x: 0, y: 0 };\n  if (commit2.parents.length > 0) {\n    const closestParent = findClosestParent(commit2.parents);\n    if (closestParent) {\n      const parentPosition = commitPos2.get(closestParent) ?? defaultCommitPosition;\n      if (dir2 === \"TB\") {\n        return parentPosition.y + COMMIT_STEP;\n      } else if (dir2 === \"BT\") {\n        const currentPosition = commitPos2.get(commit2.id) ?? defaultCommitPosition;\n        return currentPosition.y - COMMIT_STEP;\n      } else {\n        return parentPosition.x + COMMIT_STEP;\n      }\n    }\n  } else {\n    if (dir2 === \"TB\") {\n      return defaultPos;\n    } else if (dir2 === \"BT\") {\n      const currentPosition = commitPos2.get(commit2.id) ?? defaultCommitPosition;\n      return currentPosition.y - COMMIT_STEP;\n    } else {\n      return 0;\n    }\n  }\n  return 0;\n}, \"calculatePosition\");\nvar getCommitPosition = /* @__PURE__ */ __name((commit2, pos, isParallelCommits) => {\n  const posWithOffset = dir === \"BT\" && isParallelCommits ? pos : pos + LAYOUT_OFFSET;\n  const y = dir === \"TB\" || dir === \"BT\" ? posWithOffset : branchPos.get(commit2.branch)?.pos;\n  const x = dir === \"TB\" || dir === \"BT\" ? branchPos.get(commit2.branch)?.pos : posWithOffset;\n  if (x === void 0 || y === void 0) {\n    throw new Error(`Position were undefined for commit ${commit2.id}`);\n  }\n  return { x, y, posWithOffset };\n}, \"getCommitPosition\");\nvar drawCommits = /* @__PURE__ */ __name((svg, commits, modifyGraph) => {\n  if (!DEFAULT_GITGRAPH_CONFIG2) {\n    throw new Error(\"GitGraph config not found\");\n  }\n  const gBullets = svg.append(\"g\").attr(\"class\", \"commit-bullets\");\n  const gLabels = svg.append(\"g\").attr(\"class\", \"commit-labels\");\n  let pos = dir === \"TB\" || dir === \"BT\" ? defaultPos : 0;\n  const keys = [...commits.keys()];\n  const isParallelCommits = DEFAULT_GITGRAPH_CONFIG2?.parallelCommits ?? false;\n  const sortKeys = /* @__PURE__ */ __name((a, b) => {\n    const seqA = commits.get(a)?.seq;\n    const seqB = commits.get(b)?.seq;\n    return seqA !== void 0 && seqB !== void 0 ? seqA - seqB : 0;\n  }, \"sortKeys\");\n  let sortedKeys = keys.sort(sortKeys);\n  if (dir === \"BT\") {\n    if (isParallelCommits) {\n      setParallelBTPos(sortedKeys, commits, pos);\n    }\n    sortedKeys = sortedKeys.reverse();\n  }\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (!commit2) {\n      throw new Error(`Commit not found for key ${key}`);\n    }\n    if (isParallelCommits) {\n      pos = calculatePosition(commit2, dir, pos, commitPos);\n    }\n    const commitPosition = getCommitPosition(commit2, pos, isParallelCommits);\n    if (modifyGraph) {\n      const typeClass = getCommitClassType(commit2);\n      const commitSymbolType = commit2.customType ?? commit2.type;\n      const branchIndex = branchPos.get(commit2.branch)?.index ?? 0;\n      drawCommitBullet(gBullets, commit2, commitPosition, typeClass, branchIndex, commitSymbolType);\n      drawCommitLabel(gLabels, commit2, commitPosition, pos);\n      drawCommitTags(gLabels, commit2, commitPosition, pos);\n    }\n    if (dir === \"TB\" || dir === \"BT\") {\n      commitPos.set(commit2.id, { x: commitPosition.x, y: commitPosition.posWithOffset });\n    } else {\n      commitPos.set(commit2.id, { x: commitPosition.posWithOffset, y: commitPosition.y });\n    }\n    pos = dir === \"BT\" && isParallelCommits ? pos + COMMIT_STEP : pos + COMMIT_STEP + LAYOUT_OFFSET;\n    if (pos > maxPos) {\n      maxPos = pos;\n    }\n  });\n}, \"drawCommits\");\nvar shouldRerouteArrow = /* @__PURE__ */ __name((commitA, commitB, p1, p2, allCommits) => {\n  const commitBIsFurthest = dir === \"TB\" || dir === \"BT\" ? p1.x < p2.x : p1.y < p2.y;\n  const branchToGetCurve = commitBIsFurthest ? commitB.branch : commitA.branch;\n  const isOnBranchToGetCurve = /* @__PURE__ */ __name((x) => x.branch === branchToGetCurve, \"isOnBranchToGetCurve\");\n  const isBetweenCommits = /* @__PURE__ */ __name((x) => x.seq > commitA.seq && x.seq < commitB.seq, \"isBetweenCommits\");\n  return [...allCommits.values()].some((commitX) => {\n    return isBetweenCommits(commitX) && isOnBranchToGetCurve(commitX);\n  });\n}, \"shouldRerouteArrow\");\nvar findLane = /* @__PURE__ */ __name((y1, y2, depth = 0) => {\n  const candidate = y1 + Math.abs(y1 - y2) / 2;\n  if (depth > 5) {\n    return candidate;\n  }\n  const ok = lanes.every((lane) => Math.abs(lane - candidate) >= 10);\n  if (ok) {\n    lanes.push(candidate);\n    return candidate;\n  }\n  const diff = Math.abs(y1 - y2);\n  return findLane(y1, y2 - diff / 5, depth + 1);\n}, \"findLane\");\nvar drawArrow = /* @__PURE__ */ __name((svg, commitA, commitB, allCommits) => {\n  const p1 = commitPos.get(commitA.id);\n  const p2 = commitPos.get(commitB.id);\n  if (p1 === void 0 || p2 === void 0) {\n    throw new Error(`Commit positions not found for commits ${commitA.id} and ${commitB.id}`);\n  }\n  const arrowNeedsRerouting = shouldRerouteArrow(commitA, commitB, p1, p2, allCommits);\n  let arc = \"\";\n  let arc2 = \"\";\n  let radius = 0;\n  let offset = 0;\n  let colorClassNum = branchPos.get(commitB.branch)?.index;\n  if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n    colorClassNum = branchPos.get(commitA.branch)?.index;\n  }\n  let lineDef;\n  if (arrowNeedsRerouting) {\n    arc = \"A 10 10, 0, 0, 0,\";\n    arc2 = \"A 10 10, 0, 0, 1,\";\n    radius = 10;\n    offset = 10;\n    const lineY = p1.y < p2.y ? findLane(p1.y, p2.y) : findLane(p2.y, p1.y);\n    const lineX = p1.x < p2.x ? findLane(p1.x, p2.x) : findLane(p2.x, p1.x);\n    if (dir === \"TB\") {\n      if (p1.x < p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX - radius} ${p1.y} ${arc2} ${lineX} ${p1.y + offset} L ${lineX} ${p2.y - radius} ${arc} ${lineX + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX + radius} ${p1.y} ${arc} ${lineX} ${p1.y + offset} L ${lineX} ${p2.y - radius} ${arc2} ${lineX - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      }\n    } else if (dir === \"BT\") {\n      if (p1.x < p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX - radius} ${p1.y} ${arc} ${lineX} ${p1.y - offset} L ${lineX} ${p2.y + radius} ${arc2} ${lineX + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX + radius} ${p1.y} ${arc2} ${lineX} ${p1.y - offset} L ${lineX} ${p2.y + radius} ${arc} ${lineX - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY - radius} ${arc} ${p1.x + offset} ${lineY} L ${p2.x - radius} ${lineY} ${arc2} ${p2.x} ${lineY + offset} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY + radius} ${arc2} ${p1.x + offset} ${lineY} L ${p2.x - radius} ${lineY} ${arc} ${p2.x} ${lineY - offset} L ${p2.x} ${p2.y}`;\n      }\n    }\n  } else {\n    arc = \"A 20 20, 0, 0, 0,\";\n    arc2 = \"A 20 20, 0, 0, 1,\";\n    radius = 20;\n    offset = 20;\n    if (dir === \"TB\") {\n      if (p1.x < p2.x) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x > p2.x) {\n        arc = \"A 20 20, 0, 0, 0,\";\n        arc2 = \"A 20 20, 0, 0, 1,\";\n        radius = 20;\n        offset = 20;\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc2} ${p1.x - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x + radius} ${p1.y} ${arc} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x === p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    } else if (dir === \"BT\") {\n      if (p1.x < p2.x) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc2} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x > p2.x) {\n        arc = \"A 20 20, 0, 0, 0,\";\n        arc2 = \"A 20 20, 0, 0, 1,\";\n        radius = 20;\n        offset = 20;\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc} ${p1.x - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x === p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.y > p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc2} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.y === p2.y) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    }\n  }\n  if (lineDef === void 0) {\n    throw new Error(\"Line definition not found\");\n  }\n  svg.append(\"path\").attr(\"d\", lineDef).attr(\"class\", \"arrow arrow\" + colorClassNum % THEME_COLOR_LIMIT);\n}, \"drawArrow\");\nvar drawArrows = /* @__PURE__ */ __name((svg, commits) => {\n  const gArrows = svg.append(\"g\").attr(\"class\", \"commit-arrows\");\n  [...commits.keys()].forEach((key) => {\n    const commit2 = commits.get(key);\n    if (commit2.parents && commit2.parents.length > 0) {\n      commit2.parents.forEach((parent) => {\n        drawArrow(gArrows, commits.get(parent), commit2, commits);\n      });\n    }\n  });\n}, \"drawArrows\");\nvar drawBranches = /* @__PURE__ */ __name((svg, branches) => {\n  const g = svg.append(\"g\");\n  branches.forEach((branch2, index) => {\n    const adjustIndexForTheme = index % THEME_COLOR_LIMIT;\n    const pos = branchPos.get(branch2.name)?.pos;\n    if (pos === void 0) {\n      throw new Error(`Position not found for branch ${branch2.name}`);\n    }\n    const line = g.append(\"line\");\n    line.attr(\"x1\", 0);\n    line.attr(\"y1\", pos);\n    line.attr(\"x2\", maxPos);\n    line.attr(\"y2\", pos);\n    line.attr(\"class\", \"branch branch\" + adjustIndexForTheme);\n    if (dir === \"TB\") {\n      line.attr(\"y1\", defaultPos);\n      line.attr(\"x1\", pos);\n      line.attr(\"y2\", maxPos);\n      line.attr(\"x2\", pos);\n    } else if (dir === \"BT\") {\n      line.attr(\"y1\", maxPos);\n      line.attr(\"x1\", pos);\n      line.attr(\"y2\", defaultPos);\n      line.attr(\"x2\", pos);\n    }\n    lanes.push(pos);\n    const name = branch2.name;\n    const labelElement = drawText(name);\n    const bkg = g.insert(\"rect\");\n    const branchLabel = g.insert(\"g\").attr(\"class\", \"branchLabel\");\n    const label = branchLabel.insert(\"g\").attr(\"class\", \"label branch-label\" + adjustIndexForTheme);\n    label.node().appendChild(labelElement);\n    const bbox = labelElement.getBBox();\n    bkg.attr(\"class\", \"branchLabelBkg label\" + adjustIndexForTheme).attr(\"rx\", 4).attr(\"ry\", 4).attr(\"x\", -bbox.width - 4 - (DEFAULT_GITGRAPH_CONFIG2?.rotateCommitLabel === true ? 30 : 0)).attr(\"y\", -bbox.height / 2 + 8).attr(\"width\", bbox.width + 18).attr(\"height\", bbox.height + 4);\n    label.attr(\n      \"transform\",\n      \"translate(\" + (-bbox.width - 14 - (DEFAULT_GITGRAPH_CONFIG2?.rotateCommitLabel === true ? 30 : 0)) + \", \" + (pos - bbox.height / 2 - 1) + \")\"\n    );\n    if (dir === \"TB\") {\n      bkg.attr(\"x\", pos - bbox.width / 2 - 10).attr(\"y\", 0);\n      label.attr(\"transform\", \"translate(\" + (pos - bbox.width / 2 - 5) + \", 0)\");\n    } else if (dir === \"BT\") {\n      bkg.attr(\"x\", pos - bbox.width / 2 - 10).attr(\"y\", maxPos);\n      label.attr(\"transform\", \"translate(\" + (pos - bbox.width / 2 - 5) + \", \" + maxPos + \")\");\n    } else {\n      bkg.attr(\"transform\", \"translate(-19, \" + (pos - bbox.height / 2) + \")\");\n    }\n  });\n}, \"drawBranches\");\nvar setBranchPosition = /* @__PURE__ */ __name(function(name, pos, index, bbox, rotateCommitLabel) {\n  branchPos.set(name, { pos, index });\n  pos += 50 + (rotateCommitLabel ? 40 : 0) + (dir === \"TB\" || dir === \"BT\" ? bbox.width / 2 : 0);\n  return pos;\n}, \"setBranchPosition\");\nvar draw = /* @__PURE__ */ __name(function(txt, id, ver, diagObj) {\n  clear3();\n  log.debug(\"in gitgraph renderer\", txt + \"\\n\", \"id:\", id, ver);\n  if (!DEFAULT_GITGRAPH_CONFIG2) {\n    throw new Error(\"GitGraph config not found\");\n  }\n  const rotateCommitLabel = DEFAULT_GITGRAPH_CONFIG2.rotateCommitLabel ?? false;\n  const db2 = diagObj.db;\n  allCommitsDict = db2.getCommits();\n  const branches = db2.getBranchesAsObjArray();\n  dir = db2.getDirection();\n  const diagram2 = select(`[id=\"${id}\"]`);\n  let pos = 0;\n  branches.forEach((branch2, index) => {\n    const labelElement = drawText(branch2.name);\n    const g = diagram2.append(\"g\");\n    const branchLabel = g.insert(\"g\").attr(\"class\", \"branchLabel\");\n    const label = branchLabel.insert(\"g\").attr(\"class\", \"label branch-label\");\n    label.node()?.appendChild(labelElement);\n    const bbox = labelElement.getBBox();\n    pos = setBranchPosition(branch2.name, pos, index, bbox, rotateCommitLabel);\n    label.remove();\n    branchLabel.remove();\n    g.remove();\n  });\n  drawCommits(diagram2, allCommitsDict, false);\n  if (DEFAULT_GITGRAPH_CONFIG2.showBranches) {\n    drawBranches(diagram2, branches);\n  }\n  drawArrows(diagram2, allCommitsDict);\n  drawCommits(diagram2, allCommitsDict, true);\n  utils_default.insertTitle(\n    diagram2,\n    \"gitTitleText\",\n    DEFAULT_GITGRAPH_CONFIG2.titleTopMargin ?? 0,\n    db2.getDiagramTitle()\n  );\n  setupGraphViewbox(\n    void 0,\n    diagram2,\n    DEFAULT_GITGRAPH_CONFIG2.diagramPadding,\n    DEFAULT_GITGRAPH_CONFIG2.useMaxWidth\n  );\n}, \"draw\");\nvar gitGraphRenderer_default = {\n  draw\n};\nif (void 0) {\n  const { it, expect, describe } = void 0;\n  describe(\"drawText\", () => {\n    it(\"should drawText\", () => {\n      const svgLabel = drawText(\"main\");\n      expect(svgLabel).toBeDefined();\n      expect(svgLabel.children[0].innerHTML).toBe(\"main\");\n    });\n  });\n  describe(\"branchPosition\", () => {\n    const bbox = {\n      x: 0,\n      y: 0,\n      width: 10,\n      height: 10,\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      toJSON: /* @__PURE__ */ __name(() => \"\", \"toJSON\")\n    };\n    it(\"should setBranchPositions LR with two branches\", () => {\n      dir = \"LR\";\n      const pos = setBranchPosition(\"main\", 0, 0, bbox, true);\n      expect(pos).toBe(90);\n      expect(branchPos.get(\"main\")).toEqual({ pos: 0, index: 0 });\n      const posNext = setBranchPosition(\"develop\", pos, 1, bbox, true);\n      expect(posNext).toBe(180);\n      expect(branchPos.get(\"develop\")).toEqual({ pos, index: 1 });\n    });\n    it(\"should setBranchPositions TB with two branches\", () => {\n      dir = \"TB\";\n      bbox.width = 34.9921875;\n      const pos = setBranchPosition(\"main\", 0, 0, bbox, true);\n      expect(pos).toBe(107.49609375);\n      expect(branchPos.get(\"main\")).toEqual({ pos: 0, index: 0 });\n      bbox.width = 56.421875;\n      const posNext = setBranchPosition(\"develop\", pos, 1, bbox, true);\n      expect(posNext).toBe(225.70703125);\n      expect(branchPos.get(\"develop\")).toEqual({ pos, index: 1 });\n    });\n  });\n  describe(\"commitPosition\", () => {\n    const commits = /* @__PURE__ */ new Map([\n      [\n        \"commitZero\",\n        {\n          id: \"ZERO\",\n          message: \"\",\n          seq: 0,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"commitA\",\n        {\n          id: \"A\",\n          message: \"\",\n          seq: 1,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"ZERO\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"commitB\",\n        {\n          id: \"B\",\n          message: \"\",\n          seq: 2,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"A\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"commitM\",\n        {\n          id: \"M\",\n          message: \"merged branch feature into main\",\n          seq: 3,\n          type: commitType.MERGE,\n          tags: [],\n          parents: [\"ZERO\", \"B\"],\n          branch: \"main\",\n          customId: true\n        }\n      ],\n      [\n        \"commitC\",\n        {\n          id: \"C\",\n          message: \"\",\n          seq: 4,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"ZERO\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commit5_8928ea0\",\n        {\n          id: \"5-8928ea0\",\n          message: \"cherry-picked [object Object] into release\",\n          seq: 5,\n          type: commitType.CHERRY_PICK,\n          tags: [],\n          parents: [\"C\", \"M\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commitD\",\n        {\n          id: \"D\",\n          message: \"\",\n          seq: 6,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"5-8928ea0\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commit7_ed848ba\",\n        {\n          id: \"7-ed848ba\",\n          message: \"cherry-picked [object Object] into release\",\n          seq: 7,\n          type: commitType.CHERRY_PICK,\n          tags: [],\n          parents: [\"D\", \"M\"],\n          branch: \"release\"\n        }\n      ]\n    ]);\n    let pos = 0;\n    branchPos.set(\"main\", { pos: 0, index: 0 });\n    branchPos.set(\"feature\", { pos: 107.49609375, index: 1 });\n    branchPos.set(\"release\", { pos: 224.03515625, index: 2 });\n    describe(\"TB\", () => {\n      pos = 30;\n      dir = \"TB\";\n      const expectedCommitPositionTB = /* @__PURE__ */ new Map([\n        [\"commitZero\", { x: 0, y: 40, posWithOffset: 40 }],\n        [\"commitA\", { x: 107.49609375, y: 90, posWithOffset: 90 }],\n        [\"commitB\", { x: 107.49609375, y: 140, posWithOffset: 140 }],\n        [\"commitM\", { x: 0, y: 190, posWithOffset: 190 }],\n        [\"commitC\", { x: 224.03515625, y: 240, posWithOffset: 240 }],\n        [\"commit5_8928ea0\", { x: 224.03515625, y: 290, posWithOffset: 290 }],\n        [\"commitD\", { x: 224.03515625, y: 340, posWithOffset: 340 }],\n        [\"commit7_ed848ba\", { x: 224.03515625, y: 390, posWithOffset: 390 }]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct position for commit ${key}`, () => {\n          const position = getCommitPosition(commit2, pos, false);\n          expect(position).toEqual(expectedCommitPositionTB.get(key));\n          pos += 50;\n        });\n      });\n    });\n    describe(\"LR\", () => {\n      let pos2 = 30;\n      dir = \"LR\";\n      const expectedCommitPositionLR = /* @__PURE__ */ new Map([\n        [\"commitZero\", { x: 0, y: 40, posWithOffset: 40 }],\n        [\"commitA\", { x: 107.49609375, y: 90, posWithOffset: 90 }],\n        [\"commitB\", { x: 107.49609375, y: 140, posWithOffset: 140 }],\n        [\"commitM\", { x: 0, y: 190, posWithOffset: 190 }],\n        [\"commitC\", { x: 224.03515625, y: 240, posWithOffset: 240 }],\n        [\"commit5_8928ea0\", { x: 224.03515625, y: 290, posWithOffset: 290 }],\n        [\"commitD\", { x: 224.03515625, y: 340, posWithOffset: 340 }],\n        [\"commit7_ed848ba\", { x: 224.03515625, y: 390, posWithOffset: 390 }]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct position for commit ${key}`, () => {\n          const position = getCommitPosition(commit2, pos2, false);\n          expect(position).toEqual(expectedCommitPositionLR.get(key));\n          pos2 += 50;\n        });\n      });\n    });\n    describe(\"getCommitClassType\", () => {\n      const expectedCommitClassType = /* @__PURE__ */ new Map([\n        [\"commitZero\", \"commit-normal\"],\n        [\"commitA\", \"commit-normal\"],\n        [\"commitB\", \"commit-normal\"],\n        [\"commitM\", \"commit-merge\"],\n        [\"commitC\", \"commit-normal\"],\n        [\"commit5_8928ea0\", \"commit-cherry-pick\"],\n        [\"commitD\", \"commit-normal\"],\n        [\"commit7_ed848ba\", \"commit-cherry-pick\"]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct class type for commit ${key}`, () => {\n          const classType = getCommitClassType(commit2);\n          expect(classType).toBe(expectedCommitClassType.get(key));\n        });\n      });\n    });\n  });\n  describe(\"building BT parallel commit diagram\", () => {\n    const commits = /* @__PURE__ */ new Map([\n      [\n        \"1-abcdefg\",\n        {\n          id: \"1-abcdefg\",\n          message: \"\",\n          seq: 0,\n          type: 0,\n          tags: [],\n          parents: [],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"2-abcdefg\",\n        {\n          id: \"2-abcdefg\",\n          message: \"\",\n          seq: 1,\n          type: 0,\n          tags: [],\n          parents: [\"1-abcdefg\"],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"3-abcdefg\",\n        {\n          id: \"3-abcdefg\",\n          message: \"\",\n          seq: 2,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"develop\"\n        }\n      ],\n      [\n        \"4-abcdefg\",\n        {\n          id: \"4-abcdefg\",\n          message: \"\",\n          seq: 3,\n          type: 0,\n          tags: [],\n          parents: [\"3-abcdefg\"],\n          branch: \"develop\"\n        }\n      ],\n      [\n        \"5-abcdefg\",\n        {\n          id: \"5-abcdefg\",\n          message: \"\",\n          seq: 4,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"6-abcdefg\",\n        {\n          id: \"6-abcdefg\",\n          message: \"\",\n          seq: 5,\n          type: 0,\n          tags: [],\n          parents: [\"5-abcdefg\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"7-abcdefg\",\n        {\n          id: \"7-abcdefg\",\n          message: \"\",\n          seq: 6,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"8-abcdefg\",\n        {\n          id: \"8-abcdefg\",\n          message: \"\",\n          seq: 7,\n          type: 0,\n          tags: [],\n          parents: [\"7-abcdefg\"],\n          branch: \"main\"\n        }\n      ]\n    ]);\n    const expectedCommitPosition = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", { x: 0, y: 40 }],\n      [\"2-abcdefg\", { x: 0, y: 90 }],\n      [\"3-abcdefg\", { x: 107.49609375, y: 140 }],\n      [\"4-abcdefg\", { x: 107.49609375, y: 190 }],\n      [\"5-abcdefg\", { x: 225.70703125, y: 140 }],\n      [\"6-abcdefg\", { x: 225.70703125, y: 190 }],\n      [\"7-abcdefg\", { x: 0, y: 140 }],\n      [\"8-abcdefg\", { x: 0, y: 190 }]\n    ]);\n    const expectedCommitPositionAfterParallel = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", { x: 0, y: 210 }],\n      [\"2-abcdefg\", { x: 0, y: 160 }],\n      [\"3-abcdefg\", { x: 107.49609375, y: 110 }],\n      [\"4-abcdefg\", { x: 107.49609375, y: 60 }],\n      [\"5-abcdefg\", { x: 225.70703125, y: 110 }],\n      [\"6-abcdefg\", { x: 225.70703125, y: 60 }],\n      [\"7-abcdefg\", { x: 0, y: 110 }],\n      [\"8-abcdefg\", { x: 0, y: 60 }]\n    ]);\n    const expectedCommitCurrentPosition = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", 30],\n      [\"2-abcdefg\", 80],\n      [\"3-abcdefg\", 130],\n      [\"4-abcdefg\", 180],\n      [\"5-abcdefg\", 130],\n      [\"6-abcdefg\", 180],\n      [\"7-abcdefg\", 130],\n      [\"8-abcdefg\", 180]\n    ]);\n    const sortedKeys = [...expectedCommitPosition.keys()];\n    it(\"should get the correct commit position and current position\", () => {\n      dir = \"BT\";\n      let curPos = 30;\n      commitPos.clear();\n      branchPos.clear();\n      branchPos.set(\"main\", { pos: 0, index: 0 });\n      branchPos.set(\"develop\", { pos: 107.49609375, index: 1 });\n      branchPos.set(\"feature\", { pos: 225.70703125, index: 2 });\n      DEFAULT_GITGRAPH_CONFIG2.parallelCommits = true;\n      commits.forEach((commit2, key) => {\n        if (commit2.parents.length > 0) {\n          curPos = calculateCommitPosition(commit2);\n        }\n        const position = setCommitPosition(commit2, curPos);\n        expect(position).toEqual(expectedCommitPosition.get(key));\n        expect(curPos).toEqual(expectedCommitCurrentPosition.get(key));\n      });\n    });\n    it(\"should get the correct commit position after parallel commits\", () => {\n      commitPos.clear();\n      branchPos.clear();\n      dir = \"BT\";\n      const curPos = 30;\n      commitPos.clear();\n      branchPos.clear();\n      branchPos.set(\"main\", { pos: 0, index: 0 });\n      branchPos.set(\"develop\", { pos: 107.49609375, index: 1 });\n      branchPos.set(\"feature\", { pos: 225.70703125, index: 2 });\n      setParallelBTPos(sortedKeys, commits, curPos);\n      sortedKeys.forEach((commit2) => {\n        const position = commitPos.get(commit2);\n        expect(position).toEqual(expectedCommitPositionAfterParallel.get(commit2));\n      });\n    });\n  });\n  DEFAULT_GITGRAPH_CONFIG2.parallelCommits = false;\n  it(\"add\", () => {\n    commitPos.set(\"parent1\", { x: 1, y: 1 });\n    commitPos.set(\"parent2\", { x: 2, y: 2 });\n    commitPos.set(\"parent3\", { x: 3, y: 3 });\n    dir = \"LR\";\n    const parents = [\"parent1\", \"parent2\", \"parent3\"];\n    const closestParent = findClosestParent(parents);\n    expect(closestParent).toBe(\"parent3\");\n    commitPos.clear();\n  });\n}\n\n// src/diagrams/git/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .commit-id,\n  .commit-msg,\n  .branch-label {\n    fill: lightgrey;\n    color: lightgrey;\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n  }\n  ${[0, 1, 2, 3, 4, 5, 6, 7].map(\n  (i) => `\n        .branch-label${i} { fill: ${options[\"gitBranchLabel\" + i]}; }\n        .commit${i} { stroke: ${options[\"git\" + i]}; fill: ${options[\"git\" + i]}; }\n        .commit-highlight${i} { stroke: ${options[\"gitInv\" + i]}; fill: ${options[\"gitInv\" + i]}; }\n        .label${i}  { fill: ${options[\"git\" + i]}; }\n        .arrow${i} { stroke: ${options[\"git\" + i]}; }\n        `\n).join(\"\\n\")}\n\n  .branch {\n    stroke-width: 1;\n    stroke: ${options.lineColor};\n    stroke-dasharray: 2;\n  }\n  .commit-label { font-size: ${options.commitLabelFontSize}; fill: ${options.commitLabelColor};}\n  .commit-label-bkg { font-size: ${options.commitLabelFontSize}; fill: ${options.commitLabelBackground}; opacity: 0.5; }\n  .tag-label { font-size: ${options.tagLabelFontSize}; fill: ${options.tagLabelColor};}\n  .tag-label-bkg { fill: ${options.tagLabelBackground}; stroke: ${options.tagLabelBorder}; }\n  .tag-hole { fill: ${options.textColor}; }\n\n  .commit-merge {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n  }\n  .commit-reverse {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n    stroke-width: 3;\n  }\n  .commit-highlight-outer {\n  }\n  .commit-highlight-inner {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n  }\n\n  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}\n  .gitTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.textColor};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/git/gitGraphDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer: gitGraphRenderer_default,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "names": ["ImperativeState", "constructor", "init", "this", "records", "__name", "reset", "populateCommonDb", "ast", "db", "accDescr", "setAccDescription", "accTitle", "setAccTitle", "title", "setDiagramTitle", "commitType", "NORMAL", "REVERSE", "HIGHLIGHT", "MERGE", "CHERRY_PICK", "DEFAULT_GITGRAPH_CONFIG", "defaultConfig_default", "gitGraph", "getConfig3", "cleanAndMerge", "getConfig", "state", "config", "mainBranchName", "mainBranchOrder", "commits", "Map", "head", "branchConfig", "name", "order", "branches", "currBranch", "direction", "seq", "options", "getID", "random", "length", "uniqBy", "list", "fn", "recordMap", "Object", "create", "reduce", "out", "item", "key", "push", "setDirection", "dir2", "setOptions", "rawOptString", "log", "debug", "trim", "JSON", "parse", "e", "error", "message", "getOptions", "commit", "commitDB", "msg", "id", "type", "tags", "info", "common_default", "sanitizeText", "map", "tag", "newCommit", "parents", "branch", "set", "branchDB", "has", "Error", "checkout", "merge", "mergeDB", "otherBranch", "customId", "overrideType", "customTags", "currentBranchCheck", "get", "otherBranchCheck", "currentCommit", "otherCommit", "hash", "text", "token", "expected", "join", "verifiedBranch", "commit2", "customType", "cherryPick", "cherryPickDB", "sourceId", "targetId", "parentCommitId", "parent", "sourceCommit", "Array", "isArray", "includes", "sourceCommitBranch", "currentCommitId", "filter", "Boolean", "branch2", "upsert", "arr", "newVal", "index", "indexOf", "splice", "prettyPrintCommitHistory", "commitArr", "commit3", "line", "for<PERSON>ach", "c", "label", "<PERSON><PERSON><PERSON><PERSON>", "getCommitsArray", "clear2", "clear", "getBranchesAsObjArray", "values", "i", "parseFloat", "sort", "a", "b", "_ref", "getBranches", "getCommits", "o", "getCurrentBranch", "getDirection", "getHead", "getAccTitle", "getAccDescription", "getDiagramTitle", "populate", "db2", "dir", "statement", "statements", "parseStatement", "parser2", "Commit", "stmt", "parseCommit", "Branch", "parseBranch", "<PERSON><PERSON>", "parseMerge", "Checkout", "parseCheckout", "CherryPicking", "parseCherryPicking", "$type", "merge2", "checkout2", "cherryPicking", "parser", "async", "input", "DEFAULT_CONFIG", "getConfig2", "DEFAULT_GITGRAPH_CONFIG2", "LAYOUT_OFFSET", "COMMIT_STEP", "branchPos", "commitPos", "allCommitsDict", "lanes", "maxPos", "clear3", "drawText", "txt", "svgLabel", "document", "createElementNS", "split", "row", "tspan", "setAttributeNS", "setAttribute", "textContent", "append<PERSON><PERSON><PERSON>", "findClosestParent", "closestParent", "comparisonFunc", "targetPosition", "Infinity", "parentPosition", "y", "x", "findClosestParentBT", "maxPosition", "setParallelBTPos", "sortedKeys", "defaultPos2", "curPos", "roots", "calculateCommitPosition", "Math", "max", "setCommitPosition", "setRootPosition", "pos", "findClosestParentPos", "p", "closestParentPos", "drawCommitBullet", "gBullets", "commitPosition", "typeClass", "branchIndex", "commitSymbolType", "append", "attr", "circle", "circle2", "drawCommitLabel", "g<PERSON><PERSON><PERSON>", "showCommitLabel", "wrapper", "labelBkg", "insert", "bbox", "node", "getBBox", "posWithOffset", "width", "height", "rotateCommitLabel", "r_x", "r_y", "drawCommitTags", "yOffset", "maxTagBboxWidth", "maxTagBboxHeight", "tagElements", "tagValue", "reverse", "rect", "hole", "tagBbox", "yOffset2", "h2", "ly", "PX", "y<PERSON><PERSON><PERSON>", "getCommitClassType", "calculatePosition", "commitPos2", "defaultCommitPosition", "getCommitPosition", "isParallelCommits", "drawCommits", "svg", "modifyGraph", "keys", "parallelCommits", "sortKeys", "seqA", "seqB", "shouldRerouteArrow", "commitA", "commitB", "p1", "p2", "allCommits", "branchToGetCurve", "isOnBranchToGetCurve", "isBetweenCommits", "some", "commitX", "find<PERSON><PERSON>", "y1", "y2", "depth", "arguments", "undefined", "candidate", "abs", "every", "lane", "diff", "drawArrow", "arrowNeedsRerouting", "lineDef", "arc", "arc2", "radius", "offset", "colorClassNum", "lineY", "lineX", "drawArrows", "gArrows", "drawBranches", "g", "adjustIndexForTheme", "labelElement", "bkg", "setBranchPosition", "diagram", "renderer", "draw", "ver", "diagObj", "diagram2", "select", "branchLabel", "remove", "showBranches", "utils_default", "insertTitle", "titleTopMargin", "setupGraphViewbox", "diagramPadding", "useMaxWidth", "styles", "lineColor", "commitLabelFontSize", "commitLabelColor", "commitLabelBackground", "tagLabelFontSize", "tagLabelColor", "tagLabelBackground", "tagLabelBorder", "textColor", "primaryColor"], "sourceRoot": ""}