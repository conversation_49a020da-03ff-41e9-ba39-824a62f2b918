{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\imageUp\\\\EntityPopup.tsx\",\n  _s = $RefreshSig$();\n// EntityPopup.tsx\nimport React from 'react';\nimport { getEntityPopupStyles } from './EntityPopupStyles';\nimport HistoryAnalysisSection from './HistoryAnalysisSection';\nimport ConflictResolutionModal from './ConflictResolutionModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EntityPopup = ({\n  darkMode,\n  entityName,\n  position,\n  onClose,\n  onModify,\n  memoryAttributes = [],\n  memoryMethods = [],\n  extractedAttributes = [],\n  extractedMethods = [],\n  currentDiagramText\n}) => {\n  _s();\n  // État pour les checkboxes\n  const [selectedAttributes, setSelectedAttributes] = React.useState([]);\n  const [selectedMethods, setSelectedMethods] = React.useState([]);\n\n  // États pour l'analyse historique - Résolution de conflits supprimée\n\n  // Données de classe actuelle pour l'analyse historique\n  const currentClassData = React.useMemo(() => ({\n    name: entityName,\n    attributes: [...memoryAttributes, ...extractedAttributes],\n    methods: [...memoryMethods, ...extractedMethods]\n  }), [entityName, memoryAttributes, extractedAttributes, memoryMethods, extractedMethods]);\n\n  // Filtrer pour n'afficher que les attributs et méthodes qui ne sont pas dans le texte extrait\n  // Modifier la logique de filtrage pour être insensible à la casse\n  const uniqueAttributes = memoryAttributes.filter(attr => {\n    // Extraire le nom de l'attribut sans le type ou la visibilité\n    const attrName = attr.replace(/^[+-]?\\s*/, '').split(':')[0].trim().toLowerCase();\n    return !extractedAttributes.some(extractedAttr => {\n      // Extraire le nom de l'attribut extrait\n      const extractedAttrName = extractedAttr.replace(/^[+-]?\\s*/, '').split(':')[0].trim().toLowerCase();\n      return extractedAttrName === attrName;\n    });\n  });\n  const uniqueMethods = memoryMethods.filter(method => {\n    // Extraire le nom de la méthode sans les paramètres\n    const methodName = method.replace(/^[+-]?\\s*/, '').split('(')[0].trim().toLowerCase();\n    return !extractedMethods.some(extractedMethod => {\n      // Extraire le nom de la méthode extraite\n      const extractedMethodName = extractedMethod.replace(/^[+-]?\\s*/, '').split('(')[0].trim().toLowerCase();\n      return extractedMethodName === methodName;\n    });\n  });\n\n  // Gestionnaires pour les checkboxes\n  const handleAttributeToggle = attribute => {\n    setSelectedAttributes(prev => prev.includes(attribute) ? prev.filter(attr => attr !== attribute) : [...prev, attribute]);\n  };\n  const handleMethodToggle = method => {\n    setSelectedMethods(prev => prev.includes(method) ? prev.filter(m => m !== method) : [...prev, method]);\n  };\n\n  // Fonction pour gérer l'import depuis l'historique\n  const handleHistoryImport = importedData => {\n    // Import direct sans résolution de conflits\n    // Ajouter les éléments importés aux sélections actuelles\n    const newSelectedAttributes = [...selectedAttributes];\n    const newSelectedMethods = [...selectedMethods];\n\n    // Ajouter les attributs importés (éviter les doublons)\n    importedData.attributes.forEach(attr => {\n      if (!newSelectedAttributes.includes(attr)) {\n        newSelectedAttributes.push(attr);\n      }\n    });\n\n    // Ajouter les méthodes importées (éviter les doublons)\n    importedData.methods.forEach(method => {\n      if (!newSelectedMethods.includes(method)) {\n        newSelectedMethods.push(method);\n      }\n    });\n\n    // Mettre à jour les sélections\n    setSelectedAttributes(newSelectedAttributes);\n    setSelectedMethods(newSelectedMethods);\n  };\n\n  // Fonction pour appliquer les données importées\n  const applyImportedData = importedData => {\n    // Mettre à jour les sélections avec les nouvelles données\n    const combinedAttributes = [...selectedAttributes, ...importedData.attributes];\n    const combinedMethods = [...selectedMethods, ...importedData.methods];\n\n    // Supprimer les doublons manuellement\n    const newAttributes = combinedAttributes.filter((attr, index) => combinedAttributes.indexOf(attr) === index);\n    const newMethods = combinedMethods.filter((method, index) => combinedMethods.indexOf(method) === index);\n    setSelectedAttributes(newAttributes);\n    setSelectedMethods(newMethods);\n  };\n\n  // Fonction pour résoudre les conflits\n  const handleConflictResolution = resolvedData => {\n    applyImportedData(resolvedData);\n    setShowConflictModal(false);\n    setPendingImport(null);\n  };\n\n  // Fonction pour annuler la résolution de conflits\n  const handleConflictCancel = () => {\n    setShowConflictModal(false);\n    setPendingImport(null);\n  };\n\n  // Fonction pour gérer le clic sur le bouton Modifier\n  const handleModifyClick = () => {\n    onModify(selectedAttributes, selectedMethods);\n  };\n\n  // Gestionnaires d'événements pour les effets hover\n  const handleMouseEnter = e => {\n    if (e.target === e.currentTarget) {\n      e.target.style.backgroundColor = darkMode ? '#475569' : '#f3f4f6';\n    }\n  };\n  const handleMouseLeave = e => {\n    if (e.target === e.currentTarget) {\n      e.target.style.backgroundColor = darkMode ? '#334155' : '#f8fafc';\n    }\n  };\n  const handleModifyHover = (e, isEnter) => {\n    e.target.style.backgroundColor = isEnter ? '#2563eb' : '#3b82f6';\n    e.target.style.transform = isEnter ? 'translateY(-1px)' : 'translateY(0)';\n  };\n  const handleCancelHover = (e, isEnter) => {\n    e.target.style.backgroundColor = isEnter ? darkMode ? '#374151' : '#f9fafb' : 'transparent';\n  };\n\n  // Gérer le clic sur l'overlay pour fermer le popup\n  const handleOverlayClick = e => {\n    // Fermer seulement si on clique sur l'overlay, pas sur le popup\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  // Empêcher la propagation du clic depuis le popup vers l'overlay\n  const handlePopupClick = e => {\n    e.stopPropagation();\n  };\n\n  // Obtenir les styles\n  const styles = getEntityPopupStyles(darkMode);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.overlay,\n    onClick: handleOverlayClick,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.popup,\n      onClick: handlePopupClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.header,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: styles.title,\n            children: entityName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.subtitle,\n            children: \"UML IA propose plusieurs choix possibles pour cette entit\\xE9.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.closeButton,\n          onClick: onClose,\n          onMouseEnter: handleMouseEnter,\n          onMouseLeave: handleMouseLeave,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.content,\n        children: [/*#__PURE__*/_jsxDEV(HistoryAnalysisSection, {\n          darkMode: darkMode,\n          targetClassName: entityName,\n          currentClassData: currentClassData,\n          onImport: handleHistoryImport,\n          currentDiagramText: currentDiagramText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.tableContainer,\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            style: styles.table,\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              style: styles.tableHeader,\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  style: styles.tableHeaderCell,\n                  children: \"ATTRIBUTS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: styles.tableHeaderCell,\n                  children: \"M\\xC9THODES\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              style: styles.tableBody,\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                style: styles.tableRow,\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  style: styles.tableCell,\n                  children: uniqueAttributes.length > 0 ? uniqueAttributes.map((attr, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.checkboxContainer,\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      id: `attr-${index}`,\n                      checked: selectedAttributes.includes(attr),\n                      onChange: () => handleAttributeToggle(attr),\n                      style: styles.checkbox\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: `attr-${index}`,\n                      style: styles.checkboxLabel,\n                      children: attr\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 27\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 25\n                  }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.emptyState,\n                    children: \"Aucun attribut suppl\\xE9mentaire\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: styles.tableCell,\n                  children: uniqueMethods.length > 0 ? uniqueMethods.map((method, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.checkboxContainer,\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      id: `method-${index}`,\n                      checked: selectedMethods.includes(method),\n                      onChange: () => handleMethodToggle(method),\n                      style: styles.checkbox\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: `method-${index}`,\n                      style: styles.checkboxLabel,\n                      children: method\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 27\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 25\n                  }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.emptyState,\n                    children: \"Aucune m\\xE9thode suppl\\xE9mentaire\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.buttonContainer,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.modifyButton,\n            onClick: handleModifyClick,\n            onMouseEnter: e => handleModifyHover(e, true),\n            onMouseLeave: e => handleModifyHover(e, false),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u270F\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), \"Modifier l'entit\\xE9\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: styles.cancelButton,\n            onClick: onClose,\n            onMouseEnter: e => handleCancelHover(e, true),\n            onMouseLeave: e => handleCancelHover(e, false),\n            children: \"Annuler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), pendingImport && /*#__PURE__*/_jsxDEV(ConflictResolutionModal, {\n      darkMode: darkMode,\n      isOpen: showConflictModal,\n      currentClass: currentClassData,\n      importedClasses: [pendingImport.importedData],\n      conflicts: pendingImport.conflicts,\n      onResolve: handleConflictResolution,\n      onCancel: handleConflictCancel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n};\n_s(EntityPopup, \"1b+7cW6r3LY4YGBB+Tu6elFYmCk=\");\n_c = EntityPopup;\nexport default EntityPopup;\nvar _c;\n$RefreshReg$(_c, \"EntityPopup\");", "map": {"version": 3, "names": ["React", "getEntityPopupStyles", "HistoryAnalysisSection", "ConflictResolutionModal", "jsxDEV", "_jsxDEV", "EntityPopup", "darkMode", "entityName", "position", "onClose", "onModify", "memoryAttributes", "memoryMethods", "extractedAttributes", "extractedMethods", "currentDiagramText", "_s", "selectedAttributes", "setSelectedAttributes", "useState", "selectedMethods", "setSelectedMethods", "currentClassData", "useMemo", "name", "attributes", "methods", "uniqueAttributes", "filter", "attr", "attrName", "replace", "split", "trim", "toLowerCase", "some", "extractedAttr", "extractedAttrName", "uniqueMethods", "method", "methodName", "extractedMethod", "extractedMethodName", "handleAttributeToggle", "attribute", "prev", "includes", "handleMethodToggle", "m", "handleHistoryImport", "importedData", "newSelectedAttributes", "newSelectedMethods", "for<PERSON>ach", "push", "applyImportedData", "combinedAttributes", "combinedMethods", "newAttributes", "index", "indexOf", "newMethods", "handleConflictResolution", "resolvedData", "setShowConflictModal", "setPendingImport", "handleConflictCancel", "handleModifyClick", "handleMouseEnter", "e", "target", "currentTarget", "style", "backgroundColor", "handleMouseLeave", "handleModifyHover", "isEnter", "transform", "handleCancelHover", "handleOverlayClick", "handlePopupClick", "stopPropagation", "styles", "overlay", "onClick", "children", "popup", "header", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "subtitle", "closeButton", "onMouseEnter", "onMouseLeave", "content", "targetClassName", "onImport", "tableContainer", "table", "tableHeader", "tableHeaderCell", "tableBody", "tableRow", "tableCell", "length", "map", "checkboxContainer", "type", "id", "checked", "onChange", "checkbox", "htmlFor", "checkboxLabel", "emptyState", "buttonContainer", "modifyButton", "cancelButton", "pendingImport", "isOpen", "showConflictModal", "currentClass", "importedClasses", "conflicts", "onResolve", "onCancel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/imageUp/EntityPopup.tsx"], "sourcesContent": ["// EntityPopup.tsx\r\nimport React from 'react';\r\nimport { getEntityPopupStyles } from './EntityPopupStyles';\r\nimport HistoryAnalysisSection from './HistoryAnalysisSection';\r\nimport ConflictResolutionModal from './ConflictResolutionModal';\r\nimport { ClassData } from '../../services/HistoryAnalysisService';\r\n\r\ninterface EntityPopupProps {\r\n  darkMode: boolean;\r\n  entityName: string;\r\n  position: { x: number; y: number };\r\n  onClose: () => void;\r\n  onModify: (selectedAttributes: string[], selectedMethods: string[]) => void;\r\n  memoryAttributes: string[]; // Attributs de la classe depuis memoire.txt\r\n  memoryMethods: string[];    // Méthodes de la classe depuis memoire.txt\r\n  extractedAttributes: string[]; // Attributs extraits du texte actuel\r\n  extractedMethods: string[];    // Méthodes extraites du texte actuel\r\n  currentDiagramText?: string; // Texte du diagramme actuel pour exclure de l'analyse historique\r\n}\r\n\r\nconst EntityPopup: React.FC<EntityPopupProps> = ({\r\n  darkMode,\r\n  entityName,\r\n  position,\r\n  onClose,\r\n  onModify,\r\n  memoryAttributes = [],\r\n  memoryMethods = [],\r\n  extractedAttributes = [],\r\n  extractedMethods = [],\r\n  currentDiagramText\r\n}) => {\r\n  // État pour les checkboxes\r\n  const [selectedAttributes, setSelectedAttributes] = React.useState<string[]>([]);\r\n  const [selectedMethods, setSelectedMethods] = React.useState<string[]>([]);\r\n\r\n  // États pour l'analyse historique - Résolution de conflits supprimée\r\n\r\n  // Données de classe actuelle pour l'analyse historique\r\n  const currentClassData: ClassData = React.useMemo(() => ({\r\n    name: entityName,\r\n    attributes: [...memoryAttributes, ...extractedAttributes],\r\n    methods: [...memoryMethods, ...extractedMethods]\r\n  }), [entityName, memoryAttributes, extractedAttributes, memoryMethods, extractedMethods]);\r\n\r\n  // Filtrer pour n'afficher que les attributs et méthodes qui ne sont pas dans le texte extrait\r\n  // Modifier la logique de filtrage pour être insensible à la casse\r\n  const uniqueAttributes = memoryAttributes.filter(attr => {\r\n    // Extraire le nom de l'attribut sans le type ou la visibilité\r\n    const attrName = attr.replace(/^[+-]?\\s*/, '').split(':')[0].trim().toLowerCase();\r\n    \r\n    return !extractedAttributes.some(extractedAttr => {\r\n      // Extraire le nom de l'attribut extrait\r\n      const extractedAttrName = extractedAttr.replace(/^[+-]?\\s*/, '').split(':')[0].trim().toLowerCase();\r\n      return extractedAttrName === attrName;\r\n    });\r\n  });\r\n\r\n  const uniqueMethods = memoryMethods.filter(method => {\r\n    // Extraire le nom de la méthode sans les paramètres\r\n    const methodName = method.replace(/^[+-]?\\s*/, '').split('(')[0].trim().toLowerCase();\r\n    \r\n    return !extractedMethods.some(extractedMethod => {\r\n      // Extraire le nom de la méthode extraite\r\n      const extractedMethodName = extractedMethod.replace(/^[+-]?\\s*/, '').split('(')[0].trim().toLowerCase();\r\n      return extractedMethodName === methodName;\r\n    });\r\n  });\r\n\r\n  // Gestionnaires pour les checkboxes\r\n  const handleAttributeToggle = (attribute: string) => {\r\n    setSelectedAttributes(prev => \r\n      prev.includes(attribute) \r\n        ? prev.filter(attr => attr !== attribute) \r\n        : [...prev, attribute]\r\n    );\r\n  };\r\n\r\n  const handleMethodToggle = (method: string) => {\r\n    setSelectedMethods(prev =>\r\n      prev.includes(method)\r\n        ? prev.filter(m => m !== method)\r\n        : [...prev, method]\r\n    );\r\n  };\r\n\r\n  // Fonction pour gérer l'import depuis l'historique\r\n  const handleHistoryImport = (importedData: ClassData) => {\r\n    // Import direct sans résolution de conflits\r\n    // Ajouter les éléments importés aux sélections actuelles\r\n    const newSelectedAttributes = [...selectedAttributes];\r\n    const newSelectedMethods = [...selectedMethods];\r\n\r\n    // Ajouter les attributs importés (éviter les doublons)\r\n    importedData.attributes.forEach(attr => {\r\n      if (!newSelectedAttributes.includes(attr)) {\r\n        newSelectedAttributes.push(attr);\r\n      }\r\n    });\r\n\r\n    // Ajouter les méthodes importées (éviter les doublons)\r\n    importedData.methods.forEach(method => {\r\n      if (!newSelectedMethods.includes(method)) {\r\n        newSelectedMethods.push(method);\r\n      }\r\n    });\r\n\r\n    // Mettre à jour les sélections\r\n    setSelectedAttributes(newSelectedAttributes);\r\n    setSelectedMethods(newSelectedMethods);\r\n  };\r\n\r\n  // Fonction pour appliquer les données importées\r\n  const applyImportedData = (importedData: ClassData) => {\r\n    // Mettre à jour les sélections avec les nouvelles données\r\n    const combinedAttributes = [...selectedAttributes, ...importedData.attributes];\r\n    const combinedMethods = [...selectedMethods, ...importedData.methods];\r\n\r\n    // Supprimer les doublons manuellement\r\n    const newAttributes = combinedAttributes.filter((attr, index) =>\r\n      combinedAttributes.indexOf(attr) === index\r\n    );\r\n    const newMethods = combinedMethods.filter((method, index) =>\r\n      combinedMethods.indexOf(method) === index\r\n    );\r\n\r\n    setSelectedAttributes(newAttributes);\r\n    setSelectedMethods(newMethods);\r\n  };\r\n\r\n  // Fonction pour résoudre les conflits\r\n  const handleConflictResolution = (resolvedData: ClassData) => {\r\n    applyImportedData(resolvedData);\r\n    setShowConflictModal(false);\r\n    setPendingImport(null);\r\n  };\r\n\r\n  // Fonction pour annuler la résolution de conflits\r\n  const handleConflictCancel = () => {\r\n    setShowConflictModal(false);\r\n    setPendingImport(null);\r\n  };\r\n  \r\n  // Fonction pour gérer le clic sur le bouton Modifier\r\n  const handleModifyClick = () => {\r\n    onModify(selectedAttributes, selectedMethods);\r\n  };\r\n\r\n  // Gestionnaires d'événements pour les effets hover\r\n  const handleMouseEnter = (e: React.MouseEvent) => {\r\n    if (e.target === e.currentTarget) {\r\n      (e.target as HTMLButtonElement).style.backgroundColor = darkMode ? '#475569' : '#f3f4f6';\r\n    }\r\n  };\r\n\r\n  const handleMouseLeave = (e: React.MouseEvent) => {\r\n    if (e.target === e.currentTarget) {\r\n      (e.target as HTMLButtonElement).style.backgroundColor = darkMode ? '#334155' : '#f8fafc';\r\n    }\r\n  };\r\n\r\n  const handleModifyHover = (e: React.MouseEvent, isEnter: boolean) => {\r\n    (e.target as HTMLButtonElement).style.backgroundColor = isEnter ? '#2563eb' : '#3b82f6';\r\n    (e.target as HTMLButtonElement).style.transform = isEnter ? 'translateY(-1px)' : 'translateY(0)';\r\n  };\r\n\r\n  const handleCancelHover = (e: React.MouseEvent, isEnter: boolean) => {\r\n    (e.target as HTMLButtonElement).style.backgroundColor = isEnter \r\n      ? (darkMode ? '#374151' : '#f9fafb') \r\n      : 'transparent';\r\n  };\r\n\r\n  // Gérer le clic sur l'overlay pour fermer le popup\r\n  const handleOverlayClick = (e: React.MouseEvent) => {\r\n    // Fermer seulement si on clique sur l'overlay, pas sur le popup\r\n    if (e.target === e.currentTarget) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  // Empêcher la propagation du clic depuis le popup vers l'overlay\r\n  const handlePopupClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n  };\r\n\r\n  // Obtenir les styles\r\n  const styles = getEntityPopupStyles(darkMode);\r\n\r\n  return (\r\n    <div style={styles.overlay} onClick={handleOverlayClick}>\r\n      <div style={styles.popup} onClick={handlePopupClick}>\r\n        <div style={styles.header}>\r\n          <div>\r\n            <h3 style={styles.title}>{entityName}</h3>\r\n            <div style={styles.subtitle}>UML IA propose plusieurs choix possibles pour cette entité.</div>\r\n          </div>\r\n          <button \r\n            style={styles.closeButton} \r\n            onClick={onClose}\r\n            onMouseEnter={handleMouseEnter}\r\n            onMouseLeave={handleMouseLeave}\r\n          >\r\n            ×\r\n          </button>\r\n        </div>\r\n\r\n        <div style={styles.content}>\r\n          {/* Section d'analyse historique */}\r\n          <HistoryAnalysisSection\r\n            darkMode={darkMode}\r\n            targetClassName={entityName}\r\n            currentClassData={currentClassData}\r\n            onImport={handleHistoryImport}\r\n            currentDiagramText={currentDiagramText}\r\n          />\r\n          <div style={styles.tableContainer}>\r\n            <table style={styles.table}>\r\n              <thead style={styles.tableHeader}>\r\n                <tr>\r\n                  <th style={styles.tableHeaderCell}>ATTRIBUTS</th>\r\n                  <th style={styles.tableHeaderCell}>MÉTHODES</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody style={styles.tableBody}>\r\n                <tr style={styles.tableRow}>\r\n                  <td style={styles.tableCell}>\r\n                    {uniqueAttributes.length > 0 ? (\r\n                      uniqueAttributes.map((attr, index) => (\r\n                        <div key={index} style={styles.checkboxContainer}>\r\n                          <input \r\n                            type=\"checkbox\" \r\n                            id={`attr-${index}`}\r\n                            checked={selectedAttributes.includes(attr)}\r\n                            onChange={() => handleAttributeToggle(attr)}\r\n                            style={styles.checkbox}\r\n                          />\r\n                          <label \r\n                            htmlFor={`attr-${index}`}\r\n                            style={styles.checkboxLabel}\r\n                          >\r\n                            {attr}\r\n                          </label>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <div style={styles.emptyState}>\r\n                        Aucun attribut supplémentaire\r\n                      </div>\r\n                    )}\r\n                  </td>\r\n                  <td style={styles.tableCell}>\r\n                    {uniqueMethods.length > 0 ? (\r\n                      uniqueMethods.map((method, index) => (\r\n                        <div key={index} style={styles.checkboxContainer}>\r\n                          <input \r\n                            type=\"checkbox\" \r\n                            id={`method-${index}`}\r\n                            checked={selectedMethods.includes(method)}\r\n                            onChange={() => handleMethodToggle(method)}\r\n                            style={styles.checkbox}\r\n                          />\r\n                          <label \r\n                            htmlFor={`method-${index}`}\r\n                            style={styles.checkboxLabel}\r\n                          >\r\n                            {method}\r\n                          </label>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <div style={styles.emptyState}>\r\n                        Aucune méthode supplémentaire\r\n                      </div>\r\n                    )}\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n          \r\n          <div style={styles.buttonContainer}>\r\n            <button \r\n              style={styles.modifyButton} \r\n              onClick={handleModifyClick}\r\n              onMouseEnter={(e) => handleModifyHover(e, true)}\r\n              onMouseLeave={(e) => handleModifyHover(e, false)}\r\n            >\r\n              <span>✏️</span>\r\n              Modifier l'entité\r\n            </button>\r\n            <button \r\n              style={styles.cancelButton} \r\n              onClick={onClose}\r\n              onMouseEnter={(e) => handleCancelHover(e, true)}\r\n              onMouseLeave={(e) => handleCancelHover(e, false)}\r\n            >\r\n              Annuler\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Modal de résolution de conflits */}\r\n      {pendingImport && (\r\n        <ConflictResolutionModal\r\n          darkMode={darkMode}\r\n          isOpen={showConflictModal}\r\n          currentClass={currentClassData}\r\n          importedClasses={[pendingImport.importedData]}\r\n          conflicts={pendingImport.conflicts}\r\n          onResolve={handleConflictResolution}\r\n          onCancel={handleConflictCancel}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EntityPopup;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,uBAAuB,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAgBhE,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,QAAQ;EACRC,UAAU;EACVC,QAAQ;EACRC,OAAO;EACPC,QAAQ;EACRC,gBAAgB,GAAG,EAAE;EACrBC,aAAa,GAAG,EAAE;EAClBC,mBAAmB,GAAG,EAAE;EACxBC,gBAAgB,GAAG,EAAE;EACrBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnB,KAAK,CAACoB,QAAQ,CAAW,EAAE,CAAC;EAChF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,KAAK,CAACoB,QAAQ,CAAW,EAAE,CAAC;;EAE1E;;EAEA;EACA,MAAMG,gBAA2B,GAAGvB,KAAK,CAACwB,OAAO,CAAC,OAAO;IACvDC,IAAI,EAAEjB,UAAU;IAChBkB,UAAU,EAAE,CAAC,GAAGd,gBAAgB,EAAE,GAAGE,mBAAmB,CAAC;IACzDa,OAAO,EAAE,CAAC,GAAGd,aAAa,EAAE,GAAGE,gBAAgB;EACjD,CAAC,CAAC,EAAE,CAACP,UAAU,EAAEI,gBAAgB,EAAEE,mBAAmB,EAAED,aAAa,EAAEE,gBAAgB,CAAC,CAAC;;EAEzF;EACA;EACA,MAAMa,gBAAgB,GAAGhB,gBAAgB,CAACiB,MAAM,CAACC,IAAI,IAAI;IACvD;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACE,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAEjF,OAAO,CAACrB,mBAAmB,CAACsB,IAAI,CAACC,aAAa,IAAI;MAChD;MACA,MAAMC,iBAAiB,GAAGD,aAAa,CAACL,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnG,OAAOG,iBAAiB,KAAKP,QAAQ;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,MAAMQ,aAAa,GAAG1B,aAAa,CAACgB,MAAM,CAACW,MAAM,IAAI;IACnD;IACA,MAAMC,UAAU,GAAGD,MAAM,CAACR,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAErF,OAAO,CAACpB,gBAAgB,CAACqB,IAAI,CAACM,eAAe,IAAI;MAC/C;MACA,MAAMC,mBAAmB,GAAGD,eAAe,CAACV,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACvG,OAAOQ,mBAAmB,KAAKF,UAAU;IAC3C,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA,MAAMG,qBAAqB,GAAIC,SAAiB,IAAK;IACnD1B,qBAAqB,CAAC2B,IAAI,IACxBA,IAAI,CAACC,QAAQ,CAACF,SAAS,CAAC,GACpBC,IAAI,CAACjB,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKe,SAAS,CAAC,GACvC,CAAC,GAAGC,IAAI,EAAED,SAAS,CACzB,CAAC;EACH,CAAC;EAED,MAAMG,kBAAkB,GAAIR,MAAc,IAAK;IAC7ClB,kBAAkB,CAACwB,IAAI,IACrBA,IAAI,CAACC,QAAQ,CAACP,MAAM,CAAC,GACjBM,IAAI,CAACjB,MAAM,CAACoB,CAAC,IAAIA,CAAC,KAAKT,MAAM,CAAC,GAC9B,CAAC,GAAGM,IAAI,EAAEN,MAAM,CACtB,CAAC;EACH,CAAC;;EAED;EACA,MAAMU,mBAAmB,GAAIC,YAAuB,IAAK;IACvD;IACA;IACA,MAAMC,qBAAqB,GAAG,CAAC,GAAGlC,kBAAkB,CAAC;IACrD,MAAMmC,kBAAkB,GAAG,CAAC,GAAGhC,eAAe,CAAC;;IAE/C;IACA8B,YAAY,CAACzB,UAAU,CAAC4B,OAAO,CAACxB,IAAI,IAAI;MACtC,IAAI,CAACsB,qBAAqB,CAACL,QAAQ,CAACjB,IAAI,CAAC,EAAE;QACzCsB,qBAAqB,CAACG,IAAI,CAACzB,IAAI,CAAC;MAClC;IACF,CAAC,CAAC;;IAEF;IACAqB,YAAY,CAACxB,OAAO,CAAC2B,OAAO,CAACd,MAAM,IAAI;MACrC,IAAI,CAACa,kBAAkB,CAACN,QAAQ,CAACP,MAAM,CAAC,EAAE;QACxCa,kBAAkB,CAACE,IAAI,CAACf,MAAM,CAAC;MACjC;IACF,CAAC,CAAC;;IAEF;IACArB,qBAAqB,CAACiC,qBAAqB,CAAC;IAC5C9B,kBAAkB,CAAC+B,kBAAkB,CAAC;EACxC,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAIL,YAAuB,IAAK;IACrD;IACA,MAAMM,kBAAkB,GAAG,CAAC,GAAGvC,kBAAkB,EAAE,GAAGiC,YAAY,CAACzB,UAAU,CAAC;IAC9E,MAAMgC,eAAe,GAAG,CAAC,GAAGrC,eAAe,EAAE,GAAG8B,YAAY,CAACxB,OAAO,CAAC;;IAErE;IACA,MAAMgC,aAAa,GAAGF,kBAAkB,CAAC5B,MAAM,CAAC,CAACC,IAAI,EAAE8B,KAAK,KAC1DH,kBAAkB,CAACI,OAAO,CAAC/B,IAAI,CAAC,KAAK8B,KACvC,CAAC;IACD,MAAME,UAAU,GAAGJ,eAAe,CAAC7B,MAAM,CAAC,CAACW,MAAM,EAAEoB,KAAK,KACtDF,eAAe,CAACG,OAAO,CAACrB,MAAM,CAAC,KAAKoB,KACtC,CAAC;IAEDzC,qBAAqB,CAACwC,aAAa,CAAC;IACpCrC,kBAAkB,CAACwC,UAAU,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,YAAuB,IAAK;IAC5DR,iBAAiB,CAACQ,YAAY,CAAC;IAC/BC,oBAAoB,CAAC,KAAK,CAAC;IAC3BC,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCF,oBAAoB,CAAC,KAAK,CAAC;IAC3BC,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzD,QAAQ,CAACO,kBAAkB,EAAEG,eAAe,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMgD,gBAAgB,GAAIC,CAAmB,IAAK;IAChD,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAC/BF,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,GAAGnE,QAAQ,GAAG,SAAS,GAAG,SAAS;IAC1F;EACF,CAAC;EAED,MAAMoE,gBAAgB,GAAIL,CAAmB,IAAK;IAChD,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAC/BF,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,GAAGnE,QAAQ,GAAG,SAAS,GAAG,SAAS;IAC1F;EACF,CAAC;EAED,MAAMqE,iBAAiB,GAAGA,CAACN,CAAmB,EAAEO,OAAgB,KAAK;IAClEP,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,GAAGG,OAAO,GAAG,SAAS,GAAG,SAAS;IACtFP,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACK,SAAS,GAAGD,OAAO,GAAG,kBAAkB,GAAG,eAAe;EAClG,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAACT,CAAmB,EAAEO,OAAgB,KAAK;IAClEP,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,GAAGG,OAAO,GAC1DtE,QAAQ,GAAG,SAAS,GAAG,SAAS,GACjC,aAAa;EACnB,CAAC;;EAED;EACA,MAAMyE,kBAAkB,GAAIV,CAAmB,IAAK;IAClD;IACA,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAChC9D,OAAO,CAAC,CAAC;IACX;EACF,CAAC;;EAED;EACA,MAAMuE,gBAAgB,GAAIX,CAAmB,IAAK;IAChDA,CAAC,CAACY,eAAe,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMC,MAAM,GAAGlF,oBAAoB,CAACM,QAAQ,CAAC;EAE7C,oBACEF,OAAA;IAAKoE,KAAK,EAAEU,MAAM,CAACC,OAAQ;IAACC,OAAO,EAAEL,kBAAmB;IAAAM,QAAA,gBACtDjF,OAAA;MAAKoE,KAAK,EAAEU,MAAM,CAACI,KAAM;MAACF,OAAO,EAAEJ,gBAAiB;MAAAK,QAAA,gBAClDjF,OAAA;QAAKoE,KAAK,EAAEU,MAAM,CAACK,MAAO;QAAAF,QAAA,gBACxBjF,OAAA;UAAAiF,QAAA,gBACEjF,OAAA;YAAIoE,KAAK,EAAEU,MAAM,CAACM,KAAM;YAAAH,QAAA,EAAE9E;UAAU;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1CxF,OAAA;YAAKoE,KAAK,EAAEU,MAAM,CAACW,QAAS;YAAAR,QAAA,EAAC;UAA2D;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC,eACNxF,OAAA;UACEoE,KAAK,EAAEU,MAAM,CAACY,WAAY;UAC1BV,OAAO,EAAE3E,OAAQ;UACjBsF,YAAY,EAAE3B,gBAAiB;UAC/B4B,YAAY,EAAEtB,gBAAiB;UAAAW,QAAA,EAChC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxF,OAAA;QAAKoE,KAAK,EAAEU,MAAM,CAACe,OAAQ;QAAAZ,QAAA,gBAEzBjF,OAAA,CAACH,sBAAsB;UACrBK,QAAQ,EAAEA,QAAS;UACnB4F,eAAe,EAAE3F,UAAW;UAC5Be,gBAAgB,EAAEA,gBAAiB;UACnC6E,QAAQ,EAAElD,mBAAoB;UAC9BlC,kBAAkB,EAAEA;QAAmB;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACFxF,OAAA;UAAKoE,KAAK,EAAEU,MAAM,CAACkB,cAAe;UAAAf,QAAA,eAChCjF,OAAA;YAAOoE,KAAK,EAAEU,MAAM,CAACmB,KAAM;YAAAhB,QAAA,gBACzBjF,OAAA;cAAOoE,KAAK,EAAEU,MAAM,CAACoB,WAAY;cAAAjB,QAAA,eAC/BjF,OAAA;gBAAAiF,QAAA,gBACEjF,OAAA;kBAAIoE,KAAK,EAAEU,MAAM,CAACqB,eAAgB;kBAAAlB,QAAA,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDxF,OAAA;kBAAIoE,KAAK,EAAEU,MAAM,CAACqB,eAAgB;kBAAAlB,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRxF,OAAA;cAAOoE,KAAK,EAAEU,MAAM,CAACsB,SAAU;cAAAnB,QAAA,eAC7BjF,OAAA;gBAAIoE,KAAK,EAAEU,MAAM,CAACuB,QAAS;gBAAApB,QAAA,gBACzBjF,OAAA;kBAAIoE,KAAK,EAAEU,MAAM,CAACwB,SAAU;kBAAArB,QAAA,EACzB1D,gBAAgB,CAACgF,MAAM,GAAG,CAAC,GAC1BhF,gBAAgB,CAACiF,GAAG,CAAC,CAAC/E,IAAI,EAAE8B,KAAK,kBAC/BvD,OAAA;oBAAiBoE,KAAK,EAAEU,MAAM,CAAC2B,iBAAkB;oBAAAxB,QAAA,gBAC/CjF,OAAA;sBACE0G,IAAI,EAAC,UAAU;sBACfC,EAAE,EAAE,QAAQpD,KAAK,EAAG;sBACpBqD,OAAO,EAAE/F,kBAAkB,CAAC6B,QAAQ,CAACjB,IAAI,CAAE;sBAC3CoF,QAAQ,EAAEA,CAAA,KAAMtE,qBAAqB,CAACd,IAAI,CAAE;sBAC5C2C,KAAK,EAAEU,MAAM,CAACgC;oBAAS;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACFxF,OAAA;sBACE+G,OAAO,EAAE,QAAQxD,KAAK,EAAG;sBACzBa,KAAK,EAAEU,MAAM,CAACkC,aAAc;sBAAA/B,QAAA,EAE3BxD;oBAAI;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA,GAbAjC,KAAK;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAcV,CACN,CAAC,gBAEFxF,OAAA;oBAAKoE,KAAK,EAAEU,MAAM,CAACmC,UAAW;oBAAAhC,QAAA,EAAC;kBAE/B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACLxF,OAAA;kBAAIoE,KAAK,EAAEU,MAAM,CAACwB,SAAU;kBAAArB,QAAA,EACzB/C,aAAa,CAACqE,MAAM,GAAG,CAAC,GACvBrE,aAAa,CAACsE,GAAG,CAAC,CAACrE,MAAM,EAAEoB,KAAK,kBAC9BvD,OAAA;oBAAiBoE,KAAK,EAAEU,MAAM,CAAC2B,iBAAkB;oBAAAxB,QAAA,gBAC/CjF,OAAA;sBACE0G,IAAI,EAAC,UAAU;sBACfC,EAAE,EAAE,UAAUpD,KAAK,EAAG;sBACtBqD,OAAO,EAAE5F,eAAe,CAAC0B,QAAQ,CAACP,MAAM,CAAE;sBAC1C0E,QAAQ,EAAEA,CAAA,KAAMlE,kBAAkB,CAACR,MAAM,CAAE;sBAC3CiC,KAAK,EAAEU,MAAM,CAACgC;oBAAS;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACFxF,OAAA;sBACE+G,OAAO,EAAE,UAAUxD,KAAK,EAAG;sBAC3Ba,KAAK,EAAEU,MAAM,CAACkC,aAAc;sBAAA/B,QAAA,EAE3B9C;oBAAM;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA,GAbAjC,KAAK;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAcV,CACN,CAAC,gBAEFxF,OAAA;oBAAKoE,KAAK,EAAEU,MAAM,CAACmC,UAAW;oBAAAhC,QAAA,EAAC;kBAE/B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENxF,OAAA;UAAKoE,KAAK,EAAEU,MAAM,CAACoC,eAAgB;UAAAjC,QAAA,gBACjCjF,OAAA;YACEoE,KAAK,EAAEU,MAAM,CAACqC,YAAa;YAC3BnC,OAAO,EAAEjB,iBAAkB;YAC3B4B,YAAY,EAAG1B,CAAC,IAAKM,iBAAiB,CAACN,CAAC,EAAE,IAAI,CAAE;YAChD2B,YAAY,EAAG3B,CAAC,IAAKM,iBAAiB,CAACN,CAAC,EAAE,KAAK,CAAE;YAAAgB,QAAA,gBAEjDjF,OAAA;cAAAiF,QAAA,EAAM;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,wBAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxF,OAAA;YACEoE,KAAK,EAAEU,MAAM,CAACsC,YAAa;YAC3BpC,OAAO,EAAE3E,OAAQ;YACjBsF,YAAY,EAAG1B,CAAC,IAAKS,iBAAiB,CAACT,CAAC,EAAE,IAAI,CAAE;YAChD2B,YAAY,EAAG3B,CAAC,IAAKS,iBAAiB,CAACT,CAAC,EAAE,KAAK,CAAE;YAAAgB,QAAA,EAClD;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL6B,aAAa,iBACZrH,OAAA,CAACF,uBAAuB;MACtBI,QAAQ,EAAEA,QAAS;MACnBoH,MAAM,EAAEC,iBAAkB;MAC1BC,YAAY,EAAEtG,gBAAiB;MAC/BuG,eAAe,EAAE,CAACJ,aAAa,CAACvE,YAAY,CAAE;MAC9C4E,SAAS,EAAEL,aAAa,CAACK,SAAU;MACnCC,SAAS,EAAEjE,wBAAyB;MACpCkE,QAAQ,EAAE9D;IAAqB;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAxSIX,WAAuC;AAAA4H,EAAA,GAAvC5H,WAAuC;AA0S7C,eAAeA,WAAW;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}