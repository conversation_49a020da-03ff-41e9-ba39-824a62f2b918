{"version": 3, "file": "static/js/75.94918338.chunk.js", "mappings": "+NAoBIA,EAAU,CACZC,OAAQC,EAAAA,GACR,MAAIC,GACF,OAAO,IAAIC,EAAAA,GAAQ,EACrB,EACAC,SAAUC,EAAAA,GACVC,OAAQC,EAAAA,GACRC,MAAsBC,EAAAA,EAAAA,KAAQC,IACvBA,EAAIC,QACPD,EAAIC,MAAQ,CAAC,GAEfD,EAAIC,MAAMC,oBAAsBF,EAAIE,mBAAmB,GACtD,Q", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-YXO3MK2T.mjs"], "sourcesContent": ["import {\n  StateDB,\n  stateDiagram_default,\n  stateRenderer_v3_unified_default,\n  styles_default\n} from \"./chunk-AEK57VVT.mjs\";\nimport \"./chunk-RZ5BOZE2.mjs\";\nimport \"./chunk-TYCBKAJE.mjs\";\nimport \"./chunk-IIMUDSI4.mjs\";\nimport \"./chunk-VV3M67IP.mjs\";\nimport \"./chunk-HRU6DDCH.mjs\";\nimport \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/state/stateDiagram-v2.ts\nvar diagram = {\n  parser: stateDiagram_default,\n  get db() {\n    return new StateDB(2);\n  },\n  renderer: stateRenderer_v3_unified_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "names": ["diagram", "parser", "stateDiagram_default", "db", "StateDB", "renderer", "stateRenderer_v3_unified_default", "styles", "styles_default", "init", "__name", "cnf", "state", "arrowMarkerAbsolute"], "sourceRoot": ""}