{"ast": null, "code": "// HistoryAnalysisSection.tsx - Composant pour l'analyse historique et l'import de classes\nimport React,{useState,useEffect}from'react';import{useHistory}from'../../context/HistoryContext';import{useAuth}from'../../context/AuthContext';import{HistoryAnalysisService}from'../../services/HistoryAnalysisService';import{ChevronDown,Eye,Clock,Zap,AlertTriangle,Shield,Users,Lock}from'lucide-react';import'./HistoryAnalysisSection.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const HistoryAnalysisSection=_ref=>{let{darkMode,targetClassName,currentClassData,onImport,currentDiagramText}=_ref;const{historyItems}=useHistory();const{currentUser}=useAuth();const[isExpanded,setIsExpanded]=useState(false);const[matches,setMatches]=useState([]);const[selectedMatches,setSelectedMatches]=useState(new Set());const[selectedAttributes,setSelectedAttributes]=useState(new Map());const[selectedMethods,setSelectedMethods]=useState(new Map());const[sortOption,setSortOption]=useState(HistoryAnalysisService.getSortOptions()[0]);const[showPreview,setShowPreview]=useState(null);const[conflicts,setConflicts]=useState({attributes:[],methods:[]});const[previewData,setPreviewData]=useState(null);// Rechercher les correspondances lors du changement de classe cible\nuseEffect(()=>{if(!targetClassName||!currentUser)return;const foundMatches=HistoryAnalysisService.findMatchingDiagrams(targetClassName,historyItems,currentUser.uid,currentDiagramText);const sortedMatches=HistoryAnalysisService.sortMatches(foundMatches,sortOption);setMatches(sortedMatches);},[targetClassName,historyItems,currentUser,sortOption,currentDiagramText]);// Détecter les conflits lors du changement de sélection\nuseEffect(()=>{const selectedMatchObjects=matches.filter(match=>selectedMatches.has(match.historyItem.id));const selectedClasses=selectedMatchObjects.flatMap(match=>match.matchingClasses);if(selectedClasses.length>0){const detectedConflicts=HistoryAnalysisService.detectConflicts(currentClassData,selectedClasses);setConflicts(detectedConflicts);}else{setConflicts({attributes:[],methods:[]});}},[selectedMatches,matches,currentClassData]);const handleToggleExpand=()=>{setIsExpanded(!isExpanded);};const handleMatchSelection=(matchId,selected)=>{const newSelection=new Set(selectedMatches);if(selected){newSelection.add(matchId);}else{newSelection.delete(matchId);// Nettoyer les sélections d'attributs/méthodes pour ce match\nconst newSelectedAttributes=new Map(selectedAttributes);const newSelectedMethods=new Map(selectedMethods);newSelectedAttributes.delete(matchId);newSelectedMethods.delete(matchId);setSelectedAttributes(newSelectedAttributes);setSelectedMethods(newSelectedMethods);}setSelectedMatches(newSelection);};const handleAttributeSelection=(matchId,attribute,selected)=>{const newSelectedAttributes=new Map(selectedAttributes);const currentAttributes=newSelectedAttributes.get(matchId)||new Set();if(selected){currentAttributes.add(attribute);}else{currentAttributes.delete(attribute);}if(currentAttributes.size>0){newSelectedAttributes.set(matchId,currentAttributes);}else{newSelectedAttributes.delete(matchId);}setSelectedAttributes(newSelectedAttributes);};const handleMethodSelection=(matchId,method,selected)=>{const newSelectedMethods=new Map(selectedMethods);const currentMethods=newSelectedMethods.get(matchId)||new Set();if(selected){currentMethods.add(method);}else{currentMethods.delete(method);}if(currentMethods.size>0){newSelectedMethods.set(matchId,currentMethods);}else{newSelectedMethods.delete(matchId);}setSelectedMethods(newSelectedMethods);};const handleImport=()=>{// Construire les données à importer basées sur les sélections granulaires\nconst attributesToImport=[];const methodsToImport=[];// Collecter tous les attributs et méthodes sélectionnés\nselectedAttributes.forEach((attributes,matchId)=>{attributes.forEach(attr=>{if(!attributesToImport.includes(attr)){attributesToImport.push(attr);}});});selectedMethods.forEach((methods,matchId)=>{methods.forEach(method=>{if(!methodsToImport.includes(method)){methodsToImport.push(method);}});});if(attributesToImport.length>0||methodsToImport.length>0){const importedData={name:currentClassData.name,attributes:attributesToImport,methods:methodsToImport};onImport(importedData);// Reset toutes les sélections\nsetSelectedMatches(new Set());setSelectedAttributes(new Map());setSelectedMethods(new Map());}};const handlePreview=matchId=>{if(showPreview===matchId){setShowPreview(null);setPreviewData(null);}else{const match=matches.find(m=>m.historyItem.id===matchId);setShowPreview(matchId);setPreviewData(match||null);}};const getAccessIcon=match=>{const accessLevel=HistoryAnalysisService.getAccessLevel(match.historyItem,(currentUser===null||currentUser===void 0?void 0:currentUser.uid)||'');switch(accessLevel){case'owner':return/*#__PURE__*/_jsx(\"span\",{title:\"Votre diagramme\",children:/*#__PURE__*/_jsx(Shield,{size:12,color:\"#10b981\"})});case'shared':return/*#__PURE__*/_jsx(\"span\",{title:\"Diagramme partag\\xE9\",children:/*#__PURE__*/_jsx(Users,{size:12,color:\"#f59e0b\"})});case'public':return/*#__PURE__*/_jsx(\"span\",{title:\"Diagramme public\",children:/*#__PURE__*/_jsx(Eye,{size:12,color:\"#3b82f6\"})});default:return/*#__PURE__*/_jsx(\"span\",{title:\"Acc\\xE8s restreint\",children:/*#__PURE__*/_jsx(Lock,{size:12,color:\"#ef4444\"})});}};const getSectionStyles=()=>({container:{marginBottom:'24px',border:`1px solid ${darkMode?'rgba(59, 130, 246, 0.2)':'rgba(59, 130, 246, 0.1)'}`,borderRadius:'12px',background:darkMode?'linear-gradient(135deg, rgba(10, 15, 28, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)':'linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%)',backdropFilter:'blur(10px)',WebkitBackdropFilter:'blur(10px)'},header:{display:'flex',alignItems:'center',justifyContent:'space-between',padding:'16px 20px',cursor:'pointer',borderBottom:isExpanded?`1px solid ${darkMode?'rgba(59, 130, 246, 0.15)':'rgba(59, 130, 246, 0.08)'}`:'none'},headerLeft:{display:'flex',alignItems:'center',gap:'12px'},title:{fontSize:'16px',fontWeight:'600',color:darkMode?'#f8fafc':'#0f172a',margin:0},badge:{backgroundColor:matches.length>0?'#3b82f6':darkMode?'#64748b':'#94a3b8',color:'#ffffff',fontSize:'12px',fontWeight:'600',padding:'4px 8px',borderRadius:'12px',minWidth:'20px',textAlign:'center'},expandIcon:{color:darkMode?'#94a3b8':'#64748b',transition:'transform 0.2s ease',transform:isExpanded?'rotate(180deg)':'rotate(0deg)'},content:{padding:isExpanded?'20px':'0',maxHeight:isExpanded?'400px':'0',overflow:'hidden',transition:'all 0.3s ease'},sortContainer:{display:'flex',alignItems:'center',gap:'12px',marginBottom:'16px'},sortLabel:{fontSize:'14px',color:darkMode?'#cbd5e1':'#64748b',fontWeight:'500'},sortSelect:{backgroundColor:darkMode?'rgba(30, 41, 59, 0.8)':'rgba(248, 250, 252, 0.8)',border:`1px solid ${darkMode?'rgba(59, 130, 246, 0.2)':'rgba(59, 130, 246, 0.1)'}`,borderRadius:'8px',padding:'6px 12px',fontSize:'14px',color:darkMode?'#e2e8f0':'#374151',cursor:'pointer'},matchesList:{maxHeight:'250px',overflowY:'auto',marginBottom:'16px'},matchItem:{display:'flex',alignItems:'flex-start',flexDirection:'column',gap:'12px',padding:'12px',marginBottom:'8px',backgroundColor:darkMode?'rgba(30, 41, 59, 0.4)':'rgba(248, 250, 252, 0.6)',borderRadius:'8px',border:`1px solid ${darkMode?'rgba(59, 130, 246, 0.1)':'rgba(59, 130, 246, 0.05)'}`,transition:'all 0.2s ease'},matchItemHeader:{display:'flex',alignItems:'center',gap:'12px',width:'100%'},checkbox:{width:'16px',height:'16px',accentColor:'#3b82f6',cursor:'pointer'},matchInfo:{flex:1},matchTitle:{fontSize:'14px',fontWeight:'500',color:darkMode?'#e2e8f0':'#374151',marginBottom:'4px'},matchMeta:{fontSize:'12px',color:darkMode?'#94a3b8':'#64748b',display:'flex',alignItems:'center',gap:'8px'},similarityBadge:{backgroundColor:'#10b981',color:'#ffffff',fontSize:'11px',fontWeight:'600',padding:'2px 6px',borderRadius:'8px'},actionButtons:{display:'flex',gap:'8px'},actionButton:{backgroundColor:'transparent',border:`1px solid ${darkMode?'rgba(59, 130, 246, 0.3)':'rgba(59, 130, 246, 0.2)'}`,borderRadius:'6px',padding:'6px',cursor:'pointer',color:darkMode?'#60a5fa':'#3b82f6',transition:'all 0.2s ease'},conflictsWarning:{backgroundColor:darkMode?'rgba(245, 158, 11, 0.1)':'rgba(245, 158, 11, 0.05)',border:`1px solid ${darkMode?'rgba(245, 158, 11, 0.3)':'rgba(245, 158, 11, 0.2)'}`,borderRadius:'8px',padding:'12px',marginBottom:'16px',display:'flex',alignItems:'center',gap:'8px'},conflictsText:{fontSize:'13px',color:darkMode?'#fbbf24':'#d97706',fontWeight:'500'},importButton:{backgroundColor:selectedMatches.size>0?'#3b82f6':darkMode?'#374151':'#9ca3af',color:'#ffffff',border:'none',borderRadius:'8px',padding:'10px 16px',fontSize:'14px',fontWeight:'600',cursor:selectedMatches.size>0?'pointer':'not-allowed',transition:'all 0.2s ease',display:'flex',alignItems:'center',gap:'8px'},emptyState:{textAlign:'center',color:darkMode?'#64748b':'#9ca3af',fontSize:'14px',fontStyle:'italic',padding:'20px'},previewContainer:{backgroundColor:darkMode?'rgba(15, 23, 42, 0.8)':'rgba(248, 250, 252, 0.9)',border:`1px solid ${darkMode?'rgba(59, 130, 246, 0.2)':'rgba(59, 130, 246, 0.1)'}`,borderRadius:'8px',padding:'16px',marginTop:'12px',animation:'slideDown 0.3s ease'},previewHeader:{display:'flex',alignItems:'center',gap:'8px',marginBottom:'12px',fontSize:'14px',fontWeight:'600',color:darkMode?'#e2e8f0':'#374151'},previewContent:{display:'grid',gridTemplateColumns:'1fr 1fr',gap:'16px'},previewSection:{fontSize:'13px',color:darkMode?'#cbd5e1':'#64748b'},previewLabel:{fontWeight:'600',marginBottom:'4px',color:darkMode?'#f1f5f9':'#374151'},relatedClassesList:{display:'flex',flexWrap:'wrap',gap:'4px',marginTop:'4px'},relatedClassTag:{backgroundColor:darkMode?'rgba(59, 130, 246, 0.2)':'rgba(59, 130, 246, 0.1)',color:darkMode?'#93c5fd':'#3b82f6',fontSize:'11px',padding:'2px 6px',borderRadius:'4px',fontWeight:'500'},// Styles pour la sélection granulaire\ngranularSelection:{marginTop:'12px',padding:'12px',backgroundColor:darkMode?'rgba(59, 130, 246, 0.05)':'rgba(59, 130, 246, 0.02)',border:`1px solid ${darkMode?'rgba(59, 130, 246, 0.1)':'rgba(59, 130, 246, 0.08)'}`,borderRadius:'8px'},granularHeader:{fontSize:'13px',fontWeight:'600',color:darkMode?'#e2e8f0':'#374151',marginBottom:'8px'},granularContent:{display:'flex',flexDirection:'column',gap:'12px'},granularSection:{display:'flex',flexDirection:'column',gap:'6px'},granularSectionTitle:{fontSize:'12px',fontWeight:'600',color:darkMode?'#94a3b8':'#6b7280',textTransform:'uppercase',letterSpacing:'0.5px'},granularItems:{display:'flex',flexDirection:'column',gap:'4px',paddingLeft:'8px'},granularItem:{display:'flex',alignItems:'center',gap:'8px',cursor:'pointer',padding:'4px 0'},granularCheckbox:{width:'14px',height:'14px',cursor:'pointer'},granularItemText:{fontSize:'12px',color:darkMode?'#cbd5e1':'#4b5563',fontFamily:'monospace'}});const styles=getSectionStyles();if(!currentUser)return null;return/*#__PURE__*/_jsxs(\"div\",{style:styles.container,children:[/*#__PURE__*/_jsxs(\"div\",{style:styles.header,onClick:handleToggleExpand,children:[/*#__PURE__*/_jsxs(\"div\",{style:styles.headerLeft,children:[/*#__PURE__*/_jsx(Zap,{size:18,color:darkMode?'#60a5fa':'#3b82f6'}),/*#__PURE__*/_jsx(\"h4\",{style:styles.title,children:\"Analyse Historique\"}),/*#__PURE__*/_jsx(\"span\",{style:styles.badge,children:matches.length})]}),/*#__PURE__*/_jsx(ChevronDown,{size:20,style:styles.expandIcon})]}),isExpanded&&/*#__PURE__*/_jsx(\"div\",{style:styles.content,children:matches.length===0?/*#__PURE__*/_jsxs(\"div\",{style:styles.emptyState,children:[\"Aucun diagramme historique trouv\\xE9 pour la classe \\\"\",targetClassName,\"\\\"\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{style:styles.sortContainer,children:[/*#__PURE__*/_jsx(\"span\",{style:styles.sortLabel,children:\"Trier par:\"}),/*#__PURE__*/_jsx(\"select\",{style:styles.sortSelect,value:`${sortOption.key}-${sortOption.direction}`,onChange:e=>{const[key,direction]=e.target.value.split('-');const option=HistoryAnalysisService.getSortOptions().find(opt=>opt.key===key&&opt.direction===direction);if(option)setSortOption(option);},children:HistoryAnalysisService.getSortOptions().map(option=>/*#__PURE__*/_jsx(\"option\",{value:`${option.key}-${option.direction}`,children:option.label},`${option.key}-${option.direction}`))})]}),conflicts.attributes.length>0||conflicts.methods.length>0?/*#__PURE__*/_jsxs(\"div\",{style:styles.conflictsWarning,children:[/*#__PURE__*/_jsx(AlertTriangle,{size:16}),/*#__PURE__*/_jsxs(\"span\",{style:styles.conflictsText,children:[\"Conflits d\\xE9tect\\xE9s: \",conflicts.attributes.length,\" attribut(s), \",conflicts.methods.length,\" m\\xE9thode(s)\"]})]}):null,/*#__PURE__*/_jsx(\"div\",{style:styles.matchesList,children:matches.map(match=>{var _match$matchingClasse,_previewData$previewD,_previewData$previewD2,_previewData$previewD3,_match$matchingClasse2,_match$matchingClasse3;return/*#__PURE__*/_jsxs(\"div\",{style:styles.matchItem,className:\"history-match-item\",children:[/*#__PURE__*/_jsxs(\"div\",{style:styles.matchItemHeader,children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",style:styles.checkbox,checked:selectedMatches.has(match.historyItem.id),onChange:e=>handleMatchSelection(match.historyItem.id,e.target.checked)}),/*#__PURE__*/_jsxs(\"div\",{style:styles.matchInfo,children:[/*#__PURE__*/_jsx(\"div\",{style:styles.matchTitle,children:match.historyItem.title}),/*#__PURE__*/_jsxs(\"div\",{style:styles.matchMeta,className:\"history-match-meta\",children:[/*#__PURE__*/_jsx(Clock,{size:12}),match.historyItem.createdAt.toLocaleDateString('fr-FR'),/*#__PURE__*/_jsxs(\"span\",{style:styles.similarityBadge,className:\"history-similarity-badge\",children:[Math.round(match.similarity),\"%\"]}),getAccessIcon(match),match.isShared&&/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'11px',color:darkMode?'#fbbf24':'#d97706'},children:\"Partag\\xE9\"})]})]}),/*#__PURE__*/_jsx(\"div\",{style:styles.actionButtons,children:/*#__PURE__*/_jsx(\"button\",{style:styles.actionButton,className:\"history-action-button\",onClick:()=>handlePreview(match.historyItem.id),title:\"Voir aper\\xE7u\",children:/*#__PURE__*/_jsx(Eye,{size:14})})})]}),showPreview===match.historyItem.id&&previewData&&/*#__PURE__*/_jsxs(\"div\",{style:styles.previewContainer,className:\"history-preview-container\",children:[/*#__PURE__*/_jsxs(\"div\",{style:styles.previewHeader,children:[/*#__PURE__*/_jsx(Eye,{size:16}),\"Aper\\xE7u de la classe \\\"\",(_match$matchingClasse=match.matchingClasses[0])===null||_match$matchingClasse===void 0?void 0:_match$matchingClasse.name,\"\\\"\"]}),/*#__PURE__*/_jsxs(\"div\",{style:styles.previewContent,className:\"history-preview-content\",children:[/*#__PURE__*/_jsxs(\"div\",{style:styles.previewSection,children:[/*#__PURE__*/_jsx(\"div\",{style:styles.previewLabel,children:\"Contenu de la classe:\"}),/*#__PURE__*/_jsx(\"div\",{children:(_previewData$previewD=previewData.previewData)===null||_previewData$previewD===void 0?void 0:_previewData$previewD.classContext})]}),/*#__PURE__*/_jsxs(\"div\",{style:styles.previewSection,children:[/*#__PURE__*/_jsx(\"div\",{style:styles.previewLabel,children:\"Classes li\\xE9es:\"}),/*#__PURE__*/_jsxs(\"div\",{style:styles.relatedClassesList,className:\"history-related-classes-list\",children:[(_previewData$previewD2=previewData.previewData)===null||_previewData$previewD2===void 0?void 0:_previewData$previewD2.relatedClasses.map((className,idx)=>/*#__PURE__*/_jsx(\"span\",{style:styles.relatedClassTag,children:className},idx)),(!((_previewData$previewD3=previewData.previewData)!==null&&_previewData$previewD3!==void 0&&_previewData$previewD3.relatedClasses)||previewData.previewData.relatedClasses.length===0)&&/*#__PURE__*/_jsx(\"span\",{style:{fontStyle:'italic',color:darkMode?'#64748b':'#9ca3af'},children:\"Aucune classe li\\xE9e d\\xE9tect\\xE9e\"})]})]})]})]}),selectedMatches.has(match.historyItem.id)&&/*#__PURE__*/_jsxs(\"div\",{style:styles.granularSelection,className:\"history-granular-selection\",children:[/*#__PURE__*/_jsx(\"div\",{style:styles.granularHeader,children:/*#__PURE__*/_jsx(\"span\",{children:\"S\\xE9lectionner les \\xE9l\\xE9ments \\xE0 importer :\"})}),/*#__PURE__*/_jsxs(\"div\",{style:styles.granularContent,children:[((_match$matchingClasse2=match.matchingClasses[0])===null||_match$matchingClasse2===void 0?void 0:_match$matchingClasse2.attributes)&&match.matchingClasses[0].attributes.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:styles.granularSection,children:[/*#__PURE__*/_jsx(\"div\",{style:styles.granularSectionTitle,children:\"Attributs :\"}),/*#__PURE__*/_jsx(\"div\",{style:styles.granularItems,children:match.matchingClasses[0].attributes.map((attribute,idx)=>{var _selectedAttributes$g;return/*#__PURE__*/_jsxs(\"label\",{style:styles.granularItem,children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",style:styles.granularCheckbox,checked:((_selectedAttributes$g=selectedAttributes.get(match.historyItem.id))===null||_selectedAttributes$g===void 0?void 0:_selectedAttributes$g.has(attribute))||false,onChange:e=>handleAttributeSelection(match.historyItem.id,attribute,e.target.checked)}),/*#__PURE__*/_jsx(\"span\",{style:styles.granularItemText,children:attribute})]},idx);})})]}),((_match$matchingClasse3=match.matchingClasses[0])===null||_match$matchingClasse3===void 0?void 0:_match$matchingClasse3.methods)&&match.matchingClasses[0].methods.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:styles.granularSection,children:[/*#__PURE__*/_jsx(\"div\",{style:styles.granularSectionTitle,children:\"M\\xE9thodes :\"}),/*#__PURE__*/_jsx(\"div\",{style:styles.granularItems,children:match.matchingClasses[0].methods.map((method,idx)=>{var _selectedMethods$get;return/*#__PURE__*/_jsxs(\"label\",{style:styles.granularItem,children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",style:styles.granularCheckbox,checked:((_selectedMethods$get=selectedMethods.get(match.historyItem.id))===null||_selectedMethods$get===void 0?void 0:_selectedMethods$get.has(method))||false,onChange:e=>handleMethodSelection(match.historyItem.id,method,e.target.checked)}),/*#__PURE__*/_jsx(\"span\",{style:styles.granularItemText,children:method})]},idx);})})]})]})]})]},match.historyItem.id);})}),/*#__PURE__*/_jsxs(\"button\",{style:styles.importButton,onClick:handleImport,disabled:(()=>{const totalAttributes=Array.from(selectedAttributes.values()).reduce((sum,attrs)=>sum+attrs.size,0);const totalMethods=Array.from(selectedMethods.values()).reduce((sum,methods)=>sum+methods.size,0);return totalAttributes+totalMethods===0;})(),children:[/*#__PURE__*/_jsx(Zap,{size:16}),(()=>{const totalAttributes=Array.from(selectedAttributes.values()).reduce((sum,attrs)=>sum+attrs.size,0);const totalMethods=Array.from(selectedMethods.values()).reduce((sum,methods)=>sum+methods.size,0);const totalElements=totalAttributes+totalMethods;if(totalElements===0){return`Importer (${selectedMatches.size} diagramme${selectedMatches.size>1?'s':''} sélectionné${selectedMatches.size>1?'s':''})`;}return`Importer (${totalElements} élément${totalElements>1?'s':''} sélectionné${totalElements>1?'s':''})`;})()]})]})})]});};export default HistoryAnalysisSection;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useHistory", "useAuth", "HistoryAnalysisService", "ChevronDown", "Eye", "Clock", "Zap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shield", "Users", "Lock", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "HistoryAnalysisSection", "_ref", "darkMode", "targetClassName", "currentClassData", "onImport", "currentDiagramText", "historyItems", "currentUser", "isExpanded", "setIsExpanded", "matches", "setMatches", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMatches", "Set", "selectedAttributes", "setSelectedAttributes", "Map", "selectedMethods", "setSelectedMethods", "sortOption", "setSortOption", "getSortOptions", "showPreview", "setShowPreview", "conflicts", "setConflicts", "attributes", "methods", "previewData", "setPreviewData", "foundMatches", "findMatchingDiagrams", "uid", "sortedMatches", "sortMatches", "selectedMatchObjects", "filter", "match", "has", "historyItem", "id", "selectedClasses", "flatMap", "matchingClasses", "length", "detectedConflicts", "detectConflicts", "handleToggleExpand", "handleMatchSelection", "matchId", "selected", "newSelection", "add", "delete", "newSelectedAttributes", "newSelectedMethods", "handleAttributeSelection", "attribute", "currentAttributes", "get", "size", "set", "handleMethodSelection", "method", "currentMethods", "handleImport", "attributesToImport", "methodsToImport", "for<PERSON>ach", "attr", "includes", "push", "importedData", "name", "handlePreview", "find", "m", "getAccessIcon", "accessLevel", "getAccessLevel", "title", "children", "color", "getSectionStyles", "container", "marginBottom", "border", "borderRadius", "background", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "header", "display", "alignItems", "justifyContent", "padding", "cursor", "borderBottom", "headerLeft", "gap", "fontSize", "fontWeight", "margin", "badge", "backgroundColor", "min<PERSON><PERSON><PERSON>", "textAlign", "expandIcon", "transition", "transform", "content", "maxHeight", "overflow", "sortContainer", "sortLabel", "sortSelect", "matchesList", "overflowY", "matchItem", "flexDirection", "matchItemHeader", "width", "checkbox", "height", "accentColor", "matchInfo", "flex", "matchTitle", "matchMeta", "similarityBadge", "actionButtons", "actionButton", "conflictsWarning", "conflictsText", "importButton", "emptyState", "fontStyle", "previewContainer", "marginTop", "animation", "previewHeader", "previewContent", "gridTemplateColumns", "previewSection", "previewLabel", "relatedClassesList", "flexWrap", "relatedClassTag", "granularSelection", "granularHeader", "granular<PERSON>ontent", "granularSection", "granularSectionTitle", "textTransform", "letterSpacing", "granularItems", "paddingLeft", "granularItem", "granularCheckbox", "granularItemText", "fontFamily", "styles", "style", "onClick", "value", "key", "direction", "onChange", "e", "target", "split", "option", "opt", "map", "label", "_match$matchingClasse", "_previewData$previewD", "_previewData$previewD2", "_previewData$previewD3", "_match$matchingClasse2", "_match$matchingClasse3", "className", "type", "checked", "createdAt", "toLocaleDateString", "Math", "round", "similarity", "isShared", "classContext", "relatedClasses", "idx", "_selectedAttributes$g", "_selectedMethods$get", "disabled", "totalAttributes", "Array", "from", "values", "reduce", "sum", "attrs", "totalMethods", "totalElements"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/imageUp/HistoryAnalysisSection.tsx"], "sourcesContent": ["// HistoryAnalysisSection.tsx - Composant pour l'analyse historique et l'import de classes\nimport React, { useState, useEffect } from 'react';\nimport { useHistory } from '../../context/HistoryContext';\nimport { useAuth } from '../../context/AuthContext';\nimport { HistoryAnalysisService, HistoryMatch, SortOption, ClassData } from '../../services/HistoryAnalysisService';\nimport { ChevronDown, Eye, Clock, Zap, AlertTriangle, Shield, Users, Lock } from 'lucide-react';\nimport './HistoryAnalysisSection.css';\n\ninterface HistoryAnalysisSectionProps {\n  darkMode: boolean;\n  targetClassName: string;\n  currentClassData: ClassData;\n  onImport: (importedData: ClassData) => void;\n  currentDiagramText?: string; // Texte du diagramme actuel pour l'exclure\n}\n\nconst HistoryAnalysisSection: React.FC<HistoryAnalysisSectionProps> = ({\n  darkMode,\n  targetClassName,\n  currentClassData,\n  onImport,\n  currentDiagramText\n}) => {\n  const { historyItems } = useHistory();\n  const { currentUser } = useAuth();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [matches, setMatches] = useState<HistoryMatch[]>([]);\n  const [selectedMatches, setSelectedMatches] = useState<Set<string>>(new Set());\n  const [selectedAttributes, setSelectedAttributes] = useState<Map<string, Set<string>>>(new Map());\n  const [selectedMethods, setSelectedMethods] = useState<Map<string, Set<string>>>(new Map());\n  const [sortOption, setSortOption] = useState<SortOption>(HistoryAnalysisService.getSortOptions()[0]);\n  const [showPreview, setShowPreview] = useState<string | null>(null);\n  const [conflicts, setConflicts] = useState<{ attributes: string[], methods: string[] }>({ attributes: [], methods: [] });\n  const [previewData, setPreviewData] = useState<HistoryMatch | null>(null);\n\n  // Rechercher les correspondances lors du changement de classe cible\n  useEffect(() => {\n    if (!targetClassName || !currentUser) return;\n    \n    const foundMatches = HistoryAnalysisService.findMatchingDiagrams(\n      targetClassName,\n      historyItems,\n      currentUser.uid,\n      currentDiagramText\n    );\n    \n    const sortedMatches = HistoryAnalysisService.sortMatches(foundMatches, sortOption);\n    setMatches(sortedMatches);\n  }, [targetClassName, historyItems, currentUser, sortOption, currentDiagramText]);\n\n  // Détecter les conflits lors du changement de sélection\n  useEffect(() => {\n    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));\n    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);\n    \n    if (selectedClasses.length > 0) {\n      const detectedConflicts = HistoryAnalysisService.detectConflicts(currentClassData, selectedClasses);\n      setConflicts(detectedConflicts);\n    } else {\n      setConflicts({ attributes: [], methods: [] });\n    }\n  }, [selectedMatches, matches, currentClassData]);\n\n  const handleToggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n\n  const handleMatchSelection = (matchId: string, selected: boolean) => {\n    const newSelection = new Set(selectedMatches);\n    if (selected) {\n      newSelection.add(matchId);\n    } else {\n      newSelection.delete(matchId);\n      // Nettoyer les sélections d'attributs/méthodes pour ce match\n      const newSelectedAttributes = new Map(selectedAttributes);\n      const newSelectedMethods = new Map(selectedMethods);\n      newSelectedAttributes.delete(matchId);\n      newSelectedMethods.delete(matchId);\n      setSelectedAttributes(newSelectedAttributes);\n      setSelectedMethods(newSelectedMethods);\n    }\n    setSelectedMatches(newSelection);\n  };\n\n  const handleAttributeSelection = (matchId: string, attribute: string, selected: boolean) => {\n    const newSelectedAttributes = new Map(selectedAttributes);\n    const currentAttributes = newSelectedAttributes.get(matchId) || new Set();\n\n    if (selected) {\n      currentAttributes.add(attribute);\n    } else {\n      currentAttributes.delete(attribute);\n    }\n\n    if (currentAttributes.size > 0) {\n      newSelectedAttributes.set(matchId, currentAttributes);\n    } else {\n      newSelectedAttributes.delete(matchId);\n    }\n\n    setSelectedAttributes(newSelectedAttributes);\n  };\n\n  const handleMethodSelection = (matchId: string, method: string, selected: boolean) => {\n    const newSelectedMethods = new Map(selectedMethods);\n    const currentMethods = newSelectedMethods.get(matchId) || new Set();\n\n    if (selected) {\n      currentMethods.add(method);\n    } else {\n      currentMethods.delete(method);\n    }\n\n    if (currentMethods.size > 0) {\n      newSelectedMethods.set(matchId, currentMethods);\n    } else {\n      newSelectedMethods.delete(matchId);\n    }\n\n    setSelectedMethods(newSelectedMethods);\n  };\n\n  const handleImport = () => {\n    // Construire les données à importer basées sur les sélections granulaires\n    const attributesToImport: string[] = [];\n    const methodsToImport: string[] = [];\n\n    // Collecter tous les attributs et méthodes sélectionnés\n    selectedAttributes.forEach((attributes, matchId) => {\n      attributes.forEach(attr => {\n        if (!attributesToImport.includes(attr)) {\n          attributesToImport.push(attr);\n        }\n      });\n    });\n\n    selectedMethods.forEach((methods, matchId) => {\n      methods.forEach(method => {\n        if (!methodsToImport.includes(method)) {\n          methodsToImport.push(method);\n        }\n      });\n    });\n\n    if (attributesToImport.length > 0 || methodsToImport.length > 0) {\n      const importedData: ClassData = {\n        name: currentClassData.name,\n        attributes: attributesToImport,\n        methods: methodsToImport\n      };\n\n      onImport(importedData);\n\n      // Reset toutes les sélections\n      setSelectedMatches(new Set());\n      setSelectedAttributes(new Map());\n      setSelectedMethods(new Map());\n    }\n  };\n\n  const handlePreview = (matchId: string) => {\n    if (showPreview === matchId) {\n      setShowPreview(null);\n      setPreviewData(null);\n    } else {\n      const match = matches.find(m => m.historyItem.id === matchId);\n      setShowPreview(matchId);\n      setPreviewData(match || null);\n    }\n  };\n\n  const getAccessIcon = (match: HistoryMatch) => {\n    const accessLevel = HistoryAnalysisService.getAccessLevel(match.historyItem, currentUser?.uid || '');\n\n    switch (accessLevel) {\n      case 'owner':\n        return <span title=\"Votre diagramme\"><Shield size={12} color=\"#10b981\" /></span>;\n      case 'shared':\n        return <span title=\"Diagramme partagé\"><Users size={12} color=\"#f59e0b\" /></span>;\n      case 'public':\n        return <span title=\"Diagramme public\"><Eye size={12} color=\"#3b82f6\" /></span>;\n      default:\n        return <span title=\"Accès restreint\"><Lock size={12} color=\"#ef4444\" /></span>;\n    }\n  };\n\n  const getSectionStyles = () => ({\n    container: {\n      marginBottom: '24px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '12px',\n      background: darkMode \n        ? 'linear-gradient(135deg, rgba(10, 15, 28, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)' \n        : 'linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%)',\n      backdropFilter: 'blur(10px)',\n      WebkitBackdropFilter: 'blur(10px)',\n    },\n    header: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      padding: '16px 20px',\n      cursor: 'pointer',\n      borderBottom: isExpanded ? `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}` : 'none',\n    },\n    headerLeft: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n    },\n    title: {\n      fontSize: '16px',\n      fontWeight: '600',\n      color: darkMode ? '#f8fafc' : '#0f172a',\n      margin: 0,\n    },\n    badge: {\n      backgroundColor: matches.length > 0 ? '#3b82f6' : darkMode ? '#64748b' : '#94a3b8',\n      color: '#ffffff',\n      fontSize: '12px',\n      fontWeight: '600',\n      padding: '4px 8px',\n      borderRadius: '12px',\n      minWidth: '20px',\n      textAlign: 'center' as const,\n    },\n    expandIcon: {\n      color: darkMode ? '#94a3b8' : '#64748b',\n      transition: 'transform 0.2s ease',\n      transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',\n    },\n    content: {\n      padding: isExpanded ? '20px' : '0',\n      maxHeight: isExpanded ? '400px' : '0',\n      overflow: 'hidden' as const,\n      transition: 'all 0.3s ease',\n    },\n    sortContainer: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      marginBottom: '16px',\n    },\n    sortLabel: {\n      fontSize: '14px',\n      color: darkMode ? '#cbd5e1' : '#64748b',\n      fontWeight: '500',\n    },\n    sortSelect: {\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '6px 12px',\n      fontSize: '14px',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      cursor: 'pointer',\n    },\n    matchesList: {\n      maxHeight: '250px',\n      overflowY: 'auto' as const,\n      marginBottom: '16px',\n    },\n    matchItem: {\n      display: 'flex',\n      alignItems: 'flex-start',\n      flexDirection: 'column' as const,\n      gap: '12px',\n      padding: '12px',\n      marginBottom: '8px',\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.4)' : 'rgba(248, 250, 252, 0.6)',\n      borderRadius: '8px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'}`,\n      transition: 'all 0.2s ease',\n    },\n    matchItemHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      width: '100%',\n    },\n    checkbox: {\n      width: '16px',\n      height: '16px',\n      accentColor: '#3b82f6',\n      cursor: 'pointer',\n    },\n    matchInfo: {\n      flex: 1,\n    },\n    matchTitle: {\n      fontSize: '14px',\n      fontWeight: '500',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '4px',\n    },\n    matchMeta: {\n      fontSize: '12px',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    similarityBadge: {\n      backgroundColor: '#10b981',\n      color: '#ffffff',\n      fontSize: '11px',\n      fontWeight: '600',\n      padding: '2px 6px',\n      borderRadius: '8px',\n    },\n    actionButtons: {\n      display: 'flex',\n      gap: '8px',\n    },\n    actionButton: {\n      backgroundColor: 'transparent',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'}`,\n      borderRadius: '6px',\n      padding: '6px',\n      cursor: 'pointer',\n      color: darkMode ? '#60a5fa' : '#3b82f6',\n      transition: 'all 0.2s ease',\n    },\n    conflictsWarning: {\n      backgroundColor: darkMode ? 'rgba(245, 158, 11, 0.1)' : 'rgba(245, 158, 11, 0.05)',\n      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`,\n      borderRadius: '8px',\n      padding: '12px',\n      marginBottom: '16px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    conflictsText: {\n      fontSize: '13px',\n      color: darkMode ? '#fbbf24' : '#d97706',\n      fontWeight: '500',\n    },\n    importButton: {\n      backgroundColor: selectedMatches.size > 0 ? '#3b82f6' : darkMode ? '#374151' : '#9ca3af',\n      color: '#ffffff',\n      border: 'none',\n      borderRadius: '8px',\n      padding: '10px 16px',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: selectedMatches.size > 0 ? 'pointer' : 'not-allowed',\n      transition: 'all 0.2s ease',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    emptyState: {\n      textAlign: 'center' as const,\n      color: darkMode ? '#64748b' : '#9ca3af',\n      fontSize: '14px',\n      fontStyle: 'italic',\n      padding: '20px',\n    },\n    previewContainer: {\n      backgroundColor: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(248, 250, 252, 0.9)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '16px',\n      marginTop: '12px',\n      animation: 'slideDown 0.3s ease',\n    },\n    previewHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      marginBottom: '12px',\n      fontSize: '14px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151',\n    },\n    previewContent: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '16px',\n    },\n    previewSection: {\n      fontSize: '13px',\n      color: darkMode ? '#cbd5e1' : '#64748b',\n    },\n    previewLabel: {\n      fontWeight: '600',\n      marginBottom: '4px',\n      color: darkMode ? '#f1f5f9' : '#374151',\n    },\n    relatedClassesList: {\n      display: 'flex',\n      flexWrap: 'wrap' as const,\n      gap: '4px',\n      marginTop: '4px',\n    },\n    relatedClassTag: {\n      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',\n      color: darkMode ? '#93c5fd' : '#3b82f6',\n      fontSize: '11px',\n      padding: '2px 6px',\n      borderRadius: '4px',\n      fontWeight: '500',\n    },\n\n    // Styles pour la sélection granulaire\n    granularSelection: {\n      marginTop: '12px',\n      padding: '12px',\n      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.05)' : 'rgba(59, 130, 246, 0.02)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.08)'}`,\n      borderRadius: '8px',\n    },\n\n    granularHeader: {\n      fontSize: '13px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '8px',\n    },\n\n    granularContent: {\n      display: 'flex',\n      flexDirection: 'column' as const,\n      gap: '12px',\n    },\n\n    granularSection: {\n      display: 'flex',\n      flexDirection: 'column' as const,\n      gap: '6px',\n    },\n\n    granularSectionTitle: {\n      fontSize: '12px',\n      fontWeight: '600',\n      color: darkMode ? '#94a3b8' : '#6b7280',\n      textTransform: 'uppercase' as const,\n      letterSpacing: '0.5px',\n    },\n\n    granularItems: {\n      display: 'flex',\n      flexDirection: 'column' as const,\n      gap: '4px',\n      paddingLeft: '8px',\n    },\n\n    granularItem: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      cursor: 'pointer',\n      padding: '4px 0',\n    },\n\n    granularCheckbox: {\n      width: '14px',\n      height: '14px',\n      cursor: 'pointer',\n    },\n\n    granularItemText: {\n      fontSize: '12px',\n      color: darkMode ? '#cbd5e1' : '#4b5563',\n      fontFamily: 'monospace',\n    }\n  });\n\n  const styles = getSectionStyles();\n\n  if (!currentUser) return null;\n\n  return (\n    <div style={styles.container}>\n      <div style={styles.header} onClick={handleToggleExpand}>\n        <div style={styles.headerLeft}>\n          <Zap size={18} color={darkMode ? '#60a5fa' : '#3b82f6'} />\n          <h4 style={styles.title}>Analyse Historique</h4>\n          <span style={styles.badge}>{matches.length}</span>\n        </div>\n        <ChevronDown size={20} style={styles.expandIcon} />\n      </div>\n      \n      {isExpanded && (\n        <div style={styles.content}>\n          {matches.length === 0 ? (\n            <div style={styles.emptyState}>\n              Aucun diagramme historique trouvé pour la classe \"{targetClassName}\"\n            </div>\n          ) : (\n            <>\n              <div style={styles.sortContainer}>\n                <span style={styles.sortLabel}>Trier par:</span>\n                <select \n                  style={styles.sortSelect}\n                  value={`${sortOption.key}-${sortOption.direction}`}\n                  onChange={(e) => {\n                    const [key, direction] = e.target.value.split('-');\n                    const option = HistoryAnalysisService.getSortOptions().find(\n                      opt => opt.key === key && opt.direction === direction\n                    );\n                    if (option) setSortOption(option);\n                  }}\n                >\n                  {HistoryAnalysisService.getSortOptions().map(option => (\n                    <option key={`${option.key}-${option.direction}`} value={`${option.key}-${option.direction}`}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {conflicts.attributes.length > 0 || conflicts.methods.length > 0 ? (\n                <div style={styles.conflictsWarning}>\n                  <AlertTriangle size={16} />\n                  <span style={styles.conflictsText}>\n                    Conflits détectés: {conflicts.attributes.length} attribut(s), {conflicts.methods.length} méthode(s)\n                  </span>\n                </div>\n              ) : null}\n\n              <div style={styles.matchesList}>\n                {matches.map(match => (\n                  <div key={match.historyItem.id} style={styles.matchItem} className=\"history-match-item\">\n                    <div style={styles.matchItemHeader}>\n                      <input\n                        type=\"checkbox\"\n                        style={styles.checkbox}\n                        checked={selectedMatches.has(match.historyItem.id)}\n                        onChange={(e) => handleMatchSelection(match.historyItem.id, e.target.checked)}\n                      />\n                      <div style={styles.matchInfo}>\n                        <div style={styles.matchTitle}>{match.historyItem.title}</div>\n                        <div style={styles.matchMeta} className=\"history-match-meta\">\n                          <Clock size={12} />\n                          {match.historyItem.createdAt.toLocaleDateString('fr-FR')}\n                          <span style={styles.similarityBadge} className=\"history-similarity-badge\">\n                            {Math.round(match.similarity)}%\n                          </span>\n                          {getAccessIcon(match)}\n                          {match.isShared && <span style={{ fontSize: '11px', color: darkMode ? '#fbbf24' : '#d97706' }}>Partagé</span>}\n                        </div>\n                      </div>\n                      <div style={styles.actionButtons}>\n                        <button\n                          style={styles.actionButton}\n                          className=\"history-action-button\"\n                          onClick={() => handlePreview(match.historyItem.id)}\n                          title=\"Voir aperçu\"\n                        >\n                          <Eye size={14} />\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* Aperçu de la classe */}\n                    {showPreview === match.historyItem.id && previewData && (\n                      <div style={styles.previewContainer} className=\"history-preview-container\">\n                        <div style={styles.previewHeader}>\n                          <Eye size={16} />\n                          Aperçu de la classe \"{match.matchingClasses[0]?.name}\"\n                        </div>\n                        <div style={styles.previewContent} className=\"history-preview-content\">\n                          <div style={styles.previewSection}>\n                            <div style={styles.previewLabel}>Contenu de la classe:</div>\n                            <div>{previewData.previewData?.classContext}</div>\n                          </div>\n                          <div style={styles.previewSection}>\n                            <div style={styles.previewLabel}>Classes liées:</div>\n                            <div style={styles.relatedClassesList} className=\"history-related-classes-list\">\n                              {previewData.previewData?.relatedClasses.map((className, idx) => (\n                                <span key={idx} style={styles.relatedClassTag}>\n                                  {className}\n                                </span>\n                              ))}\n                              {(!previewData.previewData?.relatedClasses || previewData.previewData.relatedClasses.length === 0) && (\n                                <span style={{ fontStyle: 'italic', color: darkMode ? '#64748b' : '#9ca3af' }}>\n                                  Aucune classe liée détectée\n                                </span>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Sélection granulaire des attributs et méthodes */}\n                    {selectedMatches.has(match.historyItem.id) && (\n                      <div style={styles.granularSelection} className=\"history-granular-selection\">\n                        <div style={styles.granularHeader}>\n                          <span>Sélectionner les éléments à importer :</span>\n                        </div>\n\n                        <div style={styles.granularContent}>\n                          {/* Attributs */}\n                          {match.matchingClasses[0]?.attributes && match.matchingClasses[0].attributes.length > 0 && (\n                            <div style={styles.granularSection}>\n                              <div style={styles.granularSectionTitle}>Attributs :</div>\n                              <div style={styles.granularItems}>\n                                {match.matchingClasses[0].attributes.map((attribute, idx) => (\n                                  <label key={idx} style={styles.granularItem}>\n                                    <input\n                                      type=\"checkbox\"\n                                      style={styles.granularCheckbox}\n                                      checked={selectedAttributes.get(match.historyItem.id)?.has(attribute) || false}\n                                      onChange={(e) => handleAttributeSelection(match.historyItem.id, attribute, e.target.checked)}\n                                    />\n                                    <span style={styles.granularItemText}>{attribute}</span>\n                                  </label>\n                                ))}\n                              </div>\n                            </div>\n                          )}\n\n                          {/* Méthodes */}\n                          {match.matchingClasses[0]?.methods && match.matchingClasses[0].methods.length > 0 && (\n                            <div style={styles.granularSection}>\n                              <div style={styles.granularSectionTitle}>Méthodes :</div>\n                              <div style={styles.granularItems}>\n                                {match.matchingClasses[0].methods.map((method, idx) => (\n                                  <label key={idx} style={styles.granularItem}>\n                                    <input\n                                      type=\"checkbox\"\n                                      style={styles.granularCheckbox}\n                                      checked={selectedMethods.get(match.historyItem.id)?.has(method) || false}\n                                      onChange={(e) => handleMethodSelection(match.historyItem.id, method, e.target.checked)}\n                                    />\n                                    <span style={styles.granularItemText}>{method}</span>\n                                  </label>\n                                ))}\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n\n              <button\n                style={styles.importButton}\n                onClick={handleImport}\n                disabled={(() => {\n                  const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);\n                  const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);\n                  return totalAttributes + totalMethods === 0;\n                })()}\n              >\n                <Zap size={16} />\n                {(() => {\n                  const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);\n                  const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);\n                  const totalElements = totalAttributes + totalMethods;\n\n                  if (totalElements === 0) {\n                    return `Importer (${selectedMatches.size} diagramme${selectedMatches.size > 1 ? 's' : ''} sélectionné${selectedMatches.size > 1 ? 's' : ''})`;\n                  }\n\n                  return `Importer (${totalElements} élément${totalElements > 1 ? 's' : ''} sélectionné${totalElements > 1 ? 's' : ''})`;\n                })()}\n              </button>\n            </>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default HistoryAnalysisSection;\n"], "mappings": "AAAA;AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,UAAU,KAAQ,8BAA8B,CACzD,OAASC,OAAO,KAAQ,2BAA2B,CACnD,OAASC,sBAAsB,KAA6C,uCAAuC,CACnH,OAASC,WAAW,CAAEC,GAAG,CAAEC,KAAK,CAAEC,GAAG,CAAEC,aAAa,CAAEC,MAAM,CAAEC,KAAK,CAAEC,IAAI,KAAQ,cAAc,CAC/F,MAAO,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAUtC,KAAM,CAAAC,sBAA6D,CAAGC,IAAA,EAMhE,IANiE,CACrEC,QAAQ,CACRC,eAAe,CACfC,gBAAgB,CAChBC,QAAQ,CACRC,kBACF,CAAC,CAAAL,IAAA,CACC,KAAM,CAAEM,YAAa,CAAC,CAAGxB,UAAU,CAAC,CAAC,CACrC,KAAM,CAAEyB,WAAY,CAAC,CAAGxB,OAAO,CAAC,CAAC,CACjC,KAAM,CAACyB,UAAU,CAAEC,aAAa,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC8B,OAAO,CAAEC,UAAU,CAAC,CAAG/B,QAAQ,CAAiB,EAAE,CAAC,CAC1D,KAAM,CAACgC,eAAe,CAAEC,kBAAkB,CAAC,CAAGjC,QAAQ,CAAc,GAAI,CAAAkC,GAAG,CAAC,CAAC,CAAC,CAC9E,KAAM,CAACC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGpC,QAAQ,CAA2B,GAAI,CAAAqC,GAAG,CAAC,CAAC,CAAC,CACjG,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGvC,QAAQ,CAA2B,GAAI,CAAAqC,GAAG,CAAC,CAAC,CAAC,CAC3F,KAAM,CAACG,UAAU,CAAEC,aAAa,CAAC,CAAGzC,QAAQ,CAAaI,sBAAsB,CAACsC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACpG,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAgB,IAAI,CAAC,CACnE,KAAM,CAAC6C,SAAS,CAAEC,YAAY,CAAC,CAAG9C,QAAQ,CAA8C,CAAE+C,UAAU,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CACxH,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGlD,QAAQ,CAAsB,IAAI,CAAC,CAEzE;AACAC,SAAS,CAAC,IAAM,CACd,GAAI,CAACqB,eAAe,EAAI,CAACK,WAAW,CAAE,OAEtC,KAAM,CAAAwB,YAAY,CAAG/C,sBAAsB,CAACgD,oBAAoB,CAC9D9B,eAAe,CACfI,YAAY,CACZC,WAAW,CAAC0B,GAAG,CACf5B,kBACF,CAAC,CAED,KAAM,CAAA6B,aAAa,CAAGlD,sBAAsB,CAACmD,WAAW,CAACJ,YAAY,CAAEX,UAAU,CAAC,CAClFT,UAAU,CAACuB,aAAa,CAAC,CAC3B,CAAC,CAAE,CAAChC,eAAe,CAAEI,YAAY,CAAEC,WAAW,CAAEa,UAAU,CAAEf,kBAAkB,CAAC,CAAC,CAEhF;AACAxB,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuD,oBAAoB,CAAG1B,OAAO,CAAC2B,MAAM,CAACC,KAAK,EAAI1B,eAAe,CAAC2B,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,CAAC,CAC/F,KAAM,CAAAC,eAAe,CAAGN,oBAAoB,CAACO,OAAO,CAACL,KAAK,EAAIA,KAAK,CAACM,eAAe,CAAC,CAEpF,GAAIF,eAAe,CAACG,MAAM,CAAG,CAAC,CAAE,CAC9B,KAAM,CAAAC,iBAAiB,CAAG9D,sBAAsB,CAAC+D,eAAe,CAAC5C,gBAAgB,CAAEuC,eAAe,CAAC,CACnGhB,YAAY,CAACoB,iBAAiB,CAAC,CACjC,CAAC,IAAM,CACLpB,YAAY,CAAC,CAAEC,UAAU,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CAC/C,CACF,CAAC,CAAE,CAAChB,eAAe,CAAEF,OAAO,CAAEP,gBAAgB,CAAC,CAAC,CAEhD,KAAM,CAAA6C,kBAAkB,CAAGA,CAAA,GAAM,CAC/BvC,aAAa,CAAC,CAACD,UAAU,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAyC,oBAAoB,CAAGA,CAACC,OAAe,CAAEC,QAAiB,GAAK,CACnE,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAAtC,GAAG,CAACF,eAAe,CAAC,CAC7C,GAAIuC,QAAQ,CAAE,CACZC,YAAY,CAACC,GAAG,CAACH,OAAO,CAAC,CAC3B,CAAC,IAAM,CACLE,YAAY,CAACE,MAAM,CAACJ,OAAO,CAAC,CAC5B;AACA,KAAM,CAAAK,qBAAqB,CAAG,GAAI,CAAAtC,GAAG,CAACF,kBAAkB,CAAC,CACzD,KAAM,CAAAyC,kBAAkB,CAAG,GAAI,CAAAvC,GAAG,CAACC,eAAe,CAAC,CACnDqC,qBAAqB,CAACD,MAAM,CAACJ,OAAO,CAAC,CACrCM,kBAAkB,CAACF,MAAM,CAACJ,OAAO,CAAC,CAClClC,qBAAqB,CAACuC,qBAAqB,CAAC,CAC5CpC,kBAAkB,CAACqC,kBAAkB,CAAC,CACxC,CACA3C,kBAAkB,CAACuC,YAAY,CAAC,CAClC,CAAC,CAED,KAAM,CAAAK,wBAAwB,CAAGA,CAACP,OAAe,CAAEQ,SAAiB,CAAEP,QAAiB,GAAK,CAC1F,KAAM,CAAAI,qBAAqB,CAAG,GAAI,CAAAtC,GAAG,CAACF,kBAAkB,CAAC,CACzD,KAAM,CAAA4C,iBAAiB,CAAGJ,qBAAqB,CAACK,GAAG,CAACV,OAAO,CAAC,EAAI,GAAI,CAAApC,GAAG,CAAC,CAAC,CAEzE,GAAIqC,QAAQ,CAAE,CACZQ,iBAAiB,CAACN,GAAG,CAACK,SAAS,CAAC,CAClC,CAAC,IAAM,CACLC,iBAAiB,CAACL,MAAM,CAACI,SAAS,CAAC,CACrC,CAEA,GAAIC,iBAAiB,CAACE,IAAI,CAAG,CAAC,CAAE,CAC9BN,qBAAqB,CAACO,GAAG,CAACZ,OAAO,CAAES,iBAAiB,CAAC,CACvD,CAAC,IAAM,CACLJ,qBAAqB,CAACD,MAAM,CAACJ,OAAO,CAAC,CACvC,CAEAlC,qBAAqB,CAACuC,qBAAqB,CAAC,CAC9C,CAAC,CAED,KAAM,CAAAQ,qBAAqB,CAAGA,CAACb,OAAe,CAAEc,MAAc,CAAEb,QAAiB,GAAK,CACpF,KAAM,CAAAK,kBAAkB,CAAG,GAAI,CAAAvC,GAAG,CAACC,eAAe,CAAC,CACnD,KAAM,CAAA+C,cAAc,CAAGT,kBAAkB,CAACI,GAAG,CAACV,OAAO,CAAC,EAAI,GAAI,CAAApC,GAAG,CAAC,CAAC,CAEnE,GAAIqC,QAAQ,CAAE,CACZc,cAAc,CAACZ,GAAG,CAACW,MAAM,CAAC,CAC5B,CAAC,IAAM,CACLC,cAAc,CAACX,MAAM,CAACU,MAAM,CAAC,CAC/B,CAEA,GAAIC,cAAc,CAACJ,IAAI,CAAG,CAAC,CAAE,CAC3BL,kBAAkB,CAACM,GAAG,CAACZ,OAAO,CAAEe,cAAc,CAAC,CACjD,CAAC,IAAM,CACLT,kBAAkB,CAACF,MAAM,CAACJ,OAAO,CAAC,CACpC,CAEA/B,kBAAkB,CAACqC,kBAAkB,CAAC,CACxC,CAAC,CAED,KAAM,CAAAU,YAAY,CAAGA,CAAA,GAAM,CACzB;AACA,KAAM,CAAAC,kBAA4B,CAAG,EAAE,CACvC,KAAM,CAAAC,eAAyB,CAAG,EAAE,CAEpC;AACArD,kBAAkB,CAACsD,OAAO,CAAC,CAAC1C,UAAU,CAAEuB,OAAO,GAAK,CAClDvB,UAAU,CAAC0C,OAAO,CAACC,IAAI,EAAI,CACzB,GAAI,CAACH,kBAAkB,CAACI,QAAQ,CAACD,IAAI,CAAC,CAAE,CACtCH,kBAAkB,CAACK,IAAI,CAACF,IAAI,CAAC,CAC/B,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CAEFpD,eAAe,CAACmD,OAAO,CAAC,CAACzC,OAAO,CAAEsB,OAAO,GAAK,CAC5CtB,OAAO,CAACyC,OAAO,CAACL,MAAM,EAAI,CACxB,GAAI,CAACI,eAAe,CAACG,QAAQ,CAACP,MAAM,CAAC,CAAE,CACrCI,eAAe,CAACI,IAAI,CAACR,MAAM,CAAC,CAC9B,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,GAAIG,kBAAkB,CAACtB,MAAM,CAAG,CAAC,EAAIuB,eAAe,CAACvB,MAAM,CAAG,CAAC,CAAE,CAC/D,KAAM,CAAA4B,YAAuB,CAAG,CAC9BC,IAAI,CAAEvE,gBAAgB,CAACuE,IAAI,CAC3B/C,UAAU,CAAEwC,kBAAkB,CAC9BvC,OAAO,CAAEwC,eACX,CAAC,CAEDhE,QAAQ,CAACqE,YAAY,CAAC,CAEtB;AACA5D,kBAAkB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CAC7BE,qBAAqB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CAChCE,kBAAkB,CAAC,GAAI,CAAAF,GAAG,CAAC,CAAC,CAAC,CAC/B,CACF,CAAC,CAED,KAAM,CAAA0D,aAAa,CAAIzB,OAAe,EAAK,CACzC,GAAI3B,WAAW,GAAK2B,OAAO,CAAE,CAC3B1B,cAAc,CAAC,IAAI,CAAC,CACpBM,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,IAAM,CACL,KAAM,CAAAQ,KAAK,CAAG5B,OAAO,CAACkE,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACrC,WAAW,CAACC,EAAE,GAAKS,OAAO,CAAC,CAC7D1B,cAAc,CAAC0B,OAAO,CAAC,CACvBpB,cAAc,CAACQ,KAAK,EAAI,IAAI,CAAC,CAC/B,CACF,CAAC,CAED,KAAM,CAAAwC,aAAa,CAAIxC,KAAmB,EAAK,CAC7C,KAAM,CAAAyC,WAAW,CAAG/F,sBAAsB,CAACgG,cAAc,CAAC1C,KAAK,CAACE,WAAW,CAAE,CAAAjC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE0B,GAAG,GAAI,EAAE,CAAC,CAEpG,OAAQ8C,WAAW,EACjB,IAAK,OAAO,CACV,mBAAOrF,IAAA,SAAMuF,KAAK,CAAC,iBAAiB,CAAAC,QAAA,cAACxF,IAAA,CAACJ,MAAM,EAACuE,IAAI,CAAE,EAAG,CAACsB,KAAK,CAAC,SAAS,CAAE,CAAC,CAAM,CAAC,CAClF,IAAK,QAAQ,CACX,mBAAOzF,IAAA,SAAMuF,KAAK,CAAC,sBAAmB,CAAAC,QAAA,cAACxF,IAAA,CAACH,KAAK,EAACsE,IAAI,CAAE,EAAG,CAACsB,KAAK,CAAC,SAAS,CAAE,CAAC,CAAM,CAAC,CACnF,IAAK,QAAQ,CACX,mBAAOzF,IAAA,SAAMuF,KAAK,CAAC,kBAAkB,CAAAC,QAAA,cAACxF,IAAA,CAACR,GAAG,EAAC2E,IAAI,CAAE,EAAG,CAACsB,KAAK,CAAC,SAAS,CAAE,CAAC,CAAM,CAAC,CAChF,QACE,mBAAOzF,IAAA,SAAMuF,KAAK,CAAC,oBAAiB,CAAAC,QAAA,cAACxF,IAAA,CAACF,IAAI,EAACqE,IAAI,CAAE,EAAG,CAACsB,KAAK,CAAC,SAAS,CAAE,CAAC,CAAM,CAAC,CAClF,CACF,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,IAAO,CAC9BC,SAAS,CAAE,CACTC,YAAY,CAAE,MAAM,CACpBC,MAAM,CAAE,aAAatF,QAAQ,CAAG,yBAAyB,CAAG,yBAAyB,EAAE,CACvFuF,YAAY,CAAE,MAAM,CACpBC,UAAU,CAAExF,QAAQ,CAChB,+EAA+E,CAC/E,qFAAqF,CACzFyF,cAAc,CAAE,YAAY,CAC5BC,oBAAoB,CAAE,YACxB,CAAC,CACDC,MAAM,CAAE,CACNC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAAe,CAC/BC,OAAO,CAAE,WAAW,CACpBC,MAAM,CAAE,SAAS,CACjBC,YAAY,CAAE1F,UAAU,CAAG,aAAaP,QAAQ,CAAG,0BAA0B,CAAG,0BAA0B,EAAE,CAAG,MACjH,CAAC,CACDkG,UAAU,CAAE,CACVN,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBM,GAAG,CAAE,MACP,CAAC,CACDnB,KAAK,CAAE,CACLoB,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBnB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvCsG,MAAM,CAAE,CACV,CAAC,CACDC,KAAK,CAAE,CACLC,eAAe,CAAE/F,OAAO,CAACmC,MAAM,CAAG,CAAC,CAAG,SAAS,CAAG5C,QAAQ,CAAG,SAAS,CAAG,SAAS,CAClFkF,KAAK,CAAE,SAAS,CAChBkB,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBN,OAAO,CAAE,SAAS,CAClBR,YAAY,CAAE,MAAM,CACpBkB,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,QACb,CAAC,CACDC,UAAU,CAAE,CACVzB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvC4G,UAAU,CAAE,qBAAqB,CACjCC,SAAS,CAAEtG,UAAU,CAAG,gBAAgB,CAAG,cAC7C,CAAC,CACDuG,OAAO,CAAE,CACPf,OAAO,CAAExF,UAAU,CAAG,MAAM,CAAG,GAAG,CAClCwG,SAAS,CAAExG,UAAU,CAAG,OAAO,CAAG,GAAG,CACrCyG,QAAQ,CAAE,QAAiB,CAC3BJ,UAAU,CAAE,eACd,CAAC,CACDK,aAAa,CAAE,CACbrB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBM,GAAG,CAAE,MAAM,CACXd,YAAY,CAAE,MAChB,CAAC,CACD6B,SAAS,CAAE,CACTd,QAAQ,CAAE,MAAM,CAChBlB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvCqG,UAAU,CAAE,KACd,CAAC,CACDc,UAAU,CAAE,CACVX,eAAe,CAAExG,QAAQ,CAAG,uBAAuB,CAAG,0BAA0B,CAChFsF,MAAM,CAAE,aAAatF,QAAQ,CAAG,yBAAyB,CAAG,yBAAyB,EAAE,CACvFuF,YAAY,CAAE,KAAK,CACnBQ,OAAO,CAAE,UAAU,CACnBK,QAAQ,CAAE,MAAM,CAChBlB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvCgG,MAAM,CAAE,SACV,CAAC,CACDoB,WAAW,CAAE,CACXL,SAAS,CAAE,OAAO,CAClBM,SAAS,CAAE,MAAe,CAC1BhC,YAAY,CAAE,MAChB,CAAC,CACDiC,SAAS,CAAE,CACT1B,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,YAAY,CACxB0B,aAAa,CAAE,QAAiB,CAChCpB,GAAG,CAAE,MAAM,CACXJ,OAAO,CAAE,MAAM,CACfV,YAAY,CAAE,KAAK,CACnBmB,eAAe,CAAExG,QAAQ,CAAG,uBAAuB,CAAG,0BAA0B,CAChFuF,YAAY,CAAE,KAAK,CACnBD,MAAM,CAAE,aAAatF,QAAQ,CAAG,yBAAyB,CAAG,0BAA0B,EAAE,CACxF4G,UAAU,CAAE,eACd,CAAC,CACDY,eAAe,CAAE,CACf5B,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBM,GAAG,CAAE,MAAM,CACXsB,KAAK,CAAE,MACT,CAAC,CACDC,QAAQ,CAAE,CACRD,KAAK,CAAE,MAAM,CACbE,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,SAAS,CACtB5B,MAAM,CAAE,SACV,CAAC,CACD6B,SAAS,CAAE,CACTC,IAAI,CAAE,CACR,CAAC,CACDC,UAAU,CAAE,CACV3B,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBnB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvCqF,YAAY,CAAE,KAChB,CAAC,CACD2C,SAAS,CAAE,CACT5B,QAAQ,CAAE,MAAM,CAChBlB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvC4F,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBM,GAAG,CAAE,KACP,CAAC,CACD8B,eAAe,CAAE,CACfzB,eAAe,CAAE,SAAS,CAC1BtB,KAAK,CAAE,SAAS,CAChBkB,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBN,OAAO,CAAE,SAAS,CAClBR,YAAY,CAAE,KAChB,CAAC,CACD2C,aAAa,CAAE,CACbtC,OAAO,CAAE,MAAM,CACfO,GAAG,CAAE,KACP,CAAC,CACDgC,YAAY,CAAE,CACZ3B,eAAe,CAAE,aAAa,CAC9BlB,MAAM,CAAE,aAAatF,QAAQ,CAAG,yBAAyB,CAAG,yBAAyB,EAAE,CACvFuF,YAAY,CAAE,KAAK,CACnBQ,OAAO,CAAE,KAAK,CACdC,MAAM,CAAE,SAAS,CACjBd,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvC4G,UAAU,CAAE,eACd,CAAC,CACDwB,gBAAgB,CAAE,CAChB5B,eAAe,CAAExG,QAAQ,CAAG,yBAAyB,CAAG,0BAA0B,CAClFsF,MAAM,CAAE,aAAatF,QAAQ,CAAG,yBAAyB,CAAG,yBAAyB,EAAE,CACvFuF,YAAY,CAAE,KAAK,CACnBQ,OAAO,CAAE,MAAM,CACfV,YAAY,CAAE,MAAM,CACpBO,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBM,GAAG,CAAE,KACP,CAAC,CACDkC,aAAa,CAAE,CACbjC,QAAQ,CAAE,MAAM,CAChBlB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvCqG,UAAU,CAAE,KACd,CAAC,CACDiC,YAAY,CAAE,CACZ9B,eAAe,CAAE7F,eAAe,CAACiD,IAAI,CAAG,CAAC,CAAG,SAAS,CAAG5D,QAAQ,CAAG,SAAS,CAAG,SAAS,CACxFkF,KAAK,CAAE,SAAS,CAChBI,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBQ,OAAO,CAAE,WAAW,CACpBK,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBL,MAAM,CAAErF,eAAe,CAACiD,IAAI,CAAG,CAAC,CAAG,SAAS,CAAG,aAAa,CAC5DgD,UAAU,CAAE,eAAe,CAC3BhB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBM,GAAG,CAAE,KACP,CAAC,CACDoC,UAAU,CAAE,CACV7B,SAAS,CAAE,QAAiB,CAC5BxB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvCoG,QAAQ,CAAE,MAAM,CAChBoC,SAAS,CAAE,QAAQ,CACnBzC,OAAO,CAAE,MACX,CAAC,CACD0C,gBAAgB,CAAE,CAChBjC,eAAe,CAAExG,QAAQ,CAAG,uBAAuB,CAAG,0BAA0B,CAChFsF,MAAM,CAAE,aAAatF,QAAQ,CAAG,yBAAyB,CAAG,yBAAyB,EAAE,CACvFuF,YAAY,CAAE,KAAK,CACnBQ,OAAO,CAAE,MAAM,CACf2C,SAAS,CAAE,MAAM,CACjBC,SAAS,CAAE,qBACb,CAAC,CACDC,aAAa,CAAE,CACbhD,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBM,GAAG,CAAE,KAAK,CACVd,YAAY,CAAE,MAAM,CACpBe,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBnB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAChC,CAAC,CACD6I,cAAc,CAAE,CACdjD,OAAO,CAAE,MAAM,CACfkD,mBAAmB,CAAE,SAAS,CAC9B3C,GAAG,CAAE,MACP,CAAC,CACD4C,cAAc,CAAE,CACd3C,QAAQ,CAAE,MAAM,CAChBlB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAChC,CAAC,CACDgJ,YAAY,CAAE,CACZ3C,UAAU,CAAE,KAAK,CACjBhB,YAAY,CAAE,KAAK,CACnBH,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAChC,CAAC,CACDiJ,kBAAkB,CAAE,CAClBrD,OAAO,CAAE,MAAM,CACfsD,QAAQ,CAAE,MAAe,CACzB/C,GAAG,CAAE,KAAK,CACVuC,SAAS,CAAE,KACb,CAAC,CACDS,eAAe,CAAE,CACf3C,eAAe,CAAExG,QAAQ,CAAG,yBAAyB,CAAG,yBAAyB,CACjFkF,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvCoG,QAAQ,CAAE,MAAM,CAChBL,OAAO,CAAE,SAAS,CAClBR,YAAY,CAAE,KAAK,CACnBc,UAAU,CAAE,KACd,CAAC,CAED;AACA+C,iBAAiB,CAAE,CACjBV,SAAS,CAAE,MAAM,CACjB3C,OAAO,CAAE,MAAM,CACfS,eAAe,CAAExG,QAAQ,CAAG,0BAA0B,CAAG,0BAA0B,CACnFsF,MAAM,CAAE,aAAatF,QAAQ,CAAG,yBAAyB,CAAG,0BAA0B,EAAE,CACxFuF,YAAY,CAAE,KAChB,CAAC,CAED8D,cAAc,CAAE,CACdjD,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBnB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvCqF,YAAY,CAAE,KAChB,CAAC,CAEDiE,eAAe,CAAE,CACf1D,OAAO,CAAE,MAAM,CACf2B,aAAa,CAAE,QAAiB,CAChCpB,GAAG,CAAE,MACP,CAAC,CAEDoD,eAAe,CAAE,CACf3D,OAAO,CAAE,MAAM,CACf2B,aAAa,CAAE,QAAiB,CAChCpB,GAAG,CAAE,KACP,CAAC,CAEDqD,oBAAoB,CAAE,CACpBpD,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBnB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvCyJ,aAAa,CAAE,WAAoB,CACnCC,aAAa,CAAE,OACjB,CAAC,CAEDC,aAAa,CAAE,CACb/D,OAAO,CAAE,MAAM,CACf2B,aAAa,CAAE,QAAiB,CAChCpB,GAAG,CAAE,KAAK,CACVyD,WAAW,CAAE,KACf,CAAC,CAEDC,YAAY,CAAE,CACZjE,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBM,GAAG,CAAE,KAAK,CACVH,MAAM,CAAE,SAAS,CACjBD,OAAO,CAAE,OACX,CAAC,CAED+D,gBAAgB,CAAE,CAChBrC,KAAK,CAAE,MAAM,CACbE,MAAM,CAAE,MAAM,CACd3B,MAAM,CAAE,SACV,CAAC,CAED+D,gBAAgB,CAAE,CAChB3D,QAAQ,CAAE,MAAM,CAChBlB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAS,CACvCgK,UAAU,CAAE,WACd,CACF,CAAC,CAAC,CAEF,KAAM,CAAAC,MAAM,CAAG9E,gBAAgB,CAAC,CAAC,CAEjC,GAAI,CAAC7E,WAAW,CAAE,MAAO,KAAI,CAE7B,mBACEX,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAAC7E,SAAU,CAAAH,QAAA,eAC3BtF,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAACtE,MAAO,CAACwE,OAAO,CAAEpH,kBAAmB,CAAAkC,QAAA,eACrDtF,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAAC/D,UAAW,CAAAjB,QAAA,eAC5BxF,IAAA,CAACN,GAAG,EAACyE,IAAI,CAAE,EAAG,CAACsB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAU,CAAE,CAAC,cAC1DP,IAAA,OAAIyK,KAAK,CAAED,MAAM,CAACjF,KAAM,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAChDxF,IAAA,SAAMyK,KAAK,CAAED,MAAM,CAAC1D,KAAM,CAAAtB,QAAA,CAAExE,OAAO,CAACmC,MAAM,CAAO,CAAC,EAC/C,CAAC,cACNnD,IAAA,CAACT,WAAW,EAAC4E,IAAI,CAAE,EAAG,CAACsG,KAAK,CAAED,MAAM,CAACtD,UAAW,CAAE,CAAC,EAChD,CAAC,CAELpG,UAAU,eACTd,IAAA,QAAKyK,KAAK,CAAED,MAAM,CAACnD,OAAQ,CAAA7B,QAAA,CACxBxE,OAAO,CAACmC,MAAM,GAAK,CAAC,cACnBjD,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAAC1B,UAAW,CAAAtD,QAAA,EAAC,wDACqB,CAAChF,eAAe,CAAC,IACrE,EAAK,CAAC,cAENN,KAAA,CAAAE,SAAA,EAAAoF,QAAA,eACEtF,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAAChD,aAAc,CAAAhC,QAAA,eAC/BxF,IAAA,SAAMyK,KAAK,CAAED,MAAM,CAAC/C,SAAU,CAAAjC,QAAA,CAAC,YAAU,CAAM,CAAC,cAChDxF,IAAA,WACEyK,KAAK,CAAED,MAAM,CAAC9C,UAAW,CACzBiD,KAAK,CAAE,GAAGjJ,UAAU,CAACkJ,GAAG,IAAIlJ,UAAU,CAACmJ,SAAS,EAAG,CACnDC,QAAQ,CAAGC,CAAC,EAAK,CACf,KAAM,CAACH,GAAG,CAAEC,SAAS,CAAC,CAAGE,CAAC,CAACC,MAAM,CAACL,KAAK,CAACM,KAAK,CAAC,GAAG,CAAC,CAClD,KAAM,CAAAC,MAAM,CAAG5L,sBAAsB,CAACsC,cAAc,CAAC,CAAC,CAACsD,IAAI,CACzDiG,GAAG,EAAIA,GAAG,CAACP,GAAG,GAAKA,GAAG,EAAIO,GAAG,CAACN,SAAS,GAAKA,SAC9C,CAAC,CACD,GAAIK,MAAM,CAAEvJ,aAAa,CAACuJ,MAAM,CAAC,CACnC,CAAE,CAAA1F,QAAA,CAEDlG,sBAAsB,CAACsC,cAAc,CAAC,CAAC,CAACwJ,GAAG,CAACF,MAAM,eACjDlL,IAAA,WAAkD2K,KAAK,CAAE,GAAGO,MAAM,CAACN,GAAG,IAAIM,MAAM,CAACL,SAAS,EAAG,CAAArF,QAAA,CAC1F0F,MAAM,CAACG,KAAK,EADF,GAAGH,MAAM,CAACN,GAAG,IAAIM,MAAM,CAACL,SAAS,EAEtC,CACT,CAAC,CACI,CAAC,EACN,CAAC,CAEL9I,SAAS,CAACE,UAAU,CAACkB,MAAM,CAAG,CAAC,EAAIpB,SAAS,CAACG,OAAO,CAACiB,MAAM,CAAG,CAAC,cAC9DjD,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAAC7B,gBAAiB,CAAAnD,QAAA,eAClCxF,IAAA,CAACL,aAAa,EAACwE,IAAI,CAAE,EAAG,CAAE,CAAC,cAC3BjE,KAAA,SAAMuK,KAAK,CAAED,MAAM,CAAC5B,aAAc,CAAApD,QAAA,EAAC,2BACd,CAACzD,SAAS,CAACE,UAAU,CAACkB,MAAM,CAAC,gBAAc,CAACpB,SAAS,CAACG,OAAO,CAACiB,MAAM,CAAC,gBAC1F,EAAM,CAAC,EACJ,CAAC,CACJ,IAAI,cAERnD,IAAA,QAAKyK,KAAK,CAAED,MAAM,CAAC7C,WAAY,CAAAnC,QAAA,CAC5BxE,OAAO,CAACoK,GAAG,CAACxI,KAAK,OAAA0I,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAChBzL,KAAA,QAAgCuK,KAAK,CAAED,MAAM,CAAC3C,SAAU,CAAC+D,SAAS,CAAC,oBAAoB,CAAApG,QAAA,eACrFtF,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAACzC,eAAgB,CAAAvC,QAAA,eACjCxF,IAAA,UACE6L,IAAI,CAAC,UAAU,CACfpB,KAAK,CAAED,MAAM,CAACvC,QAAS,CACvB6D,OAAO,CAAE5K,eAAe,CAAC2B,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAE,CACnD+H,QAAQ,CAAGC,CAAC,EAAKxH,oBAAoB,CAACX,KAAK,CAACE,WAAW,CAACC,EAAE,CAAEgI,CAAC,CAACC,MAAM,CAACc,OAAO,CAAE,CAC/E,CAAC,cACF5L,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAACpC,SAAU,CAAA5C,QAAA,eAC3BxF,IAAA,QAAKyK,KAAK,CAAED,MAAM,CAAClC,UAAW,CAAA9C,QAAA,CAAE5C,KAAK,CAACE,WAAW,CAACyC,KAAK,CAAM,CAAC,cAC9DrF,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAACjC,SAAU,CAACqD,SAAS,CAAC,oBAAoB,CAAApG,QAAA,eAC1DxF,IAAA,CAACP,KAAK,EAAC0E,IAAI,CAAE,EAAG,CAAE,CAAC,CAClBvB,KAAK,CAACE,WAAW,CAACiJ,SAAS,CAACC,kBAAkB,CAAC,OAAO,CAAC,cACxD9L,KAAA,SAAMuK,KAAK,CAAED,MAAM,CAAChC,eAAgB,CAACoD,SAAS,CAAC,0BAA0B,CAAApG,QAAA,EACtEyG,IAAI,CAACC,KAAK,CAACtJ,KAAK,CAACuJ,UAAU,CAAC,CAAC,GAChC,EAAM,CAAC,CACN/G,aAAa,CAACxC,KAAK,CAAC,CACpBA,KAAK,CAACwJ,QAAQ,eAAIpM,IAAA,SAAMyK,KAAK,CAAE,CAAE9D,QAAQ,CAAE,MAAM,CAAElB,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAU,CAAE,CAAAiF,QAAA,CAAC,YAAO,CAAM,CAAC,EAC1G,CAAC,EACH,CAAC,cACNxF,IAAA,QAAKyK,KAAK,CAAED,MAAM,CAAC/B,aAAc,CAAAjD,QAAA,cAC/BxF,IAAA,WACEyK,KAAK,CAAED,MAAM,CAAC9B,YAAa,CAC3BkD,SAAS,CAAC,uBAAuB,CACjClB,OAAO,CAAEA,CAAA,GAAMzF,aAAa,CAACrC,KAAK,CAACE,WAAW,CAACC,EAAE,CAAE,CACnDwC,KAAK,CAAC,gBAAa,CAAAC,QAAA,cAEnBxF,IAAA,CAACR,GAAG,EAAC2E,IAAI,CAAE,EAAG,CAAE,CAAC,CACX,CAAC,CACN,CAAC,EACH,CAAC,CAGLtC,WAAW,GAAKe,KAAK,CAACE,WAAW,CAACC,EAAE,EAAIZ,WAAW,eAClDjC,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAACxB,gBAAiB,CAAC4C,SAAS,CAAC,2BAA2B,CAAApG,QAAA,eACxEtF,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAACrB,aAAc,CAAA3D,QAAA,eAC/BxF,IAAA,CAACR,GAAG,EAAC2E,IAAI,CAAE,EAAG,CAAE,CAAC,4BACI,EAAAmH,qBAAA,CAAC1I,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,UAAAoI,qBAAA,iBAAxBA,qBAAA,CAA0BtG,IAAI,CAAC,IACvD,EAAK,CAAC,cACN9E,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAACpB,cAAe,CAACwC,SAAS,CAAC,yBAAyB,CAAApG,QAAA,eACpEtF,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAAClB,cAAe,CAAA9D,QAAA,eAChCxF,IAAA,QAAKyK,KAAK,CAAED,MAAM,CAACjB,YAAa,CAAA/D,QAAA,CAAC,uBAAqB,CAAK,CAAC,cAC5DxF,IAAA,QAAAwF,QAAA,EAAA+F,qBAAA,CAAMpJ,WAAW,CAACA,WAAW,UAAAoJ,qBAAA,iBAAvBA,qBAAA,CAAyBc,YAAY,CAAM,CAAC,EAC/C,CAAC,cACNnM,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAAClB,cAAe,CAAA9D,QAAA,eAChCxF,IAAA,QAAKyK,KAAK,CAAED,MAAM,CAACjB,YAAa,CAAA/D,QAAA,CAAC,mBAAc,CAAK,CAAC,cACrDtF,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAAChB,kBAAmB,CAACoC,SAAS,CAAC,8BAA8B,CAAApG,QAAA,GAAAgG,sBAAA,CAC5ErJ,WAAW,CAACA,WAAW,UAAAqJ,sBAAA,iBAAvBA,sBAAA,CAAyBc,cAAc,CAAClB,GAAG,CAAC,CAACQ,SAAS,CAAEW,GAAG,gBAC1DvM,IAAA,SAAgByK,KAAK,CAAED,MAAM,CAACd,eAAgB,CAAAlE,QAAA,CAC3CoG,SAAS,EADDW,GAEL,CACP,CAAC,CACD,CAAC,GAAAd,sBAAA,CAACtJ,WAAW,CAACA,WAAW,UAAAsJ,sBAAA,WAAvBA,sBAAA,CAAyBa,cAAc,GAAInK,WAAW,CAACA,WAAW,CAACmK,cAAc,CAACnJ,MAAM,GAAK,CAAC,gBAC/FnD,IAAA,SAAMyK,KAAK,CAAE,CAAE1B,SAAS,CAAE,QAAQ,CAAEtD,KAAK,CAAElF,QAAQ,CAAG,SAAS,CAAG,SAAU,CAAE,CAAAiF,QAAA,CAAC,sCAE/E,CAAM,CACP,EACE,CAAC,EACH,CAAC,EACH,CAAC,EACH,CACN,CAGAtE,eAAe,CAAC2B,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,eACxC7C,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAACb,iBAAkB,CAACiC,SAAS,CAAC,4BAA4B,CAAApG,QAAA,eAC1ExF,IAAA,QAAKyK,KAAK,CAAED,MAAM,CAACZ,cAAe,CAAApE,QAAA,cAChCxF,IAAA,SAAAwF,QAAA,CAAM,oDAAsC,CAAM,CAAC,CAChD,CAAC,cAENtF,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAACX,eAAgB,CAAArE,QAAA,EAEhC,EAAAkG,sBAAA,CAAA9I,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,UAAAwI,sBAAA,iBAAxBA,sBAAA,CAA0BzJ,UAAU,GAAIW,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAACjB,UAAU,CAACkB,MAAM,CAAG,CAAC,eACrFjD,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAACV,eAAgB,CAAAtE,QAAA,eACjCxF,IAAA,QAAKyK,KAAK,CAAED,MAAM,CAACT,oBAAqB,CAAAvE,QAAA,CAAC,aAAW,CAAK,CAAC,cAC1DxF,IAAA,QAAKyK,KAAK,CAAED,MAAM,CAACN,aAAc,CAAA1E,QAAA,CAC9B5C,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAACjB,UAAU,CAACmJ,GAAG,CAAC,CAACpH,SAAS,CAAEuI,GAAG,QAAAC,qBAAA,oBACtDtM,KAAA,UAAiBuK,KAAK,CAAED,MAAM,CAACJ,YAAa,CAAA5E,QAAA,eAC1CxF,IAAA,UACE6L,IAAI,CAAC,UAAU,CACfpB,KAAK,CAAED,MAAM,CAACH,gBAAiB,CAC/ByB,OAAO,CAAE,EAAAU,qBAAA,CAAAnL,kBAAkB,CAAC6C,GAAG,CAACtB,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,UAAAyJ,qBAAA,iBAA5CA,qBAAA,CAA8C3J,GAAG,CAACmB,SAAS,CAAC,GAAI,KAAM,CAC/E8G,QAAQ,CAAGC,CAAC,EAAKhH,wBAAwB,CAACnB,KAAK,CAACE,WAAW,CAACC,EAAE,CAAEiB,SAAS,CAAE+G,CAAC,CAACC,MAAM,CAACc,OAAO,CAAE,CAC9F,CAAC,cACF9L,IAAA,SAAMyK,KAAK,CAAED,MAAM,CAACF,gBAAiB,CAAA9E,QAAA,CAAExB,SAAS,CAAO,CAAC,GAP9CuI,GAQL,CAAC,EACT,CAAC,CACC,CAAC,EACH,CACN,CAGA,EAAAZ,sBAAA,CAAA/I,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,UAAAyI,sBAAA,iBAAxBA,sBAAA,CAA0BzJ,OAAO,GAAIU,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAAChB,OAAO,CAACiB,MAAM,CAAG,CAAC,eAC/EjD,KAAA,QAAKuK,KAAK,CAAED,MAAM,CAACV,eAAgB,CAAAtE,QAAA,eACjCxF,IAAA,QAAKyK,KAAK,CAAED,MAAM,CAACT,oBAAqB,CAAAvE,QAAA,CAAC,eAAU,CAAK,CAAC,cACzDxF,IAAA,QAAKyK,KAAK,CAAED,MAAM,CAACN,aAAc,CAAA1E,QAAA,CAC9B5C,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAAChB,OAAO,CAACkJ,GAAG,CAAC,CAAC9G,MAAM,CAAEiI,GAAG,QAAAE,oBAAA,oBAChDvM,KAAA,UAAiBuK,KAAK,CAAED,MAAM,CAACJ,YAAa,CAAA5E,QAAA,eAC1CxF,IAAA,UACE6L,IAAI,CAAC,UAAU,CACfpB,KAAK,CAAED,MAAM,CAACH,gBAAiB,CAC/ByB,OAAO,CAAE,EAAAW,oBAAA,CAAAjL,eAAe,CAAC0C,GAAG,CAACtB,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,UAAA0J,oBAAA,iBAAzCA,oBAAA,CAA2C5J,GAAG,CAACyB,MAAM,CAAC,GAAI,KAAM,CACzEwG,QAAQ,CAAGC,CAAC,EAAK1G,qBAAqB,CAACzB,KAAK,CAACE,WAAW,CAACC,EAAE,CAAEuB,MAAM,CAAEyG,CAAC,CAACC,MAAM,CAACc,OAAO,CAAE,CACxF,CAAC,cACF9L,IAAA,SAAMyK,KAAK,CAAED,MAAM,CAACF,gBAAiB,CAAA9E,QAAA,CAAElB,MAAM,CAAO,CAAC,GAP3CiI,GAQL,CAAC,EACT,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,EACH,CACN,GAhHO3J,KAAK,CAACE,WAAW,CAACC,EAiHvB,CAAC,EACP,CAAC,CACC,CAAC,cAEN7C,KAAA,WACEuK,KAAK,CAAED,MAAM,CAAC3B,YAAa,CAC3B6B,OAAO,CAAElG,YAAa,CACtBkI,QAAQ,CAAE,CAAC,IAAM,CACf,KAAM,CAAAC,eAAe,CAAGC,KAAK,CAACC,IAAI,CAACxL,kBAAkB,CAACyL,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,CAAEC,KAAK,GAAKD,GAAG,CAAGC,KAAK,CAAC9I,IAAI,CAAE,CAAC,CAAC,CAC3G,KAAM,CAAA+I,YAAY,CAAGN,KAAK,CAACC,IAAI,CAACrL,eAAe,CAACsL,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,CAAE9K,OAAO,GAAK8K,GAAG,CAAG9K,OAAO,CAACiC,IAAI,CAAE,CAAC,CAAC,CACzG,MAAO,CAAAwI,eAAe,CAAGO,YAAY,GAAK,CAAC,CAC7C,CAAC,EAAE,CAAE,CAAA1H,QAAA,eAELxF,IAAA,CAACN,GAAG,EAACyE,IAAI,CAAE,EAAG,CAAE,CAAC,CAChB,CAAC,IAAM,CACN,KAAM,CAAAwI,eAAe,CAAGC,KAAK,CAACC,IAAI,CAACxL,kBAAkB,CAACyL,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,CAAEC,KAAK,GAAKD,GAAG,CAAGC,KAAK,CAAC9I,IAAI,CAAE,CAAC,CAAC,CAC3G,KAAM,CAAA+I,YAAY,CAAGN,KAAK,CAACC,IAAI,CAACrL,eAAe,CAACsL,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,CAAE9K,OAAO,GAAK8K,GAAG,CAAG9K,OAAO,CAACiC,IAAI,CAAE,CAAC,CAAC,CACzG,KAAM,CAAAgJ,aAAa,CAAGR,eAAe,CAAGO,YAAY,CAEpD,GAAIC,aAAa,GAAK,CAAC,CAAE,CACvB,MAAO,aAAajM,eAAe,CAACiD,IAAI,aAAajD,eAAe,CAACiD,IAAI,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,eAAejD,eAAe,CAACiD,IAAI,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,GAAG,CAC/I,CAEA,MAAO,aAAagJ,aAAa,WAAWA,aAAa,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,eAAeA,aAAa,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,GAAG,CACxH,CAAC,EAAE,CAAC,EACE,CAAC,EACT,CACH,CACE,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9M,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}