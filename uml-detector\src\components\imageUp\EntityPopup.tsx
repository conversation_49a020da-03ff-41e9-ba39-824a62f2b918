// EntityPopup.tsx
import React from 'react';
import { getEntityPopupStyles } from './EntityPopupStyles';
import HistoryAnalysisSection from './HistoryAnalysisSection';
import ConflictResolutionModal from './ConflictResolutionModal';
import { ClassData } from '../../services/HistoryAnalysisService';

interface EntityPopupProps {
  darkMode: boolean;
  entityName: string;
  position: { x: number; y: number };
  onClose: () => void;
  onModify: (selectedAttributes: string[], selectedMethods: string[]) => void;
  memoryAttributes: string[]; // Attributs de la classe depuis memoire.txt
  memoryMethods: string[];    // Méthodes de la classe depuis memoire.txt
  extractedAttributes: string[]; // Attributs extraits du texte actuel
  extractedMethods: string[];    // Méthodes extraites du texte actuel
}

const EntityPopup: React.FC<EntityPopupProps> = ({
  darkMode,
  entityName,
  position,
  onClose,
  onModify,
  memoryAttributes = [],
  memoryMethods = [],
  extractedAttributes = [],
  extractedMethods = []
}) => {
  // État pour les checkboxes
  const [selectedAttributes, setSelectedAttributes] = React.useState<string[]>([]);
  const [selectedMethods, setSelectedMethods] = React.useState<string[]>([]);

  // États pour l'analyse historique
  const [showConflictModal, setShowConflictModal] = React.useState(false);
  const [pendingImport, setPendingImport] = React.useState<{
    importedData: ClassData;
    conflicts: { attributes: string[], methods: string[] };
  } | null>(null);

  // Données de classe actuelle pour l'analyse historique
  const currentClassData: ClassData = React.useMemo(() => ({
    name: entityName,
    attributes: [...memoryAttributes, ...extractedAttributes],
    methods: [...memoryMethods, ...extractedMethods]
  }), [entityName, memoryAttributes, extractedAttributes, memoryMethods, extractedMethods]);

  // Filtrer pour n'afficher que les attributs et méthodes qui ne sont pas dans le texte extrait
  // Modifier la logique de filtrage pour être insensible à la casse
  const uniqueAttributes = memoryAttributes.filter(attr => {
    // Extraire le nom de l'attribut sans le type ou la visibilité
    const attrName = attr.replace(/^[+-]?\s*/, '').split(':')[0].trim().toLowerCase();
    
    return !extractedAttributes.some(extractedAttr => {
      // Extraire le nom de l'attribut extrait
      const extractedAttrName = extractedAttr.replace(/^[+-]?\s*/, '').split(':')[0].trim().toLowerCase();
      return extractedAttrName === attrName;
    });
  });

  const uniqueMethods = memoryMethods.filter(method => {
    // Extraire le nom de la méthode sans les paramètres
    const methodName = method.replace(/^[+-]?\s*/, '').split('(')[0].trim().toLowerCase();
    
    return !extractedMethods.some(extractedMethod => {
      // Extraire le nom de la méthode extraite
      const extractedMethodName = extractedMethod.replace(/^[+-]?\s*/, '').split('(')[0].trim().toLowerCase();
      return extractedMethodName === methodName;
    });
  });

  // Gestionnaires pour les checkboxes
  const handleAttributeToggle = (attribute: string) => {
    setSelectedAttributes(prev => 
      prev.includes(attribute) 
        ? prev.filter(attr => attr !== attribute) 
        : [...prev, attribute]
    );
  };

  const handleMethodToggle = (method: string) => {
    setSelectedMethods(prev =>
      prev.includes(method)
        ? prev.filter(m => m !== method)
        : [...prev, method]
    );
  };

  // Fonction pour gérer l'import depuis l'historique
  const handleHistoryImport = (importedData: ClassData) => {
    // Détecter les conflits
    const conflicts = {
      attributes: importedData.attributes.filter(attr =>
        currentClassData.attributes.some(existing =>
          existing.toLowerCase().includes(attr.toLowerCase()) ||
          attr.toLowerCase().includes(existing.toLowerCase())
        )
      ),
      methods: importedData.methods.filter(method =>
        currentClassData.methods.some(existing =>
          existing.toLowerCase().includes(method.toLowerCase()) ||
          method.toLowerCase().includes(existing.toLowerCase())
        )
      )
    };

    if (conflicts.attributes.length > 0 || conflicts.methods.length > 0) {
      // Il y a des conflits, ouvrir le modal de résolution
      setPendingImport({ importedData, conflicts });
      setShowConflictModal(true);
    } else {
      // Pas de conflits, fusionner directement
      applyImportedData(importedData);
    }
  };

  // Fonction pour appliquer les données importées
  const applyImportedData = (importedData: ClassData) => {
    // Mettre à jour les sélections avec les nouvelles données
    const combinedAttributes = [...selectedAttributes, ...importedData.attributes];
    const combinedMethods = [...selectedMethods, ...importedData.methods];

    // Supprimer les doublons manuellement
    const newAttributes = combinedAttributes.filter((attr, index) =>
      combinedAttributes.indexOf(attr) === index
    );
    const newMethods = combinedMethods.filter((method, index) =>
      combinedMethods.indexOf(method) === index
    );

    setSelectedAttributes(newAttributes);
    setSelectedMethods(newMethods);
  };

  // Fonction pour résoudre les conflits
  const handleConflictResolution = (resolvedData: ClassData) => {
    applyImportedData(resolvedData);
    setShowConflictModal(false);
    setPendingImport(null);
  };

  // Fonction pour annuler la résolution de conflits
  const handleConflictCancel = () => {
    setShowConflictModal(false);
    setPendingImport(null);
  };
  
  // Fonction pour gérer le clic sur le bouton Modifier
  const handleModifyClick = () => {
    onModify(selectedAttributes, selectedMethods);
  };

  // Gestionnaires d'événements pour les effets hover
  const handleMouseEnter = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      (e.target as HTMLButtonElement).style.backgroundColor = darkMode ? '#475569' : '#f3f4f6';
    }
  };

  const handleMouseLeave = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      (e.target as HTMLButtonElement).style.backgroundColor = darkMode ? '#334155' : '#f8fafc';
    }
  };

  const handleModifyHover = (e: React.MouseEvent, isEnter: boolean) => {
    (e.target as HTMLButtonElement).style.backgroundColor = isEnter ? '#2563eb' : '#3b82f6';
    (e.target as HTMLButtonElement).style.transform = isEnter ? 'translateY(-1px)' : 'translateY(0)';
  };

  const handleCancelHover = (e: React.MouseEvent, isEnter: boolean) => {
    (e.target as HTMLButtonElement).style.backgroundColor = isEnter 
      ? (darkMode ? '#374151' : '#f9fafb') 
      : 'transparent';
  };

  // Gérer le clic sur l'overlay pour fermer le popup
  const handleOverlayClick = (e: React.MouseEvent) => {
    // Fermer seulement si on clique sur l'overlay, pas sur le popup
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Empêcher la propagation du clic depuis le popup vers l'overlay
  const handlePopupClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // Obtenir les styles
  const styles = getEntityPopupStyles(darkMode);

  return (
    <div style={styles.overlay} onClick={handleOverlayClick}>
      <div style={styles.popup} onClick={handlePopupClick}>
        <div style={styles.header}>
          <div>
            <h3 style={styles.title}>{entityName}</h3>
            <div style={styles.subtitle}>UML IA propose plusieurs choix possibles pour cette entité.</div>
          </div>
          <button 
            style={styles.closeButton} 
            onClick={onClose}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            ×
          </button>
        </div>

        <div style={styles.content}>
          {/* Section d'analyse historique */}
          <HistoryAnalysisSection
            darkMode={darkMode}
            targetClassName={entityName}
            currentClassData={currentClassData}
            onImport={handleHistoryImport}
          />
          <div style={styles.tableContainer}>
            <table style={styles.table}>
              <thead style={styles.tableHeader}>
                <tr>
                  <th style={styles.tableHeaderCell}>ATTRIBUTS</th>
                  <th style={styles.tableHeaderCell}>MÉTHODES</th>
                </tr>
              </thead>
              <tbody style={styles.tableBody}>
                <tr style={styles.tableRow}>
                  <td style={styles.tableCell}>
                    {uniqueAttributes.length > 0 ? (
                      uniqueAttributes.map((attr, index) => (
                        <div key={index} style={styles.checkboxContainer}>
                          <input 
                            type="checkbox" 
                            id={`attr-${index}`}
                            checked={selectedAttributes.includes(attr)}
                            onChange={() => handleAttributeToggle(attr)}
                            style={styles.checkbox}
                          />
                          <label 
                            htmlFor={`attr-${index}`}
                            style={styles.checkboxLabel}
                          >
                            {attr}
                          </label>
                        </div>
                      ))
                    ) : (
                      <div style={styles.emptyState}>
                        Aucun attribut supplémentaire
                      </div>
                    )}
                  </td>
                  <td style={styles.tableCell}>
                    {uniqueMethods.length > 0 ? (
                      uniqueMethods.map((method, index) => (
                        <div key={index} style={styles.checkboxContainer}>
                          <input 
                            type="checkbox" 
                            id={`method-${index}`}
                            checked={selectedMethods.includes(method)}
                            onChange={() => handleMethodToggle(method)}
                            style={styles.checkbox}
                          />
                          <label 
                            htmlFor={`method-${index}`}
                            style={styles.checkboxLabel}
                          >
                            {method}
                          </label>
                        </div>
                      ))
                    ) : (
                      <div style={styles.emptyState}>
                        Aucune méthode supplémentaire
                      </div>
                    )}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div style={styles.buttonContainer}>
            <button 
              style={styles.modifyButton} 
              onClick={handleModifyClick}
              onMouseEnter={(e) => handleModifyHover(e, true)}
              onMouseLeave={(e) => handleModifyHover(e, false)}
            >
              <span>✏️</span>
              Modifier l'entité
            </button>
            <button 
              style={styles.cancelButton} 
              onClick={onClose}
              onMouseEnter={(e) => handleCancelHover(e, true)}
              onMouseLeave={(e) => handleCancelHover(e, false)}
            >
              Annuler
            </button>
          </div>
        </div>
      </div>

      {/* Modal de résolution de conflits */}
      {pendingImport && (
        <ConflictResolutionModal
          darkMode={darkMode}
          isOpen={showConflictModal}
          currentClass={currentClassData}
          importedClasses={[pendingImport.importedData]}
          conflicts={pendingImport.conflicts}
          onResolve={handleConflictResolution}
          onCancel={handleConflictCancel}
        />
      )}
    </div>
  );
};

export default EntityPopup;