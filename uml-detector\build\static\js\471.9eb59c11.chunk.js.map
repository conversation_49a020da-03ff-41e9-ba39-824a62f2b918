{"version": 3, "file": "static/js/471.9eb59c11.chunk.js", "mappings": "mMA0BIA,EAAS,WACX,IAAIC,GAAoBC,EAAAA,EAAAA,KAAO,SAASC,EAAGC,EAAGC,EAAIC,GAChD,IAAKD,EAAKA,GAAM,CAAC,EAAGC,EAAIH,EAAEI,OAAQD,IAAKD,EAAGF,EAAEG,IAAMF,GAClD,OAAOC,CACT,GAAG,KAAMG,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAChtBC,EAAU,CACZC,OAAuBrC,EAAAA,EAAAA,KAAO,WAC9B,GAAG,SACHsC,GAAI,CAAC,EACLC,SAAU,CAAE,MAAS,EAAG,MAAS,EAAG,MAAS,EAAG,GAAM,EAAG,GAAM,EAAG,SAAY,EAAG,KAAQ,EAAG,UAAa,EAAG,kBAAqB,GAAI,eAAkB,GAAI,kBAAqB,GAAI,YAAe,GAAI,MAAS,GAAI,SAAO,GAAI,WAAc,GAAI,MAAS,GAAI,MAAS,GAAI,eAAkB,GAAI,aAAgB,GAAI,YAAe,GAAI,YAAe,GAAI,GAAM,GAAI,GAAM,GAAI,KAAQ,GAAI,KAAQ,GAAI,OAAU,GAAI,WAAc,GAAI,KAAQ,GAAI,aAAgB,GAAI,UAAa,GAAI,UAAa,GAAI,UAAa,GAAI,gBAAmB,GAAI,UAAa,GAAI,gBAAmB,GAAI,0BAA6B,GAAI,SAAY,GAAI,YAAe,GAAI,mBAAsB,GAAI,QAAW,GAAI,MAAS,GAAI,UAAa,GAAI,mBAAsB,GAAI,MAAS,GAAI,gBAAmB,GAAI,WAAc,GAAI,aAAgB,GAAI,aAAgB,GAAI,aAAgB,GAAI,aAAgB,GAAI,IAAO,GAAI,IAAK,GAAI,WAAc,GAAI,gBAAmB,GAAI,QAAW,GAAI,SAAY,GAAI,QAAW,EAAG,KAAQ,GACt9BC,WAAY,CAAE,EAAG,QAAS,EAAG,QAAS,EAAG,KAAM,EAAG,KAAM,GAAI,QAAS,GAAI,SAAO,GAAI,aAAc,GAAI,QAAS,GAAI,QAAS,GAAI,iBAAkB,GAAI,eAAgB,GAAI,cAAe,GAAI,cAAe,GAAI,KAAM,GAAI,KAAM,GAAI,OAAQ,GAAI,OAAQ,GAAI,SAAU,GAAI,aAAc,GAAI,OAAQ,GAAI,YAAa,GAAI,YAAa,GAAI,kBAAmB,GAAI,YAAa,GAAI,kBAAmB,GAAI,4BAA6B,GAAI,WAAY,GAAI,cAAe,GAAI,qBAAsB,GAAI,UAAW,GAAI,QAAS,GAAI,YAAa,GAAI,qBAAsB,GAAI,QAAS,GAAI,kBAAmB,GAAI,aAAc,GAAI,eAAgB,GAAI,eAAgB,GAAI,eAAgB,GAAI,eAAgB,GAAI,IAAK,GAAI,aAAc,GAAI,kBAAmB,GAAI,UAAW,GAAI,YACrvBC,aAAc,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,IACtZC,eAA+B1C,EAAAA,EAAAA,KAAO,SAAmB2C,EAAQC,EAAQC,EAAUP,EAAIQ,EAASC,EAAIC,GAClG,IAAIC,EAAKF,EAAG1C,OAAS,EACrB,OAAQyC,GACN,KAAK,EAEH,OADAR,EAAGY,WAAWH,EAAGE,IACVF,EAAGE,GAEZ,KAAK,EACHE,KAAKC,EAAI,GACT,MACF,KAAK,EACW,MAAVL,EAAGE,KACLF,EAAGE,EAAK,GAAGI,KAAKN,EAAGE,IACnBE,KAAKC,EAAIL,EAAGE,EAAK,IAEnB,MACF,KAAK,EACL,KAAK,EAML,KAAK,GACHE,KAAKC,EAAIL,EAAGE,GACZ,MALF,KAAK,EACHE,KAAKC,EAAI,KACT,MAIF,KAAK,GACH,MAAME,EAAYP,EAAGE,EAAK,GAC1BK,EAAUC,YAAcjB,EAAGkB,UAAUT,EAAGE,IACxCE,KAAKC,EAAIE,EACT,MACF,KAAK,GACHH,KAAKC,EAAI,CAAEK,KAAM,WAAYC,OAAQX,EAAGE,EAAK,GAAIU,OAAQZ,EAAGE,IAC5D,MACF,KAAK,GACH,MAAMW,EAAiBtB,EAAGkB,UAAUT,EAAGE,IACvCE,KAAKC,EAAI,CAAEK,KAAM,WAAYC,OAAQX,EAAGE,EAAK,GAAIU,OAAQZ,EAAGE,EAAK,GAAIM,YAAaK,GAClF,MACF,KAAK,GACHT,KAAKC,EAAI,CAAEK,KAAM,QAASI,GAAId,EAAGE,EAAK,GAAIa,KAAM,UAAWP,YAAa,GAAIQ,IAAKhB,EAAGE,EAAK,IACzF,MACF,KAAK,GACH,IAAIY,EAAKd,EAAGE,GACRM,EAAcR,EAAGE,EAAK,GAAGe,OAC7B,GAAIjB,EAAGE,GAAIgB,MAAM,KAAM,CACrB,IAAIC,EAAQnB,EAAGE,GAAIkB,MAAM,KACzBN,EAAKK,EAAM,GACXX,EAAc,CAACA,EAAaW,EAAM,GACpC,CACAf,KAAKC,EAAI,CAAEK,KAAM,QAASI,KAAIC,KAAM,UAAWP,eAC/C,MACF,KAAK,GACHJ,KAAKC,EAAI,CAAEK,KAAM,QAASI,GAAId,EAAGE,EAAK,GAAIa,KAAM,UAAWP,YAAaR,EAAGE,EAAK,GAAIc,IAAKhB,EAAGE,EAAK,IACjG,MACF,KAAK,GACHE,KAAKC,EAAI,CAAEK,KAAM,QAASI,GAAId,EAAGE,GAAKa,KAAM,QAC5C,MACF,KAAK,GACHX,KAAKC,EAAI,CAAEK,KAAM,QAASI,GAAId,EAAGE,GAAKa,KAAM,QAC5C,MACF,KAAK,GACHX,KAAKC,EAAI,CAAEK,KAAM,QAASI,GAAId,EAAGE,GAAKa,KAAM,UAC5C,MACF,KAAK,GACHX,KAAKC,EAAI,CAAEK,KAAM,QAASI,GAAIvB,EAAG8B,eAAgBN,KAAM,WACvD,MACF,KAAK,GACHX,KAAKC,EAAI,CAAEK,KAAM,QAASI,GAAId,EAAGE,EAAK,GAAGe,OAAQK,KAAM,CAAEC,SAAUvB,EAAGE,EAAK,GAAGe,OAAQO,KAAMxB,EAAGE,GAAIe,SACnG,MACF,KAAK,GACHb,KAAKC,EAAIL,EAAGE,GAAIe,OAChB1B,EAAGkC,YAAYrB,KAAKC,GACpB,MACF,KAAK,GACL,KAAK,GACHD,KAAKC,EAAIL,EAAGE,GAAIe,OAChB1B,EAAGmC,kBAAkBtB,KAAKC,GAC1B,MACF,KAAK,GACL,KAAK,GACHD,KAAKC,EAAI,CAAEK,KAAM,WAAYI,GAAId,EAAGE,EAAK,GAAGe,OAAQU,QAAS3B,EAAGE,GAAIe,QACpE,MACF,KAAK,GACHb,KAAKC,EAAI,CAAEK,KAAM,QAASI,GAAId,EAAGE,EAAK,GAAGe,OAAQW,WAAY5B,EAAGE,GAAIe,QACpE,MACF,KAAK,GACHb,KAAKC,EAAI,CAAEK,KAAM,aAAcI,GAAId,EAAGE,EAAK,GAAGe,OAAQW,WAAY5B,EAAGE,GAAIe,QACzE,MACF,KAAK,GACH1B,EAAGsC,aAAa,MAChBzB,KAAKC,EAAI,CAAEK,KAAM,MAAOoB,MAAO,MAC/B,MACF,KAAK,GACHvC,EAAGsC,aAAa,MAChBzB,KAAKC,EAAI,CAAEK,KAAM,MAAOoB,MAAO,MAC/B,MACF,KAAK,GACHvC,EAAGsC,aAAa,MAChBzB,KAAKC,EAAI,CAAEK,KAAM,MAAOoB,MAAO,MAC/B,MACF,KAAK,GACHvC,EAAGsC,aAAa,MAChBzB,KAAKC,EAAI,CAAEK,KAAM,MAAOoB,MAAO,MAC/B,MACF,KAAK,GACL,KAAK,GACH1B,KAAKC,EAAI,CAAEK,KAAM,QAASI,GAAId,EAAGE,GAAIe,OAAQF,KAAM,UAAWP,YAAa,IAC3E,MACF,KAAK,GAGL,KAAK,GACHJ,KAAKC,EAAI,CAAEK,KAAM,QAASI,GAAId,EAAGE,EAAK,GAAGe,OAAQU,QAAS,CAAC3B,EAAGE,GAAIe,QAASF,KAAM,UAAWP,YAAa,IAG/G,GAAG,aACHuB,MAAO,CAAC,CAAE,EAAG,EAAG,EAAGxE,EAAK,EAAGC,EAAK,EAAGC,GAAO,CAAE,EAAG,CAAC,IAAM,CAAE,EAAG,EAAG,EAAGF,EAAK,EAAGC,EAAK,EAAGC,GAAO,CAAE,EAAG,EAAG,EAAGF,EAAK,EAAGC,EAAK,EAAGC,GAAOT,EAAE,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKU,EAAK,CAAE,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,GAAI,EAAGC,EAAK,EAAGC,EAAK,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAOjC,EAAEkC,EAAK,CAAC,EAAG,IAAK,CAAE,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIrB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAOjC,EAAEkC,EAAK,CAAC,EAAG,IAAKlC,EAAEkC,EAAK,CAAC,EAAG,IAAKlC,EAAEkC,EAAK,CAAC,EAAG,IAAKlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,MAAQlC,EAAEkC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAOlC,EAAEkC,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,GAAI,CAAC,EAAG,KAAOlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,GAAI,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAAOlC,EAAEkC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAOlC,EAAEkC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAOlC,EAAEmC,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,CAAC,EAAG,MAAQnC,EAAEmC,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,CAAC,EAAG,MAAQnC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,IAAKlC,EAAEkC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,GAAI,GAAIjB,EAAK,GAAIgB,GAAOjC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEoC,EAAK1B,EAAK,CAAE,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAOV,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAOlC,EAAEkC,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,EAAGvB,EAAK,EAAGC,EAAK,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,IAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAOjC,EAAEkC,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAOlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEmC,EAAK,CAAC,EAAG,KAAMnC,EAAEmC,EAAK,CAAC,EAAG,KAAMnC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEoC,EAAK1B,EAAK,CAAE,EAAG,KAAOV,EAAEkC,EAAK,CAAC,EAAG,KAAMlC,EAAEkC,EAAK,CAAC,EAAG,KAAM,CAAE,EAAGvB,EAAK,EAAGC,EAAK,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,IAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAOjC,EAAEkC,EAAK,CAAC,EAAG,MACjhF8C,eAAgB,CAAE,EAAG,CAAC,EAAG,GAAI,EAAG,CAAC,EAAG,GAAI,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAC7DC,YAA4BhF,EAAAA,EAAAA,KAAO,SAAoBiF,EAAKC,GAC1D,IAAIA,EAAKC,YAEF,CACL,IAAIC,EAAQ,IAAIC,MAAMJ,GAEtB,MADAG,EAAMF,KAAOA,EACPE,CACR,CALEjC,KAAKd,MAAM4C,EAMf,GAAG,cACHK,OAAuBtF,EAAAA,EAAAA,KAAO,SAAeuF,GAC3C,IAAIC,EAAOrC,KAAMsC,EAAQ,CAAC,GAAIC,EAAS,GAAIC,EAAS,CAAC,MAAOC,EAAS,GAAId,EAAQ3B,KAAK2B,MAAOnC,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAGiD,EAAa,EAC7IC,EAAOF,EAAOG,MAAMC,KAAKC,UAAW,GACpCC,EAASC,OAAOC,OAAOjD,KAAKkD,OAC5BC,EAAc,CAAEhE,GAAI,CAAC,GACzB,IAAK,IAAIrC,KAAKkD,KAAKb,GACb6D,OAAOI,UAAUC,eAAeR,KAAK7C,KAAKb,GAAIrC,KAChDqG,EAAYhE,GAAGrC,GAAKkD,KAAKb,GAAGrC,IAGhCiG,EAAOO,SAASlB,EAAOe,EAAYhE,IACnCgE,EAAYhE,GAAG+D,MAAQH,EACvBI,EAAYhE,GAAGxC,OAASqD,KACI,oBAAjB+C,EAAOQ,SAChBR,EAAOQ,OAAS,CAAC,GAEnB,IAAIC,EAAQT,EAAOQ,OACnBd,EAAOvC,KAAKsD,GACZ,IAAIC,EAASV,EAAOW,SAAWX,EAAOW,QAAQD,OAY9C,SAASE,IACP,IAAIC,EASJ,MAPqB,kBADrBA,EAAQrB,EAAOsB,OAASd,EAAOY,OA/BqI,KAiC9JC,aAAiBE,QAEnBF,GADArB,EAASqB,GACMC,OAEjBD,EAAQvB,EAAKjD,SAASwE,IAAUA,GAE3BA,CACT,CAtByC,oBAA9BT,EAAYhE,GAAG0C,WACxB7B,KAAK6B,WAAasB,EAAYhE,GAAG0C,WAEjC7B,KAAK6B,WAAamB,OAAOe,eAAe/D,MAAM6B,YAOhDhF,EAAAA,EAAAA,KALA,SAAkBmH,GAChB1B,EAAMpF,OAASoF,EAAMpF,OAAS,EAAI8G,EAClCxB,EAAOtF,OAASsF,EAAOtF,OAAS8G,EAChCvB,EAAOvF,OAASuF,EAAOvF,OAAS8G,CAClC,GACiB,aAajBnH,EAAAA,EAAAA,IAAO8G,EAAK,OAEZ,IADA,IAAIM,EAAQC,EAAgBC,EAAOC,EAAWC,EAAeC,EAAGC,EAAKC,EAAUC,EAA9BC,EAAQ,CAAC,IAC7C,CAUX,GATAP,EAAQ7B,EAAMA,EAAMpF,OAAS,GACzB8C,KAAK4B,eAAeuC,GACtBC,EAASpE,KAAK4B,eAAeuC,IAEd,OAAXF,GAAoC,oBAAVA,IAC5BA,EAASN,KAEXS,EAASzC,EAAMwC,IAAUxC,EAAMwC,GAAOF,IAElB,qBAAXG,IAA2BA,EAAOlH,SAAWkH,EAAO,GAAI,CACjE,IAAIO,EAAS,GAEb,IAAKL,KADLG,EAAW,GACD9C,EAAMwC,GACVnE,KAAKX,WAAWiF,IAAMA,EAzD6H,GA0DrJG,EAASvE,KAAK,IAAMF,KAAKX,WAAWiF,GAAK,KAI3CK,EADE5B,EAAO6B,aACA,wBAA0BlF,EAAW,GAAK,MAAQqD,EAAO6B,eAAiB,eAAiBH,EAASI,KAAK,MAAQ,WAAa7E,KAAKX,WAAW4E,IAAWA,GAAU,IAEnK,wBAA0BvE,EAAW,GAAK,iBAhE6G,GAgE1FuE,EAAgB,eAAiB,KAAOjE,KAAKX,WAAW4E,IAAWA,GAAU,KAErJjE,KAAK6B,WAAW8C,EAAQ,CACtBvD,KAAM2B,EAAOjC,MACb8C,MAAO5D,KAAKX,WAAW4E,IAAWA,EAClCa,KAAM/B,EAAOrD,SACbqF,IAAKvB,EACLiB,YAEJ,CACA,GAAIL,EAAO,aAAcN,OAASM,EAAOlH,OAAS,EAChD,MAAM,IAAIgF,MAAM,oDAAsDiC,EAAQ,YAAcF,GAE9F,OAAQG,EAAO,IACb,KAAK,EACH9B,EAAMpC,KAAK+D,GACXzB,EAAOtC,KAAK6C,EAAOvD,QACnBiD,EAAOvC,KAAK6C,EAAOQ,QACnBjB,EAAMpC,KAAKkE,EAAO,IAClBH,EAAS,KACJC,GASHD,EAASC,EACTA,EAAiB,OATjBzE,EAASsD,EAAOtD,OAChBD,EAASuD,EAAOvD,OAChBE,EAAWqD,EAAOrD,SAClB8D,EAAQT,EAAOQ,OACXb,EAAa,GACfA,KAMJ,MACF,KAAK,EAwBH,GAvBA6B,EAAMvE,KAAKV,aAAa8E,EAAO,IAAI,GACnCM,EAAMzE,EAAIuC,EAAOA,EAAOtF,OAASqH,GACjCG,EAAM7E,GAAK,CACTmF,WAAYvC,EAAOA,EAAOvF,QAAUqH,GAAO,IAAIS,WAC/CC,UAAWxC,EAAOA,EAAOvF,OAAS,GAAG+H,UACrCC,aAAczC,EAAOA,EAAOvF,QAAUqH,GAAO,IAAIW,aACjDC,YAAa1C,EAAOA,EAAOvF,OAAS,GAAGiI,aAErC1B,IACFiB,EAAM7E,GAAGuF,MAAQ,CACf3C,EAAOA,EAAOvF,QAAUqH,GAAO,IAAIa,MAAM,GACzC3C,EAAOA,EAAOvF,OAAS,GAAGkI,MAAM,KAYnB,qBATjBf,EAAIrE,KAAKT,cAAc8F,MAAMX,EAAO,CAClClF,EACAC,EACAC,EACAyD,EAAYhE,GACZiF,EAAO,GACP5B,EACAC,GACA6C,OAAO3C,KAEP,OAAO0B,EAELE,IACFjC,EAAQA,EAAMM,MAAM,GAAI,EAAI2B,EAAM,GAClC/B,EAASA,EAAOI,MAAM,GAAI,EAAI2B,GAC9B9B,EAASA,EAAOG,MAAM,GAAI,EAAI2B,IAEhCjC,EAAMpC,KAAKF,KAAKV,aAAa8E,EAAO,IAAI,IACxC5B,EAAOtC,KAAKwE,EAAMzE,GAClBwC,EAAOvC,KAAKwE,EAAM7E,IAClB2E,EAAW7C,EAAMW,EAAMA,EAAMpF,OAAS,IAAIoF,EAAMA,EAAMpF,OAAS,IAC/DoF,EAAMpC,KAAKsE,GACX,MACF,KAAK,EACH,OAAO,EAEb,CACA,OAAO,CACT,GAAG,UAEDtB,EAAwB,WA2jB1B,MA1jBa,CACXqC,IAAK,EACL1D,YAA4BhF,EAAAA,EAAAA,KAAO,SAAoBiF,EAAKC,GAC1D,IAAI/B,KAAKb,GAAGxC,OAGV,MAAM,IAAIuF,MAAMJ,GAFhB9B,KAAKb,GAAGxC,OAAOkF,WAAWC,EAAKC,EAInC,GAAG,cAEHuB,UAA0BzG,EAAAA,EAAAA,KAAO,SAASuF,EAAOjD,GAiB/C,OAhBAa,KAAKb,GAAKA,GAAMa,KAAKb,IAAM,CAAC,EAC5Ba,KAAKwF,OAASpD,EACdpC,KAAKyF,MAAQzF,KAAK0F,WAAa1F,KAAK2F,MAAO,EAC3C3F,KAAKN,SAAWM,KAAKP,OAAS,EAC9BO,KAAKR,OAASQ,KAAK4F,QAAU5F,KAAKc,MAAQ,GAC1Cd,KAAK6F,eAAiB,CAAC,WACvB7F,KAAKuD,OAAS,CACZyB,WAAY,EACZE,aAAc,EACdD,UAAW,EACXE,YAAa,GAEXnF,KAAK0D,QAAQD,SACfzD,KAAKuD,OAAO6B,MAAQ,CAAC,EAAG,IAE1BpF,KAAK8F,OAAS,EACP9F,IACT,GAAG,YAEHoC,OAAuBvF,EAAAA,EAAAA,KAAO,WAC5B,IAAIkJ,EAAK/F,KAAKwF,OAAO,GAiBrB,OAhBAxF,KAAKR,QAAUuG,EACf/F,KAAKP,SACLO,KAAK8F,SACL9F,KAAKc,OAASiF,EACd/F,KAAK4F,SAAWG,EACJA,EAAGjF,MAAM,oBAEnBd,KAAKN,WACLM,KAAKuD,OAAO0B,aAEZjF,KAAKuD,OAAO4B,cAEVnF,KAAK0D,QAAQD,QACfzD,KAAKuD,OAAO6B,MAAM,KAEpBpF,KAAKwF,OAASxF,KAAKwF,OAAO5C,MAAM,GACzBmD,CACT,GAAG,SAEHC,OAAuBnJ,EAAAA,EAAAA,KAAO,SAASkJ,GACrC,IAAIxB,EAAMwB,EAAG7I,OACT+I,EAAQF,EAAG/E,MAAM,iBACrBhB,KAAKwF,OAASO,EAAK/F,KAAKwF,OACxBxF,KAAKR,OAASQ,KAAKR,OAAO0G,OAAO,EAAGlG,KAAKR,OAAOtC,OAASqH,GACzDvE,KAAK8F,QAAUvB,EACf,IAAI4B,EAAWnG,KAAKc,MAAME,MAAM,iBAChChB,KAAKc,MAAQd,KAAKc,MAAMoF,OAAO,EAAGlG,KAAKc,MAAM5D,OAAS,GACtD8C,KAAK4F,QAAU5F,KAAK4F,QAAQM,OAAO,EAAGlG,KAAK4F,QAAQ1I,OAAS,GACxD+I,EAAM/I,OAAS,IACjB8C,KAAKN,UAAYuG,EAAM/I,OAAS,GAElC,IAAImH,EAAIrE,KAAKuD,OAAO6B,MAWpB,OAVApF,KAAKuD,OAAS,CACZyB,WAAYhF,KAAKuD,OAAOyB,WACxBC,UAAWjF,KAAKN,SAAW,EAC3BwF,aAAclF,KAAKuD,OAAO2B,aAC1BC,YAAac,GAASA,EAAM/I,SAAWiJ,EAASjJ,OAAS8C,KAAKuD,OAAO2B,aAAe,GAAKiB,EAASA,EAASjJ,OAAS+I,EAAM/I,QAAQA,OAAS+I,EAAM,GAAG/I,OAAS8C,KAAKuD,OAAO2B,aAAeX,GAEtLvE,KAAK0D,QAAQD,SACfzD,KAAKuD,OAAO6B,MAAQ,CAACf,EAAE,GAAIA,EAAE,GAAKrE,KAAKP,OAAS8E,IAElDvE,KAAKP,OAASO,KAAKR,OAAOtC,OACnB8C,IACT,GAAG,SAEHoG,MAAsBvJ,EAAAA,EAAAA,KAAO,WAE3B,OADAmD,KAAKyF,OAAQ,EACNzF,IACT,GAAG,QAEHqG,QAAwBxJ,EAAAA,EAAAA,KAAO,WAC7B,OAAImD,KAAK0D,QAAQ4C,iBACftG,KAAK0F,YAAa,EAQb1F,MANEA,KAAK6B,WAAW,0BAA4B7B,KAAKN,SAAW,GAAK,mIAAqIM,KAAK4E,eAAgB,CAChOxD,KAAM,GACNwC,MAAO,KACPkB,KAAM9E,KAAKN,UAIjB,GAAG,UAEH6G,MAAsB1J,EAAAA,EAAAA,KAAO,SAASmH,GACpChE,KAAKgG,MAAMhG,KAAKc,MAAM8B,MAAMoB,GAC9B,GAAG,QAEHwC,WAA2B3J,EAAAA,EAAAA,KAAO,WAChC,IAAI4J,EAAOzG,KAAK4F,QAAQM,OAAO,EAAGlG,KAAK4F,QAAQ1I,OAAS8C,KAAKc,MAAM5D,QACnE,OAAQuJ,EAAKvJ,OAAS,GAAK,MAAQ,IAAMuJ,EAAKP,QAAQ,IAAIQ,QAAQ,MAAO,GAC3E,GAAG,aAEHC,eAA+B9J,EAAAA,EAAAA,KAAO,WACpC,IAAI+J,EAAO5G,KAAKc,MAIhB,OAHI8F,EAAK1J,OAAS,KAChB0J,GAAQ5G,KAAKwF,OAAOU,OAAO,EAAG,GAAKU,EAAK1J,UAElC0J,EAAKV,OAAO,EAAG,KAAOU,EAAK1J,OAAS,GAAK,MAAQ,KAAKwJ,QAAQ,MAAO,GAC/E,GAAG,iBAEH9B,cAA8B/H,EAAAA,EAAAA,KAAO,WACnC,IAAIgK,EAAM7G,KAAKwG,YACXM,EAAI,IAAIhD,MAAM+C,EAAI3J,OAAS,GAAG2H,KAAK,KACvC,OAAOgC,EAAM7G,KAAK2G,gBAAkB,KAAOG,EAAI,GACjD,GAAG,gBAEHC,YAA4BlK,EAAAA,EAAAA,KAAO,SAASiE,EAAOkG,GACjD,IAAIpD,EAAOqC,EAAOgB,EAmDlB,GAlDIjH,KAAK0D,QAAQ4C,kBACfW,EAAS,CACPvH,SAAUM,KAAKN,SACf6D,OAAQ,CACNyB,WAAYhF,KAAKuD,OAAOyB,WACxBC,UAAWjF,KAAKiF,UAChBC,aAAclF,KAAKuD,OAAO2B,aAC1BC,YAAanF,KAAKuD,OAAO4B,aAE3B3F,OAAQQ,KAAKR,OACbsB,MAAOd,KAAKc,MACZoG,QAASlH,KAAKkH,QACdtB,QAAS5F,KAAK4F,QACdnG,OAAQO,KAAKP,OACbqG,OAAQ9F,KAAK8F,OACbL,MAAOzF,KAAKyF,MACZD,OAAQxF,KAAKwF,OACbrG,GAAIa,KAAKb,GACT0G,eAAgB7F,KAAK6F,eAAejD,MAAM,GAC1C+C,KAAM3F,KAAK2F,MAET3F,KAAK0D,QAAQD,SACfwD,EAAO1D,OAAO6B,MAAQpF,KAAKuD,OAAO6B,MAAMxC,MAAM,MAGlDqD,EAAQnF,EAAM,GAAGA,MAAM,sBAErBd,KAAKN,UAAYuG,EAAM/I,QAEzB8C,KAAKuD,OAAS,CACZyB,WAAYhF,KAAKuD,OAAO0B,UACxBA,UAAWjF,KAAKN,SAAW,EAC3BwF,aAAclF,KAAKuD,OAAO4B,YAC1BA,YAAac,EAAQA,EAAMA,EAAM/I,OAAS,GAAGA,OAAS+I,EAAMA,EAAM/I,OAAS,GAAG4D,MAAM,UAAU,GAAG5D,OAAS8C,KAAKuD,OAAO4B,YAAcrE,EAAM,GAAG5D,QAE/I8C,KAAKR,QAAUsB,EAAM,GACrBd,KAAKc,OAASA,EAAM,GACpBd,KAAKkH,QAAUpG,EACfd,KAAKP,OAASO,KAAKR,OAAOtC,OACtB8C,KAAK0D,QAAQD,SACfzD,KAAKuD,OAAO6B,MAAQ,CAACpF,KAAK8F,OAAQ9F,KAAK8F,QAAU9F,KAAKP,SAExDO,KAAKyF,OAAQ,EACbzF,KAAK0F,YAAa,EAClB1F,KAAKwF,OAASxF,KAAKwF,OAAO5C,MAAM9B,EAAM,GAAG5D,QACzC8C,KAAK4F,SAAW9E,EAAM,GACtB8C,EAAQ5D,KAAKT,cAAcsD,KAAK7C,KAAMA,KAAKb,GAAIa,KAAMgH,EAAchH,KAAK6F,eAAe7F,KAAK6F,eAAe3I,OAAS,IAChH8C,KAAK2F,MAAQ3F,KAAKwF,SACpBxF,KAAK2F,MAAO,GAEV/B,EACF,OAAOA,EACF,GAAI5D,KAAK0F,WAAY,CAC1B,IAAK,IAAI5I,KAAKmK,EACZjH,KAAKlD,GAAKmK,EAAOnK,GAEnB,OAAO,CACT,CACA,OAAO,CACT,GAAG,cAEH8J,MAAsB/J,EAAAA,EAAAA,KAAO,WAC3B,GAAImD,KAAK2F,KACP,OAAO3F,KAAKuF,IAKd,IAAI3B,EAAO9C,EAAOqG,EAAWC,EAHxBpH,KAAKwF,SACRxF,KAAK2F,MAAO,GAGT3F,KAAKyF,QACRzF,KAAKR,OAAS,GACdQ,KAAKc,MAAQ,IAGf,IADA,IAAIuG,EAAQrH,KAAKsH,gBACRC,EAAI,EAAGA,EAAIF,EAAMnK,OAAQqK,IAEhC,IADAJ,EAAYnH,KAAKwF,OAAO1E,MAAMd,KAAKqH,MAAMA,EAAME,SAC5BzG,GAASqG,EAAU,GAAGjK,OAAS4D,EAAM,GAAG5D,QAAS,CAGlE,GAFA4D,EAAQqG,EACRC,EAAQG,EACJvH,KAAK0D,QAAQ4C,gBAAiB,CAEhC,IAAc,KADd1C,EAAQ5D,KAAK+G,WAAWI,EAAWE,EAAME,KAEvC,OAAO3D,EACF,GAAI5D,KAAK0F,WAAY,CAC1B5E,GAAQ,EACR,QACF,CACE,OAAO,CAEX,CAAO,IAAKd,KAAK0D,QAAQ8D,KACvB,KAEJ,CAEF,OAAI1G,GAEY,KADd8C,EAAQ5D,KAAK+G,WAAWjG,EAAOuG,EAAMD,MAE5BxD,EAIS,KAAhB5D,KAAKwF,OACAxF,KAAKuF,IAELvF,KAAK6B,WAAW,0BAA4B7B,KAAKN,SAAW,GAAK,yBAA2BM,KAAK4E,eAAgB,CACtHxD,KAAM,GACNwC,MAAO,KACPkB,KAAM9E,KAAKN,UAGjB,GAAG,QAEHiE,KAAqB9G,EAAAA,EAAAA,KAAO,WAC1B,IAAIwH,EAAIrE,KAAK4G,OACb,OAAIvC,GAGKrE,KAAK2D,KAEhB,GAAG,OAEH8D,OAAuB5K,EAAAA,EAAAA,KAAO,SAAe6K,GAC3C1H,KAAK6F,eAAe3F,KAAKwH,EAC3B,GAAG,SAEHC,UAA0B9K,EAAAA,EAAAA,KAAO,WAE/B,OADQmD,KAAK6F,eAAe3I,OAAS,EAC7B,EACC8C,KAAK6F,eAAehC,MAEpB7D,KAAK6F,eAAe,EAE/B,GAAG,YAEHyB,eAA+BzK,EAAAA,EAAAA,KAAO,WACpC,OAAImD,KAAK6F,eAAe3I,QAAU8C,KAAK6F,eAAe7F,KAAK6F,eAAe3I,OAAS,GAC1E8C,KAAK4H,WAAW5H,KAAK6F,eAAe7F,KAAK6F,eAAe3I,OAAS,IAAImK,MAErErH,KAAK4H,WAAoB,QAAEP,KAEtC,GAAG,iBAEHQ,UAA0BhL,EAAAA,EAAAA,KAAO,SAAkBmH,GAEjD,OADAA,EAAIhE,KAAK6F,eAAe3I,OAAS,EAAI4K,KAAKC,IAAI/D,GAAK,KAC1C,EACAhE,KAAK6F,eAAe7B,GAEpB,SAEX,GAAG,YAEHgE,WAA2BnL,EAAAA,EAAAA,KAAO,SAAmB6K,GACnD1H,KAAKyH,MAAMC,EACb,GAAG,aAEHO,gBAAgCpL,EAAAA,EAAAA,KAAO,WACrC,OAAOmD,KAAK6F,eAAe3I,MAC7B,GAAG,kBACHwG,QAAS,CAAE,oBAAoB,GAC/BnE,eAA+B1C,EAAAA,EAAAA,KAAO,SAAmBsC,EAAI+I,EAAKC,EAA2BC,GAE3F,OAAQD,GACN,KAAK,EACH,OAAO,GAET,KAAK,EAuJL,KAAK,GACH,OAAO,GArJT,KAAK,EAuJL,KAAK,GACH,OAAO,GArJT,KAAK,EAuJL,KAAK,GACH,OAAO,GArJT,KAAK,EAuJL,KAAK,GACH,OAAO,GArJT,KAAK,EAEL,KAAK,EAOL,KAAK,EAEL,KAAK,EAEL,KAAK,GAEL,KAAK,GAoKL,KAAK,GAML,KAAK,GAyBL,KAAK,GACH,MA7MF,KAAK,EAgQL,KAAK,GACH,OAAO,EAtPT,KAAK,GA+EL,KAAK,GAEH,OADAnI,KAAKgI,UAAU,SACR,GA7ET,KAAK,GA+EL,KAAK,GACH,OAAO,GA7ET,KAAK,GAsBL,KAAK,GAyDL,KAAK,GA2DL,KAAK,GASL,KAAK,GACHhI,KAAK2H,WACL,MAlJF,KAAK,GAEH,OADA3H,KAAKyH,MAAM,aACJ,GAET,KAAK,GAEH,OADAzH,KAAK2H,WACE,kBAET,KAAK,GAEH,OADA3H,KAAKyH,MAAM,aACJ,GAET,KAAK,GAEH,OADAzH,KAAK2H,WACE,kBAET,KAAK,GACH3H,KAAKyH,MAAM,uBACX,MAIF,KAAK,GACH,MAAO,4BAET,KAAK,GAEH,OADAzH,KAAKgI,UAAU,YACR,GAET,KAAK,GAGH,OAFAhI,KAAK2H,WACL3H,KAAKgI,UAAU,cACR,sBAET,KAAK,GAGH,OAFAhI,KAAK2H,WACL3H,KAAKgI,UAAU,cACR,GAET,KAAK,GAEH,OADAhI,KAAK2H,WACE,GAET,KAAK,GAEH,OADA3H,KAAKgI,UAAU,SACR,GAET,KAAK,GAGH,OAFAhI,KAAK2H,WACL3H,KAAKgI,UAAU,eACR,GAET,KAAK,GAEH,OADAhI,KAAK2H,WACE,GAET,KAAK,GAEH,OADA3H,KAAKgI,UAAU,SACR,GAET,KAAK,GAGH,OAFAhI,KAAK2H,WACL3H,KAAKgI,UAAU,mBACR,GAET,KAAK,GAEH,OADAhI,KAAK2H,WACE,GAYT,KAAK,GACH3H,KAAKgI,UAAU,SACf,MACF,KAAK,GAeL,KAAK,GAGH,OAFAhI,KAAK2H,WACLO,EAAI1I,OAAS0I,EAAI1I,OAAOoD,MAAM,GAAI,GAAG/B,OAC9B,GAbT,KAAK,GAeL,KAAK,GAGH,OAFAb,KAAK2H,WACLO,EAAI1I,OAAS0I,EAAI1I,OAAOoD,MAAM,GAAI,GAAG/B,OAC9B,GAbT,KAAK,GAeL,KAAK,GAGH,OAFAb,KAAK2H,WACLO,EAAI1I,OAAS0I,EAAI1I,OAAOoD,MAAM,GAAI,IAAI/B,OAC/B,GAcT,KAAK,GACHb,KAAKgI,UAAU,gBACf,MACF,KAAK,GAEH,OADAhI,KAAKgI,UAAU,YACR,KAET,KAAK,GAyDL,KAAK,GAEH,OADAhI,KAAK2H,WACE,KApDT,KAAK,GACH,MAAO,cAET,KAAK,GACH,OAAO,GAKT,KAAK,GAGH,OAFA3H,KAAK2H,WACL3H,KAAKgI,UAAU,UACR,GAIT,KAAK,GAEH,OADAhI,KAAK2H,WACE,GAIT,KAAK,GAEH,OADA3H,KAAKyH,MAAM,QACJ,GAET,KAAK,GAGH,OAFAzH,KAAK2H,WACL3H,KAAKgI,UAAU,WACR,GAET,KAAK,GAGH,OAFAhI,KAAK2H,WACL3H,KAAKgI,UAAU,WACR,GAET,KAAK,GACHhI,KAAK2H,WACL3H,KAAKgI,UAAU,iBACf,MACF,KAAK,GAGH,OAFAhI,KAAK2H,WACL3H,KAAKgI,UAAU,oBACR,KAIT,KAAK,GACH,MAAO,YAMT,KAAK,GAGH,OAFAhI,KAAK2H,WACL3H,KAAKgI,UAAU,aACR,GAET,KAAK,GAGH,OAFAhI,KAAK2H,WACLO,EAAI1I,OAAS0I,EAAI1I,OAAO0G,OAAO,GAAGrF,OAC3B,GAET,KAAK,GAGH,OAFAb,KAAK2H,WACLO,EAAI1I,OAAS0I,EAAI1I,OAAOoD,MAAM,GAAI,GAAG/B,OAC9B,GAET,KAAK,GAGL,KAAK,GACH,OAAO,EAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GAEH,OADAqH,EAAI1I,OAAS0I,EAAI1I,OAAOqB,OACjB,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAKT,KAAK,GACH,MAAO,UAGb,GAAG,aACHwG,MAAO,CAAC,kBAAmB,+BAAgC,+BAAgC,+BAAgC,+BAAgC,uBAAwB,sBAAuB,cAAe,cAAe,oBAAqB,gBAAiB,gBAAiB,iBAAkB,YAAa,mBAAoB,wBAAyB,wBAAyB,wBAAyB,wBAAyB,yBAA0B,aAAc,eAAgB,oBAAqB,mBAAoB,eAAgB,eAAgB,iBAAkB,2BAA4B,eAAgB,iBAAkB,kBAAmB,eAAgB,iBAAkB,YAAa,mBAAoB,iBAAkB,mBAAoB,mBAAoB,qBAAsB,uBAAwB,uBAAwB,yBAA0B,+BAAgC,+BAAgC,+BAAgC,+BAAgC,YAAa,iBAAkB,iBAAkB,YAAa,cAAe,mBAAoB,WAAY,WAAY,uBAAwB,WAAY,aAAc,gBAAiB,kBAAmB,mBAAoB,UAAW,iBAAkB,YAAa,cAAe,eAAgB,uBAAwB,qBAAsB,2BAA4B,wBAAyB,2BAA4B,iCAAkC,eAAgB,sBAAuB,qBAAsB,YAAa,WAAY,YAAa,UAAW,WACljDO,WAAY,CAAE,KAAQ,CAAE,MAAS,CAAC,EAAG,IAAK,WAAa,GAAS,OAAU,CAAE,MAAS,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,iBAAoB,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,cAAiB,CAAE,MAAS,CAAC,GAAI,GAAI,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,QAAW,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,KAAQ,CAAE,MAAS,CAAC,GAAI,GAAI,IAAK,WAAa,GAAS,mBAAsB,CAAE,MAAS,GAAI,WAAa,GAAS,gBAAmB,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,GAAI,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,YAAe,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,WAAc,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,SAAY,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,oBAAuB,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,MAAS,CAAE,MAAS,GAAI,WAAa,GAAS,SAAY,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,aAAgB,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,WAAc,CAAE,MAAS,GAAI,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,GAAM,CAAE,MAAS,CAAC,EAAG,IAAK,WAAa,GAAS,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,IAGjmD,CA5jB4B,GA8jB5B,SAASS,IACPrI,KAAKb,GAAK,CAAC,CACb,CAIA,OAPAF,EAAQiE,MAAQA,GAIhBrG,EAAAA,EAAAA,IAAOwL,EAAQ,UACfA,EAAOjF,UAAYnE,EACnBA,EAAQoJ,OAASA,EACV,IAAIA,CACb,CA/1Ba,GAg2Bb1L,EAAOA,OAASA,EAChB,IAAI2L,EAAuB3L,EAMvB4L,EAAa,QACbC,EAAgB,WAIhBC,EAAqB,UACrBC,EAAe,UACfC,EAAe,YACfC,EAAwB,aAExBC,EAAmB,OACnBC,EAAmB,SACnBC,EAAc,OACdC,EAAwB,gBAGxBC,EAAgB,UAChBC,EAAc,mBAGdC,EAAc,eAEdC,EAAoB,GAAGD,UACvBE,EAAW,aAGXC,EAAqB,GAAGD,cACxBE,EAAmB,GAAGJ,SAEtBK,EAAsB,GAAGL,YAEzBM,EAA0B,GAAGN,gBAC7BO,EAAS,SACTC,EAAO,OAEPC,EAAoB,OACpBC,EAAU,GAAGD,IAAoBD,IACjCG,EAAY,GAAGF,IAAoBF,IAGnCK,GAAyBlN,EAAAA,EAAAA,KAAO,SAACmN,GAAoD,IAAxCC,EAAUnH,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GA1C9B,KA2C3B,IAAKkH,EAAWpJ,IACd,OAAOqJ,EAET,IAAIE,EAAMF,EACV,IAAK,MAAMG,KAAiBJ,EAAWpJ,IACV,QAAvBwJ,EAAc9J,OAChB6J,EAAMC,EAAc1I,OAGxB,OAAOyI,CACT,GAAG,UA2BCE,EAAmC,CACrCC,YA3B+BzN,EAAAA,EAAAA,KAAO,SAASuE,EAAMmJ,GACrD,OAAOA,EAAWC,GAAGF,YACvB,GAAG,cA0BDG,MAzByB5N,EAAAA,EAAAA,KAAO6N,eAAetJ,EAAMV,EAAIiK,EAAUC,GACnEC,EAAAA,GAAIC,KAAK,SACTD,EAAAA,GAAIC,KAAK,6BAA8BpK,GACvC,MAAM,cAAEqK,EAAe5G,MAAO6G,EAAI,OAAEC,IAAWC,EAAAA,EAAAA,MAC/CN,EAAKJ,GAAGW,QAAQP,EAAKJ,GAAGY,gBACxB,MAAMC,EAAcT,EAAKJ,GAAGc,UACtBC,GAAMC,EAAAA,EAAAA,GAAkB9K,EAAIqK,GAClCM,EAAY1K,KAAOiK,EAAKjK,KACxB0K,EAAYI,gBAAkBR,EAC9BI,EAAYK,YAAcV,GAAMU,aAAe,GAC/CL,EAAYM,YAAcX,GAAMW,aAAe,GAC/CN,EAAYO,QAAU,CAAC,QACvBP,EAAYQ,UAAYnL,QAClBoL,EAAAA,EAAAA,IAAOT,EAAaE,GAE1BQ,EAAAA,GAAcC,YACZT,EACA,wBACAP,GAAMiB,gBAAkB,GACxBrB,EAAKJ,GAAG0B,oBAEVC,EAAAA,EAAAA,GAAoBZ,EAPJ,EAOkBpC,EAAa6B,GAAMoB,cAAe,EACtE,GAAG,QAIDrC,UAIEsC,EAAyB,IAAIC,IAC7BC,EAAiB,EACrB,SAASC,IAAgF,IAAxDC,EAAO3J,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,EAAGnC,EAAImC,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,GAAI4J,EAAU5J,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG8G,EAEpE,MAAO,SAFiB9G,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,KACF,OAATnC,GAAiBA,EAAKzD,OAAS,EAAI,GAAGwP,IAAa/L,IAAS,MAC/B8L,GAC/C,EACA5P,EAAAA,EAAAA,IAAO2P,EAAY,cACnB,IAAIG,GAA2B9P,EAAAA,EAAAA,KAAO,CAAC+P,EAAkBhM,EAAKiM,EAAeC,EAAOC,EAAOC,EAASC,EAAM1L,KACxGsJ,EAAAA,GAAI3L,MAAM,QAAS0B,GACnBA,EAAIsM,SAASC,IACX,OAAQA,EAAK7M,MACX,KAAKiI,EAGL,KAAKE,EACH2E,EAAYR,EAAkBO,EAAMN,EAAeC,EAAOC,EAAOC,EAASC,EAAM1L,GAChF,MACF,KAAKiH,EACH,CACE4E,EACER,EACAO,EAAK5M,OACLsM,EACAC,EACAC,EACAC,EACAC,EACA1L,GAEF6L,EACER,EACAO,EAAK3M,OACLqM,EACAC,EACAC,EACAC,EACAC,EACA1L,GAEF,MAAM8L,EAAW,CACf3M,GAAI,OAAS6L,EACbe,MAAOH,EAAK5M,OAAOG,GACnB6M,IAAKJ,EAAK3M,OAAOE,GACjB8M,UAAW,SACXC,aAAc,aACdC,MAAO/E,EACPgF,WAAY,GACZC,MAAOC,EAAAA,GAAeC,aAAaX,EAAK/M,aAAa8K,EAAAA,EAAAA,OACrD6C,eAAgBnF,EAChBoF,SA7HU,IA8HVC,UAAWpF,EACXqF,UAAWpF,EACXvH,QAAS8H,EACT4D,QAEFF,EAAM7M,KAAKmN,GACXd,GACF,EAEJ,GACA,GACD,YACC4B,GAA0BtR,EAAAA,EAAAA,KAAO,SAACmN,GAAoD,IACpFG,EADsDrH,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GArJ/B,KAuJ3B,GAAIkH,EAAWpJ,IACb,IAAK,MAAMwJ,KAAiBJ,EAAWpJ,IACV,QAAvBwJ,EAAc9J,OAChB6J,EAAMC,EAAc1I,OAI1B,OAAOyI,CACT,GAAG,UACH,SAASiE,EAAmBtB,EAAOuB,EAAU9M,GAC3C,IAAK8M,EAAS3N,IAAsB,mBAAhB2N,EAAS3N,IAA2C,cAAhB2N,EAAS3N,GAC/D,OAEE2N,EAASC,aACNxK,MAAMyK,QAAQF,EAASG,qBAC1BH,EAASG,kBAAoB,IAE/BH,EAASC,WAAWtN,MAAM,KAAKkM,SAASuB,IACtC,GAAIlN,EAAQmN,IAAID,GAAW,CACzB,MAAME,EAAWpN,EAAQmN,IAAID,GAC7BJ,EAASG,kBAAoB,IAAIH,EAASG,qBAAsBG,EAASC,OAC3E,MAGJ,MAAMC,EAAmB/B,EAAMgC,MAAMC,GAASA,EAAKrO,KAAO2N,EAAS3N,KAC/DmO,EACF7L,OAAOgM,OAAOH,EAAkBR,GAEhCvB,EAAM5M,KAAKmO,EAEf,CAEA,SAASY,EAAqBC,GAC5B,OAAOA,GAAY3N,SAASsD,KAAK,MAAQ,EAC3C,CAEA,SAASsK,EAAoBD,GAC3B,OAAOA,GAAYN,QAAU,EAC/B,EAPA/R,EAAAA,EAAAA,IAAOuR,EAAoB,uBAI3BvR,EAAAA,EAAAA,IAAOoS,EAAsB,yBAI7BpS,EAAAA,EAAAA,IAAOsS,EAAqB,uBAC5B,IAAI/B,GAA8BvQ,EAAAA,EAAAA,KAAO,CAACuS,EAAQpF,EAAY6C,EAAeC,EAAOC,EAAOC,EAASC,EAAM1L,KACxG,MAAM8N,EAASrF,EAAWtJ,GACpB4O,EAAUzC,EAAc6B,IAAIW,GAC5BE,EAAWN,EAAqBK,GAChC5B,EAAQyB,EAAoBG,GAElC,GADAzE,EAAAA,GAAIC,KAAK,yBAA0Bd,EAAYsF,EAAS5B,GACzC,SAAX2B,EAAmB,CACrB,IAAIG,EAAQzG,GACa,IAArBiB,EAAWsD,MACbkC,EAxLY,cAyLkB,IAArBxF,EAAWsD,QACpBkC,EAzLU,YA2LRxF,EAAWrJ,OAAS8H,IACtB+G,EAAQxF,EAAWrJ,MAEhB0L,EAAOqC,IAAIW,IACdhD,EAAOoD,IAAIJ,EAAQ,CACjB3O,GAAI2O,EACJG,QACApP,YAAayN,EAAAA,GAAeC,aAAauB,GAAQnE,EAAAA,EAAAA,OACjDoD,WAAY,GAAGiB,KAAYnG,IAC3BsG,UAAWhC,IAGf,MAAMiC,EAAUtD,EAAOqC,IAAIW,GACvBrF,EAAW5J,cACT0D,MAAMyK,QAAQoB,EAAQvP,cACxBuP,EAAQH,MAAQxG,EAChB2G,EAAQvP,YAAYF,KAAK8J,EAAW5J,cAEhCuP,EAAQvP,aAAalD,OAAS,GAChCyS,EAAQH,MAAQxG,EACZ2G,EAAQvP,cAAgBiP,EAC1BM,EAAQvP,YAAc,CAAC4J,EAAW5J,aAElCuP,EAAQvP,YAAc,CAACuP,EAAQvP,YAAa4J,EAAW5J,eAGzDuP,EAAQH,MAAQzG,EAChB4G,EAAQvP,YAAc4J,EAAW5J,aAGrCuP,EAAQvP,YAAcyN,EAAAA,GAAe+B,oBAAoBD,EAAQvP,aAAa8K,EAAAA,EAAAA,QAE5C,IAAhCyE,EAAQvP,aAAalD,QAAgByS,EAAQH,QAAUxG,IACpC,UAAjB2G,EAAQhP,KACVgP,EAAQH,MAAQtG,EAEhByG,EAAQH,MAAQzG,IAGf4G,EAAQhP,MAAQqJ,EAAWpJ,MAC9BiK,EAAAA,GAAIC,KAAK,0BAA2BuE,EAAQlB,EAAQnE,IACpD2F,EAAQhP,KAAO,QACfgP,EAAQE,SAAU,EAClBF,EAAQxF,IAAMgE,EAAQnE,GACtB2F,EAAQH,MAAQxF,EAAWrJ,OAAS+H,EAAeO,EAAgBC,EACnEyG,EAAQrB,WAAa,GAAGqB,EAAQrB,cAAc9E,KAAuBwD,EAAUvD,EAA0B,MAE3G,MAAM4E,EAAW,CACfV,WAAY,GACZ6B,MAAOG,EAAQH,MACf5B,MAAO+B,EAAQvP,YACfkO,WAAYqB,EAAQrB,WACpBE,kBAAmB,GACnBkB,UAAWC,EAAQD,UACnBhP,GAAI2O,EACJlF,IAAKwF,EAAQxF,IACb2F,MAAOtD,EAAW6C,EAAQ9C,GAC1B5L,KAAMgP,EAAQhP,KACdkP,QAA0B,UAAjBF,EAAQhP,KACjBoP,QAAS,EACTC,GAAI,GACJC,GAAI,GACJhD,QAUF,GARIoB,EAASmB,QAAUvG,IACrBoF,EAAST,MAAQ,IAEfwB,GAAwB,SAAdA,EAAO1O,KACnBmK,EAAAA,GAAI3L,MAAM,gBAAiBmQ,EAAQ,8BAA+BD,EAAO1O,IACzE2N,EAAS6B,SAAWd,EAAO1O,IAE7B2N,EAAS8B,aAAc,EACnBnG,EAAW9I,KAAM,CACnB,MAAMkP,EAAW,CACfzC,WAAY,GACZ6B,MAnQS,OAoQT5B,MAAO5D,EAAW9I,KAAKE,KACvBkN,WAAY/E,EAEZmG,UAAW,GACXW,kBAAmB,GACnB3P,GAAI2O,EAASxF,EAAU,IAAM0C,EAC7BuD,MAAOtD,EAAW6C,EAAQ9C,EAAgB5C,GAC1ChJ,KAAMgP,EAAQhP,KACdkP,QAA0B,UAAjBF,EAAQhP,KACjBoP,SAAS7E,EAAAA,EAAAA,MAAYoF,UAAUP,QAC/B9C,OACA9L,SAAU6I,EAAW9I,KAAKC,UAEtBoP,EAAelB,EAASvF,EACxB0G,EAAY,CAChB7C,WAAY,GACZ6B,MAnRc,YAoRd5B,MAAO5D,EAAW9I,KAAKE,KACvBkN,WAAYqB,EAAQrB,WACpBoB,UAAW,GACXhP,GAAI2O,EAASvF,EACbgG,MAAOtD,EAAW6C,EAAQ9C,EAAgB7C,GAC1C/I,KAAM,QACNkP,SAAS,EACTE,QAAS,GAET9C,OACA9L,SAAU6I,EAAW9I,KAAKC,UAE5BoL,IACAiE,EAAU9P,GAAK6P,EACfH,EAASF,SAAWK,EACpBnC,EAAmBtB,EAAO0D,EAAWjP,GACrC6M,EAAmBtB,EAAOsD,EAAU7O,GACpC6M,EAAmBtB,EAAOuB,EAAU9M,GACpC,IAAIkP,EAAOpB,EACPqB,EAAKN,EAAS1P,GACe,YAA7BsJ,EAAW9I,KAAKC,WAClBsP,EAAOL,EAAS1P,GAChBgQ,EAAKrB,GAEPtC,EAAM7M,KAAK,CACTQ,GAAI+P,EAAO,IAAMC,EACjBpD,MAAOmD,EACPlD,IAAKmD,EACLlD,UAAW,OACXC,aAAc,GACdC,MAAO/E,EACPgF,WAAY,GACZpM,QAAS+H,EACTyE,eAAgBnF,EAChBoF,SAhUc,IAiUdC,UAAWpF,EACXqF,UAAWpF,EACXmE,QAEJ,MACEmB,EAAmBtB,EAAOuB,EAAU9M,EAExC,CACIyI,EAAWpJ,MACbiK,EAAAA,GAAI3L,MAAM,0BACVyN,EAAS3C,EAAYA,EAAWpJ,IAAKiM,EAAeC,EAAOC,GAAQC,EAASC,EAAM1L,GACpF,GACC,eACCoP,GAAwB9T,EAAAA,EAAAA,KAAO,KACjCwP,EAAOuE,QACPrE,EAAiB,CAAC,GACjB,SAGCsE,EAAa,MACbC,EAAa,QACbC,EAAWF,EAEXG,EAAgB,QAChBC,EAAe,OAGnB,SAASC,IACP,OAAuB,IAAI5E,GAC7B,EACAzP,EAAAA,EAAAA,IAAOqU,EAAgB,kBACvB,IAAIC,GAAyBtU,EAAAA,EAAAA,KAAO,KAC3B,CAELuU,UAAW,GACXC,OAAwB,IAAI/E,IAC5BgF,UAAW,CAAC,KAEb,UACCC,GAAwB1U,EAAAA,EAAAA,KAAQD,GAAM4U,KAAKrP,MAAMqP,KAAKC,UAAU7U,KAAK,SACrE8U,EAAU,MAAM,eAEhB7U,EAAAA,EAAAA,IAAOmD,KAAM,WAFG,GAOlB2R,WAAAA,CAAYC,GACV5R,KAAK4Q,QACL5Q,KAAK4R,QAAUA,EACf5R,KAAKD,WAAaC,KAAKD,WAAW8R,KAAK7R,MACvCA,KAAKiB,aAAejB,KAAKiB,aAAa4Q,KAAK7R,MAC3CA,KAAKyB,aAAezB,KAAKyB,aAAaoQ,KAAK7R,MAC3CA,KAAKK,UAAYL,KAAKK,UAAUwR,KAAK7R,KACvC,CAKA4R,QAKA9E,MAAQ,GAKRC,MAAQ,GAKR+E,QAAU,GAKVvQ,QAAU,KAAA2P,IAAA,GAMVI,UAAY,MAAH,CACPS,KAAMZ,MADI,GAOZa,gBAAkBhS,KAAKsR,UAAUS,KAKjCE,cAAgB,EAKhBC,WAAa,EACbC,oBAAsB,CACpBC,YAAa,EACbC,UAAW,EACXC,YAAa,EACbC,WAAY,GAEdxS,UAAAA,CAAWnD,GACTiO,EAAAA,GAAIC,KAAK,mBAAoBlO,GAC7BoD,KAAK8R,QAAUlV,EACM,IAAjBoD,KAAK4R,QACP5R,KAAKmL,QAAQvO,GAEboD,KAAKmL,QAAQnL,KAAKoL,eAEtB,CACAoH,UAAAA,GACE,OAAOxS,KAAK8R,OACd,CAOAW,aAAAA,CAAcrD,EAAQL,EAAM2D,GAC1B,GAAI3D,EAAKzO,OAASkI,EAChBxI,KAAKyS,cAAcrD,EAAQL,EAAKxO,QAAQ,GACxCP,KAAKyS,cAAcrD,EAAQL,EAAKvO,QAAQ,QAUxC,GARIuO,EAAKzO,OAASiI,IACA,QAAZwG,EAAKrO,IACPqO,EAAKrO,GAAKgS,EAAQtD,EAAO1O,GAAK,SAAW0O,EAAO1O,GAAK,OACrDqO,EAAKzB,MAAQoF,GAEb3D,EAAKrO,GAAKqO,EAAKrO,GAAGG,QAGlBkO,EAAKnO,IAAK,CACZ,MAAMA,EAAM,GACZ,IACI2G,EADAoL,EAAa,GAEjB,IAAKpL,EAAI,EAAGA,EAAIwH,EAAKnO,IAAI1D,OAAQqK,IAC/B,GAAIwH,EAAKnO,IAAI2G,GAAG5G,OAAS+H,EAAc,CACrC,MAAMiH,EAAU4B,EAAMxC,EAAKnO,IAAI2G,IAC/BoI,EAAQ/O,IAAM2Q,EAAMoB,GACpB/R,EAAIV,KAAKyP,GACTgD,EAAa,EACf,MACEA,EAAWzS,KAAK6O,EAAKnO,IAAI2G,IAG7B,GAAI3G,EAAI1D,OAAS,GAAKyV,EAAWzV,OAAS,EAAG,CAC3C,MAAMyS,EAAU,CACdrP,KAAMiI,EACN7H,IAAIkS,EAAAA,EAAAA,MACJjS,KAAM,UACNC,IAAK2Q,EAAMoB,IAEb/R,EAAIV,KAAKqR,EAAM5B,IACfZ,EAAKnO,IAAMA,CACb,CACAmO,EAAKnO,IAAIsM,SAAS2F,GAAY7S,KAAKyS,cAAc1D,EAAM8D,GAAS,IAClE,CAEJ,CAIAzH,YAAAA,GAEE,OADApL,KAAKyS,cAAc,CAAE/R,GAAI,QAAU,CAAEA,GAAI,OAAQE,IAAKZ,KAAK8R,UAAW,GAC/D,CAAEpR,GAAI,OAAQE,IAAKZ,KAAK8R,QACjC,CAYA3G,OAAAA,CAAQ2H,GACN,IAAIlS,EAEFA,EADEkS,EAAKlS,IACDkS,EAAKlS,IAELkS,EAERjI,EAAAA,GAAIC,KAAKlK,GACTZ,KAAK4Q,OAAM,GACX/F,EAAAA,GAAIC,KAAK,4BAA6BlK,GACtCA,EAAIsM,SAASC,IAEX,OADAtC,EAAAA,GAAIkI,KAAK,YAAa5F,EAAK7M,MACnB6M,EAAK7M,MACX,KAAKiI,EACHvI,KAAKgT,SACH7F,EAAKzM,GAAGG,OACRsM,EAAKxM,KACLwM,EAAKvM,IACLuM,EAAK/M,YACL+M,EAAKjM,KACLiM,EAAK5L,QACL4L,EAAKyB,OACLzB,EAAK8F,YAEP,MACF,KAAKzK,EACHxI,KAAKkT,YAAY/F,EAAK5M,OAAQ4M,EAAK3M,OAAQ2M,EAAK/M,aAChD,MACF,IA9hBY,WA+hBVJ,KAAKmT,cAAchG,EAAKzM,GAAGG,OAAQsM,EAAK5L,SACxC,MACF,IAhiBY,QAiiBV,CACE,MAAM6R,EAAMjG,EAAKzM,GAAGG,OAAOG,MAAM,KAC3B4N,EAASzB,EAAK3L,WAAWR,MAAM,KACrCoS,EAAIlG,SAASxM,IACX,IAAI2S,EAAarT,KAAKsT,SAAS5S,GAC/B,QAAmB,IAAf2S,EAAuB,CACzB,MAAME,EAAY7S,EAAGG,OACrBb,KAAKgT,SAASO,GACdF,EAAarT,KAAKsT,SAASC,EAC7B,CACAF,EAAWzE,OAASA,EAAO4E,KAAKC,GAAMA,EAAE/M,QAAQ,KAAM,KAAK7F,QAAO,GAEtE,CACA,MACF,IA9iBc,aA+iBZb,KAAK0T,YAAYvG,EAAKzM,GAAGG,OAAQsM,EAAK3L,YAE1C,IAEF,MAAMqL,EAAgB7M,KAAK2T,YAErB1G,GADS/B,EAAAA,EAAAA,MACK+B,KACpB0D,IACAvD,OACE,EACApN,KAAKoL,eACLyB,EACA7M,KAAK8M,MACL9M,KAAK+M,OACL,EACAE,EACAjN,KAAKuB,SAEPvB,KAAK8M,MAAMI,SAAS6B,IAClB,GAAIjL,MAAMyK,QAAQQ,EAAKnB,OAAQ,CAE7B,GADAmB,EAAK3O,YAAc2O,EAAKnB,MAAMhL,MAAM,GAChCmM,EAAKc,SAAWd,EAAK3O,YAAYlD,OAAS,EAC5C,MAAM,IAAIgF,MACR,gFAAkF6M,EAAKrO,GAAK,KAGhGqO,EAAKnB,MAAQmB,EAAKnB,MAAM,EAC1B,IAEJ,CAaAoF,QAAAA,CAAStS,GAAwH,IAApHC,EAAImC,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG2F,EAAoB7H,EAAGkC,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,KAAM8Q,EAAK9Q,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,KAAM5B,EAAI4B,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,KAAMvB,EAAOuB,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,KAAM8L,EAAM9L,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,KAAMmQ,EAAUnQ,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,KACzH,MAAMyQ,EAAY7S,GAAIG,OA8BtB,GA7BKb,KAAKgS,gBAAgBX,OAAOwC,IAAIN,IAa9BvT,KAAKgS,gBAAgBX,OAAO3C,IAAI6E,GAAW3S,MAC9CZ,KAAKgS,gBAAgBX,OAAO3C,IAAI6E,GAAW3S,IAAMA,GAE9CZ,KAAKgS,gBAAgBX,OAAO3C,IAAI6E,GAAW5S,OAC9CX,KAAKgS,gBAAgBX,OAAO3C,IAAI6E,GAAW5S,KAAOA,KAhBpDkK,EAAAA,GAAIC,KAAK,gBAAiByI,EAAWK,GACrC5T,KAAKgS,gBAAgBX,OAAO5B,IAAI8D,EAAW,CACzC7S,GAAI6S,EACJO,aAAc,GACdnT,OACAC,MACAM,OACAK,QAAS,GACTqN,OAAQ,GACRqE,WAAY,MAUZW,IACF/I,EAAAA,GAAIC,KAAK,4BAA6ByI,EAAWK,GAC5B,kBAAVA,GACT5T,KAAK+T,eAAeR,EAAWK,EAAM/S,QAElB,kBAAV+S,GACTA,EAAM1G,SAAS8G,GAAQhU,KAAK+T,eAAeR,EAAWS,EAAInT,WAG1DK,EAAM,CACR,MAAM+S,EAAOjU,KAAKgS,gBAAgBX,OAAO3C,IAAI6E,GAC7CU,EAAK/S,KAAOA,EACZ+S,EAAK/S,KAAKE,KAAOyM,EAAAA,GAAeC,aAAamG,EAAK/S,KAAKE,MAAM8J,EAAAA,EAAAA,MAC/D,CACA,GAAI3J,EAAS,CACXsJ,EAAAA,GAAIC,KAAK,wBAAyByI,EAAWhS,IACN,kBAAZA,EAAuB,CAACA,GAAWA,GAClD2L,SAASuB,GAAazO,KAAK0T,YAAYH,EAAW9E,EAAS5N,SACzE,CACA,GAAI+N,EAAQ,CACV/D,EAAAA,GAAIC,KAAK,uBAAwByI,EAAW3E,IACP,kBAAXA,EAAsB,CAACA,GAAUA,GAChD1B,SAASQ,GAAU1N,KAAKkU,SAASX,EAAW7F,EAAM7M,SAC/D,CACA,GAAIoS,EAAY,CACdpI,EAAAA,GAAIC,KAAK,uBAAwByI,EAAW3E,IACC,kBAAfqE,EAA0B,CAACA,GAAcA,GACxD/F,SAASiH,GAAcnU,KAAKoU,aAAab,EAAWY,EAAUtT,SAC/E,CACF,CACA+P,KAAAA,CAAMyD,GACJrU,KAAK8M,MAAQ,GACb9M,KAAK+M,MAAQ,GACb/M,KAAKsR,UAAY,CACfS,KAAMZ,KAERnR,KAAKgS,gBAAkBhS,KAAKsR,UAAUS,KACtC/R,KAAKiS,cAAgB,EACrBjS,KAAKuB,QAAU2P,IACVmD,IACHzD,EAAAA,EAAAA,KAEJ,CACA0C,QAAAA,CAAS5S,GACP,OAAOV,KAAKgS,gBAAgBX,OAAO3C,IAAIhO,EACzC,CACAiT,SAAAA,GACE,OAAO3T,KAAKgS,gBAAgBX,MAC9B,CACAiD,YAAAA,GACEzJ,EAAAA,GAAIC,KAAK,eAAgB9K,KAAKsR,UAChC,CACAiD,YAAAA,GACE,OAAOvU,KAAKgS,gBAAgBZ,SAC9B,CAUAoD,eAAAA,GAAyB,IAAT9T,EAAEoC,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,GACf2R,EAAU/T,EAKd,OAJIA,IAAOmQ,IACT7Q,KAAKiS,gBACLwC,EAAU,GAAG3D,IAAa9Q,KAAKiS,iBAE1BwC,CACT,CAUAC,iBAAAA,GACE,OADkB5R,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,MACP+N,EAAaC,EADEhO,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG2F,CAElC,CAUAkM,aAAAA,GAAuB,IAATjU,EAAEoC,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,GACb2R,EAAU/T,EAKd,OAJIA,IAAOqQ,IACT/Q,KAAKiS,gBACLwC,EAAU,MAAczU,KAAKiS,iBAExBwC,CACT,CAUAG,eAAAA,GACE,OADgB9R,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,MACLiO,EAlYH,MAiYgBjO,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG2F,CAEhC,CAOAoM,eAAAA,CAAgBC,EAAOC,EAAOC,GAC5B,IAAIC,EAAMjV,KAAKwU,gBAAgBM,EAAMpU,GAAGG,QACpCqU,EAAQlV,KAAK0U,kBAAkBI,EAAMpU,GAAGG,OAAQiU,EAAMnU,MACtDwU,EAAMnV,KAAKwU,gBAAgBO,EAAMrU,GAAGG,QACpCuU,EAAQpV,KAAK0U,kBAAkBK,EAAMrU,GAAGG,OAAQkU,EAAMpU,MAC1DX,KAAKgT,SACHiC,EACAC,EACAJ,EAAMlU,IACNkU,EAAM1U,YACN0U,EAAM5T,KACN4T,EAAMvT,QACNuT,EAAMlG,OACNkG,EAAM7B,YAERjT,KAAKgT,SACHmC,EACAC,EACAL,EAAMnU,IACNmU,EAAM3U,YACN2U,EAAM7T,KACN6T,EAAMxT,QACNwT,EAAMnG,OACNmG,EAAM9B,YAERjT,KAAKgS,gBAAgBZ,UAAUlR,KAAK,CAClC+U,MACAE,MACAH,cAAenH,EAAAA,GAAeC,aAAakH,GAAe9J,EAAAA,EAAAA,QAE9D,CAQAgI,WAAAA,CAAY4B,EAAOC,EAAOM,GACxB,GAAqB,kBAAVP,EACT9U,KAAK6U,gBAAgBC,EAAOC,EAAOM,OAC9B,CACL,MAAMJ,EAAMjV,KAAKwU,gBAAgBM,EAAMjU,QACjCqU,EAAQlV,KAAK0U,kBAAkBI,GAC/BK,EAAMnV,KAAK2U,cAAcI,EAAMlU,QAC/BuU,EAAQpV,KAAK4U,gBAAgBG,GACnC/U,KAAKgT,SAASiC,EAAKC,GACnBlV,KAAKgT,SAASmC,EAAKC,GACnBpV,KAAKgS,gBAAgBZ,UAAUlR,KAAK,CAClC+U,MACAE,MACAE,MAAOxH,EAAAA,GAAeC,aAAauH,GAAOnK,EAAAA,EAAAA,QAE9C,CACF,CACA6I,cAAAA,CAAerT,EAAIkT,GACjB,MAAM0B,EAAWtV,KAAKgS,gBAAgBX,OAAO3C,IAAIhO,GAC3C6U,EAAS3B,EAAM4B,WAAW,KAAO5B,EAAMlN,QAAQ,IAAK,IAAI7F,OAAS+S,EACvE0B,EAASxB,aAAa5T,KAAK2N,EAAAA,GAAeC,aAAayH,GAAQrK,EAAAA,EAAAA,OACjE,CACAuK,YAAAA,CAAa7H,GACX,MAA8B,MAA1BA,EAAM8H,UAAU,EAAG,GACd9H,EAAM1H,OAAO,GAAGrF,OAEhB+M,EAAM/M,MAEjB,CACAI,YAAAA,GAEE,OADAjB,KAAKkS,aACE,cAAgBlS,KAAKkS,UAC9B,CAQAiB,aAAAA,CAAczS,GAA0B,IAAtBiV,EAAe7S,UAAA5F,OAAA,QAAAgN,IAAApH,UAAA,GAAAA,UAAA,GAAG,GAC7B9C,KAAKuB,QAAQsS,IAAInT,IACpBV,KAAKuB,QAAQkO,IAAI/O,EAAI,CAAEA,KAAIkO,OAAQ,GAAIqE,WAAY,KAErD,MAAM2C,EAAa5V,KAAKuB,QAAQmN,IAAIhO,QACZ,IAApBiV,GAAkD,OAApBA,GAChCA,EAAgB3U,MA1dD,KA0duBkM,SAAS2I,IAC7C,MAAMC,EAAcD,EAAOnP,QAAQ,WAAY,MAAM7F,OACrD,GAAIkV,OAAO/E,GAAegF,KAAKH,GAAS,CACtC,MACMI,EADYH,EAAYpP,QAAQuK,EA9dlC,UA+dwBvK,QAAQsK,EAAeC,GACnD2E,EAAW3C,WAAW/S,KAAK+V,EAC7B,CACAL,EAAWhH,OAAO1O,KAAK4V,EAAY,GAGzC,CAKAxL,UAAAA,GACE,OAAOtK,KAAKuB,OACd,CASAmS,WAAAA,CAAYwC,EAASC,GACnBD,EAAQlV,MAAM,KAAKkM,SAASxM,IAC1B,IAAI2S,EAAarT,KAAKsT,SAAS5S,GAC/B,QAAmB,IAAf2S,EAAuB,CACzB,MAAME,EAAY7S,EAAGG,OACrBb,KAAKgT,SAASO,GACdF,EAAarT,KAAKsT,SAASC,EAC7B,CACAF,EAAW9R,QAAQrB,KAAKiW,EAAa,GAEzC,CAWAjC,QAAAA,CAAS7E,EAAQ+G,GACf,MAAMjJ,EAAOnN,KAAKsT,SAASjE,QACd,IAATlC,GACFA,EAAKyB,OAAO1O,KAAKkW,EAErB,CAOAhC,YAAAA,CAAa/E,EAAQ8G,GACnB,MAAMhJ,EAAOnN,KAAKsT,SAASjE,QACd,IAATlC,GACFA,EAAK8F,WAAW/S,KAAKiW,EAEzB,CAMAE,qBAAAA,GACE,OAAOrW,KAAK8R,QAAQhD,MAAMlO,GAt4BT,QAs4BiBA,EAAIN,MACxC,CACAgW,YAAAA,GACE,OAAOtW,KAAKqW,yBAAyB3U,OA34BT,IA44B9B,CACAD,YAAAA,CAAa0I,GACX,MAAMvJ,EAAMZ,KAAKqW,wBACbzV,EACFA,EAAIc,MAAQyI,EAEZnK,KAAK8R,QAAQyE,QAAQ,CAAEjW,KAh5BR,MAg5B8BoB,MAAOyI,GAExD,CACA9J,SAAAA,CAAUyB,GACR,OAAOA,GAAkB,MAAXA,EAAI,GAAaA,EAAIoE,OAAO,GAAGrF,OAASiB,EAAIjB,MAC5D,CACAyK,OAAAA,GACE,MAAMkL,GAAStL,EAAAA,EAAAA,MACf,MAAO,CACL4B,MAAO9M,KAAK8M,MACZC,MAAO/M,KAAK+M,MACZ0J,MAAO,CAAC,EACRD,SACAE,UAAW3M,EAAO/J,KAAKoL,gBAE3B,CACAF,SAAAA,GACE,OAAOA,EAAAA,EAAAA,MAAY/G,KACrB,CACAwS,YAAc,KAAAA,EAAAA,GAAA,GACdtV,YAAc,KAAAA,EAAAA,GAAA,GACduV,kBAAoB,KAAAA,EAAAA,GAAA,GACpBtV,kBAAoB,KAAAA,EAAAA,GAAA,GACpBuV,gBAAkB,KAAAA,EAAAA,GAAA,GAClB3K,gBAAkB,KAAAA,EAAAA,GAAA,IA2NhB4K,IAvN4Bja,EAAAA,EAAAA,KAAQ6G,GAAY,6CAExCA,EAAQqT,iCACNrT,EAAQqT,uDAGZrT,EAAQsT,qFAKRtT,EAAQuT,sHAORvT,EAAQwT,uDAIRxT,EAAQyT,uBACNzT,EAAQsT,oDAIRtT,EAAQ0T,iEAKR1T,EAAQqT,8FAMVrT,EAAQ2T,8IAUN3T,EAAQ4T,6BACV5T,EAAQ6T,wCAGN7T,EAAQ8T,uIASV9T,EAAQyT,qEAKRzT,EAAQ+T,gFAII/T,EAAQgU,sDAENhU,EAAQgU,iFAIRhU,EAAQgU,mCACpBhU,EAAQgU,0FAKVhU,EAAQiU,sBAAwBjU,EAAQkU,4DAGvClU,EAAQiU,sBAAwBjU,EAAQkU,wDAIzClU,EAAQwT,wGAMRxT,EAAQmU,iCACNnU,EAAQmU,wDAIVnU,EAAQmU,iCACNnU,EAAQmU,8DAIVnU,EAAQoU,kCACNpU,EAAQ2T,oEAIV3T,EAAQqU,qBAAuBrU,EAAQ2T,6BAClC3T,EAAQ2T,gEAKb3T,EAAQsU,UAAYtU,EAAQyT,uBAC1BzT,EAAQuU,aAAevU,EAAQsT,kEAIjCtT,EAAQyT,uBACNzT,EAAQuU,aAAevU,EAAQsT,2EAIjCtT,EAAQ0T,0DAIR1T,EAAQwU,wCACNxU,EAAQuU,aAAevU,EAAQsT,kFAKhCtT,EAAQwT,yJASPxT,EAAQuU,aAAevU,EAAQsT,iJAQjCtT,EAAQqU,qBAAuBrU,EAAQ2T,oFAGvC3T,EAAQyU,cAAgBzU,EAAQyU,cAAgB,gNAchDzU,EAAQyU,cAAgBzU,EAAQyU,cAAgB,kGAQhDzU,EAAQ6T,4BACN7T,EAAQ4T,qGAMV5T,EAAQ6T,4BACN7T,EAAQ4T,uGAOV5T,EAAQ8T,kEAIP9T,EAAQ8T,mEAGA9T,EAAQ8T,oEAIjB9T,EAAQ0T,yBACN1T,EAAQ0T,sHAOV1T,EAAQuT,mBAEf,Y,kEC1/DCzL,GAAoC3O,EAAAA,EAAAA,KAAO,CAAC6D,EAAIqK,KAClD,IAAIqN,EACkB,YAAlBrN,IACFqN,GAAiBC,EAAAA,EAAAA,KAAO,KAAO3X,IAIjC,OAF+B,YAAlBqK,GAA8BsN,EAAAA,EAAAA,KAAOD,EAAetL,QAAQ,GAAGwL,gBAAgBC,OAAQF,EAAAA,EAAAA,KAAO,SAC1FA,OAAO,QAAQ3X,MACtB,GACT,qBAGCyL,GAAsCtP,EAAAA,EAAAA,KAAO,CAAC0O,EAAKwE,EAASyI,EAAYpM,KAC1Eb,EAAIkN,KAAK,QAASD,GAClB,MAAM,MAAEE,EAAK,OAAEC,EAAM,EAAEC,EAAC,EAAEC,GAAMC,EAA+BvN,EAAKwE,IACpEgJ,EAAAA,EAAAA,IAAiBxN,EAAKoN,EAAQD,EAAOtM,GACrC,MAAM4M,EAAUC,EAAcL,EAAGC,EAAGH,EAAOC,EAAQ5I,GACnDxE,EAAIkN,KAAK,UAAWO,GACpBnO,EAAAA,GAAIqO,MAAM,uBAAuBF,mBAAyBjJ,IAAU,GACnE,uBACC+I,GAAiDjc,EAAAA,EAAAA,KAAO,CAAC0O,EAAKwE,KAChE,MAAMoJ,EAAS5N,EAAIwD,QAAQqK,WAAa,CAAEV,MAAO,EAAGC,OAAQ,EAAGC,EAAG,EAAGC,EAAG,GACxE,MAAO,CACLH,MAAOS,EAAOT,MAAkB,EAAV3I,EACtB4I,OAAQQ,EAAOR,OAAmB,EAAV5I,EACxB6I,EAAGO,EAAOP,EACVC,EAAGM,EAAON,EACX,GACA,kCACCI,GAAgCpc,EAAAA,EAAAA,KAAO,CAAC+b,EAAGC,EAAGH,EAAOC,EAAQ5I,IACxD,GAAG6I,EAAI7I,KAAW8I,EAAI9I,KAAW2I,KAASC,KAChD,gB", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-AEK57VVT.mjs", "../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs"], "sourcesContent": ["import {\n  getDiagramElement,\n  setupViewPortForSVG\n} from \"./chunk-RZ5BOZE2.mjs\";\nimport {\n  render\n} from \"./chunk-TYCBKAJE.mjs\";\nimport {\n  generateId,\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  clear,\n  common_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/state/parser/stateDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 2], $V1 = [1, 3], $V2 = [1, 4], $V3 = [2, 4], $V4 = [1, 9], $V5 = [1, 11], $V6 = [1, 16], $V7 = [1, 17], $V8 = [1, 18], $V9 = [1, 19], $Va = [1, 32], $Vb = [1, 20], $Vc = [1, 21], $Vd = [1, 22], $Ve = [1, 23], $Vf = [1, 24], $Vg = [1, 26], $Vh = [1, 27], $Vi = [1, 28], $Vj = [1, 29], $Vk = [1, 30], $Vl = [1, 31], $Vm = [1, 34], $Vn = [1, 35], $Vo = [1, 36], $Vp = [1, 37], $Vq = [1, 33], $Vr = [1, 4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $Vs = [1, 4, 5, 14, 15, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $Vt = [4, 5, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SPACE\": 4, \"NL\": 5, \"SD\": 6, \"document\": 7, \"line\": 8, \"statement\": 9, \"classDefStatement\": 10, \"styleStatement\": 11, \"cssClassStatement\": 12, \"idStatement\": 13, \"DESCR\": 14, \"-->\": 15, \"HIDE_EMPTY\": 16, \"scale\": 17, \"WIDTH\": 18, \"COMPOSIT_STATE\": 19, \"STRUCT_START\": 20, \"STRUCT_STOP\": 21, \"STATE_DESCR\": 22, \"AS\": 23, \"ID\": 24, \"FORK\": 25, \"JOIN\": 26, \"CHOICE\": 27, \"CONCURRENT\": 28, \"note\": 29, \"notePosition\": 30, \"NOTE_TEXT\": 31, \"direction\": 32, \"acc_title\": 33, \"acc_title_value\": 34, \"acc_descr\": 35, \"acc_descr_value\": 36, \"acc_descr_multiline_value\": 37, \"classDef\": 38, \"CLASSDEF_ID\": 39, \"CLASSDEF_STYLEOPTS\": 40, \"DEFAULT\": 41, \"style\": 42, \"STYLE_IDS\": 43, \"STYLEDEF_STYLEOPTS\": 44, \"class\": 45, \"CLASSENTITY_IDS\": 46, \"STYLECLASS\": 47, \"direction_tb\": 48, \"direction_bt\": 49, \"direction_rl\": 50, \"direction_lr\": 51, \"eol\": 52, \";\": 53, \"EDGE_STATE\": 54, \"STYLE_SEPARATOR\": 55, \"left_of\": 56, \"right_of\": 57, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SPACE\", 5: \"NL\", 6: \"SD\", 14: \"DESCR\", 15: \"-->\", 16: \"HIDE_EMPTY\", 17: \"scale\", 18: \"WIDTH\", 19: \"COMPOSIT_STATE\", 20: \"STRUCT_START\", 21: \"STRUCT_STOP\", 22: \"STATE_DESCR\", 23: \"AS\", 24: \"ID\", 25: \"FORK\", 26: \"JOIN\", 27: \"CHOICE\", 28: \"CONCURRENT\", 29: \"note\", 31: \"NOTE_TEXT\", 33: \"acc_title\", 34: \"acc_title_value\", 35: \"acc_descr\", 36: \"acc_descr_value\", 37: \"acc_descr_multiline_value\", 38: \"classDef\", 39: \"CLASSDEF_ID\", 40: \"CLASSDEF_STYLEOPTS\", 41: \"DEFAULT\", 42: \"style\", 43: \"STYLE_IDS\", 44: \"STYLEDEF_STYLEOPTS\", 45: \"class\", 46: \"CLASSENTITY_IDS\", 47: \"STYLECLASS\", 48: \"direction_tb\", 49: \"direction_bt\", 50: \"direction_rl\", 51: \"direction_lr\", 53: \";\", 54: \"EDGE_STATE\", 55: \"STYLE_SEPARATOR\", 56: \"left_of\", 57: \"right_of\" },\n    productions_: [0, [3, 2], [3, 2], [3, 2], [7, 0], [7, 2], [8, 2], [8, 1], [8, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 2], [9, 3], [9, 4], [9, 1], [9, 2], [9, 1], [9, 4], [9, 3], [9, 6], [9, 1], [9, 1], [9, 1], [9, 1], [9, 4], [9, 4], [9, 1], [9, 2], [9, 2], [9, 1], [10, 3], [10, 3], [11, 3], [12, 3], [32, 1], [32, 1], [32, 1], [32, 1], [52, 1], [52, 1], [13, 1], [13, 1], [13, 3], [13, 3], [30, 1], [30, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setRootDoc($$[$0]);\n          return $$[$0];\n          break;\n        case 4:\n          this.$ = [];\n          break;\n        case 5:\n          if ($$[$0] != \"nl\") {\n            $$[$0 - 1].push($$[$0]);\n            this.$ = $$[$0 - 1];\n          }\n          break;\n        case 6:\n        case 7:\n          this.$ = $$[$0];\n          break;\n        case 8:\n          this.$ = \"nl\";\n          break;\n        case 12:\n          this.$ = $$[$0];\n          break;\n        case 13:\n          const stateStmt = $$[$0 - 1];\n          stateStmt.description = yy.trimColon($$[$0]);\n          this.$ = stateStmt;\n          break;\n        case 14:\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 2], state2: $$[$0] };\n          break;\n        case 15:\n          const relDescription = yy.trimColon($$[$0]);\n          this.$ = { stmt: \"relation\", state1: $$[$0 - 3], state2: $$[$0 - 1], description: relDescription };\n          break;\n        case 19:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: \"\", doc: $$[$0 - 1] };\n          break;\n        case 20:\n          var id = $$[$0];\n          var description = $$[$0 - 2].trim();\n          if ($$[$0].match(\":\")) {\n            var parts = $$[$0].split(\":\");\n            id = parts[0];\n            description = [description, parts[1]];\n          }\n          this.$ = { stmt: \"state\", id, type: \"default\", description };\n          break;\n        case 21:\n          this.$ = { stmt: \"state\", id: $$[$0 - 3], type: \"default\", description: $$[$0 - 5], doc: $$[$0 - 1] };\n          break;\n        case 22:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"fork\" };\n          break;\n        case 23:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"join\" };\n          break;\n        case 24:\n          this.$ = { stmt: \"state\", id: $$[$0], type: \"choice\" };\n          break;\n        case 25:\n          this.$ = { stmt: \"state\", id: yy.getDividerId(), type: \"divider\" };\n          break;\n        case 26:\n          this.$ = { stmt: \"state\", id: $$[$0 - 1].trim(), note: { position: $$[$0 - 2].trim(), text: $$[$0].trim() } };\n          break;\n        case 29:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 30:\n        case 31:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 32:\n        case 33:\n          this.$ = { stmt: \"classDef\", id: $$[$0 - 1].trim(), classes: $$[$0].trim() };\n          break;\n        case 34:\n          this.$ = { stmt: \"style\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 35:\n          this.$ = { stmt: \"applyClass\", id: $$[$0 - 1].trim(), styleClass: $$[$0].trim() };\n          break;\n        case 36:\n          yy.setDirection(\"TB\");\n          this.$ = { stmt: \"dir\", value: \"TB\" };\n          break;\n        case 37:\n          yy.setDirection(\"BT\");\n          this.$ = { stmt: \"dir\", value: \"BT\" };\n          break;\n        case 38:\n          yy.setDirection(\"RL\");\n          this.$ = { stmt: \"dir\", value: \"RL\" };\n          break;\n        case 39:\n          yy.setDirection(\"LR\");\n          this.$ = { stmt: \"dir\", value: \"LR\" };\n          break;\n        case 42:\n        case 43:\n          this.$ = { stmt: \"state\", id: $$[$0].trim(), type: \"default\", description: \"\" };\n          break;\n        case 44:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n        case 45:\n          this.$ = { stmt: \"state\", id: $$[$0 - 2].trim(), classes: [$$[$0].trim()], type: \"default\", description: \"\" };\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: $V0, 5: $V1, 6: $V2 }, { 1: [3] }, { 3: 5, 4: $V0, 5: $V1, 6: $V2 }, { 3: 6, 4: $V0, 5: $V1, 6: $V2 }, o([1, 4, 5, 16, 17, 19, 22, 24, 25, 26, 27, 28, 29, 33, 35, 37, 38, 42, 45, 48, 49, 50, 51, 54], $V3, { 7: 7 }), { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 3], 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 5]), { 9: 38, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 7]), o($Vr, [2, 8]), o($Vr, [2, 9]), o($Vr, [2, 10]), o($Vr, [2, 11]), o($Vr, [2, 12], { 14: [1, 39], 15: [1, 40] }), o($Vr, [2, 16]), { 18: [1, 41] }, o($Vr, [2, 18], { 20: [1, 42] }), { 23: [1, 43] }, o($Vr, [2, 22]), o($Vr, [2, 23]), o($Vr, [2, 24]), o($Vr, [2, 25]), { 30: 44, 31: [1, 45], 56: [1, 46], 57: [1, 47] }, o($Vr, [2, 28]), { 34: [1, 48] }, { 36: [1, 49] }, o($Vr, [2, 31]), { 39: [1, 50], 41: [1, 51] }, { 43: [1, 52] }, { 46: [1, 53] }, o($Vs, [2, 42], { 55: [1, 54] }), o($Vs, [2, 43], { 55: [1, 55] }), o($Vr, [2, 36]), o($Vr, [2, 37]), o($Vr, [2, 38]), o($Vr, [2, 39]), o($Vr, [2, 6]), o($Vr, [2, 13]), { 13: 56, 24: $Va, 54: $Vq }, o($Vr, [2, 17]), o($Vt, $V3, { 7: 57 }), { 24: [1, 58] }, { 24: [1, 59] }, { 23: [1, 60] }, { 24: [2, 46] }, { 24: [2, 47] }, o($Vr, [2, 29]), o($Vr, [2, 30]), { 40: [1, 61] }, { 40: [1, 62] }, { 44: [1, 63] }, { 47: [1, 64] }, { 24: [1, 65] }, { 24: [1, 66] }, o($Vr, [2, 14], { 14: [1, 67] }), { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 68], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 20], { 20: [1, 69] }), { 31: [1, 70] }, { 24: [1, 71] }, o($Vr, [2, 32]), o($Vr, [2, 33]), o($Vr, [2, 34]), o($Vr, [2, 35]), o($Vs, [2, 44]), o($Vs, [2, 45]), o($Vr, [2, 15]), o($Vr, [2, 19]), o($Vt, $V3, { 7: 72 }), o($Vr, [2, 26]), o($Vr, [2, 27]), { 4: $V4, 5: $V5, 8: 8, 9: 10, 10: 12, 11: 13, 12: 14, 13: 15, 16: $V6, 17: $V7, 19: $V8, 21: [1, 73], 22: $V9, 24: $Va, 25: $Vb, 26: $Vc, 27: $Vd, 28: $Ve, 29: $Vf, 32: 25, 33: $Vg, 35: $Vh, 37: $Vi, 38: $Vj, 42: $Vk, 45: $Vl, 48: $Vm, 49: $Vn, 50: $Vo, 51: $Vp, 54: $Vq }, o($Vr, [2, 21])],\n    defaultActions: { 5: [2, 1], 6: [2, 2], 46: [2, 46], 47: [2, 47] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 41;\n            break;\n          case 1:\n            return 48;\n            break;\n          case 2:\n            return 49;\n            break;\n          case 3:\n            return 50;\n            break;\n          case 4:\n            return 51;\n            break;\n          case 5:\n            break;\n          case 6:\n            {\n            }\n            break;\n          case 7:\n            return 5;\n            break;\n          case 8:\n            break;\n          case 9:\n            break;\n          case 10:\n            break;\n          case 11:\n            break;\n          case 12:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 13:\n            return 18;\n            break;\n          case 14:\n            this.popState();\n            break;\n          case 15:\n            this.begin(\"acc_title\");\n            return 33;\n            break;\n          case 16:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 17:\n            this.begin(\"acc_descr\");\n            return 35;\n            break;\n          case 18:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 19:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 20:\n            this.popState();\n            break;\n          case 21:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 22:\n            this.pushState(\"CLASSDEF\");\n            return 38;\n            break;\n          case 23:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return \"DEFAULT_CLASSDEF_ID\";\n            break;\n          case 24:\n            this.popState();\n            this.pushState(\"CLASSDEFID\");\n            return 39;\n            break;\n          case 25:\n            this.popState();\n            return 40;\n            break;\n          case 26:\n            this.pushState(\"CLASS\");\n            return 45;\n            break;\n          case 27:\n            this.popState();\n            this.pushState(\"CLASS_STYLE\");\n            return 46;\n            break;\n          case 28:\n            this.popState();\n            return 47;\n            break;\n          case 29:\n            this.pushState(\"STYLE\");\n            return 42;\n            break;\n          case 30:\n            this.popState();\n            this.pushState(\"STYLEDEF_STYLES\");\n            return 43;\n            break;\n          case 31:\n            this.popState();\n            return 44;\n            break;\n          case 32:\n            this.pushState(\"SCALE\");\n            return 17;\n            break;\n          case 33:\n            return 18;\n            break;\n          case 34:\n            this.popState();\n            break;\n          case 35:\n            this.pushState(\"STATE\");\n            break;\n          case 36:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 37:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 38:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 39:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 25;\n            break;\n          case 40:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 26;\n            break;\n          case 41:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -10).trim();\n            return 27;\n            break;\n          case 42:\n            return 48;\n            break;\n          case 43:\n            return 49;\n            break;\n          case 44:\n            return 50;\n            break;\n          case 45:\n            return 51;\n            break;\n          case 46:\n            this.pushState(\"STATE_STRING\");\n            break;\n          case 47:\n            this.pushState(\"STATE_ID\");\n            return \"AS\";\n            break;\n          case 48:\n            this.popState();\n            return \"ID\";\n            break;\n          case 49:\n            this.popState();\n            break;\n          case 50:\n            return \"STATE_DESCR\";\n            break;\n          case 51:\n            return 19;\n            break;\n          case 52:\n            this.popState();\n            break;\n          case 53:\n            this.popState();\n            this.pushState(\"struct\");\n            return 20;\n            break;\n          case 54:\n            break;\n          case 55:\n            this.popState();\n            return 21;\n            break;\n          case 56:\n            break;\n          case 57:\n            this.begin(\"NOTE\");\n            return 29;\n            break;\n          case 58:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 56;\n            break;\n          case 59:\n            this.popState();\n            this.pushState(\"NOTE_ID\");\n            return 57;\n            break;\n          case 60:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE\");\n            break;\n          case 61:\n            this.popState();\n            this.pushState(\"FLOATING_NOTE_ID\");\n            return \"AS\";\n            break;\n          case 62:\n            break;\n          case 63:\n            return \"NOTE_TEXT\";\n            break;\n          case 64:\n            this.popState();\n            return \"ID\";\n            break;\n          case 65:\n            this.popState();\n            this.pushState(\"NOTE_TEXT\");\n            return 24;\n            break;\n          case 66:\n            this.popState();\n            yy_.yytext = yy_.yytext.substr(2).trim();\n            return 31;\n            break;\n          case 67:\n            this.popState();\n            yy_.yytext = yy_.yytext.slice(0, -8).trim();\n            return 31;\n            break;\n          case 68:\n            return 6;\n            break;\n          case 69:\n            return 6;\n            break;\n          case 70:\n            return 16;\n            break;\n          case 71:\n            return 54;\n            break;\n          case 72:\n            return 24;\n            break;\n          case 73:\n            yy_.yytext = yy_.yytext.trim();\n            return 14;\n            break;\n          case 74:\n            return 15;\n            break;\n          case 75:\n            return 28;\n            break;\n          case 76:\n            return 55;\n            break;\n          case 77:\n            return 5;\n            break;\n          case 78:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:default\\b)/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:[\\s]+)/i, /^(?:((?!\\n)\\s)+)/i, /^(?:#[^\\n]*)/i, /^(?:%[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:classDef\\s+)/i, /^(?:DEFAULT\\s+)/i, /^(?:\\w+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:class\\s+)/i, /^(?:(\\w+)+((,\\s*\\w+)*))/i, /^(?:[^\\n]*)/i, /^(?:style\\s+)/i, /^(?:[\\w,]+\\s+)/i, /^(?:[^\\n]*)/i, /^(?:scale\\s+)/i, /^(?:\\d+)/i, /^(?:\\s+width\\b)/i, /^(?:state\\s+)/i, /^(?:.*<<fork>>)/i, /^(?:.*<<join>>)/i, /^(?:.*<<choice>>)/i, /^(?:.*\\[\\[fork\\]\\])/i, /^(?:.*\\[\\[join\\]\\])/i, /^(?:.*\\[\\[choice\\]\\])/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:[\"])/i, /^(?:\\s*as\\s+)/i, /^(?:[^\\n\\{]*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n\\s\\{]+)/i, /^(?:\\n)/i, /^(?:\\{)/i, /^(?:%%(?!\\{)[^\\n]*)/i, /^(?:\\})/i, /^(?:[\\n])/i, /^(?:note\\s+)/i, /^(?:left of\\b)/i, /^(?:right of\\b)/i, /^(?:\")/i, /^(?:\\s*as\\s*)/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[^\\n]*)/i, /^(?:\\s*[^:\\n\\s\\-]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:[\\s\\S]*?end note\\b)/i, /^(?:stateDiagram\\s+)/i, /^(?:stateDiagram-v2\\s+)/i, /^(?:hide empty description\\b)/i, /^(?:\\[\\*\\])/i, /^(?:[^:\\n\\s\\-\\{]+)/i, /^(?:\\s*:[^:\\n;]+)/i, /^(?:-->)/i, /^(?:--)/i, /^(?::::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"LINE\": { \"rules\": [9, 10], \"inclusive\": false }, \"struct\": { \"rules\": [9, 10, 22, 26, 29, 35, 42, 43, 44, 45, 54, 55, 56, 57, 71, 72, 73, 74, 75], \"inclusive\": false }, \"FLOATING_NOTE_ID\": { \"rules\": [64], \"inclusive\": false }, \"FLOATING_NOTE\": { \"rules\": [61, 62, 63], \"inclusive\": false }, \"NOTE_TEXT\": { \"rules\": [66, 67], \"inclusive\": false }, \"NOTE_ID\": { \"rules\": [65], \"inclusive\": false }, \"NOTE\": { \"rules\": [58, 59, 60], \"inclusive\": false }, \"STYLEDEF_STYLEOPTS\": { \"rules\": [], \"inclusive\": false }, \"STYLEDEF_STYLES\": { \"rules\": [31], \"inclusive\": false }, \"STYLE_IDS\": { \"rules\": [], \"inclusive\": false }, \"STYLE\": { \"rules\": [30], \"inclusive\": false }, \"CLASS_STYLE\": { \"rules\": [28], \"inclusive\": false }, \"CLASS\": { \"rules\": [27], \"inclusive\": false }, \"CLASSDEFID\": { \"rules\": [25], \"inclusive\": false }, \"CLASSDEF\": { \"rules\": [23, 24], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [20, 21], \"inclusive\": false }, \"acc_descr\": { \"rules\": [18], \"inclusive\": false }, \"acc_title\": { \"rules\": [16], \"inclusive\": false }, \"SCALE\": { \"rules\": [13, 14, 33, 34], \"inclusive\": false }, \"ALIAS\": { \"rules\": [], \"inclusive\": false }, \"STATE_ID\": { \"rules\": [48], \"inclusive\": false }, \"STATE_STRING\": { \"rules\": [49, 50], \"inclusive\": false }, \"FORK_STATE\": { \"rules\": [], \"inclusive\": false }, \"STATE\": { \"rules\": [9, 10, 36, 37, 38, 39, 40, 41, 46, 47, 51, 52, 53], \"inclusive\": false }, \"ID\": { \"rules\": [9, 10], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 15, 17, 19, 22, 26, 29, 32, 35, 53, 57, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar stateDiagram_default = parser;\n\n// src/diagrams/state/stateCommon.ts\nvar DEFAULT_DIAGRAM_DIRECTION = \"TB\";\nvar DEFAULT_NESTED_DOC_DIR = \"TB\";\nvar STMT_DIRECTION = \"dir\";\nvar STMT_STATE = \"state\";\nvar STMT_RELATION = \"relation\";\nvar STMT_CLASSDEF = \"classDef\";\nvar STMT_STYLEDEF = \"style\";\nvar STMT_APPLYCLASS = \"applyClass\";\nvar DEFAULT_STATE_TYPE = \"default\";\nvar DIVIDER_TYPE = \"divider\";\nvar G_EDGE_STYLE = \"fill:none\";\nvar G_EDGE_ARROWHEADSTYLE = \"fill: #333\";\nvar G_EDGE_LABELPOS = \"c\";\nvar G_EDGE_LABELTYPE = \"text\";\nvar G_EDGE_THICKNESS = \"normal\";\nvar SHAPE_STATE = \"rect\";\nvar SHAPE_STATE_WITH_DESC = \"rectWithTitle\";\nvar SHAPE_START = \"stateStart\";\nvar SHAPE_END = \"stateEnd\";\nvar SHAPE_DIVIDER = \"divider\";\nvar SHAPE_GROUP = \"roundedWithTitle\";\nvar SHAPE_NOTE = \"note\";\nvar SHAPE_NOTEGROUP = \"noteGroup\";\nvar CSS_DIAGRAM = \"statediagram\";\nvar CSS_STATE = \"state\";\nvar CSS_DIAGRAM_STATE = `${CSS_DIAGRAM}-${CSS_STATE}`;\nvar CSS_EDGE = \"transition\";\nvar CSS_NOTE = \"note\";\nvar CSS_NOTE_EDGE = \"note-edge\";\nvar CSS_EDGE_NOTE_EDGE = `${CSS_EDGE} ${CSS_NOTE_EDGE}`;\nvar CSS_DIAGRAM_NOTE = `${CSS_DIAGRAM}-${CSS_NOTE}`;\nvar CSS_CLUSTER = \"cluster\";\nvar CSS_DIAGRAM_CLUSTER = `${CSS_DIAGRAM}-${CSS_CLUSTER}`;\nvar CSS_CLUSTER_ALT = \"cluster-alt\";\nvar CSS_DIAGRAM_CLUSTER_ALT = `${CSS_DIAGRAM}-${CSS_CLUSTER_ALT}`;\nvar PARENT = \"parent\";\nvar NOTE = \"note\";\nvar DOMID_STATE = \"state\";\nvar DOMID_TYPE_SPACER = \"----\";\nvar NOTE_ID = `${DOMID_TYPE_SPACER}${NOTE}`;\nvar PARENT_ID = `${DOMID_TYPE_SPACER}${PARENT}`;\n\n// src/diagrams/state/stateRenderer-v3-unified.ts\nvar getDir = /* @__PURE__ */ __name((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  if (!parsedItem.doc) {\n    return defaultDir;\n  }\n  let dir = defaultDir;\n  for (const parsedItemDoc of parsedItem.doc) {\n    if (parsedItemDoc.stmt === \"dir\") {\n      dir = parsedItemDoc.value;\n    }\n  }\n  return dir;\n}, \"getDir\");\nvar getClasses = /* @__PURE__ */ __name(function(text, diagramObj) {\n  return diagramObj.db.getClasses();\n}, \"getClasses\");\nvar draw = /* @__PURE__ */ __name(async function(text, id, _version, diag) {\n  log.info(\"REF0:\");\n  log.info(\"Drawing state diagram (v2)\", id);\n  const { securityLevel, state: conf, layout } = getConfig();\n  diag.db.extract(diag.db.getRootDocV2());\n  const data4Layout = diag.db.getData();\n  const svg = getDiagramElement(id, securityLevel);\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = layout;\n  data4Layout.nodeSpacing = conf?.nodeSpacing || 50;\n  data4Layout.rankSpacing = conf?.rankSpacing || 50;\n  data4Layout.markers = [\"barb\"];\n  data4Layout.diagramId = id;\n  await render(data4Layout, svg);\n  const padding = 8;\n  utils_default.insertTitle(\n    svg,\n    \"statediagramTitleText\",\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  setupViewPortForSVG(svg, padding, CSS_DIAGRAM, conf?.useMaxWidth ?? true);\n}, \"draw\");\nvar stateRenderer_v3_unified_default = {\n  getClasses,\n  draw,\n  getDir\n};\n\n// src/diagrams/state/dataFetcher.js\nvar nodeDb = /* @__PURE__ */ new Map();\nvar graphItemCount = 0;\nfunction stateDomId(itemId = \"\", counter = 0, type = \"\", typeSpacer = DOMID_TYPE_SPACER) {\n  const typeStr = type !== null && type.length > 0 ? `${typeSpacer}${type}` : \"\";\n  return `${DOMID_STATE}-${itemId}${typeStr}-${counter}`;\n}\n__name(stateDomId, \"stateDomId\");\nvar setupDoc = /* @__PURE__ */ __name((parentParsedItem, doc, diagramStates, nodes, edges, altFlag, look, classes) => {\n  log.trace(\"items\", doc);\n  doc.forEach((item) => {\n    switch (item.stmt) {\n      case STMT_STATE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case DEFAULT_STATE_TYPE:\n        dataFetcher(parentParsedItem, item, diagramStates, nodes, edges, altFlag, look, classes);\n        break;\n      case STMT_RELATION:\n        {\n          dataFetcher(\n            parentParsedItem,\n            item.state1,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          dataFetcher(\n            parentParsedItem,\n            item.state2,\n            diagramStates,\n            nodes,\n            edges,\n            altFlag,\n            look,\n            classes\n          );\n          const edgeData = {\n            id: \"edge\" + graphItemCount,\n            start: item.state1.id,\n            end: item.state2.id,\n            arrowhead: \"normal\",\n            arrowTypeEnd: \"arrow_barb\",\n            style: G_EDGE_STYLE,\n            labelStyle: \"\",\n            label: common_default.sanitizeText(item.description, getConfig()),\n            arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n            labelpos: G_EDGE_LABELPOS,\n            labelType: G_EDGE_LABELTYPE,\n            thickness: G_EDGE_THICKNESS,\n            classes: CSS_EDGE,\n            look\n          };\n          edges.push(edgeData);\n          graphItemCount++;\n        }\n        break;\n    }\n  });\n}, \"setupDoc\");\nvar getDir2 = /* @__PURE__ */ __name((parsedItem, defaultDir = DEFAULT_NESTED_DOC_DIR) => {\n  let dir = defaultDir;\n  if (parsedItem.doc) {\n    for (const parsedItemDoc of parsedItem.doc) {\n      if (parsedItemDoc.stmt === \"dir\") {\n        dir = parsedItemDoc.value;\n      }\n    }\n  }\n  return dir;\n}, \"getDir\");\nfunction insertOrUpdateNode(nodes, nodeData, classes) {\n  if (!nodeData.id || nodeData.id === \"</join></fork>\" || nodeData.id === \"</choice>\") {\n    return;\n  }\n  if (nodeData.cssClasses) {\n    if (!Array.isArray(nodeData.cssCompiledStyles)) {\n      nodeData.cssCompiledStyles = [];\n    }\n    nodeData.cssClasses.split(\" \").forEach((cssClass) => {\n      if (classes.get(cssClass)) {\n        const classDef = classes.get(cssClass);\n        nodeData.cssCompiledStyles = [...nodeData.cssCompiledStyles, ...classDef.styles];\n      }\n    });\n  }\n  const existingNodeData = nodes.find((node) => node.id === nodeData.id);\n  if (existingNodeData) {\n    Object.assign(existingNodeData, nodeData);\n  } else {\n    nodes.push(nodeData);\n  }\n}\n__name(insertOrUpdateNode, \"insertOrUpdateNode\");\nfunction getClassesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.classes?.join(\" \") ?? \"\";\n}\n__name(getClassesFromDbInfo, \"getClassesFromDbInfo\");\nfunction getStylesFromDbInfo(dbInfoItem) {\n  return dbInfoItem?.styles ?? [];\n}\n__name(getStylesFromDbInfo, \"getStylesFromDbInfo\");\nvar dataFetcher = /* @__PURE__ */ __name((parent, parsedItem, diagramStates, nodes, edges, altFlag, look, classes) => {\n  const itemId = parsedItem.id;\n  const dbState = diagramStates.get(itemId);\n  const classStr = getClassesFromDbInfo(dbState);\n  const style = getStylesFromDbInfo(dbState);\n  log.info(\"dataFetcher parsedItem\", parsedItem, dbState, style);\n  if (itemId !== \"root\") {\n    let shape = SHAPE_STATE;\n    if (parsedItem.start === true) {\n      shape = SHAPE_START;\n    } else if (parsedItem.start === false) {\n      shape = SHAPE_END;\n    }\n    if (parsedItem.type !== DEFAULT_STATE_TYPE) {\n      shape = parsedItem.type;\n    }\n    if (!nodeDb.get(itemId)) {\n      nodeDb.set(itemId, {\n        id: itemId,\n        shape,\n        description: common_default.sanitizeText(itemId, getConfig()),\n        cssClasses: `${classStr} ${CSS_DIAGRAM_STATE}`,\n        cssStyles: style\n      });\n    }\n    const newNode = nodeDb.get(itemId);\n    if (parsedItem.description) {\n      if (Array.isArray(newNode.description)) {\n        newNode.shape = SHAPE_STATE_WITH_DESC;\n        newNode.description.push(parsedItem.description);\n      } else {\n        if (newNode.description?.length > 0) {\n          newNode.shape = SHAPE_STATE_WITH_DESC;\n          if (newNode.description === itemId) {\n            newNode.description = [parsedItem.description];\n          } else {\n            newNode.description = [newNode.description, parsedItem.description];\n          }\n        } else {\n          newNode.shape = SHAPE_STATE;\n          newNode.description = parsedItem.description;\n        }\n      }\n      newNode.description = common_default.sanitizeTextOrArray(newNode.description, getConfig());\n    }\n    if (newNode.description?.length === 1 && newNode.shape === SHAPE_STATE_WITH_DESC) {\n      if (newNode.type === \"group\") {\n        newNode.shape = SHAPE_GROUP;\n      } else {\n        newNode.shape = SHAPE_STATE;\n      }\n    }\n    if (!newNode.type && parsedItem.doc) {\n      log.info(\"Setting cluster for XCX\", itemId, getDir2(parsedItem));\n      newNode.type = \"group\";\n      newNode.isGroup = true;\n      newNode.dir = getDir2(parsedItem);\n      newNode.shape = parsedItem.type === DIVIDER_TYPE ? SHAPE_DIVIDER : SHAPE_GROUP;\n      newNode.cssClasses = `${newNode.cssClasses} ${CSS_DIAGRAM_CLUSTER} ${altFlag ? CSS_DIAGRAM_CLUSTER_ALT : \"\"}`;\n    }\n    const nodeData = {\n      labelStyle: \"\",\n      shape: newNode.shape,\n      label: newNode.description,\n      cssClasses: newNode.cssClasses,\n      cssCompiledStyles: [],\n      cssStyles: newNode.cssStyles,\n      id: itemId,\n      dir: newNode.dir,\n      domId: stateDomId(itemId, graphItemCount),\n      type: newNode.type,\n      isGroup: newNode.type === \"group\",\n      padding: 8,\n      rx: 10,\n      ry: 10,\n      look\n    };\n    if (nodeData.shape === SHAPE_DIVIDER) {\n      nodeData.label = \"\";\n    }\n    if (parent && parent.id !== \"root\") {\n      log.trace(\"Setting node \", itemId, \" to be child of its parent \", parent.id);\n      nodeData.parentId = parent.id;\n    }\n    nodeData.centerLabel = true;\n    if (parsedItem.note) {\n      const noteData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTE,\n        label: parsedItem.note.text,\n        cssClasses: CSS_DIAGRAM_NOTE,\n        // useHtmlLabels: false,\n        cssStyles: [],\n        cssCompilesStyles: [],\n        id: itemId + NOTE_ID + \"-\" + graphItemCount,\n        domId: stateDomId(itemId, graphItemCount, NOTE),\n        type: newNode.type,\n        isGroup: newNode.type === \"group\",\n        padding: getConfig().flowchart.padding,\n        look,\n        position: parsedItem.note.position\n      };\n      const parentNodeId = itemId + PARENT_ID;\n      const groupData = {\n        labelStyle: \"\",\n        shape: SHAPE_NOTEGROUP,\n        label: parsedItem.note.text,\n        cssClasses: newNode.cssClasses,\n        cssStyles: [],\n        id: itemId + PARENT_ID,\n        domId: stateDomId(itemId, graphItemCount, PARENT),\n        type: \"group\",\n        isGroup: true,\n        padding: 16,\n        //getConfig().flowchart.padding\n        look,\n        position: parsedItem.note.position\n      };\n      graphItemCount++;\n      groupData.id = parentNodeId;\n      noteData.parentId = parentNodeId;\n      insertOrUpdateNode(nodes, groupData, classes);\n      insertOrUpdateNode(nodes, noteData, classes);\n      insertOrUpdateNode(nodes, nodeData, classes);\n      let from = itemId;\n      let to = noteData.id;\n      if (parsedItem.note.position === \"left of\") {\n        from = noteData.id;\n        to = itemId;\n      }\n      edges.push({\n        id: from + \"-\" + to,\n        start: from,\n        end: to,\n        arrowhead: \"none\",\n        arrowTypeEnd: \"\",\n        style: G_EDGE_STYLE,\n        labelStyle: \"\",\n        classes: CSS_EDGE_NOTE_EDGE,\n        arrowheadStyle: G_EDGE_ARROWHEADSTYLE,\n        labelpos: G_EDGE_LABELPOS,\n        labelType: G_EDGE_LABELTYPE,\n        thickness: G_EDGE_THICKNESS,\n        look\n      });\n    } else {\n      insertOrUpdateNode(nodes, nodeData, classes);\n    }\n  }\n  if (parsedItem.doc) {\n    log.trace(\"Adding nodes children \");\n    setupDoc(parsedItem, parsedItem.doc, diagramStates, nodes, edges, !altFlag, look, classes);\n  }\n}, \"dataFetcher\");\nvar reset = /* @__PURE__ */ __name(() => {\n  nodeDb.clear();\n  graphItemCount = 0;\n}, \"reset\");\n\n// src/diagrams/state/stateDb.js\nvar START_NODE = \"[*]\";\nvar START_TYPE = \"start\";\nvar END_NODE = START_NODE;\nvar END_TYPE = \"end\";\nvar COLOR_KEYWORD = \"color\";\nvar FILL_KEYWORD = \"fill\";\nvar BG_FILL = \"bgFill\";\nvar STYLECLASS_SEP = \",\";\nfunction newClassesList() {\n  return /* @__PURE__ */ new Map();\n}\n__name(newClassesList, \"newClassesList\");\nvar newDoc = /* @__PURE__ */ __name(() => {\n  return {\n    /** @type {{ id1: string, id2: string, relationTitle: string }[]} */\n    relations: [],\n    states: /* @__PURE__ */ new Map(),\n    documents: {}\n  };\n}, \"newDoc\");\nvar clone = /* @__PURE__ */ __name((o) => JSON.parse(JSON.stringify(o)), \"clone\");\nvar StateDB = class {\n  static {\n    __name(this, \"StateDB\");\n  }\n  /**\n   * @param {1 | 2} version - v1 renderer or v2 renderer.\n   */\n  constructor(version) {\n    this.clear();\n    this.version = version;\n    this.setRootDoc = this.setRootDoc.bind(this);\n    this.getDividerId = this.getDividerId.bind(this);\n    this.setDirection = this.setDirection.bind(this);\n    this.trimColon = this.trimColon.bind(this);\n  }\n  /**\n   * @private\n   * @type {1 | 2}\n   */\n  version;\n  /**\n   * @private\n   * @type {Array}\n   */\n  nodes = [];\n  /**\n   * @private\n   * @type {Array}\n   */\n  edges = [];\n  /**\n   * @private\n   * @type {Array}\n   */\n  rootDoc = [];\n  /**\n   * @private\n   * @type {Map<string, any>}\n   */\n  classes = newClassesList();\n  // style classes defined by a classDef\n  /**\n   * @private\n   * @type {Object}\n   */\n  documents = {\n    root: newDoc()\n  };\n  /**\n   * @private\n   * @type {Object}\n   */\n  currentDocument = this.documents.root;\n  /**\n   * @private\n   * @type {number}\n   */\n  startEndCount = 0;\n  /**\n   * @private\n   * @type {number}\n   */\n  dividerCnt = 0;\n  static relationType = {\n    AGGREGATION: 0,\n    EXTENSION: 1,\n    COMPOSITION: 2,\n    DEPENDENCY: 3\n  };\n  setRootDoc(o) {\n    log.info(\"Setting root doc\", o);\n    this.rootDoc = o;\n    if (this.version === 1) {\n      this.extract(o);\n    } else {\n      this.extract(this.getRootDocV2());\n    }\n  }\n  getRootDoc() {\n    return this.rootDoc;\n  }\n  /**\n   * @private\n   * @param {Object} parent\n   * @param {Object} node\n   * @param {boolean} first\n   */\n  docTranslator(parent, node, first) {\n    if (node.stmt === STMT_RELATION) {\n      this.docTranslator(parent, node.state1, true);\n      this.docTranslator(parent, node.state2, false);\n    } else {\n      if (node.stmt === STMT_STATE) {\n        if (node.id === \"[*]\") {\n          node.id = first ? parent.id + \"_start\" : parent.id + \"_end\";\n          node.start = first;\n        } else {\n          node.id = node.id.trim();\n        }\n      }\n      if (node.doc) {\n        const doc = [];\n        let currentDoc = [];\n        let i;\n        for (i = 0; i < node.doc.length; i++) {\n          if (node.doc[i].type === DIVIDER_TYPE) {\n            const newNode = clone(node.doc[i]);\n            newNode.doc = clone(currentDoc);\n            doc.push(newNode);\n            currentDoc = [];\n          } else {\n            currentDoc.push(node.doc[i]);\n          }\n        }\n        if (doc.length > 0 && currentDoc.length > 0) {\n          const newNode = {\n            stmt: STMT_STATE,\n            id: generateId(),\n            type: \"divider\",\n            doc: clone(currentDoc)\n          };\n          doc.push(clone(newNode));\n          node.doc = doc;\n        }\n        node.doc.forEach((docNode) => this.docTranslator(node, docNode, true));\n      }\n    }\n  }\n  /**\n   * @private\n   */\n  getRootDocV2() {\n    this.docTranslator({ id: \"root\" }, { id: \"root\", doc: this.rootDoc }, true);\n    return { id: \"root\", doc: this.rootDoc };\n  }\n  /**\n   * Convert all of the statements (stmts) that were parsed into states and relationships.\n   * This is done because a state diagram may have nested sections,\n   * where each section is a 'document' and has its own set of statements.\n   * Ex: the section within a fork has its own statements, and incoming and outgoing statements\n   * refer to the fork as a whole (document).\n   * See the parser grammar:  the definition of a document is a document then a 'line', where a line can be a statement.\n   * This will push the statement into the list of statements for the current document.\n   * @private\n   * @param _doc\n   */\n  extract(_doc) {\n    let doc;\n    if (_doc.doc) {\n      doc = _doc.doc;\n    } else {\n      doc = _doc;\n    }\n    log.info(doc);\n    this.clear(true);\n    log.info(\"Extract initial document:\", doc);\n    doc.forEach((item) => {\n      log.warn(\"Statement\", item.stmt);\n      switch (item.stmt) {\n        case STMT_STATE:\n          this.addState(\n            item.id.trim(),\n            item.type,\n            item.doc,\n            item.description,\n            item.note,\n            item.classes,\n            item.styles,\n            item.textStyles\n          );\n          break;\n        case STMT_RELATION:\n          this.addRelation(item.state1, item.state2, item.description);\n          break;\n        case STMT_CLASSDEF:\n          this.addStyleClass(item.id.trim(), item.classes);\n          break;\n        case STMT_STYLEDEF:\n          {\n            const ids = item.id.trim().split(\",\");\n            const styles = item.styleClass.split(\",\");\n            ids.forEach((id) => {\n              let foundState = this.getState(id);\n              if (foundState === void 0) {\n                const trimmedId = id.trim();\n                this.addState(trimmedId);\n                foundState = this.getState(trimmedId);\n              }\n              foundState.styles = styles.map((s) => s.replace(/;/g, \"\")?.trim());\n            });\n          }\n          break;\n        case STMT_APPLYCLASS:\n          this.setCssClass(item.id.trim(), item.styleClass);\n          break;\n      }\n    });\n    const diagramStates = this.getStates();\n    const config = getConfig();\n    const look = config.look;\n    reset();\n    dataFetcher(\n      void 0,\n      this.getRootDocV2(),\n      diagramStates,\n      this.nodes,\n      this.edges,\n      true,\n      look,\n      this.classes\n    );\n    this.nodes.forEach((node) => {\n      if (Array.isArray(node.label)) {\n        node.description = node.label.slice(1);\n        if (node.isGroup && node.description.length > 0) {\n          throw new Error(\n            \"Group nodes can only have label. Remove the additional description for node [\" + node.id + \"]\"\n          );\n        }\n        node.label = node.label[0];\n      }\n    });\n  }\n  /**\n   * Function called by parser when a node definition has been found.\n   *\n   * @param {null | string} id\n   * @param {null | string} type\n   * @param {null | string} doc\n   * @param {null | string | string[]} descr - description for the state. Can be a string or a list or strings\n   * @param {null | string} note\n   * @param {null | string | string[]} classes - class styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 class, convert it to an array of that 1 class.\n   * @param {null | string | string[]} styles - styles to apply to this state. Can be a string (1 style) or an array of styles. If it's just 1 style, convert it to an array of that 1 style.\n   * @param {null | string | string[]} textStyles - text styles to apply to this state. Can be a string (1 text test) or an array of text styles. If it's just 1 text style, convert it to an array of that 1 text style.\n   */\n  addState(id, type = DEFAULT_STATE_TYPE, doc = null, descr = null, note = null, classes = null, styles = null, textStyles = null) {\n    const trimmedId = id?.trim();\n    if (!this.currentDocument.states.has(trimmedId)) {\n      log.info(\"Adding state \", trimmedId, descr);\n      this.currentDocument.states.set(trimmedId, {\n        id: trimmedId,\n        descriptions: [],\n        type,\n        doc,\n        note,\n        classes: [],\n        styles: [],\n        textStyles: []\n      });\n    } else {\n      if (!this.currentDocument.states.get(trimmedId).doc) {\n        this.currentDocument.states.get(trimmedId).doc = doc;\n      }\n      if (!this.currentDocument.states.get(trimmedId).type) {\n        this.currentDocument.states.get(trimmedId).type = type;\n      }\n    }\n    if (descr) {\n      log.info(\"Setting state description\", trimmedId, descr);\n      if (typeof descr === \"string\") {\n        this.addDescription(trimmedId, descr.trim());\n      }\n      if (typeof descr === \"object\") {\n        descr.forEach((des) => this.addDescription(trimmedId, des.trim()));\n      }\n    }\n    if (note) {\n      const doc2 = this.currentDocument.states.get(trimmedId);\n      doc2.note = note;\n      doc2.note.text = common_default.sanitizeText(doc2.note.text, getConfig());\n    }\n    if (classes) {\n      log.info(\"Setting state classes\", trimmedId, classes);\n      const classesList = typeof classes === \"string\" ? [classes] : classes;\n      classesList.forEach((cssClass) => this.setCssClass(trimmedId, cssClass.trim()));\n    }\n    if (styles) {\n      log.info(\"Setting state styles\", trimmedId, styles);\n      const stylesList = typeof styles === \"string\" ? [styles] : styles;\n      stylesList.forEach((style) => this.setStyle(trimmedId, style.trim()));\n    }\n    if (textStyles) {\n      log.info(\"Setting state styles\", trimmedId, styles);\n      const textStylesList = typeof textStyles === \"string\" ? [textStyles] : textStyles;\n      textStylesList.forEach((textStyle) => this.setTextStyle(trimmedId, textStyle.trim()));\n    }\n  }\n  clear(saveCommon) {\n    this.nodes = [];\n    this.edges = [];\n    this.documents = {\n      root: newDoc()\n    };\n    this.currentDocument = this.documents.root;\n    this.startEndCount = 0;\n    this.classes = newClassesList();\n    if (!saveCommon) {\n      clear();\n    }\n  }\n  getState(id) {\n    return this.currentDocument.states.get(id);\n  }\n  getStates() {\n    return this.currentDocument.states;\n  }\n  logDocuments() {\n    log.info(\"Documents = \", this.documents);\n  }\n  getRelations() {\n    return this.currentDocument.relations;\n  }\n  /**\n   * If the id is a start node ( [*] ), then return a new id constructed from\n   * the start node name and the current start node count.\n   * else return the given id\n   *\n   * @param {string} id\n   * @returns {string} - the id (original or constructed)\n   * @private\n   */\n  startIdIfNeeded(id = \"\") {\n    let fixedId = id;\n    if (id === START_NODE) {\n      this.startEndCount++;\n      fixedId = `${START_TYPE}${this.startEndCount}`;\n    }\n    return fixedId;\n  }\n  /**\n   * If the id is a start node ( [*] ), then return the start type ('start')\n   * else return the given type\n   *\n   * @param {string} id\n   * @param {string} type\n   * @returns {string} - the type that should be used\n   * @private\n   */\n  startTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n    return id === START_NODE ? START_TYPE : type;\n  }\n  /**\n   * If the id is an end node ( [*] ), then return a new id constructed from\n   * the end node name and the current start_end node count.\n   * else return the given id\n   *\n   * @param {string} id\n   * @returns {string} - the id (original or constructed)\n   * @private\n   */\n  endIdIfNeeded(id = \"\") {\n    let fixedId = id;\n    if (id === END_NODE) {\n      this.startEndCount++;\n      fixedId = `${END_TYPE}${this.startEndCount}`;\n    }\n    return fixedId;\n  }\n  /**\n   * If the id is an end node ( [*] ), then return the end type\n   * else return the given type\n   *\n   * @param {string} id\n   * @param {string} type\n   * @returns {string} - the type that should be used\n   * @private\n   */\n  endTypeIfNeeded(id = \"\", type = DEFAULT_STATE_TYPE) {\n    return id === END_NODE ? END_TYPE : type;\n  }\n  /**\n   *\n   * @param item1\n   * @param item2\n   * @param relationTitle\n   */\n  addRelationObjs(item1, item2, relationTitle) {\n    let id1 = this.startIdIfNeeded(item1.id.trim());\n    let type1 = this.startTypeIfNeeded(item1.id.trim(), item1.type);\n    let id2 = this.startIdIfNeeded(item2.id.trim());\n    let type2 = this.startTypeIfNeeded(item2.id.trim(), item2.type);\n    this.addState(\n      id1,\n      type1,\n      item1.doc,\n      item1.description,\n      item1.note,\n      item1.classes,\n      item1.styles,\n      item1.textStyles\n    );\n    this.addState(\n      id2,\n      type2,\n      item2.doc,\n      item2.description,\n      item2.note,\n      item2.classes,\n      item2.styles,\n      item2.textStyles\n    );\n    this.currentDocument.relations.push({\n      id1,\n      id2,\n      relationTitle: common_default.sanitizeText(relationTitle, getConfig())\n    });\n  }\n  /**\n   * Add a relation between two items.  The items may be full objects or just the string id of a state.\n   *\n   * @param {string | object} item1\n   * @param {string | object} item2\n   * @param {string} title\n   */\n  addRelation(item1, item2, title) {\n    if (typeof item1 === \"object\") {\n      this.addRelationObjs(item1, item2, title);\n    } else {\n      const id1 = this.startIdIfNeeded(item1.trim());\n      const type1 = this.startTypeIfNeeded(item1);\n      const id2 = this.endIdIfNeeded(item2.trim());\n      const type2 = this.endTypeIfNeeded(item2);\n      this.addState(id1, type1);\n      this.addState(id2, type2);\n      this.currentDocument.relations.push({\n        id1,\n        id2,\n        title: common_default.sanitizeText(title, getConfig())\n      });\n    }\n  }\n  addDescription(id, descr) {\n    const theState = this.currentDocument.states.get(id);\n    const _descr = descr.startsWith(\":\") ? descr.replace(\":\", \"\").trim() : descr;\n    theState.descriptions.push(common_default.sanitizeText(_descr, getConfig()));\n  }\n  cleanupLabel(label) {\n    if (label.substring(0, 1) === \":\") {\n      return label.substr(2).trim();\n    } else {\n      return label.trim();\n    }\n  }\n  getDividerId() {\n    this.dividerCnt++;\n    return \"divider-id-\" + this.dividerCnt;\n  }\n  /**\n   * Called when the parser comes across a (style) class definition\n   * @example classDef my-style fill:#f96;\n   *\n   * @param {string} id - the id of this (style) class\n   * @param  {string | null} styleAttributes - the string with 1 or more style attributes (each separated by a comma)\n   */\n  addStyleClass(id, styleAttributes = \"\") {\n    if (!this.classes.has(id)) {\n      this.classes.set(id, { id, styles: [], textStyles: [] });\n    }\n    const foundClass = this.classes.get(id);\n    if (styleAttributes !== void 0 && styleAttributes !== null) {\n      styleAttributes.split(STYLECLASS_SEP).forEach((attrib) => {\n        const fixedAttrib = attrib.replace(/([^;]*);/, \"$1\").trim();\n        if (RegExp(COLOR_KEYWORD).exec(attrib)) {\n          const newStyle1 = fixedAttrib.replace(FILL_KEYWORD, BG_FILL);\n          const newStyle2 = newStyle1.replace(COLOR_KEYWORD, FILL_KEYWORD);\n          foundClass.textStyles.push(newStyle2);\n        }\n        foundClass.styles.push(fixedAttrib);\n      });\n    }\n  }\n  /**\n   * Return all of the style classes\n   * @returns {{} | any | classes}\n   */\n  getClasses() {\n    return this.classes;\n  }\n  /**\n   * Add a (style) class or css class to a state with the given id.\n   * If the state isn't already in the list of known states, add it.\n   * Might be called by parser when a style class or CSS class should be applied to a state\n   *\n   * @param {string | string[]} itemIds The id or a list of ids of the item(s) to apply the css class to\n   * @param {string} cssClassName CSS class name\n   */\n  setCssClass(itemIds, cssClassName) {\n    itemIds.split(\",\").forEach((id) => {\n      let foundState = this.getState(id);\n      if (foundState === void 0) {\n        const trimmedId = id.trim();\n        this.addState(trimmedId);\n        foundState = this.getState(trimmedId);\n      }\n      foundState.classes.push(cssClassName);\n    });\n  }\n  /**\n   * Add a style to a state with the given id.\n   * @example style stateId fill:#f9f,stroke:#333,stroke-width:4px\n   *   where 'style' is the keyword\n   *   stateId is the id of a state\n   *   the rest of the string is the styleText (all of the attributes to be applied to the state)\n   *\n   * @param itemId The id of item to apply the style to\n   * @param styleText - the text of the attributes for the style\n   */\n  setStyle(itemId, styleText) {\n    const item = this.getState(itemId);\n    if (item !== void 0) {\n      item.styles.push(styleText);\n    }\n  }\n  /**\n   * Add a text style to a state with the given id\n   *\n   * @param itemId The id of item to apply the css class to\n   * @param cssClassName CSS class name\n   */\n  setTextStyle(itemId, cssClassName) {\n    const item = this.getState(itemId);\n    if (item !== void 0) {\n      item.textStyles.push(cssClassName);\n    }\n  }\n  /**\n   * Finds the direction statement in the root document.\n   * @private\n   * @returns {{ value: string } | undefined} - the direction statement if present\n   */\n  getDirectionStatement() {\n    return this.rootDoc.find((doc) => doc.stmt === STMT_DIRECTION);\n  }\n  getDirection() {\n    return this.getDirectionStatement()?.value ?? DEFAULT_DIAGRAM_DIRECTION;\n  }\n  setDirection(dir) {\n    const doc = this.getDirectionStatement();\n    if (doc) {\n      doc.value = dir;\n    } else {\n      this.rootDoc.unshift({ stmt: STMT_DIRECTION, value: dir });\n    }\n  }\n  trimColon(str) {\n    return str && str[0] === \":\" ? str.substr(1).trim() : str.trim();\n  }\n  getData() {\n    const config = getConfig();\n    return {\n      nodes: this.nodes,\n      edges: this.edges,\n      other: {},\n      config,\n      direction: getDir(this.getRootDocV2())\n    };\n  }\n  getConfig() {\n    return getConfig().state;\n  }\n  getAccTitle = getAccTitle;\n  setAccTitle = setAccTitle;\n  getAccDescription = getAccDescription;\n  setAccDescription = setAccDescription;\n  setDiagramTitle = setDiagramTitle;\n  getDiagramTitle = getDiagramTitle;\n};\n\n// src/diagrams/state/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `\ndefs #statediagram-barbEnd {\n    fill: ${options.transitionColor};\n    stroke: ${options.transitionColor};\n  }\ng.stateGroup text {\n  fill: ${options.nodeBorder};\n  stroke: none;\n  font-size: 10px;\n}\ng.stateGroup text {\n  fill: ${options.textColor};\n  stroke: none;\n  font-size: 10px;\n\n}\ng.stateGroup .state-title {\n  font-weight: bolder;\n  fill: ${options.stateLabelColor};\n}\n\ng.stateGroup rect {\n  fill: ${options.mainBkg};\n  stroke: ${options.nodeBorder};\n}\n\ng.stateGroup line {\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.transition {\n  stroke: ${options.transitionColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.stateGroup .composit {\n  fill: ${options.background};\n  border-bottom: 1px\n}\n\n.stateGroup .alt-composit {\n  fill: #e0e0e0;\n  border-bottom: 1px\n}\n\n.state-note {\n  stroke: ${options.noteBorderColor};\n  fill: ${options.noteBkgColor};\n\n  text {\n    fill: ${options.noteTextColor};\n    stroke: none;\n    font-size: 10px;\n  }\n}\n\n.stateLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${options.mainBkg};\n  opacity: 0.5;\n}\n\n.edgeLabel .label rect {\n  fill: ${options.labelBackgroundColor};\n  opacity: 0.5;\n}\n.edgeLabel {\n  background-color: ${options.edgeLabelBackground};\n  p {\n    background-color: ${options.edgeLabelBackground};\n  }\n  rect {\n    opacity: 0.5;\n    background-color: ${options.edgeLabelBackground};\n    fill: ${options.edgeLabelBackground};\n  }\n  text-align: center;\n}\n.edgeLabel .label text {\n  fill: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n.label div .edgeLabel {\n  color: ${options.transitionLabelColor || options.tertiaryTextColor};\n}\n\n.stateLabel text {\n  fill: ${options.stateLabelColor};\n  font-size: 10px;\n  font-weight: bold;\n}\n\n.node circle.state-start {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node .fork-join {\n  fill: ${options.specialStateColor};\n  stroke: ${options.specialStateColor};\n}\n\n.node circle.state-end {\n  fill: ${options.innerEndBackground};\n  stroke: ${options.background};\n  stroke-width: 1.5\n}\n.end-state-inner {\n  fill: ${options.compositeBackground || options.background};\n  // stroke: ${options.background};\n  stroke-width: 1.5\n}\n\n.node rect {\n  fill: ${options.stateBkg || options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n.node polygon {\n  fill: ${options.mainBkg};\n  stroke: ${options.stateBorder || options.nodeBorder};;\n  stroke-width: 1px;\n}\n#statediagram-barbEnd {\n  fill: ${options.lineColor};\n}\n\n.statediagram-cluster rect {\n  fill: ${options.compositeTitleBackground};\n  stroke: ${options.stateBorder || options.nodeBorder};\n  stroke-width: 1px;\n}\n\n.cluster-label, .nodeLabel {\n  color: ${options.stateLabelColor};\n  // line-height: 1;\n}\n\n.statediagram-cluster rect.outer {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state .divider {\n  stroke: ${options.stateBorder || options.nodeBorder};\n}\n\n.statediagram-state .title-state {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-cluster.statediagram-cluster .inner {\n  fill: ${options.compositeBackground || options.background};\n}\n.statediagram-cluster.statediagram-cluster-alt .inner {\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.statediagram-cluster .inner {\n  rx:0;\n  ry:0;\n}\n\n.statediagram-state rect.basic {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state rect.divider {\n  stroke-dasharray: 10,10;\n  fill: ${options.altBackground ? options.altBackground : \"#efefef\"};\n}\n\n.note-edge {\n  stroke-dasharray: 5;\n}\n\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n.statediagram-note rect {\n  fill: ${options.noteBkgColor};\n  stroke: ${options.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n\n.statediagram-note text {\n  fill: ${options.noteTextColor};\n}\n\n.statediagram-note .nodeLabel {\n  color: ${options.noteTextColor};\n}\n.statediagram .edgeLabel {\n  color: red; // ${options.noteTextColor};\n}\n\n#dependencyStart, #dependencyEnd {\n  fill: ${options.lineColor};\n  stroke: ${options.lineColor};\n  stroke-width: 1;\n}\n\n.statediagramTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${options.textColor};\n}\n`, \"getStyles\");\nvar styles_default = getStyles;\n\nexport {\n  stateDiagram_default,\n  stateRenderer_v3_unified_default,\n  StateDB,\n  styles_default\n};\n", "import {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/insertElementsForSize.js\nimport { select } from \"d3\";\nvar getDiagramElement = /* @__PURE__ */ __name((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ __name((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  configureSvgSize(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ __name((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ __name((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\nexport {\n  getDiagramElement,\n  setupViewPortForSVG\n};\n"], "names": ["parser", "o", "__name", "k", "v", "o2", "l", "length", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "$Vr", "$Vs", "$Vt", "parser2", "trace", "yy", "symbols_", "terminals_", "productions_", "performAction", "yytext", "yyleng", "y<PERSON><PERSON>o", "yystate", "$$", "_$", "$0", "setRootDoc", "this", "$", "push", "stateStmt", "description", "trimColon", "stmt", "state1", "state2", "relDescription", "id", "type", "doc", "trim", "match", "parts", "split", "getDividerId", "note", "position", "text", "setAccTitle", "setAccDescription", "classes", "styleClass", "setDirection", "value", "table", "defaultActions", "parseError", "str", "hash", "recoverable", "error", "Error", "parse", "input", "self", "stack", "tstack", "vstack", "lstack", "recovering", "args", "slice", "call", "arguments", "lexer2", "Object", "create", "lexer", "sharedState", "prototype", "hasOwnProperty", "setInput", "yylloc", "yyloc", "ranges", "options", "lex", "token", "pop", "Array", "getPrototypeOf", "n", "symbol", "preErrorSymbol", "state", "action", "r", "p", "len", "newState", "expected", "yyval", "errStr", "showPosition", "join", "line", "loc", "first_line", "last_line", "first_column", "last_column", "range", "apply", "concat", "EOF", "_input", "_more", "_backtrack", "done", "matched", "conditionStack", "offset", "ch", "unput", "lines", "substr", "oldLines", "more", "reject", "backtrack_lexer", "less", "pastInput", "past", "replace", "upcomingInput", "next", "pre", "c", "test_match", "indexed_rule", "backup", "matches", "tempMatch", "index", "rules", "_currentRules", "i", "flex", "begin", "condition", "popState", "conditions", "topState", "Math", "abs", "pushState", "stateStackSize", "yy_", "$avoiding_name_collisions", "YY_START", "<PERSON><PERSON><PERSON>", "stateDiagram_default", "STMT_STATE", "STMT_RELATION", "DEFAULT_STATE_TYPE", "DIVIDER_TYPE", "G_EDGE_STYLE", "G_EDGE_ARROWHEADSTYLE", "G_EDGE_LABELTYPE", "G_EDGE_THICKNESS", "SHAPE_STATE", "SHAPE_STATE_WITH_DESC", "SHAPE_DIVIDER", "SHAPE_GROUP", "CSS_DIAGRAM", "CSS_DIAGRAM_STATE", "CSS_EDGE", "CSS_EDGE_NOTE_EDGE", "CSS_DIAGRAM_NOTE", "CSS_DIAGRAM_CLUSTER", "CSS_DIAGRAM_CLUSTER_ALT", "PARENT", "NOTE", "DOMID_TYPE_SPACER", "NOTE_ID", "PARENT_ID", "getDir", "parsedItem", "defaultDir", "undefined", "dir", "parsedItemDoc", "stateRenderer_v3_unified_default", "getClasses", "diagramObj", "db", "draw", "async", "_version", "diag", "log", "info", "securityLevel", "conf", "layout", "getConfig", "extract", "getRootDocV2", "data4Layout", "getData", "svg", "getDiagramElement", "layoutAlgorithm", "nodeSpacing", "rankSpacing", "markers", "diagramId", "render", "utils_default", "insertTitle", "titleTopMargin", "getDiagramTitle", "setupViewPortForSVG", "useMaxWidth", "nodeDb", "Map", "graphItemCount", "stateDomId", "counter", "typeSpacer", "setupDoc", "parentParsedItem", "diagramStates", "nodes", "edges", "altFlag", "look", "for<PERSON>ach", "item", "dataFetcher", "edgeData", "start", "end", "arrowhead", "arrowTypeEnd", "style", "labelStyle", "label", "common_default", "sanitizeText", "arrowheadStyle", "labelpos", "labelType", "thickness", "getDir2", "insertOrUpdateNode", "nodeData", "cssClasses", "isArray", "cssCompiledStyles", "cssClass", "get", "classDef", "styles", "existingNodeData", "find", "node", "assign", "getClassesFromDbInfo", "dbInfoItem", "getStylesFromDbInfo", "parent", "itemId", "dbState", "classStr", "shape", "set", "cssStyles", "newNode", "sanitizeTextOrArray", "isGroup", "domId", "padding", "rx", "ry", "parentId", "centerLabel", "noteData", "cssCompilesStyles", "flowchart", "parentNodeId", "groupData", "from", "to", "reset", "clear", "START_NODE", "START_TYPE", "END_NODE", "COLOR_KEYWORD", "FILL_KEYWORD", "newClassesList", "newDoc", "relations", "states", "documents", "clone", "JSON", "stringify", "StateDB", "constructor", "version", "bind", "rootDoc", "root", "currentDocument", "startEndCount", "dividerCnt", "static", "AGGREGATION", "EXTENSION", "COMPOSITION", "DEPENDENCY", "getRootDoc", "docTranslator", "first", "currentDoc", "generateId", "docNode", "_doc", "warn", "addState", "textStyles", "addRelation", "addStyleClass", "ids", "foundState", "getState", "trimmedId", "map", "s", "setCssClass", "getStates", "descr", "has", "descriptions", "addDescription", "des", "doc2", "setStyle", "textStyle", "setTextStyle", "saveCommon", "logDocuments", "getRelations", "startIdIfNeeded", "fixedId", "startTypeIfNeeded", "endIdIfNeeded", "endTypeIfNeeded", "addRelationObjs", "item1", "item2", "relationTitle", "id1", "type1", "id2", "type2", "title", "theState", "_descr", "startsWith", "cleanupLabel", "substring", "styleAttributes", "foundClass", "attrib", "fixedAttrib", "RegExp", "exec", "newStyle2", "itemIds", "cssClassName", "styleText", "getDirectionStatement", "getDirection", "unshift", "config", "other", "direction", "getAccTitle", "getAccDescription", "setDiagramTitle", "styles_default", "transitionColor", "nodeBorder", "textColor", "stateLabelColor", "mainBkg", "lineColor", "background", "noteBorderColor", "noteBkgColor", "noteTextColor", "labelBackgroundColor", "edgeLabelBackground", "transitionLabelColor", "tertiaryTextColor", "specialStateColor", "innerEndBackground", "compositeBackground", "stateBkg", "stateBorder", "compositeTitleBackground", "altBackground", "sandboxElement", "select", "contentDocument", "body", "cssDiagram", "attr", "width", "height", "x", "y", "calculateDimensionsWithPadding", "configureSvgSize", "viewBox", "createViewBox", "debug", "bounds", "getBBox"], "sourceRoot": ""}