{"version": 3, "file": "static/js/569.a929d4b5.chunk.js", "mappings": "iMA6BIA,EAAqBC,EAAAA,GAAsBC,IAC3CC,EAAiB,CACnBC,SAA0B,IAAIC,IAC9BC,UAAU,EACVC,OAAQP,GAENI,EAAWD,EAAeC,SAC1BE,EAAWH,EAAeG,SAC1BC,EAASC,gBAAgBR,GACzBS,GAA6BC,EAAAA,EAAAA,KAAO,IAAMF,gBAAgBD,IAAS,aACnEI,GAAyBD,EAAAA,EAAAA,KAAO,KAClCN,EAA2B,IAAIC,IAC/BC,EAAWH,EAAeG,UAC1BM,EAAAA,EAAAA,KAAO,GACN,SACCC,GAA6BH,EAAAA,EAAAA,KAAOI,IAAsB,IAArB,MAAEC,EAAK,MAAEC,GAAOF,EAClDV,EAASa,IAAIF,KAChBX,EAASc,IAAIH,EAAOC,GACpBG,EAAAA,GAAIC,MAAM,sBAAsBL,kBAAsBC,KACxD,GACC,cACCK,GAA8BX,EAAAA,EAAAA,KAAO,IAAMN,GAAU,eACrDkB,GAA8BZ,EAAAA,EAAAA,KAAQa,IACxCjB,EAAWiB,CAAM,GAChB,eACCC,GAA8Bd,EAAAA,EAAAA,KAAO,IAAMJ,GAAU,eACrDmB,EAAK,CACPC,UAAWjB,EACXG,MAAOD,EACPgB,gBAAe,KACfC,gBAAe,KACfC,YAAW,KACXC,YAAW,KACXC,kBAAiB,KACjBC,kBAAiB,KACjBnB,aACAQ,cACAC,cACAE,eAIES,GAA6BvB,EAAAA,EAAAA,KAAO,CAACwB,EAAKC,MAC5CC,EAAAA,EAAAA,GAAiBF,EAAKC,GACtBA,EAAIb,YAAYY,EAAI5B,UACpB4B,EAAI9B,SAASiC,IAAIF,EAAItB,WAAW,GAC/B,cACCyB,EAAS,CACXC,OAAuB7B,EAAAA,EAAAA,KAAO8B,UAC5B,MAAMN,QAAYK,EAAAA,EAAAA,IAAM,MAAOE,GAC/BtB,EAAAA,GAAIC,MAAMc,GACVD,EAAWC,EAAKT,EAAG,GAClB,UAiCDiB,GA7B4BhC,EAAAA,EAAAA,KAAQiC,GAAY,gCAEtCA,EAAQC,uCACDD,EAAQE,kCACbF,EAAQG,qDAGVH,EAAQI,2CACFJ,EAAQK,2GAKXL,EAAQM,gCACbN,EAAQO,wCACDP,EAAQQ,kDAGRR,EAAQQ,0BACfR,EAAQS,uCACJT,EAAQU,8EAIZV,EAAQW,yCACDX,EAAQQ,+BACVR,EAAQY,6BAEtB,aAKCC,GAAgC9C,EAAAA,EAAAA,KAAQ+C,IAC1C,MAAMC,EAAU,IAAID,EAAUE,WAAWtB,KAAKuB,IACrC,CACL7C,MAAO6C,EAAQ,GACf5C,MAAO4C,EAAQ,OAEhBC,MAAK,CAACC,EAAGC,IACHA,EAAE/C,MAAQ8C,EAAE9C,QAKrB,OAHYgD,EAAAA,EAAAA,OAAQhD,OACjBiD,GAAcA,EAAUjD,OAEpBd,CAAIwD,EAAQ,GAClB,iBA6ECQ,EAAU,CACZ5B,SACAb,KACA0C,SANa,CAAEC,MAzEU1D,EAAAA,EAAAA,KAAO,CAAC2D,EAAMC,EAAIC,EAAUC,KACrDrD,EAAAA,GAAIC,MAAM,wBAA0BiD,GACpC,MAAMlC,EAAMqC,EAAQ/C,GACdgD,GAAe/C,EAAAA,EAAAA,MACfgD,GAAYC,EAAAA,EAAAA,IAAcxC,EAAIT,YAAa+C,EAAavE,KAExD0E,EAAmB,GAEnBC,EAAS,IACTC,EAAWD,EACXE,GAAMC,EAAAA,EAAAA,GAAiBV,GACvBW,EAAQF,EAAIG,OAAO,KACzBD,EAAME,KAAK,YAAa,sBACxB,MAAM,eAAEC,GAAmBX,EAC3B,IAAKY,IAAoBC,EAAAA,EAAAA,IAAcF,EAAepC,qBACtDqC,IAAqB,EACrB,MAAME,EAAeb,EAAUa,aACzBC,EAASC,KAAKC,IAAIZ,EAAUD,GAAU,EAZ7B,GAaTc,GAAeC,EAAAA,EAAAA,OAAMC,YAAY,GAAGC,YAAYN,GAChDO,GAAoBH,EAAAA,EAAAA,OAAMC,YAAYL,EAASD,GAAcO,YAAYN,EAASD,GACxFN,EAAMC,OAAO,UAAUC,KAAK,KAAM,GAAGA,KAAK,KAAM,GAAGA,KAAK,IAAKK,EAASH,EAAmB,GAAGF,KAAK,QAAS,kBAC1G,MAAM1B,EAAYtB,EAAId,cAChB2E,EAAOxC,EAAcC,GACrBwC,EAAoB,CACxBb,EAAec,KACfd,EAAee,KACff,EAAegB,KACfhB,EAAeiB,KACfjB,EAAekB,KACflB,EAAemB,KACfnB,EAAeoB,KACfpB,EAAeqB,KACfrB,EAAesB,KACftB,EAAeuB,MACfvB,EAAewB,MACfxB,EAAeyB,OAEXC,GAAQC,EAAAA,EAAAA,KAAad,GAC3BhB,EAAM+B,UAAU,YAAYC,KAAKjB,GAAMkB,QAAQhC,OAAO,QAAQC,KAAK,IAAKQ,GAAcR,KAAK,QAASgC,GAC3FL,EAAMK,EAAMF,KAAKlG,SACvBoE,KAAK,QAAS,aACjB,IAAIiC,EAAM,EACV3D,EAAU4D,SAASC,IACjBF,GAAOE,CAAO,IAEhBrC,EAAM+B,UAAU,YAAYC,KAAKjB,GAAMkB,QAAQhC,OAAO,QAAQb,MAAM8C,IAC1DA,EAAMF,KAAKjG,MAAQoG,EAAM,KAAKG,QAAQ,GAAK,MAClDpC,KAAK,aAAcgC,GACb,aAAepB,EAAkByB,SAASL,GAAS,MACzDM,MAAM,cAAe,UAAUtC,KAAK,QAAS,SAChDF,EAAMC,OAAO,QAAQb,KAAKlC,EAAIP,mBAAmBuD,KAAK,IAAK,GAAGA,KAAK,KAAK,KAAoBA,KAAK,QAAS,gBAC1G,MAAMuC,EAASzC,EAAM+B,UAAU,WAAWC,KAAKH,EAAMa,UAAUT,QAAQhC,OAAO,KAAKC,KAAK,QAAS,UAAUA,KAAK,aAAa,CAACyC,EAAQC,IAK7H,kBAJSjD,GAGCiD,EAHDjD,GACSkC,EAAMa,SAASG,OAAS,GAGG,MAEtDJ,EAAOxC,OAAO,QAAQC,KAAK,QAASP,GAAkBO,KAAK,SAAUP,GAAkB6C,MAAM,OAAQX,GAAOW,MAAM,SAAUX,GAC5HY,EAAOT,KAAKjB,GAAMd,OAAO,QAAQC,KAAK,IAAKP,IAAmCO,KAAK,IAAKP,IAAmCP,MAAM8C,IAC/H,MAAM,MAAEpG,EAAK,MAAEC,GAAUmG,EAAMF,KAC/B,OAAI9E,EAAIX,cACC,GAAGT,MAAUC,KAEfD,CAAK,IAEd,MAGMgH,EAAajD,IAHMW,KAAKuC,OACzBN,EAAOV,UAAU,QAAQiB,QAAQ5F,KAAK6F,GAASA,GAAMC,wBAAwBC,OAAS,KAG3FrD,EAAII,KAAK,UAAW,OAAO4C,UAC3BM,EAAAA,EAAAA,IAAiBtD,EAAKF,EAAQkD,EAAYrD,EAAU4D,YAAY,GAC/D,SAQDC,OAAQ7F,E,iBC/MV,SAASN,EAAiBF,EAAKT,GACzBS,EAAIsG,UACN/G,EAAGM,oBAAoBG,EAAIsG,UAEzBtG,EAAIuG,UACNhH,EAAGI,cAAcK,EAAIuG,UAEnBvG,EAAIwG,OACNjH,EAAGE,kBAAkBO,EAAIwG,MAE7B,C,kBACAhI,E,QAAAA,IAAO0B,EAAkB,mB", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/pieDiagram-IB7DONF6.mjs", "../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-4BMEZGHF.mjs"], "sourcesContent": ["import {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  cleanAndMerge,\n  parseFontSize\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/pie/pieParser.ts\nimport { parse } from \"@mermaid-js/parser\";\n\n// src/diagrams/pie/pieDb.ts\nvar DEFAULT_PIE_CONFIG = defaultConfig_default.pie;\nvar DEFAULT_PIE_DB = {\n  sections: /* @__PURE__ */ new Map(),\n  showData: false,\n  config: DEFAULT_PIE_CONFIG\n};\nvar sections = DEFAULT_PIE_DB.sections;\nvar showData = DEFAULT_PIE_DB.showData;\nvar config = structuredClone(DEFAULT_PIE_CONFIG);\nvar getConfig2 = /* @__PURE__ */ __name(() => structuredClone(config), \"getConfig\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  sections = /* @__PURE__ */ new Map();\n  showData = DEFAULT_PIE_DB.showData;\n  clear();\n}, \"clear\");\nvar addSection = /* @__PURE__ */ __name(({ label, value }) => {\n  if (!sections.has(label)) {\n    sections.set(label, value);\n    log.debug(`added new section: ${label}, with value: ${value}`);\n  }\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ __name(() => sections, \"getSections\");\nvar setShowData = /* @__PURE__ */ __name((toggle) => {\n  showData = toggle;\n}, \"setShowData\");\nvar getShowData = /* @__PURE__ */ __name(() => showData, \"getShowData\");\nvar db = {\n  getConfig: getConfig2,\n  clear: clear2,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n  addSection,\n  getSections,\n  setShowData,\n  getShowData\n};\n\n// src/diagrams/pie/pieParser.ts\nvar populateDb = /* @__PURE__ */ __name((ast, db2) => {\n  populateCommonDb(ast, db2);\n  db2.setShowData(ast.showData);\n  ast.sections.map(db2.addSection);\n}, \"populateDb\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"pie\", input);\n    log.debug(ast);\n    populateDb(ast, db);\n  }, \"parse\")\n};\n\n// src/diagrams/pie/pieStyles.ts\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .pieCircle{\n    stroke: ${options.pieStrokeColor};\n    stroke-width : ${options.pieStrokeWidth};\n    opacity : ${options.pieOpacity};\n  }\n  .pieOuterCircle{\n    stroke: ${options.pieOuterStrokeColor};\n    stroke-width: ${options.pieOuterStrokeWidth};\n    fill: none;\n  }\n  .pieTitleText {\n    text-anchor: middle;\n    font-size: ${options.pieTitleTextSize};\n    fill: ${options.pieTitleTextColor};\n    font-family: ${options.fontFamily};\n  }\n  .slice {\n    font-family: ${options.fontFamily};\n    fill: ${options.pieSectionTextColor};\n    font-size:${options.pieSectionTextSize};\n    // fill: white;\n  }\n  .legend text {\n    fill: ${options.pieLegendTextColor};\n    font-family: ${options.fontFamily};\n    font-size: ${options.pieLegendTextSize};\n  }\n`, \"getStyles\");\nvar pieStyles_default = getStyles;\n\n// src/diagrams/pie/pieRenderer.ts\nimport { arc, pie as d3pie, scaleOrdinal } from \"d3\";\nvar createPieArcs = /* @__PURE__ */ __name((sections2) => {\n  const pieData = [...sections2.entries()].map((element) => {\n    return {\n      label: element[0],\n      value: element[1]\n    };\n  }).sort((a, b) => {\n    return b.value - a.value;\n  });\n  const pie = d3pie().value(\n    (d3Section) => d3Section.value\n  );\n  return pie(pieData);\n}, \"createPieArcs\");\nvar draw = /* @__PURE__ */ __name((text, id, _version, diagObj) => {\n  log.debug(\"rendering pie chart\\n\" + text);\n  const db2 = diagObj.db;\n  const globalConfig = getConfig();\n  const pieConfig = cleanAndMerge(db2.getConfig(), globalConfig.pie);\n  const MARGIN = 40;\n  const LEGEND_RECT_SIZE = 18;\n  const LEGEND_SPACING = 4;\n  const height = 450;\n  const pieWidth = height;\n  const svg = selectSvgElement(id);\n  const group = svg.append(\"g\");\n  group.attr(\"transform\", \"translate(\" + pieWidth / 2 + \",\" + height / 2 + \")\");\n  const { themeVariables } = globalConfig;\n  let [outerStrokeWidth] = parseFontSize(themeVariables.pieOuterStrokeWidth);\n  outerStrokeWidth ??= 2;\n  const textPosition = pieConfig.textPosition;\n  const radius = Math.min(pieWidth, height) / 2 - MARGIN;\n  const arcGenerator = arc().innerRadius(0).outerRadius(radius);\n  const labelArcGenerator = arc().innerRadius(radius * textPosition).outerRadius(radius * textPosition);\n  group.append(\"circle\").attr(\"cx\", 0).attr(\"cy\", 0).attr(\"r\", radius + outerStrokeWidth / 2).attr(\"class\", \"pieOuterCircle\");\n  const sections2 = db2.getSections();\n  const arcs = createPieArcs(sections2);\n  const myGeneratedColors = [\n    themeVariables.pie1,\n    themeVariables.pie2,\n    themeVariables.pie3,\n    themeVariables.pie4,\n    themeVariables.pie5,\n    themeVariables.pie6,\n    themeVariables.pie7,\n    themeVariables.pie8,\n    themeVariables.pie9,\n    themeVariables.pie10,\n    themeVariables.pie11,\n    themeVariables.pie12\n  ];\n  const color = scaleOrdinal(myGeneratedColors);\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"path\").attr(\"d\", arcGenerator).attr(\"fill\", (datum) => {\n    return color(datum.data.label);\n  }).attr(\"class\", \"pieCircle\");\n  let sum = 0;\n  sections2.forEach((section) => {\n    sum += section;\n  });\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"text\").text((datum) => {\n    return (datum.data.value / sum * 100).toFixed(0) + \"%\";\n  }).attr(\"transform\", (datum) => {\n    return \"translate(\" + labelArcGenerator.centroid(datum) + \")\";\n  }).style(\"text-anchor\", \"middle\").attr(\"class\", \"slice\");\n  group.append(\"text\").text(db2.getDiagramTitle()).attr(\"x\", 0).attr(\"y\", -(height - 50) / 2).attr(\"class\", \"pieTitleText\");\n  const legend = group.selectAll(\".legend\").data(color.domain()).enter().append(\"g\").attr(\"class\", \"legend\").attr(\"transform\", (_datum, index) => {\n    const height2 = LEGEND_RECT_SIZE + LEGEND_SPACING;\n    const offset = height2 * color.domain().length / 2;\n    const horizontal = 12 * LEGEND_RECT_SIZE;\n    const vertical = index * height2 - offset;\n    return \"translate(\" + horizontal + \",\" + vertical + \")\";\n  });\n  legend.append(\"rect\").attr(\"width\", LEGEND_RECT_SIZE).attr(\"height\", LEGEND_RECT_SIZE).style(\"fill\", color).style(\"stroke\", color);\n  legend.data(arcs).append(\"text\").attr(\"x\", LEGEND_RECT_SIZE + LEGEND_SPACING).attr(\"y\", LEGEND_RECT_SIZE - LEGEND_SPACING).text((datum) => {\n    const { label, value } = datum.data;\n    if (db2.getShowData()) {\n      return `${label} [${value}]`;\n    }\n    return label;\n  });\n  const longestTextWidth = Math.max(\n    ...legend.selectAll(\"text\").nodes().map((node) => node?.getBoundingClientRect().width ?? 0)\n  );\n  const totalWidth = pieWidth + MARGIN + LEGEND_RECT_SIZE + LEGEND_SPACING + longestTextWidth;\n  svg.attr(\"viewBox\", `0 0 ${totalWidth} ${height}`);\n  configureSvgSize(svg, height, totalWidth, pieConfig.useMaxWidth);\n}, \"draw\");\nvar renderer = { draw };\n\n// src/diagrams/pie/pieDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles: pieStyles_default\n};\nexport {\n  diagram\n};\n", "import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/common/populateCommonDb.ts\nfunction populateCommonDb(ast, db) {\n  if (ast.accDescr) {\n    db.setAccDescription?.(ast.accDescr);\n  }\n  if (ast.accTitle) {\n    db.setAccTitle?.(ast.accTitle);\n  }\n  if (ast.title) {\n    db.setDiagramTitle?.(ast.title);\n  }\n}\n__name(populateCommonDb, \"populateCommonDb\");\n\nexport {\n  populateCommonDb\n};\n"], "names": ["DEFAULT_PIE_CONFIG", "defaultConfig_default", "pie", "DEFAULT_PIE_DB", "sections", "Map", "showData", "config", "structuredClone", "getConfig2", "__name", "clear2", "clear", "addSection", "_ref", "label", "value", "has", "set", "log", "debug", "getSections", "setShowData", "toggle", "getShowData", "db", "getConfig", "setDiagramTitle", "getDiagramTitle", "setAccTitle", "getAccTitle", "setAccDescription", "getAccDescription", "populateDb", "ast", "db2", "populateCommonDb", "map", "parser", "parse", "async", "input", "pieStyles_default", "options", "pieStrokeColor", "pieStrokeWidth", "pieOpacity", "pieOuterStrokeColor", "pieOuterStrokeWidth", "pieTitleTextSize", "pieTitleTextColor", "fontFamily", "pieSectionTextColor", "pieSectionTextSize", "pieLegendTextColor", "pieLegendTextSize", "createPieArcs", "sections2", "pieData", "entries", "element", "sort", "a", "b", "d3pie", "d3Section", "diagram", "renderer", "draw", "text", "id", "_version", "diagObj", "globalConfig", "pieConfig", "cleanAndMerge", "LEGEND_RECT_SIZE", "height", "<PERSON><PERSON><PERSON><PERSON>", "svg", "selectSvgElement", "group", "append", "attr", "themeVariables", "outerStrokeWidth", "parseFontSize", "textPosition", "radius", "Math", "min", "arcGenerator", "arc", "innerRadius", "outerRadius", "labelArcGenerator", "arcs", "myGeneratedColors", "pie1", "pie2", "pie3", "pie4", "pie5", "pie6", "pie7", "pie8", "pie9", "pie10", "pie11", "pie12", "color", "scaleOrdinal", "selectAll", "data", "enter", "datum", "sum", "for<PERSON>ach", "section", "toFixed", "centroid", "style", "legend", "domain", "_datum", "index", "length", "totalWidth", "max", "nodes", "node", "getBoundingClientRect", "width", "configureSvgSize", "useMaxWidth", "styles", "accDescr", "accTitle", "title"], "sourceRoot": ""}