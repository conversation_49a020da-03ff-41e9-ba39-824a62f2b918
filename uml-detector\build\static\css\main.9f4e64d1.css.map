{"version": 3, "file": "static/css/main.9f4e64d1.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCVA,qBACE,GAGE,YAAa,CAFb,SAAU,CACV,2BAEF,CACA,GAGE,gBAAiB,CAFjB,SAAU,CACV,uBAEF,CACF,CAEA,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAEA,iBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAGA,0BAEE,+BAA+C,CAD/C,0BAEF,CAEA,6BACE,oCAAoD,CACpD,qBACF,CAEA,2BACE,4BACF,CAEA,0BACE,2BACF,CAGA,kBAEE,kBAAmB,CAGnB,kBAAmB,CAJnB,mBAAoB,CAKpB,cAAe,CACf,eAAgB,CAJhB,OAAQ,CACR,eAIF,CAEA,wBACE,0BAAyC,CAEzC,0BAAyC,CADzC,aAEF,CAEA,yBACE,0BAAyC,CAEzC,0BAAyC,CADzC,aAEF,CAEA,yBACE,0BAAyC,CAEzC,0BAAyC,CADzC,aAEF,CAEA,6BACE,0BAAwC,CAExC,0BAAwC,CADxC,aAEF,CAGA,8BACE,0BAAyC,CACzC,aACF,CAEA,+BACE,0BAAyC,CACzC,aACF,CAEA,+BACE,0BAAyC,CACzC,aACF,CAEA,mCACE,0BAAwC,CACxC,aACF,CAGA,yBACE,yBACE,mCACF,CAEA,oBAEE,gCAAkC,CADlC,qBAAsB,CAEtB,iBACF,CAEA,8BACE,+BACF,CACF", "sources": ["index.css", "components/imageUp/HistoryAnalysisSection.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", "/* Animations pour HistoryAnalysisSection */\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n    max-height: 0;\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n    max-height: 200px;\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.7;\n  }\n}\n\n/* Styles pour les éléments interactifs */\n.history-match-item:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.history-action-button:hover {\n  background-color: rgba(59, 130, 246, 0.1) !important;\n  transform: scale(1.05);\n}\n\n.history-preview-container {\n  animation: slideDown 0.3s ease;\n}\n\n.history-similarity-badge {\n  animation: pulse 2s infinite;\n}\n\n/* Styles pour les indicateurs de sécurité */\n.access-indicator {\n  display: inline-flex;\n  align-items: center;\n  gap: 4px;\n  padding: 2px 6px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: 600;\n}\n\n.access-indicator.owner {\n  background-color: rgba(16, 185, 129, 0.1);\n  color: #10b981;\n  border: 1px solid rgba(16, 185, 129, 0.2);\n}\n\n.access-indicator.shared {\n  background-color: rgba(245, 158, 11, 0.1);\n  color: #f59e0b;\n  border: 1px solid rgba(245, 158, 11, 0.2);\n}\n\n.access-indicator.public {\n  background-color: rgba(59, 130, 246, 0.1);\n  color: #3b82f6;\n  border: 1px solid rgba(59, 130, 246, 0.2);\n}\n\n.access-indicator.restricted {\n  background-color: rgba(239, 68, 68, 0.1);\n  color: #ef4444;\n  border: 1px solid rgba(239, 68, 68, 0.2);\n}\n\n/* Mode sombre */\n.dark .access-indicator.owner {\n  background-color: rgba(16, 185, 129, 0.2);\n  color: #34d399;\n}\n\n.dark .access-indicator.shared {\n  background-color: rgba(245, 158, 11, 0.2);\n  color: #fbbf24;\n}\n\n.dark .access-indicator.public {\n  background-color: rgba(59, 130, 246, 0.2);\n  color: #60a5fa;\n}\n\n.dark .access-indicator.restricted {\n  background-color: rgba(239, 68, 68, 0.2);\n  color: #f87171;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .history-preview-content {\n    grid-template-columns: 1fr !important;\n  }\n  \n  .history-match-meta {\n    flex-direction: column;\n    align-items: flex-start !important;\n    gap: 4px !important;\n  }\n  \n  .history-related-classes-list {\n    flex-direction: column !important;\n  }\n}\n"], "names": [], "sourceRoot": ""}