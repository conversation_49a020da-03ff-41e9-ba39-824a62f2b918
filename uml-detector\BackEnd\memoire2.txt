NOM_CLASSE: Client
ATTRIBUTS:
uid : Integer
email : varchar
password : varchar
nom : varchar
prenom : varchar
telephone : Integer
adresse : varchar
MÉTHODES:
ajouterdemande()
modifierdemande()
Client()
~Client()


NOM_CLASSE: client
ATTRIBUTS:
nom
prenom
adresse
telephone
code postal
MÉTHODES:
Passe commande()
passe commande ()
paie commande (cmd)


NOM_CLASSE: Commande
ATTRIBUTS:
numBonCommande: int
dateCommande: Date
dateReglement: Date
moyenPaiement: String
totalCommande: float
paiementValide: boolean
etatCde: string
statutCde: String
MÉTHODES:
calculerTotal()
validerPaiement()
annulerCommande()


NOM_CLASSE: Produit
ATTRIBUTS:
idProd: int
nomProd: String
prixProd: float
stockProd: int
descriptionProd: String
MÉTHODES:
ajouterStock()
retirerStock()
modifierPrix()


NOM_CLASSE: Admin
ATTRIBUTS:
aid : Integer
name : varchar
email : varchar
password : varchar
MÉTHODES:
gererUtilisateurs()
gererProduits()
consulterStatistiques()
