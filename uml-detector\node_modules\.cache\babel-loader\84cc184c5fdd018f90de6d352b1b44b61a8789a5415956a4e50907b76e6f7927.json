{"ast": null, "code": "// HistoryAnalysisService.ts - Service pour analyser l'historique et proposer des imports de classes\n\nexport class HistoryAnalysisService {\n  /**\n   * Recherche dans l'historique les diagrammes contenant une classe du même nom\n   */\n  static findMatchingDiagrams(targetClassName, historyItems, currentUserId) {\n    const matches = [];\n    historyItems.forEach(item => {\n      const extractedClasses = this.extractClassesFromText(item.extractedText);\n      const matchingClasses = extractedClasses.filter(cls => cls.name.toLowerCase() === targetClassName.toLowerCase());\n      if (matchingClasses.length > 0) {\n        const similarity = this.calculateSimilarity(targetClassName, matchingClasses[0]);\n        matches.push({\n          historyItem: item,\n          matchingClasses,\n          similarity,\n          isShared: item.userId !== currentUserId,\n          hasAccess: this.checkAccess(item, currentUserId)\n        });\n      }\n    });\n    return matches;\n  }\n\n  /**\n   * Extrait les classes depuis le texte d'analyse\n   */\n  static extractClassesFromText(extractedText) {\n    const classes = [];\n    if (!extractedText) return classes;\n\n    // Diviser le texte par sections de classe\n    const classSections = extractedText.split(/class \\d+:/g).filter(section => section.trim());\n    classSections.forEach(section => {\n      var _section$split$;\n      const classNameMatch = section.match(/NOM_CLASSE:\\s*(.+)/);\n      if (!classNameMatch) return;\n      const className = classNameMatch[1].trim();\n\n      // Extraire les attributs\n      const attributesSection = ((_section$split$ = section.split('ATTRIBUTS:')[1]) === null || _section$split$ === void 0 ? void 0 : _section$split$.split('MÉTHODES:')[0]) || '';\n      const attributes = attributesSection.split('\\n').map(attr => attr.trim()).filter(attr => attr && !attr.includes('NOM_CLASSE:'));\n\n      // Extraire les méthodes\n      const methodsSection = section.split('MÉTHODES:')[1] || '';\n      const methods = methodsSection.split('\\n').map(method => method.trim()).filter(method => method && !method.includes('class ') && !method.includes('RELATIONS'));\n      classes.push({\n        name: className,\n        attributes,\n        methods\n      });\n    });\n    return classes;\n  }\n\n  /**\n   * Calcule la similarité entre deux classes\n   */\n  static calculateSimilarity(targetClassName, classData) {\n    // Similarité basée sur le nom (exact = 100%)\n    let similarity = targetClassName.toLowerCase() === classData.name.toLowerCase() ? 100 : 0;\n\n    // Bonus pour le nombre d'attributs et méthodes (plus il y en a, plus c'est intéressant)\n    const contentScore = Math.min((classData.attributes.length + classData.methods.length) * 5, 50);\n    similarity += contentScore;\n    return Math.min(similarity, 100);\n  }\n\n  /**\n   * Vérifie les droits d'accès à un diagramme\n   */\n  static checkAccess(item, currentUserId) {\n    // Pour l'instant, accès uniquement aux diagrammes de l'utilisateur\n    // TODO: Implémenter la logique de partage/collaboration\n    return item.userId === currentUserId;\n  }\n\n  /**\n   * Trie les résultats selon les critères spécifiés\n   */\n  static sortMatches(matches, sortOption) {\n    return [...matches].sort((a, b) => {\n      let comparison = 0;\n      switch (sortOption.key) {\n        case 'date':\n          comparison = a.historyItem.createdAt.getTime() - b.historyItem.createdAt.getTime();\n          break;\n        case 'similarity':\n          comparison = a.similarity - b.similarity;\n          break;\n        case 'name':\n          comparison = a.historyItem.title.localeCompare(b.historyItem.title);\n          break;\n      }\n      return sortOption.direction === 'desc' ? -comparison : comparison;\n    });\n  }\n\n  /**\n   * Fusionne les attributs et méthodes de plusieurs classes\n   */\n  static mergeClassData(currentClass, importedClasses, conflictResolution = 'merge') {\n    const mergedAttributes = [...currentClass.attributes];\n    const mergedMethods = [...currentClass.methods];\n    importedClasses.forEach(importedClass => {\n      // Fusionner les attributs\n      importedClass.attributes.forEach(attr => {\n        const exists = mergedAttributes.some(existing => this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr));\n        if (!exists) {\n          mergedAttributes.push(attr);\n        } else if (conflictResolution === 'replace') {\n          const index = mergedAttributes.findIndex(existing => this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr));\n          if (index !== -1) {\n            mergedAttributes[index] = attr;\n          }\n        }\n      });\n\n      // Fusionner les méthodes\n      importedClass.methods.forEach(method => {\n        const exists = mergedMethods.some(existing => this.normalizeMethodName(existing) === this.normalizeMethodName(method));\n        if (!exists) {\n          mergedMethods.push(method);\n        } else if (conflictResolution === 'replace') {\n          const index = mergedMethods.findIndex(existing => this.normalizeMethodName(existing) === this.normalizeMethodName(method));\n          if (index !== -1) {\n            mergedMethods[index] = method;\n          }\n        }\n      });\n    });\n    return {\n      name: currentClass.name,\n      attributes: mergedAttributes,\n      methods: mergedMethods\n    };\n  }\n\n  /**\n   * Normalise le nom d'un attribut pour la comparaison\n   */\n  static normalizeAttributeName(attribute) {\n    return attribute.toLowerCase().replace(/[^a-z0-9]/g, '');\n  }\n\n  /**\n   * Normalise le nom d'une méthode pour la comparaison\n   */\n  static normalizeMethodName(method) {\n    return method.toLowerCase().replace(/[^a-z0-9]/g, '');\n  }\n\n  /**\n   * Détecte les conflits potentiels lors de la fusion\n   */\n  static detectConflicts(currentClass, importedClasses) {\n    const conflictingAttributes = [];\n    const conflictingMethods = [];\n    importedClasses.forEach(importedClass => {\n      importedClass.attributes.forEach(attr => {\n        const exists = currentClass.attributes.some(existing => this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr));\n        if (exists && !conflictingAttributes.includes(attr)) {\n          conflictingAttributes.push(attr);\n        }\n      });\n      importedClass.methods.forEach(method => {\n        const exists = currentClass.methods.some(existing => this.normalizeMethodName(existing) === this.normalizeMethodName(method));\n        if (exists && !conflictingMethods.includes(method)) {\n          conflictingMethods.push(method);\n        }\n      });\n    });\n    return {\n      attributes: conflictingAttributes,\n      methods: conflictingMethods\n    };\n  }\n\n  /**\n   * Options de tri disponibles\n   */\n  static getSortOptions() {\n    return [{\n      key: 'similarity',\n      label: 'Similarité',\n      direction: 'desc'\n    }, {\n      key: 'date',\n      label: 'Date (récent)',\n      direction: 'desc'\n    }, {\n      key: 'date',\n      label: 'Date (ancien)',\n      direction: 'asc'\n    }, {\n      key: 'name',\n      label: 'Nom A-Z',\n      direction: 'asc'\n    }, {\n      key: 'name',\n      label: 'Nom Z-A',\n      direction: 'desc'\n    }];\n  }\n}", "map": {"version": 3, "names": ["HistoryAnalysisService", "findMatchingDiagrams", "targetClassName", "historyItems", "currentUserId", "matches", "for<PERSON>ach", "item", "extractedClasses", "extractClassesFromText", "extractedText", "matchingClasses", "filter", "cls", "name", "toLowerCase", "length", "similarity", "calculateSimilarity", "push", "historyItem", "isShared", "userId", "hasAccess", "checkAccess", "classes", "classSections", "split", "section", "trim", "_section$split$", "classNameMatch", "match", "className", "attributesSection", "attributes", "map", "attr", "includes", "methodsSection", "methods", "method", "classData", "contentScore", "Math", "min", "sortMatches", "sortOption", "sort", "a", "b", "comparison", "key", "createdAt", "getTime", "title", "localeCompare", "direction", "mergeClassData", "currentClass", "importedClasses", "conflictResolution", "mergedAttributes", "mergedMethods", "importedClass", "exists", "some", "existing", "normalizeAttributeName", "index", "findIndex", "normalizeMethodName", "attribute", "replace", "detectConflicts", "conflictingAttributes", "conflictingMethods", "getSortOptions", "label"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/services/HistoryAnalysisService.ts"], "sourcesContent": ["// HistoryAnalysisService.ts - Service pour analyser l'historique et proposer des imports de classes\nimport { HistoryItem } from '../components/types/HistoryTypes';\n\nexport interface ClassData {\n  name: string;\n  attributes: string[];\n  methods: string[];\n}\n\nexport interface HistoryMatch {\n  historyItem: HistoryItem;\n  matchingClasses: ClassData[];\n  similarity: number;\n  isShared: boolean;\n  hasAccess: boolean;\n}\n\nexport interface SortOption {\n  key: 'date' | 'similarity' | 'name';\n  label: string;\n  direction: 'asc' | 'desc';\n}\n\nexport class HistoryAnalysisService {\n  \n  /**\n   * Recherche dans l'historique les diagrammes contenant une classe du même nom\n   */\n  static findMatchingDiagrams(\n    targetClassName: string, \n    historyItems: HistoryItem[], \n    currentUserId: string\n  ): HistoryMatch[] {\n    const matches: HistoryMatch[] = [];\n    \n    historyItems.forEach(item => {\n      const extractedClasses = this.extractClassesFromText(item.extractedText);\n      const matchingClasses = extractedClasses.filter(cls => \n        cls.name.toLowerCase() === targetClassName.toLowerCase()\n      );\n      \n      if (matchingClasses.length > 0) {\n        const similarity = this.calculateSimilarity(targetClassName, matchingClasses[0]);\n        \n        matches.push({\n          historyItem: item,\n          matchingClasses,\n          similarity,\n          isShared: item.userId !== currentUserId,\n          hasAccess: this.checkAccess(item, currentUserId)\n        });\n      }\n    });\n    \n    return matches;\n  }\n  \n  /**\n   * Extrait les classes depuis le texte d'analyse\n   */\n  static extractClassesFromText(extractedText: string): ClassData[] {\n    const classes: ClassData[] = [];\n    \n    if (!extractedText) return classes;\n    \n    // Diviser le texte par sections de classe\n    const classSections = extractedText.split(/class \\d+:/g).filter(section => section.trim());\n    \n    classSections.forEach(section => {\n      const classNameMatch = section.match(/NOM_CLASSE:\\s*(.+)/);\n      if (!classNameMatch) return;\n      \n      const className = classNameMatch[1].trim();\n      \n      // Extraire les attributs\n      const attributesSection = section.split('ATTRIBUTS:')[1]?.split('MÉTHODES:')[0] || '';\n      const attributes = attributesSection\n        .split('\\n')\n        .map(attr => attr.trim())\n        .filter(attr => attr && !attr.includes('NOM_CLASSE:'));\n      \n      // Extraire les méthodes\n      const methodsSection = section.split('MÉTHODES:')[1] || '';\n      const methods = methodsSection\n        .split('\\n')\n        .map(method => method.trim())\n        .filter(method => method && !method.includes('class ') && !method.includes('RELATIONS'));\n      \n      classes.push({\n        name: className,\n        attributes,\n        methods\n      });\n    });\n    \n    return classes;\n  }\n  \n  /**\n   * Calcule la similarité entre deux classes\n   */\n  static calculateSimilarity(targetClassName: string, classData: ClassData): number {\n    // Similarité basée sur le nom (exact = 100%)\n    let similarity = targetClassName.toLowerCase() === classData.name.toLowerCase() ? 100 : 0;\n    \n    // Bonus pour le nombre d'attributs et méthodes (plus il y en a, plus c'est intéressant)\n    const contentScore = Math.min((classData.attributes.length + classData.methods.length) * 5, 50);\n    similarity += contentScore;\n    \n    return Math.min(similarity, 100);\n  }\n  \n  /**\n   * Vérifie les droits d'accès à un diagramme\n   */\n  static checkAccess(item: HistoryItem, currentUserId: string): boolean {\n    // Pour l'instant, accès uniquement aux diagrammes de l'utilisateur\n    // TODO: Implémenter la logique de partage/collaboration\n    return item.userId === currentUserId;\n  }\n  \n  /**\n   * Trie les résultats selon les critères spécifiés\n   */\n  static sortMatches(matches: HistoryMatch[], sortOption: SortOption): HistoryMatch[] {\n    return [...matches].sort((a, b) => {\n      let comparison = 0;\n      \n      switch (sortOption.key) {\n        case 'date':\n          comparison = a.historyItem.createdAt.getTime() - b.historyItem.createdAt.getTime();\n          break;\n        case 'similarity':\n          comparison = a.similarity - b.similarity;\n          break;\n        case 'name':\n          comparison = a.historyItem.title.localeCompare(b.historyItem.title);\n          break;\n      }\n      \n      return sortOption.direction === 'desc' ? -comparison : comparison;\n    });\n  }\n  \n  /**\n   * Fusionne les attributs et méthodes de plusieurs classes\n   */\n  static mergeClassData(\n    currentClass: ClassData, \n    importedClasses: ClassData[], \n    conflictResolution: 'replace' | 'merge' | 'skip' = 'merge'\n  ): ClassData {\n    const mergedAttributes = [...currentClass.attributes];\n    const mergedMethods = [...currentClass.methods];\n    \n    importedClasses.forEach(importedClass => {\n      // Fusionner les attributs\n      importedClass.attributes.forEach(attr => {\n        const exists = mergedAttributes.some(existing => \n          this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)\n        );\n        \n        if (!exists) {\n          mergedAttributes.push(attr);\n        } else if (conflictResolution === 'replace') {\n          const index = mergedAttributes.findIndex(existing => \n            this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)\n          );\n          if (index !== -1) {\n            mergedAttributes[index] = attr;\n          }\n        }\n      });\n      \n      // Fusionner les méthodes\n      importedClass.methods.forEach(method => {\n        const exists = mergedMethods.some(existing => \n          this.normalizeMethodName(existing) === this.normalizeMethodName(method)\n        );\n        \n        if (!exists) {\n          mergedMethods.push(method);\n        } else if (conflictResolution === 'replace') {\n          const index = mergedMethods.findIndex(existing => \n            this.normalizeMethodName(existing) === this.normalizeMethodName(method)\n          );\n          if (index !== -1) {\n            mergedMethods[index] = method;\n          }\n        }\n      });\n    });\n    \n    return {\n      name: currentClass.name,\n      attributes: mergedAttributes,\n      methods: mergedMethods\n    };\n  }\n  \n  /**\n   * Normalise le nom d'un attribut pour la comparaison\n   */\n  static normalizeAttributeName(attribute: string): string {\n    return attribute.toLowerCase().replace(/[^a-z0-9]/g, '');\n  }\n  \n  /**\n   * Normalise le nom d'une méthode pour la comparaison\n   */\n  static normalizeMethodName(method: string): string {\n    return method.toLowerCase().replace(/[^a-z0-9]/g, '');\n  }\n  \n  /**\n   * Détecte les conflits potentiels lors de la fusion\n   */\n  static detectConflicts(\n    currentClass: ClassData, \n    importedClasses: ClassData[]\n  ): { attributes: string[], methods: string[] } {\n    const conflictingAttributes: string[] = [];\n    const conflictingMethods: string[] = [];\n    \n    importedClasses.forEach(importedClass => {\n      importedClass.attributes.forEach(attr => {\n        const exists = currentClass.attributes.some(existing => \n          this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)\n        );\n        if (exists && !conflictingAttributes.includes(attr)) {\n          conflictingAttributes.push(attr);\n        }\n      });\n      \n      importedClass.methods.forEach(method => {\n        const exists = currentClass.methods.some(existing => \n          this.normalizeMethodName(existing) === this.normalizeMethodName(method)\n        );\n        if (exists && !conflictingMethods.includes(method)) {\n          conflictingMethods.push(method);\n        }\n      });\n    });\n    \n    return { attributes: conflictingAttributes, methods: conflictingMethods };\n  }\n  \n  /**\n   * Options de tri disponibles\n   */\n  static getSortOptions(): SortOption[] {\n    return [\n      { key: 'similarity', label: 'Similarité', direction: 'desc' },\n      { key: 'date', label: 'Date (récent)', direction: 'desc' },\n      { key: 'date', label: 'Date (ancien)', direction: 'asc' },\n      { key: 'name', label: 'Nom A-Z', direction: 'asc' },\n      { key: 'name', label: 'Nom Z-A', direction: 'desc' }\n    ];\n  }\n}\n"], "mappings": "AAAA;;AAuBA,OAAO,MAAMA,sBAAsB,CAAC;EAElC;AACF;AACA;EACE,OAAOC,oBAAoBA,CACzBC,eAAuB,EACvBC,YAA2B,EAC3BC,aAAqB,EACL;IAChB,MAAMC,OAAuB,GAAG,EAAE;IAElCF,YAAY,CAACG,OAAO,CAACC,IAAI,IAAI;MAC3B,MAAMC,gBAAgB,GAAG,IAAI,CAACC,sBAAsB,CAACF,IAAI,CAACG,aAAa,CAAC;MACxE,MAAMC,eAAe,GAAGH,gBAAgB,CAACI,MAAM,CAACC,GAAG,IACjDA,GAAG,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAKb,eAAe,CAACa,WAAW,CAAC,CACzD,CAAC;MAED,IAAIJ,eAAe,CAACK,MAAM,GAAG,CAAC,EAAE;QAC9B,MAAMC,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAAChB,eAAe,EAAES,eAAe,CAAC,CAAC,CAAC,CAAC;QAEhFN,OAAO,CAACc,IAAI,CAAC;UACXC,WAAW,EAAEb,IAAI;UACjBI,eAAe;UACfM,UAAU;UACVI,QAAQ,EAAEd,IAAI,CAACe,MAAM,KAAKlB,aAAa;UACvCmB,SAAS,EAAE,IAAI,CAACC,WAAW,CAACjB,IAAI,EAAEH,aAAa;QACjD,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOC,OAAO;EAChB;;EAEA;AACF;AACA;EACE,OAAOI,sBAAsBA,CAACC,aAAqB,EAAe;IAChE,MAAMe,OAAoB,GAAG,EAAE;IAE/B,IAAI,CAACf,aAAa,EAAE,OAAOe,OAAO;;IAElC;IACA,MAAMC,aAAa,GAAGhB,aAAa,CAACiB,KAAK,CAAC,aAAa,CAAC,CAACf,MAAM,CAACgB,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;IAE1FH,aAAa,CAACpB,OAAO,CAACsB,OAAO,IAAI;MAAA,IAAAE,eAAA;MAC/B,MAAMC,cAAc,GAAGH,OAAO,CAACI,KAAK,CAAC,oBAAoB,CAAC;MAC1D,IAAI,CAACD,cAAc,EAAE;MAErB,MAAME,SAAS,GAAGF,cAAc,CAAC,CAAC,CAAC,CAACF,IAAI,CAAC,CAAC;;MAE1C;MACA,MAAMK,iBAAiB,GAAG,EAAAJ,eAAA,GAAAF,OAAO,CAACD,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAAG,eAAA,uBAA9BA,eAAA,CAAgCH,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAI,EAAE;MACrF,MAAMQ,UAAU,GAAGD,iBAAiB,CACjCP,KAAK,CAAC,IAAI,CAAC,CACXS,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,CACxBjB,MAAM,CAACyB,IAAI,IAAIA,IAAI,IAAI,CAACA,IAAI,CAACC,QAAQ,CAAC,aAAa,CAAC,CAAC;;MAExD;MACA,MAAMC,cAAc,GAAGX,OAAO,CAACD,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;MAC1D,MAAMa,OAAO,GAAGD,cAAc,CAC3BZ,KAAK,CAAC,IAAI,CAAC,CACXS,GAAG,CAACK,MAAM,IAAIA,MAAM,CAACZ,IAAI,CAAC,CAAC,CAAC,CAC5BjB,MAAM,CAAC6B,MAAM,IAAIA,MAAM,IAAI,CAACA,MAAM,CAACH,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAACG,MAAM,CAACH,QAAQ,CAAC,WAAW,CAAC,CAAC;MAE1Fb,OAAO,CAACN,IAAI,CAAC;QACXL,IAAI,EAAEmB,SAAS;QACfE,UAAU;QACVK;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOf,OAAO;EAChB;;EAEA;AACF;AACA;EACE,OAAOP,mBAAmBA,CAAChB,eAAuB,EAAEwC,SAAoB,EAAU;IAChF;IACA,IAAIzB,UAAU,GAAGf,eAAe,CAACa,WAAW,CAAC,CAAC,KAAK2B,SAAS,CAAC5B,IAAI,CAACC,WAAW,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;;IAEzF;IACA,MAAM4B,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACH,SAAS,CAACP,UAAU,CAACnB,MAAM,GAAG0B,SAAS,CAACF,OAAO,CAACxB,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC;IAC/FC,UAAU,IAAI0B,YAAY;IAE1B,OAAOC,IAAI,CAACC,GAAG,CAAC5B,UAAU,EAAE,GAAG,CAAC;EAClC;;EAEA;AACF;AACA;EACE,OAAOO,WAAWA,CAACjB,IAAiB,EAAEH,aAAqB,EAAW;IACpE;IACA;IACA,OAAOG,IAAI,CAACe,MAAM,KAAKlB,aAAa;EACtC;;EAEA;AACF;AACA;EACE,OAAO0C,WAAWA,CAACzC,OAAuB,EAAE0C,UAAsB,EAAkB;IAClF,OAAO,CAAC,GAAG1C,OAAO,CAAC,CAAC2C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACjC,IAAIC,UAAU,GAAG,CAAC;MAElB,QAAQJ,UAAU,CAACK,GAAG;QACpB,KAAK,MAAM;UACTD,UAAU,GAAGF,CAAC,CAAC7B,WAAW,CAACiC,SAAS,CAACC,OAAO,CAAC,CAAC,GAAGJ,CAAC,CAAC9B,WAAW,CAACiC,SAAS,CAACC,OAAO,CAAC,CAAC;UAClF;QACF,KAAK,YAAY;UACfH,UAAU,GAAGF,CAAC,CAAChC,UAAU,GAAGiC,CAAC,CAACjC,UAAU;UACxC;QACF,KAAK,MAAM;UACTkC,UAAU,GAAGF,CAAC,CAAC7B,WAAW,CAACmC,KAAK,CAACC,aAAa,CAACN,CAAC,CAAC9B,WAAW,CAACmC,KAAK,CAAC;UACnE;MACJ;MAEA,OAAOR,UAAU,CAACU,SAAS,KAAK,MAAM,GAAG,CAACN,UAAU,GAAGA,UAAU;IACnE,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,OAAOO,cAAcA,CACnBC,YAAuB,EACvBC,eAA4B,EAC5BC,kBAAgD,GAAG,OAAO,EAC/C;IACX,MAAMC,gBAAgB,GAAG,CAAC,GAAGH,YAAY,CAACxB,UAAU,CAAC;IACrD,MAAM4B,aAAa,GAAG,CAAC,GAAGJ,YAAY,CAACnB,OAAO,CAAC;IAE/CoB,eAAe,CAACtD,OAAO,CAAC0D,aAAa,IAAI;MACvC;MACAA,aAAa,CAAC7B,UAAU,CAAC7B,OAAO,CAAC+B,IAAI,IAAI;QACvC,MAAM4B,MAAM,GAAGH,gBAAgB,CAACI,IAAI,CAACC,QAAQ,IAC3C,IAAI,CAACC,sBAAsB,CAACD,QAAQ,CAAC,KAAK,IAAI,CAACC,sBAAsB,CAAC/B,IAAI,CAC5E,CAAC;QAED,IAAI,CAAC4B,MAAM,EAAE;UACXH,gBAAgB,CAAC3C,IAAI,CAACkB,IAAI,CAAC;QAC7B,CAAC,MAAM,IAAIwB,kBAAkB,KAAK,SAAS,EAAE;UAC3C,MAAMQ,KAAK,GAAGP,gBAAgB,CAACQ,SAAS,CAACH,QAAQ,IAC/C,IAAI,CAACC,sBAAsB,CAACD,QAAQ,CAAC,KAAK,IAAI,CAACC,sBAAsB,CAAC/B,IAAI,CAC5E,CAAC;UACD,IAAIgC,KAAK,KAAK,CAAC,CAAC,EAAE;YAChBP,gBAAgB,CAACO,KAAK,CAAC,GAAGhC,IAAI;UAChC;QACF;MACF,CAAC,CAAC;;MAEF;MACA2B,aAAa,CAACxB,OAAO,CAAClC,OAAO,CAACmC,MAAM,IAAI;QACtC,MAAMwB,MAAM,GAAGF,aAAa,CAACG,IAAI,CAACC,QAAQ,IACxC,IAAI,CAACI,mBAAmB,CAACJ,QAAQ,CAAC,KAAK,IAAI,CAACI,mBAAmB,CAAC9B,MAAM,CACxE,CAAC;QAED,IAAI,CAACwB,MAAM,EAAE;UACXF,aAAa,CAAC5C,IAAI,CAACsB,MAAM,CAAC;QAC5B,CAAC,MAAM,IAAIoB,kBAAkB,KAAK,SAAS,EAAE;UAC3C,MAAMQ,KAAK,GAAGN,aAAa,CAACO,SAAS,CAACH,QAAQ,IAC5C,IAAI,CAACI,mBAAmB,CAACJ,QAAQ,CAAC,KAAK,IAAI,CAACI,mBAAmB,CAAC9B,MAAM,CACxE,CAAC;UACD,IAAI4B,KAAK,KAAK,CAAC,CAAC,EAAE;YAChBN,aAAa,CAACM,KAAK,CAAC,GAAG5B,MAAM;UAC/B;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MACL3B,IAAI,EAAE6C,YAAY,CAAC7C,IAAI;MACvBqB,UAAU,EAAE2B,gBAAgB;MAC5BtB,OAAO,EAAEuB;IACX,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOK,sBAAsBA,CAACI,SAAiB,EAAU;IACvD,OAAOA,SAAS,CAACzD,WAAW,CAAC,CAAC,CAAC0D,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;EAC1D;;EAEA;AACF;AACA;EACE,OAAOF,mBAAmBA,CAAC9B,MAAc,EAAU;IACjD,OAAOA,MAAM,CAAC1B,WAAW,CAAC,CAAC,CAAC0D,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;EACvD;;EAEA;AACF;AACA;EACE,OAAOC,eAAeA,CACpBf,YAAuB,EACvBC,eAA4B,EACiB;IAC7C,MAAMe,qBAA+B,GAAG,EAAE;IAC1C,MAAMC,kBAA4B,GAAG,EAAE;IAEvChB,eAAe,CAACtD,OAAO,CAAC0D,aAAa,IAAI;MACvCA,aAAa,CAAC7B,UAAU,CAAC7B,OAAO,CAAC+B,IAAI,IAAI;QACvC,MAAM4B,MAAM,GAAGN,YAAY,CAACxB,UAAU,CAAC+B,IAAI,CAACC,QAAQ,IAClD,IAAI,CAACC,sBAAsB,CAACD,QAAQ,CAAC,KAAK,IAAI,CAACC,sBAAsB,CAAC/B,IAAI,CAC5E,CAAC;QACD,IAAI4B,MAAM,IAAI,CAACU,qBAAqB,CAACrC,QAAQ,CAACD,IAAI,CAAC,EAAE;UACnDsC,qBAAqB,CAACxD,IAAI,CAACkB,IAAI,CAAC;QAClC;MACF,CAAC,CAAC;MAEF2B,aAAa,CAACxB,OAAO,CAAClC,OAAO,CAACmC,MAAM,IAAI;QACtC,MAAMwB,MAAM,GAAGN,YAAY,CAACnB,OAAO,CAAC0B,IAAI,CAACC,QAAQ,IAC/C,IAAI,CAACI,mBAAmB,CAACJ,QAAQ,CAAC,KAAK,IAAI,CAACI,mBAAmB,CAAC9B,MAAM,CACxE,CAAC;QACD,IAAIwB,MAAM,IAAI,CAACW,kBAAkB,CAACtC,QAAQ,CAACG,MAAM,CAAC,EAAE;UAClDmC,kBAAkB,CAACzD,IAAI,CAACsB,MAAM,CAAC;QACjC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MAAEN,UAAU,EAAEwC,qBAAqB;MAAEnC,OAAO,EAAEoC;IAAmB,CAAC;EAC3E;;EAEA;AACF;AACA;EACE,OAAOC,cAAcA,CAAA,EAAiB;IACpC,OAAO,CACL;MAAEzB,GAAG,EAAE,YAAY;MAAE0B,KAAK,EAAE,YAAY;MAAErB,SAAS,EAAE;IAAO,CAAC,EAC7D;MAAEL,GAAG,EAAE,MAAM;MAAE0B,KAAK,EAAE,eAAe;MAAErB,SAAS,EAAE;IAAO,CAAC,EAC1D;MAAEL,GAAG,EAAE,MAAM;MAAE0B,KAAK,EAAE,eAAe;MAAErB,SAAS,EAAE;IAAM,CAAC,EACzD;MAAEL,GAAG,EAAE,MAAM;MAAE0B,KAAK,EAAE,SAAS;MAAErB,SAAS,EAAE;IAAM,CAAC,EACnD;MAAEL,GAAG,EAAE,MAAM;MAAE0B,KAAK,EAAE,SAAS;MAAErB,SAAS,EAAE;IAAO,CAAC,CACrD;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}