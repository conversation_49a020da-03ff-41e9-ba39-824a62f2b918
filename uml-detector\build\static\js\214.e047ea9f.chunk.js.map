{"version": 3, "file": "static/js/214.e047ea9f.chunk.js", "mappings": "2GAKA,SAASA,EAAiBC,EAAKC,GACzBD,EAAIE,UACND,EAAGE,oBAAoBH,EAAIE,UAEzBF,EAAII,UACNH,EAAGI,cAAcL,EAAII,UAEnBJ,EAAIM,OACNL,EAAGM,kBAAkBP,EAAIM,MAE7B,C,kBACAE,E,QAAAA,IAAOT,EAAkB,mB,6FCSrBU,EAAoB,CACtBC,OAAQ,IAENC,EAAOC,gBAAgBH,GACvBI,EAAwBC,EAAAA,GAAsBJ,OAC9CK,GAA6BP,EAAAA,EAAAA,KAAO,KACtC,MAAMQ,GAASC,EAAAA,EAAAA,IAAc,IACxBJ,MACAK,EAAAA,EAAAA,MAAYR,SAKjB,OAHIM,EAAOG,WACTH,EAAOI,UAAY,IAEdJ,CAAM,GACZ,aACCK,GAA4Bb,EAAAA,EAAAA,KAAO,IAAMG,EAAKD,QAAQ,aAUtDT,EAAK,CACPqB,UAV6Bd,EAAAA,EAAAA,KAAQe,IACjCA,EAAKC,OAAS,GAChBb,EAAKD,OAAOe,KAAKF,EACnB,GACC,YAODF,YACAH,UAAWH,EACXW,OAR2BlB,EAAAA,EAAAA,KAAO,MAClCkB,EAAAA,EAAAA,MACAf,EAAOC,gBAAgBH,EAAkB,GACxC,SAMDJ,YAAW,KACXsB,YAAW,KACXpB,gBAAe,KACfqB,gBAAe,KACfC,kBAAiB,KACjB1B,kBAAiBA,EAAAA,IAMf2B,GAA2BtB,EAAAA,EAAAA,KAAQR,KACrCD,EAAAA,EAAAA,GAAiBC,EAAKC,GACtB,IAAI8B,GAAY,EACZR,EAAO,GACPS,EAAM,EACV,MAAM,WAAEC,GAAehC,EAAGiB,YAC1B,IAAK,IAAI,MAAEgB,EAAK,IAAEC,EAAG,MAAEC,KAAWpC,EAAIqC,OAAQ,CAC5C,GAAIF,GAAOA,EAAMD,EACf,MAAM,IAAII,MAAM,gBAAgBJ,OAAWC,iDAE7C,GAAID,IAAUH,EAAW,EACvB,MAAM,IAAIO,MACR,gBAAgBJ,OAAWC,GAAOD,6CAAiDH,EAAW,MAKlG,IAFAA,EAAWI,GAAOD,EAClBK,EAAAA,GAAIC,MAAM,gBAAgBN,OAAWH,gBAAuBK,KACrDb,EAAKC,QAAUS,EAAa,GAAKhC,EAAGoB,YAAYG,OAlBvC,KAkB+D,CAC7E,MAAOiB,EAAOC,GAAaC,EAAoB,CAAET,QAAOC,MAAKC,SAASJ,EAAKC,GAO3E,GANAV,EAAKE,KAAKgB,GACNA,EAAMN,IAAM,IAAMH,EAAMC,IAC1BhC,EAAGqB,SAASC,GACZA,EAAO,GACPS,MAEGU,EACH,QAECR,QAAOC,MAAKC,SAAUM,EAC3B,CACF,CACAzC,EAAGqB,SAASC,EAAK,GAChB,YACCoB,GAAsCnC,EAAAA,EAAAA,KAAO,CAACiC,EAAOT,EAAKC,KAI5D,QAHkB,IAAdQ,EAAMN,MACRM,EAAMN,IAAMM,EAAMP,OAEhBO,EAAMP,MAAQO,EAAMN,IACtB,MAAM,IAAIG,MAAM,eAAeG,EAAMP,mCAAmCO,EAAMN,QAEhF,OAAIM,EAAMN,IAAM,GAAKH,EAAMC,EAClB,CAACQ,OAAO,GAEV,CACL,CACEP,MAAOO,EAAMP,MACbC,IAAKH,EAAMC,EAAa,EACxBG,MAAOK,EAAML,OAEf,CACEF,MAAOF,EAAMC,EACbE,IAAKM,EAAMN,IACXC,MAAOK,EAAML,OAEhB,GACA,uBACCQ,EAAS,CACXC,OAAuBrC,EAAAA,EAAAA,KAAOsC,UAC5B,MAAM9C,QAAY6C,EAAAA,EAAAA,IAAM,SAAUE,GAClCR,EAAAA,GAAIC,MAAMxC,GACV8B,EAAS9B,EAAI,GACZ,UAIDgD,GAAuBxC,EAAAA,EAAAA,KAAO,CAACyC,EAAOC,EAAIC,EAAUC,KACtD,MAAMC,EAAMD,EAASnD,GACfe,EAASqC,EAAInC,aACb,UAAEoC,EAAS,SAAElC,EAAQ,SAAEmC,EAAQ,WAAEtB,GAAejB,EAChDwC,EAAQH,EAAIhC,YACZf,EAAQ+C,EAAIzB,kBACZ6B,EAAiBH,EAAYlC,EAC7BsC,EAAYD,GAAkBD,EAAMhC,OAAS,IAAMlB,EAAQ,EAAIgD,GAC/DK,EAAWJ,EAAWtB,EAAa,EACnC2B,GAAMC,EAAAA,EAAAA,GAAiBX,GAC7BU,EAAIE,KAAK,UAAW,OAAOH,KAAYD,MACvCK,EAAAA,EAAAA,IAAiBH,EAAKF,EAAWC,EAAU3C,EAAOgD,aAClD,IAAK,MAAOzC,EAAMb,KAAW8C,EAAMS,UACjCC,EAASN,EAAKlD,EAAQa,EAAMP,GAE9B4C,EAAIO,OAAO,QAAQC,KAAK9D,GAAOwD,KAAK,IAAKH,EAAW,GAAGG,KAAK,IAAKJ,EAAYD,EAAiB,GAAGK,KAAK,oBAAqB,UAAUA,KAAK,cAAe,UAAUA,KAAK,QAAS,cAAc,GAC9L,QACCI,GAA2B1D,EAAAA,EAAAA,KAAO,CAACoD,EAAKrC,EAAM8C,EAASC,KAAwE,IAAtE,UAAEhB,EAAS,SAAEiB,EAAQ,SAAEnD,EAAQ,SAAEmC,EAAQ,WAAEtB,EAAU,SAAEd,GAAUmD,EAC5H,MAAME,EAAQZ,EAAIO,OAAO,KACnBM,EAAQJ,GAAaf,EAAYlC,GAAYA,EACnD,IAAK,MAAMqB,KAASlB,EAAM,CACxB,MAAMmD,EAASjC,EAAMP,MAAQD,EAAasB,EAAW,EAC/CoB,GAASlC,EAAMN,IAAMM,EAAMP,MAAQ,GAAKqB,EAAWgB,EAGzD,GAFAC,EAAML,OAAO,QAAQL,KAAK,IAAKY,GAAQZ,KAAK,IAAKW,GAAOX,KAAK,QAASa,GAAOb,KAAK,SAAUR,GAAWQ,KAAK,QAAS,eACrHU,EAAML,OAAO,QAAQL,KAAK,IAAKY,EAASC,EAAQ,GAAGb,KAAK,IAAKW,EAAQnB,EAAY,GAAGQ,KAAK,QAAS,eAAeA,KAAK,oBAAqB,UAAUA,KAAK,cAAe,UAAUM,KAAK3B,EAAML,QACzLjB,EACH,SAEF,MAAMyD,EAAgBnC,EAAMN,MAAQM,EAAMP,MACpC2C,EAAaJ,EAAQ,EAC3BD,EAAML,OAAO,QAAQL,KAAK,IAAKY,GAAUE,EAAgBD,EAAQ,EAAI,IAAIb,KAAK,IAAKe,GAAYf,KAAK,QAAS,oBAAoBA,KAAK,oBAAqB,QAAQA,KAAK,cAAec,EAAgB,SAAW,SAASR,KAAK3B,EAAMP,OACjO0C,GACHJ,EAAML,OAAO,QAAQL,KAAK,IAAKY,EAASC,GAAOb,KAAK,IAAKe,GAAYf,KAAK,QAAS,kBAAkBA,KAAK,oBAAqB,QAAQA,KAAK,cAAe,OAAOM,KAAK3B,EAAMN,IAEjL,IACC,YAIC2C,EAA4B,CAC9BC,aAAc,OACdC,eAAgB,QAChBC,aAAc,QACdC,WAAY,QACZC,cAAe,OACfC,WAAY,QACZC,cAAe,OACfC,iBAAkB,QAClBC,iBAAkB,IAClBC,eAAgB,WA+BdC,EAAU,CACZ7C,SACA3C,KACAyF,SA/Ca,CAAE1C,QAgDf2C,QAjC2BnF,EAAAA,EAAAA,KAAO,WAAqB,IAApB,OAAEE,GAAQkF,UAAApE,OAAA,QAAAqE,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EACjD,MAAME,GAAU7E,EAAAA,EAAAA,IAAc6D,EAA2BpE,GACzD,MAAO,qCAEMoF,EAAQf,wDAGbe,EAAQd,wDAGRc,EAAQb,mDAGRa,EAAQZ,+BACHY,EAAQX,oDAGbW,EAAQV,+BACHU,EAAQT,sDAGXS,EAAQR,wCACFQ,EAAQP,gCAChBO,EAAQN,0BAGlB,GAAG,U", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-4BMEZGHF.mjs", "../../node_modules/mermaid/dist/chunks/mermaid.core/diagram-VNBRO52H.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/common/populateCommonDb.ts\nfunction populateCommonDb(ast, db) {\n  if (ast.accDescr) {\n    db.setAccDescription?.(ast.accDescr);\n  }\n  if (ast.accTitle) {\n    db.setAccTitle?.(ast.accTitle);\n  }\n  if (ast.title) {\n    db.setDiagramTitle?.(ast.title);\n  }\n}\n__name(populateCommonDb, \"populateCommonDb\");\n\nexport {\n  populateCommonDb\n};\n", "import {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/packet/db.ts\nvar defaultPacketData = {\n  packet: []\n};\nvar data = structuredClone(defaultPacketData);\nvar DEFAULT_PACKET_CONFIG = defaultConfig_default.packet;\nvar getConfig2 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_PACKET_CONFIG,\n    ...getConfig().packet\n  });\n  if (config.showBits) {\n    config.paddingY += 10;\n  }\n  return config;\n}, \"getConfig\");\nvar getPacket = /* @__PURE__ */ __name(() => data.packet, \"getPacket\");\nvar pushWord = /* @__PURE__ */ __name((word) => {\n  if (word.length > 0) {\n    data.packet.push(word);\n  }\n}, \"pushWord\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  clear();\n  data = structuredClone(defaultPacketData);\n}, \"clear\");\nvar db = {\n  pushWord,\n  getPacket,\n  getConfig: getConfig2,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription\n};\n\n// src/diagrams/packet/parser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar maxPacketSize = 1e4;\nvar populate = /* @__PURE__ */ __name((ast) => {\n  populateCommonDb(ast, db);\n  let lastByte = -1;\n  let word = [];\n  let row = 1;\n  const { bitsPerRow } = db.getConfig();\n  for (let { start, end, label } of ast.blocks) {\n    if (end && end < start) {\n      throw new Error(`Packet block ${start} - ${end} is invalid. End must be greater than start.`);\n    }\n    if (start !== lastByte + 1) {\n      throw new Error(\n        `Packet block ${start} - ${end ?? start} is not contiguous. It should start from ${lastByte + 1}.`\n      );\n    }\n    lastByte = end ?? start;\n    log.debug(`Packet block ${start} - ${lastByte} with label ${label}`);\n    while (word.length <= bitsPerRow + 1 && db.getPacket().length < maxPacketSize) {\n      const [block, nextBlock] = getNextFittingBlock({ start, end, label }, row, bitsPerRow);\n      word.push(block);\n      if (block.end + 1 === row * bitsPerRow) {\n        db.pushWord(word);\n        word = [];\n        row++;\n      }\n      if (!nextBlock) {\n        break;\n      }\n      ({ start, end, label } = nextBlock);\n    }\n  }\n  db.pushWord(word);\n}, \"populate\");\nvar getNextFittingBlock = /* @__PURE__ */ __name((block, row, bitsPerRow) => {\n  if (block.end === void 0) {\n    block.end = block.start;\n  }\n  if (block.start > block.end) {\n    throw new Error(`Block start ${block.start} is greater than block end ${block.end}.`);\n  }\n  if (block.end + 1 <= row * bitsPerRow) {\n    return [block, void 0];\n  }\n  return [\n    {\n      start: block.start,\n      end: row * bitsPerRow - 1,\n      label: block.label\n    },\n    {\n      start: row * bitsPerRow,\n      end: block.end,\n      label: block.label\n    }\n  ];\n}, \"getNextFittingBlock\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"packet\", input);\n    log.debug(ast);\n    populate(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/packet/renderer.ts\nvar draw = /* @__PURE__ */ __name((_text, id, _version, diagram2) => {\n  const db2 = diagram2.db;\n  const config = db2.getConfig();\n  const { rowHeight, paddingY, bitWidth, bitsPerRow } = config;\n  const words = db2.getPacket();\n  const title = db2.getDiagramTitle();\n  const totalRowHeight = rowHeight + paddingY;\n  const svgHeight = totalRowHeight * (words.length + 1) - (title ? 0 : rowHeight);\n  const svgWidth = bitWidth * bitsPerRow + 2;\n  const svg = selectSvgElement(id);\n  svg.attr(\"viewbox\", `0 0 ${svgWidth} ${svgHeight}`);\n  configureSvgSize(svg, svgHeight, svgWidth, config.useMaxWidth);\n  for (const [word, packet] of words.entries()) {\n    drawWord(svg, packet, word, config);\n  }\n  svg.append(\"text\").text(title).attr(\"x\", svgWidth / 2).attr(\"y\", svgHeight - totalRowHeight / 2).attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").attr(\"class\", \"packetTitle\");\n}, \"draw\");\nvar drawWord = /* @__PURE__ */ __name((svg, word, rowNumber, { rowHeight, paddingX, paddingY, bitWidth, bitsPerRow, showBits }) => {\n  const group = svg.append(\"g\");\n  const wordY = rowNumber * (rowHeight + paddingY) + paddingY;\n  for (const block of word) {\n    const blockX = block.start % bitsPerRow * bitWidth + 1;\n    const width = (block.end - block.start + 1) * bitWidth - paddingX;\n    group.append(\"rect\").attr(\"x\", blockX).attr(\"y\", wordY).attr(\"width\", width).attr(\"height\", rowHeight).attr(\"class\", \"packetBlock\");\n    group.append(\"text\").attr(\"x\", blockX + width / 2).attr(\"y\", wordY + rowHeight / 2).attr(\"class\", \"packetLabel\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").text(block.label);\n    if (!showBits) {\n      continue;\n    }\n    const isSingleBlock = block.end === block.start;\n    const bitNumberY = wordY - 2;\n    group.append(\"text\").attr(\"x\", blockX + (isSingleBlock ? width / 2 : 0)).attr(\"y\", bitNumberY).attr(\"class\", \"packetByte start\").attr(\"dominant-baseline\", \"auto\").attr(\"text-anchor\", isSingleBlock ? \"middle\" : \"start\").text(block.start);\n    if (!isSingleBlock) {\n      group.append(\"text\").attr(\"x\", blockX + width).attr(\"y\", bitNumberY).attr(\"class\", \"packetByte end\").attr(\"dominant-baseline\", \"auto\").attr(\"text-anchor\", \"end\").text(block.end);\n    }\n  }\n}, \"drawWord\");\nvar renderer = { draw };\n\n// src/diagrams/packet/styles.ts\nvar defaultPacketStyleOptions = {\n  byteFontSize: \"10px\",\n  startByteColor: \"black\",\n  endByteColor: \"black\",\n  labelColor: \"black\",\n  labelFontSize: \"12px\",\n  titleColor: \"black\",\n  titleFontSize: \"14px\",\n  blockStrokeColor: \"black\",\n  blockStrokeWidth: \"1\",\n  blockFillColor: \"#efefef\"\n};\nvar styles = /* @__PURE__ */ __name(({ packet } = {}) => {\n  const options = cleanAndMerge(defaultPacketStyleOptions, packet);\n  return `\n\t.packetByte {\n\t\tfont-size: ${options.byteFontSize};\n\t}\n\t.packetByte.start {\n\t\tfill: ${options.startByteColor};\n\t}\n\t.packetByte.end {\n\t\tfill: ${options.endByteColor};\n\t}\n\t.packetLabel {\n\t\tfill: ${options.labelColor};\n\t\tfont-size: ${options.labelFontSize};\n\t}\n\t.packetTitle {\n\t\tfill: ${options.titleColor};\n\t\tfont-size: ${options.titleFontSize};\n\t}\n\t.packetBlock {\n\t\tstroke: ${options.blockStrokeColor};\n\t\tstroke-width: ${options.blockStrokeWidth};\n\t\tfill: ${options.blockFillColor};\n\t}\n\t`;\n}, \"styles\");\n\n// src/diagrams/packet/diagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles\n};\nexport {\n  diagram\n};\n"], "names": ["populateCommonDb", "ast", "db", "accDescr", "setAccDescription", "accTitle", "setAccTitle", "title", "setDiagramTitle", "__name", "defaultPacketData", "packet", "data", "structuredClone", "DEFAULT_PACKET_CONFIG", "defaultConfig_default", "getConfig2", "config", "cleanAndMerge", "getConfig", "showBits", "paddingY", "getPacket", "pushWord", "word", "length", "push", "clear", "getAccTitle", "getDiagramTitle", "getAccDescription", "populate", "lastByte", "row", "bitsPerRow", "start", "end", "label", "blocks", "Error", "log", "debug", "block", "nextBlock", "getNextFittingBlock", "parser", "parse", "async", "input", "draw", "_text", "id", "_version", "diagram2", "db2", "rowHeight", "bitWidth", "words", "totalRowHeight", "svgHeight", "svgWidth", "svg", "selectSvgElement", "attr", "configureSvgSize", "useMaxWidth", "entries", "drawWord", "append", "text", "rowNumber", "_ref", "paddingX", "group", "wordY", "blockX", "width", "isSingleBlock", "bitNumberY", "defaultPacketStyleOptions", "byteFontSize", "startByteColor", "endByteColor", "labelColor", "labelFontSize", "titleColor", "titleFontSize", "blockStrokeColor", "blockStrokeWidth", "blockFillColor", "diagram", "renderer", "styles", "arguments", "undefined", "options"], "sourceRoot": ""}