{"version": 3, "file": "static/js/967.2ec672b2.chunk.js", "mappings": "0JAkBIA,EAAS,WACX,IAAIC,GAAoBC,EAAAA,EAAAA,KAAO,SAASC,EAAGC,EAAGC,EAAIC,GAChD,IAAKD,EAAKA,GAAM,CAAC,EAAGC,EAAIH,EAAEI,OAAQD,IAAKD,EAAGF,EAAEG,IAAMF,GAClD,OAAOC,CACT,GAAG,KAAMG,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,IAAKC,GAAM,CAAC,EAAG,IAAKC,GAAM,CAAC,EAAG,IAAKC,GAAM,CAAC,EAAG,IAAKC,GAAM,CAAC,EAAG,IAAKC,GAAM,CAAC,EAAG,IAAKC,GAAM,CAAC,EAAG,IAAKC,GAAM,CAAC,EAAG,IAAKC,GAAM,CAAC,EAAG,IAAKC,GAAM,CAAC,EAAG,IAAKC,GAAM,CAAC,EAAG,KAAMC,GAAM,CAAC,GAAI,GAAI,GAAI,IAAKC,GAAM,CAAC,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IACh0CC,GAAU,CACZC,OAAuBvE,EAAAA,EAAAA,KAAO,WAC9B,GAAG,SACHwE,GAAI,CAAC,EACLC,SAAU,CAAE,MAAS,EAAG,cAAiB,EAAG,MAAS,EAAG,IAAO,EAAG,YAAe,EAAG,KAAQ,EAAG,MAAS,EAAG,QAAW,EAAG,MAAS,GAAI,MAAS,GAAI,IAAO,GAAI,KAAQ,GAAI,KAAQ,GAAI,aAAgB,GAAI,eAAkB,GAAI,KAAQ,GAAI,MAAS,GAAI,MAAS,GAAI,IAAO,GAAI,SAAY,GAAI,MAAS,GAAI,UAAa,GAAI,kBAAqB,GAAI,SAAY,GAAI,MAAS,GAAI,IAAO,GAAI,SAAY,GAAI,SAAY,GAAI,KAAQ,GAAI,UAAa,GAAI,YAAe,GAAI,gBAAmB,GAAI,OAAU,GAAI,MAAS,GAAI,YAAe,GAAI,UAAa,GAAI,gBAAmB,GAAI,UAAa,GAAI,gBAAmB,GAAI,0BAA6B,GAAI,QAAW,GAAI,KAAQ,GAAI,YAAe,GAAI,QAAW,GAAI,QAAW,GAAI,WAAc,GAAI,SAAU,GAAI,sBAAuB,GAAI,SAAU,GAAI,WAAc,GAAI,WAAc,GAAI,WAAc,GAAI,WAAc,GAAI,QAAW,GAAI,KAAQ,GAAI,IAAO,GAAI,cAAiB,GAAI,gBAAmB,GAAI,IAAO,GAAI,OAAU,GAAI,SAAY,GAAI,YAAe,GAAI,KAAQ,GAAI,OAAU,GAAI,IAAO,GAAI,WAAc,GAAI,QAAW,EAAG,KAAQ,GAC9jCC,WAAY,CAAE,EAAG,QAAS,EAAG,QAAS,EAAG,MAAO,EAAG,cAAe,EAAG,OAAQ,EAAG,QAAS,EAAG,UAAW,GAAI,QAAS,GAAI,QAAS,GAAI,MAAO,GAAI,OAAQ,GAAI,OAAQ,GAAI,eAAgB,GAAI,OAAQ,GAAI,QAAS,GAAI,QAAS,GAAI,MAAO,GAAI,WAAY,GAAI,WAAY,GAAI,QAAS,GAAI,cAAe,GAAI,YAAa,GAAI,kBAAmB,GAAI,YAAa,GAAI,kBAAmB,GAAI,4BAA6B,GAAI,UAAW,GAAI,cAAe,GAAI,UAAW,GAAI,UAAW,GAAI,aAAc,GAAI,SAAU,GAAI,sBAAuB,GAAI,SAAU,GAAI,aAAc,GAAI,aAAc,GAAI,aAAc,GAAI,aAAc,GAAI,UAAW,GAAI,OAAQ,GAAI,MAAO,GAAI,MAAO,GAAI,SAAU,GAAI,cAAe,GAAI,OAAQ,GAAI,SAAU,GAAI,MAAO,GAAI,cACtuBC,aAAc,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,IAC7uBC,eAA+B5E,EAAAA,EAAAA,KAAO,SAAmB6E,EAAQC,EAAQC,EAAUP,EAAIQ,EAASC,EAAIC,GAClG,IAAIC,EAAKF,EAAG5E,OAAS,EACrB,OAAQ2E,GACN,KAAK,GAkGL,KAAK,GACHI,KAAKC,EAAIJ,EAAGE,GACZ,MAjGF,KAAK,GAkGL,KAAK,GACHC,KAAKC,EAAIJ,EAAGE,EAAK,GAAK,GAAKF,EAAGE,GAC9B,MAjGF,KAAK,GACHC,KAAKC,EAAIJ,EAAGE,EAAK,GAAKF,EAAGE,GACzB,MACF,KAAK,GACHC,KAAKC,EAAI,CAACJ,EAAGE,GAAIG,QACjB,MACF,KAAK,GACHL,EAAGE,EAAK,GAAGI,KAAKN,EAAGE,GAAIG,QACvBF,KAAKC,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHC,KAAKC,EAAIJ,EAAGE,EAAK,GACjBX,EAAGgB,SAASP,EAAGE,EAAK,GAAIF,EAAGE,IAC3B,MACF,KAAK,GACHC,KAAKC,EAAI,GACT,MACF,KAAK,GACHD,KAAKC,EAAIJ,EAAGE,GAAIG,OAChBd,EAAGiB,gBAAgBL,KAAKC,GACxB,MACF,KAAK,GACHD,KAAKC,EAAIJ,EAAGE,GAAIG,OAChBd,EAAGkB,YAAYN,KAAKC,GACpB,MACF,KAAK,GACL,KAAK,GACHD,KAAKC,EAAIJ,EAAGE,GAAIG,OAChBd,EAAGmB,kBAAkBP,KAAKC,GAC1B,MACF,KAAK,GACHb,EAAGoB,WAAWX,EAAGE,GAAIU,OAAO,IAC5BT,KAAKC,EAAIJ,EAAGE,GAAIU,OAAO,GACvB,MACF,KAAK,GACHrB,EAAGsB,SAASb,EAAGE,EAAK,GAAI,GAAIF,EAAGE,EAAK,GAAIF,EAAGE,GAAK,IAChD,MACF,KAAK,GACHX,EAAGsB,SAASb,EAAGE,EAAK,GAAIF,EAAGE,EAAK,GAAIF,EAAGE,EAAK,GAAIF,EAAGE,GAAK,IACxD,MACF,KAAK,GACHX,EAAGsB,SAASb,EAAGE,EAAK,GAAI,GAAIF,EAAGE,EAAK,GAAIF,EAAGE,EAAK,GAAIF,EAAGE,IACvD,MACF,KAAK,GACHX,EAAGsB,SAASb,EAAGE,EAAK,GAAIF,EAAGE,EAAK,GAAIF,EAAGE,EAAK,GAAIF,EAAGE,EAAK,GAAIF,EAAGE,IAC/D,MACF,KAAK,GACHX,EAAGuB,iBAAiBd,EAAGE,EAAK,IAC5BX,EAAGwB,kBAAkBf,EAAGE,IACxB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGc,MAAQ,WACnBzB,EAAGuB,iBAAiBd,EAAGE,EAAK,IAC5B,MACF,KAAK,GACHX,EAAGuB,iBAAiBd,EAAGE,IACvB,MACF,KAAK,GACHX,EAAG0B,mBAAmBjB,EAAGE,EAAK,IAC9BX,EAAG2B,gBAAgBlB,EAAGE,IACtB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGc,MAAQ,WACnBzB,EAAG0B,mBAAmBjB,EAAGE,EAAK,IAC9B,MACF,KAAK,GACHX,EAAG0B,mBAAmBjB,EAAGE,IACzB,MACF,KAAK,GACHX,EAAG4B,iBAAiBnB,EAAGE,IACvB,MACF,KAAK,GACHX,EAAG6B,iBAAiBpB,EAAGE,IACvB,MACF,KAAK,GACHX,EAAG8B,iBAAiBrB,EAAGE,IACvB,MACF,KAAK,GACHX,EAAG+B,iBAAiBtB,EAAGE,IACvB,MACF,KAAK,GAML,KAAK,GACHC,KAAKC,EAAI,CAAEY,KAAMhB,EAAGE,GAAKqB,KAAM,QAC/B,MALF,KAAK,GACHpB,KAAKC,EAAI,CAAEY,KAAMhB,EAAGE,EAAK,GAAGc,KAAO,GAAKhB,EAAGE,GAAKqB,KAAMvB,EAAGE,EAAK,GAAGqB,MACjE,MAIF,KAAK,GACHpB,KAAKC,EAAI,CAAEY,KAAMhB,EAAGE,GAAKqB,KAAM,YASrC,GAAG,aACHC,MAAO,CAAC,CAAE,GAAInG,EAAK,GAAI,EAAG,GAAI,EAAG,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAG,CAAC,IAAM,CAAE,GAAIJ,EAAK,GAAI,EAAG,GAAI,EAAG,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAIJ,EAAK,GAAI,EAAG,GAAI,EAAG,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAOX,EAAEY,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,KAAOZ,EAAEa,EAAK,CAAC,EAAG,KAAMb,EAAEa,EAAK,CAAC,EAAG,KAAMb,EAAEa,EAAK,CAAC,EAAG,KAAM,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAOb,EAAEc,EAAKC,EAAK,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,CAAC,EAAG,IAAK,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQzC,EAAEY,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,GAAOX,EAAEc,EAAK,CAAC,EAAG,KAAMd,EAAEc,EAAKC,EAAK,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQzC,EAAEc,EAAK,CAAC,EAAG,KAAMd,EAAEc,EAAK,CAAC,EAAG,KAAMd,EAAEc,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAOd,EAAEc,EAAK,CAAC,EAAG,KAAMd,EAAEc,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,EAAGE,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAGzB,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAGzB,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAGzB,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAGzB,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAGzB,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAGzB,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,GAAI,GAAI,GAAI,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAOzC,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAEY,EAAK,CAAC,EAAG,KAAMZ,EAAEc,EAAK,CAAC,EAAG,KAAMd,EAAEc,EAAK,CAAC,EAAG,KAAMd,EAAEc,EAAK,CAAC,EAAG,KAAMd,EAAEc,EAAK,CAAC,EAAG,KAAM,CAAE,EAAG,GAAI,EAAG+B,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,IAAMxD,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAI,CAAC,EAAG,IAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQzC,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAI,CAAC,EAAG,IAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQzC,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQzC,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQzC,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQzC,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQ,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAOzC,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM5C,EAAE4C,EAAK,CAAC,EAAG,KAAM,CAAE,EAAG,GAAI,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,KAAOxD,EAAEyD,EAAK,CAAC,EAAG,KAAMzD,EAAEyD,EAAK,CAAC,EAAG,IAAKzD,EAAEyD,EAAK,CAAC,EAAG,IAAKzD,EAAEyD,EAAK,CAAC,EAAG,IAAKzD,EAAEyD,EAAK,CAAC,EAAG,IAAKzD,EAAEyD,EAAK,CAAC,EAAG,IAAKzD,EAAEyD,EAAK,CAAC,EAAG,IAAKzD,EAAEyD,EAAK,CAAC,EAAG,IAAKzD,EAAEyD,EAAK,CAAC,EAAG,IAAKzD,EAAEyD,EAAK,CAAC,EAAG,IAAKzD,EAAEyD,EAAK,CAAC,EAAG,KAAMzD,EAAEyD,EAAK,CAAC,EAAG,KAAMzD,EAAEyD,EAAK,CAAC,EAAG,KAAMzD,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQzC,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIc,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQ,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,EAAGiB,EAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAI,GAAI,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAI,GAAI,GAAI,IAAMnE,EAAEyD,EAAK,CAAC,EAAG,KAAMzD,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQzC,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,EAAGE,EAAK,EAAGC,EAAK,EAAGyB,EAAK,GAAIxB,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIsB,EAAK,GAAIN,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQzC,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,EAAG4C,EAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,KAAQ,CAAE,GAAI,CAAC,EAAG,MAAQnE,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAIsD,KAAQpE,EAAEqE,GAAK,CAAC,EAAG,IAAK,CAAE,GAAI,IAAK,EAAGX,EAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,KAAQnE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAIsD,KAAQpE,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,EAAG4C,EAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,KAAQ,CAAE,EAAGT,EAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAI,GAAI,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAI,KAAOnE,EAAEsE,GAAK,CAAC,EAAG,KAAMtE,EAAEc,EAAK,CAAC,EAAG,IAAK,CAAE,GAAIsD,KAAQpE,EAAEqE,GAAK,CAAC,EAAG,IAAK,CAAE,GAAI,IAAK,EAAGX,EAAK,EAAGC,GAAK,EAAGC,GAAK,EAAGC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,GAAK,GAAIC,MACzgLwC,eAAgB,CAAE,EAAG,CAAC,EAAG,IAAK,EAAG,CAAC,EAAG,KACrCC,YAA4B3G,EAAAA,EAAAA,KAAO,SAAoB4G,EAAKC,GAC1D,IAAIA,EAAKC,YAEF,CACL,IAAIC,EAAQ,IAAIC,MAAMJ,GAEtB,MADAG,EAAMF,KAAOA,EACPE,CACR,CALE3B,KAAKb,MAAMqC,EAMf,GAAG,cACHK,OAAuBjH,EAAAA,EAAAA,KAAO,SAAekH,GAC3C,IAAIC,EAAO/B,KAAMgC,EAAQ,CAAC,GAAIC,EAAS,GAAIC,EAAS,CAAC,MAAOC,EAAS,GAAId,EAAQrB,KAAKqB,MAAO5B,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAG0C,EAAa,EAC7IC,EAAOF,EAAOG,MAAMC,KAAKC,UAAW,GACpCC,EAASC,OAAOC,OAAO3C,KAAK4C,OAC5BC,EAAc,CAAEzD,GAAI,CAAC,GACzB,IAAK,IAAIvE,KAAKmF,KAAKZ,GACbsD,OAAOI,UAAUC,eAAeR,KAAKvC,KAAKZ,GAAIvE,KAChDgI,EAAYzD,GAAGvE,GAAKmF,KAAKZ,GAAGvE,IAGhC4H,EAAOO,SAASlB,EAAOe,EAAYzD,IACnCyD,EAAYzD,GAAGwD,MAAQH,EACvBI,EAAYzD,GAAG1E,OAASsF,KACI,oBAAjByC,EAAOQ,SAChBR,EAAOQ,OAAS,CAAC,GAEnB,IAAIC,EAAQT,EAAOQ,OACnBd,EAAOhC,KAAK+C,GACZ,IAAIC,EAASV,EAAOW,SAAWX,EAAOW,QAAQD,OAY9C,SAASE,IACP,IAAIC,EASJ,MAPqB,kBADrBA,EAAQrB,EAAOsB,OAASd,EAAOY,OA/BqI,KAiC9JC,aAAiBE,QAEnBF,GADArB,EAASqB,GACMC,OAEjBD,EAAQvB,EAAK1C,SAASiE,IAAUA,GAE3BA,CACT,CAtByC,oBAA9BT,EAAYzD,GAAGmC,WACxBvB,KAAKuB,WAAasB,EAAYzD,GAAGmC,WAEjCvB,KAAKuB,WAAamB,OAAOe,eAAezD,MAAMuB,YAOhD3G,EAAAA,EAAAA,KALA,SAAkB8I,GAChB1B,EAAM/G,OAAS+G,EAAM/G,OAAS,EAAIyI,EAClCxB,EAAOjH,OAASiH,EAAOjH,OAASyI,EAChCvB,EAAOlH,OAASkH,EAAOlH,OAASyI,CAClC,GACiB,aAajB9I,EAAAA,EAAAA,IAAOyI,EAAK,OAEZ,IADA,IAAIM,EAAQC,EAAgBC,EAAOC,EAAWC,EAAeC,EAAGC,EAAKC,EAAUC,EAA9BC,EAAQ,CAAC,IAC7C,CAUX,GATAP,EAAQ7B,EAAMA,EAAM/G,OAAS,GACzB+E,KAAKsB,eAAeuC,GACtBC,EAAS9D,KAAKsB,eAAeuC,IAEd,OAAXF,GAAoC,oBAAVA,IAC5BA,EAASN,KAEXS,EAASzC,EAAMwC,IAAUxC,EAAMwC,GAAOF,IAElB,qBAAXG,IAA2BA,EAAO7I,SAAW6I,EAAO,GAAI,CACjE,IAAIO,EAAS,GAEb,IAAKL,KADLG,EAAW,GACD9C,EAAMwC,GACV7D,KAAKV,WAAW0E,IAAMA,EAzD6H,GA0DrJG,EAAShE,KAAK,IAAMH,KAAKV,WAAW0E,GAAK,KAI3CK,EADE5B,EAAO6B,aACA,wBAA0B3E,EAAW,GAAK,MAAQ8C,EAAO6B,eAAiB,eAAiBH,EAASI,KAAK,MAAQ,WAAavE,KAAKV,WAAWqE,IAAWA,GAAU,IAEnK,wBAA0BhE,EAAW,GAAK,iBAhE6G,GAgE1FgE,EAAgB,eAAiB,KAAO3D,KAAKV,WAAWqE,IAAWA,GAAU,KAErJ3D,KAAKuB,WAAW8C,EAAQ,CACtBxD,KAAM4B,EAAO+B,MACblB,MAAOtD,KAAKV,WAAWqE,IAAWA,EAClCc,KAAMhC,EAAO9C,SACb+E,IAAKxB,EACLiB,YAEJ,CACA,GAAIL,EAAO,aAAcN,OAASM,EAAO7I,OAAS,EAChD,MAAM,IAAI2G,MAAM,oDAAsDiC,EAAQ,YAAcF,GAE9F,OAAQG,EAAO,IACb,KAAK,EACH9B,EAAM7B,KAAKwD,GACXzB,EAAO/B,KAAKsC,EAAOhD,QACnB0C,EAAOhC,KAAKsC,EAAOQ,QACnBjB,EAAM7B,KAAK2D,EAAO,IAClBH,EAAS,KACJC,GASHD,EAASC,EACTA,EAAiB,OATjBlE,EAAS+C,EAAO/C,OAChBD,EAASgD,EAAOhD,OAChBE,EAAW8C,EAAO9C,SAClBuD,EAAQT,EAAOQ,OACXb,EAAa,GACfA,KAMJ,MACF,KAAK,EAwBH,GAvBA6B,EAAMjE,KAAKT,aAAauE,EAAO,IAAI,GACnCM,EAAMnE,EAAIiC,EAAOA,EAAOjH,OAASgJ,GACjCG,EAAMtE,GAAK,CACT6E,WAAYxC,EAAOA,EAAOlH,QAAUgJ,GAAO,IAAIU,WAC/CC,UAAWzC,EAAOA,EAAOlH,OAAS,GAAG2J,UACrCC,aAAc1C,EAAOA,EAAOlH,QAAUgJ,GAAO,IAAIY,aACjDC,YAAa3C,EAAOA,EAAOlH,OAAS,GAAG6J,aAErC3B,IACFiB,EAAMtE,GAAGiF,MAAQ,CACf5C,EAAOA,EAAOlH,QAAUgJ,GAAO,IAAIc,MAAM,GACzC5C,EAAOA,EAAOlH,OAAS,GAAG8J,MAAM,KAYnB,qBATjBhB,EAAI/D,KAAKR,cAAcwF,MAAMZ,EAAO,CAClC3E,EACAC,EACAC,EACAkD,EAAYzD,GACZ0E,EAAO,GACP5B,EACAC,GACA8C,OAAO5C,KAEP,OAAO0B,EAELE,IACFjC,EAAQA,EAAMM,MAAM,GAAI,EAAI2B,EAAM,GAClC/B,EAASA,EAAOI,MAAM,GAAI,EAAI2B,GAC9B9B,EAASA,EAAOG,MAAM,GAAI,EAAI2B,IAEhCjC,EAAM7B,KAAKH,KAAKT,aAAauE,EAAO,IAAI,IACxC5B,EAAO/B,KAAKiE,EAAMnE,GAClBkC,EAAOhC,KAAKiE,EAAMtE,IAClBoE,EAAW7C,EAAMW,EAAMA,EAAM/G,OAAS,IAAI+G,EAAMA,EAAM/G,OAAS,IAC/D+G,EAAM7B,KAAK+D,GACX,MACF,KAAK,EACH,OAAO,EAEb,CACA,OAAO,CACT,GAAG,UAEDtB,GAAwB,WAoc1B,MAnca,CACXsC,IAAK,EACL3D,YAA4B3G,EAAAA,EAAAA,KAAO,SAAoB4G,EAAKC,GAC1D,IAAIzB,KAAKZ,GAAG1E,OAGV,MAAM,IAAIkH,MAAMJ,GAFhBxB,KAAKZ,GAAG1E,OAAO6G,WAAWC,EAAKC,EAInC,GAAG,cAEHuB,UAA0BpI,EAAAA,EAAAA,KAAO,SAASkH,EAAO1C,GAiB/C,OAhBAY,KAAKZ,GAAKA,GAAMY,KAAKZ,IAAM,CAAC,EAC5BY,KAAKmF,OAASrD,EACd9B,KAAKoF,MAAQpF,KAAKqF,WAAarF,KAAKsF,MAAO,EAC3CtF,KAAKL,SAAWK,KAAKN,OAAS,EAC9BM,KAAKP,OAASO,KAAKuF,QAAUvF,KAAKwE,MAAQ,GAC1CxE,KAAKwF,eAAiB,CAAC,WACvBxF,KAAKiD,OAAS,CACZ0B,WAAY,EACZE,aAAc,EACdD,UAAW,EACXE,YAAa,GAEX9E,KAAKoD,QAAQD,SACfnD,KAAKiD,OAAO8B,MAAQ,CAAC,EAAG,IAE1B/E,KAAKyF,OAAS,EACPzF,IACT,GAAG,YAEH8B,OAAuBlH,EAAAA,EAAAA,KAAO,WAC5B,IAAI8K,EAAK1F,KAAKmF,OAAO,GAiBrB,OAhBAnF,KAAKP,QAAUiG,EACf1F,KAAKN,SACLM,KAAKyF,SACLzF,KAAKwE,OAASkB,EACd1F,KAAKuF,SAAWG,EACJA,EAAGlB,MAAM,oBAEnBxE,KAAKL,WACLK,KAAKiD,OAAO2B,aAEZ5E,KAAKiD,OAAO6B,cAEV9E,KAAKoD,QAAQD,QACfnD,KAAKiD,OAAO8B,MAAM,KAEpB/E,KAAKmF,OAASnF,KAAKmF,OAAO7C,MAAM,GACzBoD,CACT,GAAG,SAEHC,OAAuB/K,EAAAA,EAAAA,KAAO,SAAS8K,GACrC,IAAIzB,EAAMyB,EAAGzK,OACT2K,EAAQF,EAAGG,MAAM,iBACrB7F,KAAKmF,OAASO,EAAK1F,KAAKmF,OACxBnF,KAAKP,OAASO,KAAKP,OAAOgB,OAAO,EAAGT,KAAKP,OAAOxE,OAASgJ,GACzDjE,KAAKyF,QAAUxB,EACf,IAAI6B,EAAW9F,KAAKwE,MAAMqB,MAAM,iBAChC7F,KAAKwE,MAAQxE,KAAKwE,MAAM/D,OAAO,EAAGT,KAAKwE,MAAMvJ,OAAS,GACtD+E,KAAKuF,QAAUvF,KAAKuF,QAAQ9E,OAAO,EAAGT,KAAKuF,QAAQtK,OAAS,GACxD2K,EAAM3K,OAAS,IACjB+E,KAAKL,UAAYiG,EAAM3K,OAAS,GAElC,IAAI8I,EAAI/D,KAAKiD,OAAO8B,MAWpB,OAVA/E,KAAKiD,OAAS,CACZ0B,WAAY3E,KAAKiD,OAAO0B,WACxBC,UAAW5E,KAAKL,SAAW,EAC3BkF,aAAc7E,KAAKiD,OAAO4B,aAC1BC,YAAac,GAASA,EAAM3K,SAAW6K,EAAS7K,OAAS+E,KAAKiD,OAAO4B,aAAe,GAAKiB,EAASA,EAAS7K,OAAS2K,EAAM3K,QAAQA,OAAS2K,EAAM,GAAG3K,OAAS+E,KAAKiD,OAAO4B,aAAeZ,GAEtLjE,KAAKoD,QAAQD,SACfnD,KAAKiD,OAAO8B,MAAQ,CAAChB,EAAE,GAAIA,EAAE,GAAK/D,KAAKN,OAASuE,IAElDjE,KAAKN,OAASM,KAAKP,OAAOxE,OACnB+E,IACT,GAAG,SAEH+F,MAAsBnL,EAAAA,EAAAA,KAAO,WAE3B,OADAoF,KAAKoF,OAAQ,EACNpF,IACT,GAAG,QAEHgG,QAAwBpL,EAAAA,EAAAA,KAAO,WAC7B,OAAIoF,KAAKoD,QAAQ6C,iBACfjG,KAAKqF,YAAa,EAQbrF,MANEA,KAAKuB,WAAW,0BAA4BvB,KAAKL,SAAW,GAAK,mIAAqIK,KAAKsE,eAAgB,CAChOzD,KAAM,GACNyC,MAAO,KACPmB,KAAMzE,KAAKL,UAIjB,GAAG,UAEHuG,MAAsBtL,EAAAA,EAAAA,KAAO,SAAS8I,GACpC1D,KAAK2F,MAAM3F,KAAKwE,MAAMlC,MAAMoB,GAC9B,GAAG,QAEHyC,WAA2BvL,EAAAA,EAAAA,KAAO,WAChC,IAAIwL,EAAOpG,KAAKuF,QAAQ9E,OAAO,EAAGT,KAAKuF,QAAQtK,OAAS+E,KAAKwE,MAAMvJ,QACnE,OAAQmL,EAAKnL,OAAS,GAAK,MAAQ,IAAMmL,EAAK3F,QAAQ,IAAI4F,QAAQ,MAAO,GAC3E,GAAG,aAEHC,eAA+B1L,EAAAA,EAAAA,KAAO,WACpC,IAAI2L,EAAOvG,KAAKwE,MAIhB,OAHI+B,EAAKtL,OAAS,KAChBsL,GAAQvG,KAAKmF,OAAO1E,OAAO,EAAG,GAAK8F,EAAKtL,UAElCsL,EAAK9F,OAAO,EAAG,KAAO8F,EAAKtL,OAAS,GAAK,MAAQ,KAAKoL,QAAQ,MAAO,GAC/E,GAAG,iBAEH/B,cAA8B1J,EAAAA,EAAAA,KAAO,WACnC,IAAI4L,EAAMxG,KAAKmG,YACXM,EAAI,IAAIjD,MAAMgD,EAAIvL,OAAS,GAAGsJ,KAAK,KACvC,OAAOiC,EAAMxG,KAAKsG,gBAAkB,KAAOG,EAAI,GACjD,GAAG,gBAEHC,YAA4B9L,EAAAA,EAAAA,KAAO,SAAS4J,EAAOmC,GACjD,IAAIrD,EAAOsC,EAAOgB,EAmDlB,GAlDI5G,KAAKoD,QAAQ6C,kBACfW,EAAS,CACPjH,SAAUK,KAAKL,SACfsD,OAAQ,CACN0B,WAAY3E,KAAKiD,OAAO0B,WACxBC,UAAW5E,KAAK4E,UAChBC,aAAc7E,KAAKiD,OAAO4B,aAC1BC,YAAa9E,KAAKiD,OAAO6B,aAE3BrF,OAAQO,KAAKP,OACb+E,MAAOxE,KAAKwE,MACZqC,QAAS7G,KAAK6G,QACdtB,QAASvF,KAAKuF,QACd7F,OAAQM,KAAKN,OACb+F,OAAQzF,KAAKyF,OACbL,MAAOpF,KAAKoF,MACZD,OAAQnF,KAAKmF,OACb/F,GAAIY,KAAKZ,GACToG,eAAgBxF,KAAKwF,eAAelD,MAAM,GAC1CgD,KAAMtF,KAAKsF,MAETtF,KAAKoD,QAAQD,SACfyD,EAAO3D,OAAO8B,MAAQ/E,KAAKiD,OAAO8B,MAAMzC,MAAM,MAGlDsD,EAAQpB,EAAM,GAAGA,MAAM,sBAErBxE,KAAKL,UAAYiG,EAAM3K,QAEzB+E,KAAKiD,OAAS,CACZ0B,WAAY3E,KAAKiD,OAAO2B,UACxBA,UAAW5E,KAAKL,SAAW,EAC3BkF,aAAc7E,KAAKiD,OAAO6B,YAC1BA,YAAac,EAAQA,EAAMA,EAAM3K,OAAS,GAAGA,OAAS2K,EAAMA,EAAM3K,OAAS,GAAGuJ,MAAM,UAAU,GAAGvJ,OAAS+E,KAAKiD,OAAO6B,YAAcN,EAAM,GAAGvJ,QAE/I+E,KAAKP,QAAU+E,EAAM,GACrBxE,KAAKwE,OAASA,EAAM,GACpBxE,KAAK6G,QAAUrC,EACfxE,KAAKN,OAASM,KAAKP,OAAOxE,OACtB+E,KAAKoD,QAAQD,SACfnD,KAAKiD,OAAO8B,MAAQ,CAAC/E,KAAKyF,OAAQzF,KAAKyF,QAAUzF,KAAKN,SAExDM,KAAKoF,OAAQ,EACbpF,KAAKqF,YAAa,EAClBrF,KAAKmF,OAASnF,KAAKmF,OAAO7C,MAAMkC,EAAM,GAAGvJ,QACzC+E,KAAKuF,SAAWf,EAAM,GACtBlB,EAAQtD,KAAKR,cAAc+C,KAAKvC,KAAMA,KAAKZ,GAAIY,KAAM2G,EAAc3G,KAAKwF,eAAexF,KAAKwF,eAAevK,OAAS,IAChH+E,KAAKsF,MAAQtF,KAAKmF,SACpBnF,KAAKsF,MAAO,GAEVhC,EACF,OAAOA,EACF,GAAItD,KAAKqF,WAAY,CAC1B,IAAK,IAAIxK,KAAK+L,EACZ5G,KAAKnF,GAAK+L,EAAO/L,GAEnB,OAAO,CACT,CACA,OAAO,CACT,GAAG,cAEH0L,MAAsB3L,EAAAA,EAAAA,KAAO,WAC3B,GAAIoF,KAAKsF,KACP,OAAOtF,KAAKkF,IAKd,IAAI5B,EAAOkB,EAAOsC,EAAWC,EAHxB/G,KAAKmF,SACRnF,KAAKsF,MAAO,GAGTtF,KAAKoF,QACRpF,KAAKP,OAAS,GACdO,KAAKwE,MAAQ,IAGf,IADA,IAAIwC,EAAQhH,KAAKiH,gBACRC,EAAI,EAAGA,EAAIF,EAAM/L,OAAQiM,IAEhC,IADAJ,EAAY9G,KAAKmF,OAAOX,MAAMxE,KAAKgH,MAAMA,EAAME,SAC5B1C,GAASsC,EAAU,GAAG7L,OAASuJ,EAAM,GAAGvJ,QAAS,CAGlE,GAFAuJ,EAAQsC,EACRC,EAAQG,EACJlH,KAAKoD,QAAQ6C,gBAAiB,CAEhC,IAAc,KADd3C,EAAQtD,KAAK0G,WAAWI,EAAWE,EAAME,KAEvC,OAAO5D,EACF,GAAItD,KAAKqF,WAAY,CAC1Bb,GAAQ,EACR,QACF,CACE,OAAO,CAEX,CAAO,IAAKxE,KAAKoD,QAAQ+D,KACvB,KAEJ,CAEF,OAAI3C,GAEY,KADdlB,EAAQtD,KAAK0G,WAAWlC,EAAOwC,EAAMD,MAE5BzD,EAIS,KAAhBtD,KAAKmF,OACAnF,KAAKkF,IAELlF,KAAKuB,WAAW,0BAA4BvB,KAAKL,SAAW,GAAK,yBAA2BK,KAAKsE,eAAgB,CACtHzD,KAAM,GACNyC,MAAO,KACPmB,KAAMzE,KAAKL,UAGjB,GAAG,QAEH0D,KAAqBzI,EAAAA,EAAAA,KAAO,WAC1B,IAAImJ,EAAI/D,KAAKuG,OACb,OAAIxC,GAGK/D,KAAKqD,KAEhB,GAAG,OAEH+D,OAAuBxM,EAAAA,EAAAA,KAAO,SAAeyM,GAC3CrH,KAAKwF,eAAerF,KAAKkH,EAC3B,GAAG,SAEHC,UAA0B1M,EAAAA,EAAAA,KAAO,WAE/B,OADQoF,KAAKwF,eAAevK,OAAS,EAC7B,EACC+E,KAAKwF,eAAejC,MAEpBvD,KAAKwF,eAAe,EAE/B,GAAG,YAEHyB,eAA+BrM,EAAAA,EAAAA,KAAO,WACpC,OAAIoF,KAAKwF,eAAevK,QAAU+E,KAAKwF,eAAexF,KAAKwF,eAAevK,OAAS,GAC1E+E,KAAKuH,WAAWvH,KAAKwF,eAAexF,KAAKwF,eAAevK,OAAS,IAAI+L,MAErEhH,KAAKuH,WAAoB,QAAEP,KAEtC,GAAG,iBAEHQ,UAA0B5M,EAAAA,EAAAA,KAAO,SAAkB8I,GAEjD,OADAA,EAAI1D,KAAKwF,eAAevK,OAAS,EAAIwM,KAAKC,IAAIhE,GAAK,KAC1C,EACA1D,KAAKwF,eAAe9B,GAEpB,SAEX,GAAG,YAEHiE,WAA2B/M,EAAAA,EAAAA,KAAO,SAAmByM,GACnDrH,KAAKoH,MAAMC,EACb,GAAG,aAEHO,gBAAgChN,EAAAA,EAAAA,KAAO,WACrC,OAAOoF,KAAKwF,eAAevK,MAC7B,GAAG,kBACHmI,QAAS,CAAE,oBAAoB,GAC/B5D,eAA+B5E,EAAAA,EAAAA,KAAO,SAAmBwE,EAAIyI,EAAKC,EAA2BC,GAE3F,OAAQD,GACN,KAAK,EAEL,KAAK,EAKL,KAAK,EACH,MAJF,KAAK,EACH,OAAO,GAIT,KAAK,EAEH,OADA9H,KAAKoH,MAAM,SACJ,GAET,KAAK,EAEH,OADApH,KAAKsH,WACE,cAET,KAAK,EAEH,OADAtH,KAAKoH,MAAM,aACJ,GAET,KAAK,EAEH,OADApH,KAAKsH,WACE,kBAET,KAAK,EAEH,OADAtH,KAAKoH,MAAM,aACJ,GAET,KAAK,EAEH,OADApH,KAAKsH,WACE,kBAET,KAAK,GACHtH,KAAKoH,MAAM,uBACX,MACF,KAAK,GAoCL,KAAK,GAML,KAAK,GAqBL,KAAK,GACHpH,KAAKsH,WACL,MA9DF,KAAK,GACH,MAAO,4BAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACHtH,KAAKoH,MAAM,aACX,MACF,KAAK,GACH,MAAO,SAKT,KAAK,GACHpH,KAAKoH,MAAM,UACX,MAIF,KAAK,GACH,MAAO,MAET,KAAK,GACHpH,KAAKoH,MAAM,cACX,MACF,KAAK,GAEH,OADApH,KAAKsH,WACE,GAET,KAAK,GAEH,OADAtH,KAAKoH,MAAM,eACJ,GAET,KAAK,GAEH,OADApH,KAAKoH,MAAM,WACJ,GAKT,KAAK,GACHpH,KAAKsH,WACLtH,KAAKoH,MAAM,WACX,MACF,KAAK,GAEH,OADApH,KAAKsH,WACE,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,EAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GAGL,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,EAET,KAAK,GACH,OAAO,EAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAGb,GAAG,aACHN,MAAO,CAAC,uBAAwB,sBAAuB,gBAAiB,iBAAkB,gBAAiB,wBAAyB,wBAAyB,wBAAyB,wBAAyB,wBAAyB,yBAA0B,aAAc,eAAgB,mBAAoB,mBAAoB,iBAAkB,uBAAwB,uBAAwB,uBAAwB,uBAAwB,mBAAoB,eAAgB,eAAgB,eAAgB,YAAa,YAAa,cAAe,YAAa,aAAc,qBAAsB,uBAAwB,gBAAiB,gBAAiB,uBAAwB,0BAA2B,kBAAmB,UAAW,WAAY,UAAW,UAAW,UAAW,WAAY,UAAW,aAAc,WAAY,UAAW,UAAW,eAAgB,WAAY,UAAW,6BAA8B,WAC55BO,WAAY,CAAE,WAAc,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,QAAW,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,QAAW,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,YAAe,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,oBAAuB,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,GAAI,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,OAAU,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,IAG/tB,CArc4B,GAuc5B,SAASS,KACPhI,KAAKZ,GAAK,CAAC,CACb,CAIA,OAPAF,GAAQ0D,MAAQA,IAIhBhI,EAAAA,EAAAA,IAAOoN,GAAQ,UACfA,GAAOlF,UAAY5D,GACnBA,GAAQ8I,OAASA,GACV,IAAIA,EACb,CAjuBa,GAkuBbtN,EAAOA,OAASA,EAChB,IAAIuN,EAAmBvN,EAInBwN,GAAwBC,EAAAA,EAAAA,MACxBC,EAAkB,MACpBC,WAAAA,GACErI,KAAKsI,QAA0B,IAAIC,IACnCvI,KAAKwI,OAASxI,KAAKyI,mBACnBzI,KAAK0I,YAAc1I,KAAK2I,wBACxB3I,KAAK4I,KAAO5I,KAAK6I,gBACnB,CAAC,eAECjO,EAAAA,EAAAA,IAAOoF,KAAM,mBAFd,GAID6I,cAAAA,GACE,MAAO,CACLC,UAAW,GACXC,cAAe,GACfC,cAAe,GACfC,cAAe,GACfC,cAAe,GACfC,cAAe,GACfC,eAAgB,GAChBC,gBAAiB,GACjBC,aAAc,GACdC,OAAQ,GAEZ,CACAd,gBAAAA,GACE,MAAO,CACLe,WAAW,EACXC,WAAW,EACXC,WAAW,EACXC,YAAaC,EAAAA,GAAsBC,eAAeC,YAAc,IAChEA,WAAYF,EAAAA,GAAsBC,eAAeF,aAAe,IAChEI,aAAcH,EAAAA,GAAsBC,eAAeE,cAAgB,GACnEC,cAAeJ,EAAAA,GAAsBC,eAAeG,eAAiB,GACrEC,gBAAiBL,EAAAA,GAAsBC,eAAeI,iBAAmB,EACzEC,kBAAmBN,EAAAA,GAAsBC,eAAeK,mBAAqB,EAC7EC,kBAAmBP,EAAAA,GAAsBC,eAAeM,mBAAqB,EAC7EC,mBAAoBR,EAAAA,GAAsBC,eAAeO,oBAAsB,GAC/EC,mBAAoBT,EAAAA,GAAsBC,eAAeQ,oBAAsB,GAC/EC,sBAAuBV,EAAAA,GAAsBC,eAAeS,uBAAyB,GACrFC,uBAAwBX,EAAAA,GAAsBC,eAAeU,wBAA0B,EACvFC,iBAAkBZ,EAAAA,GAAsBC,eAAeW,kBAAoB,EAC3EC,mBAAoBb,EAAAA,GAAsBC,eAAeY,oBAAsB,GAC/EC,YAAad,EAAAA,GAAsBC,eAAea,aAAe,EACjEC,cAAef,EAAAA,GAAsBC,eAAec,eAAiB,MACrEC,cAAehB,EAAAA,GAAsBC,eAAee,eAAiB,OACrEC,kCAAmCjB,EAAAA,GAAsBC,eAAegB,mCAAqC,EAC7GC,kCAAmClB,EAAAA,GAAsBC,eAAeiB,mCAAqC,EAEjH,CACAnC,qBAAAA,GACE,MAAO,CACLoC,cAAe7C,EAAsB6C,cACrCC,cAAe9C,EAAsB8C,cACrCC,cAAe/C,EAAsB+C,cACrCC,cAAehD,EAAsBgD,cACrCC,kBAAmBjD,EAAsBiD,kBACzCC,kBAAmBlD,EAAsBkD,kBACzCC,kBAAmBnD,EAAsBmD,kBACzCC,kBAAmBpD,EAAsBoD,kBACzCC,kBAAmBrD,EAAsBqD,kBACzCC,sBAAuBtD,EAAsBsD,sBAC7CC,sBAAuBvD,EAAsBuD,sBAC7CC,sBAAuBxD,EAAsBwD,sBAC7CC,kBAAmBzD,EAAsByD,kBACzCC,iCAAkC1D,EAAsB0D,iCACxDC,iCAAkC3D,EAAsB2D,iCAE5D,CACAC,KAAAA,GACE9L,KAAKwI,OAASxI,KAAKyI,mBACnBzI,KAAK0I,YAAc1I,KAAK2I,wBACxB3I,KAAK4I,KAAO5I,KAAK6I,iBACjB7I,KAAKsI,QAA0B,IAAIC,IACnCwD,EAAAA,GAAIC,KAAK,eACX,CACAC,OAAAA,CAAQrD,GACN5I,KAAK4I,KAAO,IAAK5I,KAAK4I,QAASA,EACjC,CACAsD,SAAAA,CAAU3C,GACRvJ,KAAK4I,KAAKW,OAAS,IAAIA,KAAWvJ,KAAK4I,KAAKW,OAC9C,CACAnJ,QAAAA,CAAS+L,EAAWC,GAClBpM,KAAKsI,QAAQ+D,IAAIF,EAAWC,EAC9B,CACAE,SAAAA,CAAUC,GACRR,EAAAA,GAAI5M,MAAM,0BAA2BoN,GACrCvM,KAAKwI,OAAS,IAAKxI,KAAKwI,UAAW+D,EACrC,CACAC,cAAAA,CAAe9D,GACbqD,EAAAA,GAAI5M,MAAM,+BAAgCuJ,GAC1C1I,KAAK0I,YAAc,IAAK1I,KAAK0I,eAAgBA,EAC/C,CACA+D,cAAAA,CAAe9B,EAAenB,EAAWC,EAAWC,GAClD,MAAMgD,EAAwD,EAAhC1M,KAAKwI,OAAO0B,kBAAwBlK,KAAKwI,OAAO4B,mBACxEuC,EAAa,CACjBC,IAAuB,QAAlBjC,GAA2BnB,EAAYkD,EAAwB,EACpEG,OAA0B,WAAlBlC,GAA8BnB,EAAYkD,EAAwB,GAEtEI,EAAwD,EAAhC9M,KAAKwI,OAAO2B,kBAAwBnK,KAAKwI,OAAO6B,mBACxE0C,EAAa,CACjBC,KAAoC,SAA9BhN,KAAKwI,OAAOoC,eAA4BnB,EAAYqD,EAAwB,EAClFG,MAAqC,UAA9BjN,KAAKwI,OAAOoC,eAA6BnB,EAAYqD,EAAwB,GAEhFI,EAAwBlN,KAAKwI,OAAOwB,cAA2C,EAA3BhK,KAAKwI,OAAOuB,aAChEoD,EAAa,CACjBP,IAAKlD,EAAYwD,EAAwB,GAErCE,EAAepN,KAAKwI,OAAOyB,gBAAkB8C,EAAWC,KACxDK,EAAcrN,KAAKwI,OAAOyB,gBAAkB0C,EAAWC,IAAMO,EAAWP,IACxEU,EAAgBtN,KAAKwI,OAAOsB,WAA2C,EAA9B9J,KAAKwI,OAAOyB,gBAAsB8C,EAAWC,KAAOD,EAAWE,MACxGM,EAAiBvN,KAAKwI,OAAOmB,YAA4C,EAA9B3J,KAAKwI,OAAOyB,gBAAsB0C,EAAWC,IAAMD,EAAWE,OAASM,EAAWP,IAWnI,MAAO,CACLD,aACAI,aACAI,aACAK,cAZoB,CACpBJ,eACAC,cACAC,gBACAG,kBANwBH,EAAgB,EAOxCC,iBACAG,mBAPyBH,EAAiB,GAe9C,CACAI,aAAAA,CAAchD,EAAenB,EAAWC,EAAWmE,GACjD,MAAM,cAAEJ,EAAa,WAAEL,GAAeS,GAChC,mBACJF,EAAkB,eAClBH,EAAc,aACdH,EAAY,kBACZK,EAAiB,YACjBJ,EAAW,cACXC,GACEE,EACEK,EAA0BC,QAAQ9N,KAAK4I,KAAKQ,gBAC5C2E,EAA0BD,QAAQ9N,KAAK4I,KAAKU,cAC5C0E,EAAa,GAiDnB,OAhDIhO,KAAK4I,KAAKO,eAAiBK,GAC7BwE,EAAW7N,KAAK,CACdU,KAAMb,KAAK4I,KAAKO,cAChB8E,KAAMjO,KAAK0I,YAAY+C,sBACvByC,EAAGd,GAAgBS,EAA0BJ,EAAoB,EAAI,GACrEU,EAAqB,QAAlBxD,EAA0B3K,KAAKwI,OAAO0B,kBAAoBiD,EAAWP,IAAM5M,KAAKwI,OAAO0B,kBAAoBmD,EAAcE,EAAiBvN,KAAKwI,OAAOyB,gBACzJmE,SAAUpO,KAAKwI,OAAO4B,mBACtBiE,YAAaR,EAA0B,SAAW,OAClDS,cAAe,MACfC,SAAU,IAGVvO,KAAK4I,KAAKQ,gBAAkBI,GAC9BwE,EAAW7N,KAAK,CACdU,KAAMb,KAAK4I,KAAKQ,eAChB6E,KAAMjO,KAAK0I,YAAY+C,sBACvByC,EAAGd,EAAeK,GAAqBI,EAA0BJ,EAAoB,EAAI,GACzFU,EAAqB,QAAlBxD,EAA0B3K,KAAKwI,OAAO0B,kBAAoBiD,EAAWP,IAAM5M,KAAKwI,OAAO0B,kBAAoBmD,EAAcE,EAAiBvN,KAAKwI,OAAOyB,gBACzJmE,SAAUpO,KAAKwI,OAAO4B,mBACtBiE,YAAaR,EAA0B,SAAW,OAClDS,cAAe,MACfC,SAAU,IAGVvO,KAAK4I,KAAKS,iBAAmBI,GAC/BuE,EAAW7N,KAAK,CACdU,KAAMb,KAAK4I,KAAKS,gBAChB4E,KAAMjO,KAAK0I,YAAYgD,sBACvBwC,EAAiC,SAA9BlO,KAAKwI,OAAOoC,cAA2B5K,KAAKwI,OAAO2B,kBAAoBnK,KAAKwI,OAAO2B,kBAAoBiD,EAAeE,EAAgBtN,KAAKwI,OAAOyB,gBACrJkE,EAAGd,EAAcE,GAAkBQ,EAA0BL,EAAqB,EAAI,GACtFU,SAAUpO,KAAKwI,OAAO6B,mBACtBgE,YAAaN,EAA0B,SAAW,OAClDO,cAAe,MACfC,UAAW,KAGXvO,KAAK4I,KAAKU,cAAgBG,GAC5BuE,EAAW7N,KAAK,CACdU,KAAMb,KAAK4I,KAAKU,aAChB2E,KAAMjO,KAAK0I,YAAYgD,sBACvBwC,EAAiC,SAA9BlO,KAAKwI,OAAOoC,cAA2B5K,KAAKwI,OAAO2B,kBAAoBnK,KAAKwI,OAAO2B,kBAAoBiD,EAAeE,EAAgBtN,KAAKwI,OAAOyB,gBACrJkE,EAAGd,EAAcK,GAAsBK,EAA0BL,EAAqB,EAAI,GAC1FU,SAAUpO,KAAKwI,OAAO6B,mBACtBgE,YAAaN,EAA0B,SAAW,OAClDO,cAAe,MACfC,UAAW,KAGRP,CACT,CACAQ,YAAAA,CAAaZ,GACX,MAAM,cAAEJ,GAAkBI,GACpB,mBAAEF,EAAkB,aAAEN,EAAY,kBAAEK,EAAiB,YAAEJ,GAAgBG,EACvEiB,EAAY,CAChB,CACE5N,KAAM,CACJA,KAAMb,KAAK4I,KAAKG,cAChBkF,KAAMjO,KAAK0I,YAAYyC,kBACvB+C,EAAG,EACHC,EAAG,EACHC,SAAUpO,KAAKwI,OAAO8B,sBACtB+D,YAAa,SACbC,cAAe,SACfC,SAAU,GAEZL,EAAGd,EAAeK,EAClBU,EAAGd,EACHqB,MAAOjB,EACPkB,OAAQjB,EACRO,KAAMjO,KAAK0I,YAAYqC,eAEzB,CACElK,KAAM,CACJA,KAAMb,KAAK4I,KAAKI,cAChBiF,KAAMjO,KAAK0I,YAAY0C,kBACvB8C,EAAG,EACHC,EAAG,EACHC,SAAUpO,KAAKwI,OAAO8B,sBACtB+D,YAAa,SACbC,cAAe,SACfC,SAAU,GAEZL,EAAGd,EACHe,EAAGd,EACHqB,MAAOjB,EACPkB,OAAQjB,EACRO,KAAMjO,KAAK0I,YAAYsC,eAEzB,CACEnK,KAAM,CACJA,KAAMb,KAAK4I,KAAKK,cAChBgF,KAAMjO,KAAK0I,YAAY2C,kBACvB6C,EAAG,EACHC,EAAG,EACHC,SAAUpO,KAAKwI,OAAO8B,sBACtB+D,YAAa,SACbC,cAAe,SACfC,SAAU,GAEZL,EAAGd,EACHe,EAAGd,EAAcK,EACjBgB,MAAOjB,EACPkB,OAAQjB,EACRO,KAAMjO,KAAK0I,YAAYuC,eAEzB,CACEpK,KAAM,CACJA,KAAMb,KAAK4I,KAAKM,cAChB+E,KAAMjO,KAAK0I,YAAY4C,kBACvB4C,EAAG,EACHC,EAAG,EACHC,SAAUpO,KAAKwI,OAAO8B,sBACtB+D,YAAa,SACbC,cAAe,SACfC,SAAU,GAEZL,EAAGd,EAAeK,EAClBU,EAAGd,EAAcK,EACjBgB,MAAOjB,EACPkB,OAAQjB,EACRO,KAAMjO,KAAK0I,YAAYwC,gBAG3B,IAAK,MAAM0D,KAAYH,EACrBG,EAAS/N,KAAKqN,EAAIU,EAASV,EAAIU,EAASF,MAAQ,EAChB,IAA5B1O,KAAK4I,KAAKW,OAAOtO,QACnB2T,EAAS/N,KAAKsN,EAAIS,EAAST,EAAIS,EAASD,OAAS,EACjDC,EAAS/N,KAAKyN,cAAgB,WAE9BM,EAAS/N,KAAKsN,EAAIS,EAAST,EAAInO,KAAKwI,OAAO+B,uBAC3CqE,EAAS/N,KAAKyN,cAAgB,OAGlC,OAAOG,CACT,CACAI,iBAAAA,CAAkBjB,GAChB,MAAM,cAAEJ,GAAkBI,GACpB,eAAEL,EAAc,aAAEH,EAAY,YAAEC,EAAW,cAAEC,GAAkBE,EAC/DsB,GAAQC,EAAAA,EAAAA,OAAcC,OAAO,CAAC,EAAG,IAAIjK,MAAM,CAACqI,EAAcE,EAAgBF,IAC1E6B,GAAQF,EAAAA,EAAAA,OAAcC,OAAO,CAAC,EAAG,IAAIjK,MAAM,CAACwI,EAAiBF,EAAaA,IA0BhF,OAzBerN,KAAK4I,KAAKW,OAAO2F,KAAKC,IACnC,MAAMC,EAAcpP,KAAKsI,QAAQ+G,IAAIF,EAAMhD,WACvCiD,IACFD,EAAQ,IAAKC,KAAgBD,IAoB/B,MAlBc,CACZjB,EAAGY,EAAMK,EAAMjB,GACfC,EAAGc,EAAME,EAAMhB,GACfF,KAAMkB,EAAMG,OAAStP,KAAK0I,YAAY6C,kBACtCgE,OAAQJ,EAAMI,QAAUvP,KAAKwI,OAAOkC,YACpC7J,KAAM,CACJA,KAAMsO,EAAMtO,KACZoN,KAAMjO,KAAK0I,YAAY8C,sBACvB0C,EAAGY,EAAMK,EAAMjB,GACfC,EAAGc,EAAME,EAAMhB,GAAKnO,KAAKwI,OAAOgC,iBAChC6D,YAAa,SACbC,cAAe,MACfF,SAAUpO,KAAKwI,OAAOiC,mBACtB8D,SAAU,GAEZiB,YAAaL,EAAMK,aAAexP,KAAK0I,YAAY6C,kBACnDkE,YAAaN,EAAMM,aAAe,MAExB,GAGhB,CACAC,UAAAA,CAAW9B,GACT,MAAM+B,EAA0B3P,KAAKwI,OAAOsC,kCAAoC,GAC1E,cAAE0C,GAAkBI,GACpB,mBACJF,EAAkB,eAClBH,EAAc,aACdH,EAAY,kBACZK,EAAiB,YACjBJ,EAAW,cACXC,GACEE,EAyDJ,MAxDoB,CAElB,CACEoC,WAAY5P,KAAK0I,YAAYmD,iCAC7B4D,YAAazP,KAAKwI,OAAOsC,kCACzB+E,GAAIzC,EAAeuC,EACnBG,GAAIzC,EACJ0C,GAAI3C,EAAeE,EAAgBqC,EACnCK,GAAI3C,GAGN,CACEuC,WAAY5P,KAAK0I,YAAYmD,iCAC7B4D,YAAazP,KAAKwI,OAAOsC,kCACzB+E,GAAIzC,EAAeE,EACnBwC,GAAIzC,EAAcsC,EAClBI,GAAI3C,EAAeE,EACnB0C,GAAI3C,EAAcE,EAAiBoC,GAGrC,CACEC,WAAY5P,KAAK0I,YAAYmD,iCAC7B4D,YAAazP,KAAKwI,OAAOsC,kCACzB+E,GAAIzC,EAAeuC,EACnBG,GAAIzC,EAAcE,EAClBwC,GAAI3C,EAAeE,EAAgBqC,EACnCK,GAAI3C,EAAcE,GAGpB,CACEqC,WAAY5P,KAAK0I,YAAYmD,iCAC7B4D,YAAazP,KAAKwI,OAAOsC,kCACzB+E,GAAIzC,EACJ0C,GAAIzC,EAAcsC,EAClBI,GAAI3C,EACJ4C,GAAI3C,EAAcE,EAAiBoC,GAGrC,CACEC,WAAY5P,KAAK0I,YAAYkD,iCAC7B6D,YAAazP,KAAKwI,OAAOqC,kCACzBgF,GAAIzC,EAAeK,EACnBqC,GAAIzC,EAAcsC,EAClBI,GAAI3C,EAAeK,EACnBuC,GAAI3C,EAAcE,EAAiBoC,GAGrC,CACEC,WAAY5P,KAAK0I,YAAYkD,iCAC7B6D,YAAazP,KAAKwI,OAAOqC,kCACzBgF,GAAIzC,EAAeuC,EACnBG,GAAIzC,EAAcK,EAClBqC,GAAI3C,EAAeE,EAAgBqC,EACnCK,GAAI3C,EAAcK,GAIxB,CACAuC,QAAAA,CAASvG,GACP,GAAIA,EACF,MAAO,CACL7I,KAAMb,KAAK4I,KAAKE,UAChBmF,KAAMjO,KAAK0I,YAAYiD,kBACvByC,SAAUpO,KAAKwI,OAAOwB,cACtBsE,cAAe,MACfD,YAAa,SACbE,SAAU,EACVJ,EAAGnO,KAAKwI,OAAOuB,aACfmE,EAAGlO,KAAKwI,OAAOsB,WAAa,EAIlC,CACAoG,KAAAA,GACE,MAAM1G,EAAYxJ,KAAKwI,OAAOgB,cAAgBxJ,KAAK4I,KAAKO,gBAAiBnJ,KAAK4I,KAAKQ,gBAC7EK,EAAYzJ,KAAKwI,OAAOiB,cAAgBzJ,KAAK4I,KAAKU,eAAgBtJ,KAAK4I,KAAKS,iBAC5EK,EAAY1J,KAAKwI,OAAOkB,aAAe1J,KAAK4I,KAAKE,UACjD6B,EAAgB3K,KAAK4I,KAAKW,OAAOtO,OAAS,EAAI,SAAW+E,KAAKwI,OAAOmC,cACrEwF,EAAkBnQ,KAAKyM,eAAe9B,EAAenB,EAAWC,EAAWC,GACjF,MAAO,CACLH,OAAQvJ,KAAK6O,kBAAkBsB,GAC/B1B,UAAWzO,KAAKwO,aAAa2B,GAC7BnC,WAAYhO,KAAK2N,cAAchD,EAAenB,EAAWC,EAAW0G,GACpEC,YAAapQ,KAAK0P,WAAWS,GAC7BE,MAAOrQ,KAAKiQ,SAASvG,GAEzB,GAIE4G,EAAoB,cAAc1O,MAAM,eAExChH,EAAAA,EAAAA,IAAOoF,KAAM,qBAF2B,GAI1CqI,WAAAA,CAAYkI,EAAOC,EAAOpP,GACxBqP,MAAM,aAAaF,KAASC,oCAAwCpP,KACpEpB,KAAK0Q,KAAO,mBACd,GAEF,SAASC,EAAgBH,GACvB,OAAQ,oCAAoCI,KAAKJ,EACnD,CAEA,SAASK,EAAeL,GACtB,OAAQ,QAAQI,KAAKJ,EACvB,CAEA,SAASM,EAAqBN,GAC5B,OAAQ,UAAUI,KAAKJ,EACzB,EAPA5V,EAAAA,EAAAA,IAAO+V,EAAiB,oBAIxB/V,EAAAA,EAAAA,IAAOiW,EAAgB,mBAIvBjW,EAAAA,EAAAA,IAAOkW,EAAsB,wBAG7B,IAAItI,GAASuI,EAAAA,EAAAA,MACb,SAASC,EAAcnQ,GACrB,OAAOoQ,EAAAA,EAAAA,IAAapQ,EAAKX,OAAQsI,EACnC,EACA5N,EAAAA,EAAAA,IAAOoW,EAAe,iBACtB,IAAIE,EAAkB,IAAI9I,EAC1B,SAASpH,EAAiBmQ,GACxBD,EAAgBjF,QAAQ,CAAElD,cAAeiI,EAAcG,EAAQtQ,OACjE,CAEA,SAASI,EAAiBkQ,GACxBD,EAAgBjF,QAAQ,CAAEjD,cAAegI,EAAcG,EAAQtQ,OACjE,CAEA,SAASK,EAAiBiQ,GACxBD,EAAgBjF,QAAQ,CAAEhD,cAAe+H,EAAcG,EAAQtQ,OACjE,CAEA,SAASM,EAAiBgQ,GACxBD,EAAgBjF,QAAQ,CAAE/C,cAAe8H,EAAcG,EAAQtQ,OACjE,CAEA,SAASF,EAAiBwQ,GACxBD,EAAgBjF,QAAQ,CAAE9C,cAAe6H,EAAcG,EAAQtQ,OACjE,CAEA,SAASD,EAAkBuQ,GACzBD,EAAgBjF,QAAQ,CAAE7C,eAAgB4H,EAAcG,EAAQtQ,OAClE,CAEA,SAASE,EAAgBoQ,GACvBD,EAAgBjF,QAAQ,CAAE3C,aAAc0H,EAAcG,EAAQtQ,OAChE,CAEA,SAASC,EAAmBqQ,GAC1BD,EAAgBjF,QAAQ,CAAE5C,gBAAiB2H,EAAcG,EAAQtQ,OACnE,CAEA,SAASuQ,EAAYhF,GACnB,MAAMiF,EAAe,CAAC,EACtB,IAAK,MAAMd,KAASnE,EAAQ,CAC1B,MAAOkF,EAAKd,GAASD,EAAMrQ,OAAO2F,MAAM,WACxC,GAAY,WAARyL,EAAkB,CACpB,GAAIT,EAAeL,GACjB,MAAM,IAAIF,EAAkBgB,EAAKd,EAAO,UAE1Ca,EAAa9B,OAASgC,SAASf,EACjC,MAAO,GAAY,UAARc,EAAiB,CAC1B,GAAIX,EAAgBH,GAClB,MAAM,IAAIF,EAAkBgB,EAAKd,EAAO,YAE1Ca,EAAa/B,MAAQkB,CACvB,MAAO,GAAY,iBAARc,EAAwB,CACjC,GAAIX,EAAgBH,GAClB,MAAM,IAAIF,EAAkBgB,EAAKd,EAAO,YAE1Ca,EAAa7B,YAAcgB,CAC7B,KAAO,IAAY,iBAARc,EAMT,MAAM,IAAI1P,MAAM,eAAe0P,uBAL/B,GAAIR,EAAqBN,GACvB,MAAM,IAAIF,EAAkBgB,EAAKd,EAAO,+BAE1Ca,EAAa5B,YAAce,CAG7B,CACF,CACA,OAAOa,CACT,CAEA,SAAS3Q,EAASyQ,EAAShF,EAAW+B,EAAGC,EAAG/B,GAC1C,MAAMiF,EAAeD,EAAYhF,GACjC8E,EAAgBhF,UAAU,CACxB,CACEgC,IACAC,IACAtN,KAAMmQ,EAAcG,EAAQtQ,MAC5BsL,eACGkF,IAGT,CAEA,SAASjR,EAAS+L,EAAWC,GAC3B8E,EAAgB9Q,SAAS+L,EAAWiF,EAAYhF,GAClD,CAEA,SAASoF,EAAS9C,GAChBwC,EAAgB5E,UAAU,CAAExC,WAAY4E,GAC1C,CAEA,SAAS+C,EAAU9C,GACjBuC,EAAgB5E,UAAU,CAAE3C,YAAagF,GAC3C,CAEA,SAAS+C,IACP,MAAMnF,GAAUwE,EAAAA,EAAAA,OACV,eAAEY,EAAgB9H,cAAe+H,GAAwBrF,EAsB/D,OArBIqF,GACFV,EAAgB5E,UAAUsF,GAE5BV,EAAgB1E,eAAe,CAC7BzB,cAAe4G,EAAe5G,cAC9BC,cAAe2G,EAAe3G,cAC9BC,cAAe0G,EAAe1G,cAC9BC,cAAeyG,EAAezG,cAC9BC,kBAAmBwG,EAAexG,kBAClCC,kBAAmBuG,EAAevG,kBAClCC,kBAAmBsG,EAAetG,kBAClCC,kBAAmBqG,EAAerG,kBAClCC,kBAAmBoG,EAAepG,kBAClCC,sBAAuBmG,EAAenG,sBACtCC,sBAAuBkG,EAAelG,sBACtCC,sBAAuBiG,EAAejG,sBACtCG,iCAAkC8F,EAAe9F,iCACjDD,iCAAkC+F,EAAe/F,iCACjDD,kBAAmBgG,EAAehG,oBAEpCuF,EAAgBjF,QAAQ,CAAEnD,WAAW+I,EAAAA,EAAAA,QAC9BX,EAAgBhB,OACzB,EA9GAtV,EAAAA,EAAAA,IAAOoG,EAAkB,qBAIzBpG,EAAAA,EAAAA,IAAOqG,EAAkB,qBAIzBrG,EAAAA,EAAAA,IAAOsG,EAAkB,qBAIzBtG,EAAAA,EAAAA,IAAOuG,EAAkB,qBAIzBvG,EAAAA,EAAAA,IAAO+F,EAAkB,qBAIzB/F,EAAAA,EAAAA,IAAOgG,EAAmB,sBAI1BhG,EAAAA,EAAAA,IAAOmG,EAAiB,oBAIxBnG,EAAAA,EAAAA,IAAOkG,EAAoB,uBA+B3BlG,EAAAA,EAAAA,IAAOwW,EAAa,gBAapBxW,EAAAA,EAAAA,IAAO8F,EAAU,aAIjB9F,EAAAA,EAAAA,IAAOwF,EAAU,aAIjBxF,EAAAA,EAAAA,IAAO4W,EAAU,aAIjB5W,EAAAA,EAAAA,IAAO6W,EAAW,cA2BlB7W,EAAAA,EAAAA,IAAO8W,EAAiB,mBACxB,IA2FII,EAAU,CACZpX,OAAQuN,EACR8J,GAzFuB,CACvBP,WACAC,YACAzQ,mBACAC,mBACAC,mBACAC,mBACAR,mBACAC,oBACAG,kBACAD,qBACAsQ,cACA1Q,WACAN,WACAsR,kBACA5F,OAnB2BlR,EAAAA,EAAAA,KAAO,WAClCsW,EAAgBpF,SAChBA,EAAAA,EAAAA,KACF,GAAG,SAiBDxL,YAAW,KACX0R,YAAW,KACX3R,gBAAe,KACfwR,gBAAe,KACfI,kBAAiB,KACjB1R,kBAAiBA,EAAAA,IAqEjB2R,SAR6B,CAC7BC,MAzDyBvX,EAAAA,EAAAA,KAAO,CAACwX,EAAKC,EAAIC,EAAUC,KACpD,SAASC,EAAoBlE,GAC3B,MAAyB,QAAlBA,EAA0B,UAAY,QAC/C,CAEA,SAASmE,EAAcpE,GACrB,MAAuB,SAAhBA,EAAyB,QAAU,QAC5C,CAEA,SAASqE,EAAkB9J,GACzB,MAAO,aAAaA,EAAKsF,MAAMtF,EAAKuF,aAAavF,EAAK2F,UAAY,IACpE,EAPA3T,EAAAA,EAAAA,IAAO4X,EAAqB,wBAI5B5X,EAAAA,EAAAA,IAAO6X,EAAe,kBAItB7X,EAAAA,EAAAA,IAAO8X,EAAmB,qBAC1B,MAAMC,GAAO5B,EAAAA,EAAAA,MACbhF,EAAAA,GAAI6G,MAAM,6BAA+BR,GACzC,MAAMS,EAAgBF,EAAKE,cAC3B,IAAIC,EACkB,YAAlBD,IACFC,GAAiBC,EAAAA,EAAAA,KAAO,KAAOV,IAEjC,MACMW,GADyB,YAAlBH,GAA8BE,EAAAA,EAAAA,KAAOD,EAAeG,QAAQ,GAAGC,gBAAgBC,OAAQJ,EAAAA,EAAAA,KAAO,SAC1FA,OAAO,QAAQV,OAC1Be,EAAQJ,EAAIK,OAAO,KAAKC,KAAK,QAAS,QACtC5E,EAAQiE,EAAK9I,eAAeC,YAAc,IAC1C6E,EAASgE,EAAK9I,eAAeF,aAAe,KAClD4J,EAAAA,EAAAA,IAAiBP,EAAKrE,EAAQD,EAAOiE,EAAK9I,eAAe2J,cAAe,GACxER,EAAIM,KAAK,UAAW,OAAS5E,EAAQ,IAAMC,GAC3C4D,EAAQR,GAAGN,UAAU9C,GACrB4D,EAAQR,GAAGP,SAAS9C,GACpB,MAAM+E,EAAelB,EAAQR,GAAGL,kBAC1BgC,EAAiBN,EAAMC,OAAO,KAAKC,KAAK,QAAS,aACjDK,EAAcP,EAAMC,OAAO,KAAKC,KAAK,QAAS,UAC9CM,EAAiBR,EAAMC,OAAO,KAAKC,KAAK,QAAS,eACjDO,EAAaT,EAAMC,OAAO,KAAKC,KAAK,QAAS,UAC7CQ,EAAaV,EAAMC,OAAO,KAAKC,KAAK,QAAS,SAC/CG,EAAapD,OACfyD,EAAWT,OAAO,QAAQC,KAAK,IAAK,GAAGA,KAAK,IAAK,GAAGA,KAAK,OAAQG,EAAapD,MAAMpC,MAAMqF,KAAK,YAAaG,EAAapD,MAAMjC,UAAUkF,KAAK,oBAAqBd,EAAoBiB,EAAapD,MAAM/B,gBAAgBgF,KAAK,cAAeb,EAAcgB,EAAapD,MAAMhC,cAAciF,KAAK,YAAaZ,EAAkBe,EAAapD,QAAQxP,KAAK4S,EAAapD,MAAMxP,MAE5W4S,EAAarD,aACfuD,EAAYI,UAAU,QAAQnL,KAAK6K,EAAarD,aAAa4D,QAAQX,OAAO,QAAQC,KAAK,MAAO1K,GAASA,EAAKiH,KAAIyD,KAAK,MAAO1K,GAASA,EAAKkH,KAAIwD,KAAK,MAAO1K,GAASA,EAAKmH,KAAIuD,KAAK,MAAO1K,GAASA,EAAKoH,KAAIO,MAAM,UAAW3H,GAASA,EAAKgH,aAAYW,MAAM,gBAAiB3H,GAASA,EAAK6G,cAE9R,MAAMhB,EAAYiF,EAAeK,UAAU,cAAcnL,KAAK6K,EAAahF,WAAWuF,QAAQX,OAAO,KAAKC,KAAK,QAAS,YACxH7E,EAAU4E,OAAO,QAAQC,KAAK,KAAM1K,GAASA,EAAKsF,IAAGoF,KAAK,KAAM1K,GAASA,EAAKuF,IAAGmF,KAAK,SAAU1K,GAASA,EAAK8F,QAAO4E,KAAK,UAAW1K,GAASA,EAAK+F,SAAQ2E,KAAK,QAAS1K,GAASA,EAAKqF,OACvLQ,EAAU4E,OAAO,QAAQC,KAAK,IAAK,GAAGA,KAAK,IAAK,GAAGA,KAAK,QAAS1K,GAASA,EAAK/H,KAAKoN,OAAMqF,KAAK,aAAc1K,GAASA,EAAK/H,KAAKuN,WAAUkF,KACxI,qBACC1K,GAAS4J,EAAoB5J,EAAK/H,KAAKyN,iBACxCgF,KAAK,eAAgB1K,GAAS6J,EAAc7J,EAAK/H,KAAKwN,eAAciF,KAAK,aAAc1K,GAAS8J,EAAkB9J,EAAK/H,QAAOA,MAAM+H,GAASA,EAAK/H,KAAKA,OAC1IgT,EAAWE,UAAU,WAAWnL,KAAK6K,EAAazF,YAAYgG,QAAQX,OAAO,KAAKC,KAAK,QAAS,SACxGD,OAAO,QAAQC,KAAK,IAAK,GAAGA,KAAK,IAAK,GAAGzS,MAAM+H,GAASA,EAAK/H,OAAMyS,KAAK,QAAS1K,GAASA,EAAKqF,OAAMqF,KAAK,aAAc1K,GAASA,EAAKwF,WAAUkF,KAAK,qBAAsB1K,GAAS4J,EAAoB5J,EAAK0F,iBAAgBgF,KAAK,eAAgB1K,GAAS6J,EAAc7J,EAAKyF,eAAciF,KAAK,aAAc1K,GAAS8J,EAAkB9J,KACjV,MAAMqL,EAAaL,EAAeG,UAAU,gBAAgBnL,KAAK6K,EAAalK,QAAQyK,QAAQX,OAAO,KAAKC,KAAK,QAAS,cACxHW,EAAWZ,OAAO,UAAUC,KAAK,MAAO1K,GAASA,EAAKsF,IAAGoF,KAAK,MAAO1K,GAASA,EAAKuF,IAAGmF,KAAK,KAAM1K,GAASA,EAAK2G,SAAQ+D,KAAK,QAAS1K,GAASA,EAAKqF,OAAMqF,KAAK,UAAW1K,GAASA,EAAK4G,cAAa8D,KAAK,gBAAiB1K,GAASA,EAAK6G,cACxOwE,EAAWZ,OAAO,QAAQC,KAAK,IAAK,GAAGA,KAAK,IAAK,GAAGzS,MAAM+H,GAASA,EAAK/H,KAAKA,OAAMyS,KAAK,QAAS1K,GAASA,EAAK/H,KAAKoN,OAAMqF,KAAK,aAAc1K,GAASA,EAAK/H,KAAKuN,WAAUkF,KACxK,qBACC1K,GAAS4J,EAAoB5J,EAAK/H,KAAKyN,iBACxCgF,KAAK,eAAgB1K,GAAS6J,EAAc7J,EAAK/H,KAAKwN,eAAciF,KAAK,aAAc1K,GAAS8J,EAAkB9J,EAAK/H,OAAM,GAC9H,SAUDuL,QAAwBxR,EAAAA,EAAAA,KAAO,IAAM,IAAI,U", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/quadrantDiagram-7GDLP6J5.mjs"], "sourcesContent": ["import {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  getThemeVariables,\n  log,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/quadrant-chart/parser/quadrant.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 3], $V1 = [1, 4], $V2 = [1, 5], $V3 = [1, 6], $V4 = [1, 7], $V5 = [1, 4, 5, 10, 12, 13, 14, 18, 25, 35, 37, 39, 41, 42, 48, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 63, 64, 65, 66, 67], $V6 = [1, 4, 5, 10, 12, 13, 14, 18, 25, 28, 35, 37, 39, 41, 42, 48, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 63, 64, 65, 66, 67], $V7 = [55, 56, 57], $V8 = [2, 36], $V9 = [1, 37], $Va = [1, 36], $Vb = [1, 38], $Vc = [1, 35], $Vd = [1, 43], $Ve = [1, 41], $Vf = [1, 14], $Vg = [1, 23], $Vh = [1, 18], $Vi = [1, 19], $Vj = [1, 20], $Vk = [1, 21], $Vl = [1, 22], $Vm = [1, 24], $Vn = [1, 25], $Vo = [1, 26], $Vp = [1, 27], $Vq = [1, 28], $Vr = [1, 29], $Vs = [1, 32], $Vt = [1, 33], $Vu = [1, 34], $Vv = [1, 39], $Vw = [1, 40], $Vx = [1, 42], $Vy = [1, 44], $Vz = [1, 62], $VA = [1, 61], $VB = [4, 5, 8, 10, 12, 13, 14, 18, 44, 47, 49, 55, 56, 57, 63, 64, 65, 66, 67], $VC = [1, 65], $VD = [1, 66], $VE = [1, 67], $VF = [1, 68], $VG = [1, 69], $VH = [1, 70], $VI = [1, 71], $VJ = [1, 72], $VK = [1, 73], $VL = [1, 74], $VM = [1, 75], $VN = [1, 76], $VO = [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18], $VP = [1, 90], $VQ = [1, 91], $VR = [1, 92], $VS = [1, 99], $VT = [1, 93], $VU = [1, 96], $VV = [1, 94], $VW = [1, 95], $VX = [1, 97], $VY = [1, 98], $VZ = [1, 102], $V_ = [10, 55, 56, 57], $V$ = [4, 5, 6, 8, 10, 11, 13, 17, 18, 19, 20, 55, 56, 57];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"idStringToken\": 3, \"ALPHA\": 4, \"NUM\": 5, \"NODE_STRING\": 6, \"DOWN\": 7, \"MINUS\": 8, \"DEFAULT\": 9, \"COMMA\": 10, \"COLON\": 11, \"AMP\": 12, \"BRKT\": 13, \"MULT\": 14, \"UNICODE_TEXT\": 15, \"styleComponent\": 16, \"UNIT\": 17, \"SPACE\": 18, \"STYLE\": 19, \"PCT\": 20, \"idString\": 21, \"style\": 22, \"stylesOpt\": 23, \"classDefStatement\": 24, \"CLASSDEF\": 25, \"start\": 26, \"eol\": 27, \"QUADRANT\": 28, \"document\": 29, \"line\": 30, \"statement\": 31, \"axisDetails\": 32, \"quadrantDetails\": 33, \"points\": 34, \"title\": 35, \"title_value\": 36, \"acc_title\": 37, \"acc_title_value\": 38, \"acc_descr\": 39, \"acc_descr_value\": 40, \"acc_descr_multiline_value\": 41, \"section\": 42, \"text\": 43, \"point_start\": 44, \"point_x\": 45, \"point_y\": 46, \"class_name\": 47, \"X-AXIS\": 48, \"AXIS-TEXT-DELIMITER\": 49, \"Y-AXIS\": 50, \"QUADRANT_1\": 51, \"QUADRANT_2\": 52, \"QUADRANT_3\": 53, \"QUADRANT_4\": 54, \"NEWLINE\": 55, \"SEMI\": 56, \"EOF\": 57, \"alphaNumToken\": 58, \"textNoTagsToken\": 59, \"STR\": 60, \"MD_STR\": 61, \"alphaNum\": 62, \"PUNCTUATION\": 63, \"PLUS\": 64, \"EQUALS\": 65, \"DOT\": 66, \"UNDERSCORE\": 67, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"ALPHA\", 5: \"NUM\", 6: \"NODE_STRING\", 7: \"DOWN\", 8: \"MINUS\", 9: \"DEFAULT\", 10: \"COMMA\", 11: \"COLON\", 12: \"AMP\", 13: \"BRKT\", 14: \"MULT\", 15: \"UNICODE_TEXT\", 17: \"UNIT\", 18: \"SPACE\", 19: \"STYLE\", 20: \"PCT\", 25: \"CLASSDEF\", 28: \"QUADRANT\", 35: \"title\", 36: \"title_value\", 37: \"acc_title\", 38: \"acc_title_value\", 39: \"acc_descr\", 40: \"acc_descr_value\", 41: \"acc_descr_multiline_value\", 42: \"section\", 44: \"point_start\", 45: \"point_x\", 46: \"point_y\", 47: \"class_name\", 48: \"X-AXIS\", 49: \"AXIS-TEXT-DELIMITER\", 50: \"Y-AXIS\", 51: \"QUADRANT_1\", 52: \"QUADRANT_2\", 53: \"QUADRANT_3\", 54: \"QUADRANT_4\", 55: \"NEWLINE\", 56: \"SEMI\", 57: \"EOF\", 60: \"STR\", 61: \"MD_STR\", 63: \"PUNCTUATION\", 64: \"PLUS\", 65: \"EQUALS\", 66: \"DOT\", 67: \"UNDERSCORE\" },\n    productions_: [0, [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [21, 1], [21, 2], [22, 1], [22, 2], [23, 1], [23, 3], [24, 5], [26, 2], [26, 2], [26, 2], [29, 0], [29, 2], [30, 2], [31, 0], [31, 1], [31, 2], [31, 1], [31, 1], [31, 1], [31, 2], [31, 2], [31, 2], [31, 1], [31, 1], [34, 4], [34, 5], [34, 5], [34, 6], [32, 4], [32, 3], [32, 2], [32, 4], [32, 3], [32, 2], [33, 2], [33, 2], [33, 2], [33, 2], [27, 1], [27, 1], [27, 1], [43, 1], [43, 2], [43, 1], [43, 1], [62, 1], [62, 2], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [59, 1], [59, 1], [59, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 23:\n          this.$ = $$[$0];\n          break;\n        case 24:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n        case 26:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n        case 27:\n          this.$ = [$$[$0].trim()];\n          break;\n        case 28:\n          $$[$0 - 2].push($$[$0].trim());\n          this.$ = $$[$0 - 2];\n          break;\n        case 29:\n          this.$ = $$[$0 - 4];\n          yy.addClass($$[$0 - 2], $$[$0]);\n          break;\n        case 37:\n          this.$ = [];\n          break;\n        case 42:\n          this.$ = $$[$0].trim();\n          yy.setDiagramTitle(this.$);\n          break;\n        case 43:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 44:\n        case 45:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 46:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 47:\n          yy.addPoint($$[$0 - 3], \"\", $$[$0 - 1], $$[$0], []);\n          break;\n        case 48:\n          yy.addPoint($$[$0 - 4], $$[$0 - 3], $$[$0 - 1], $$[$0], []);\n          break;\n        case 49:\n          yy.addPoint($$[$0 - 4], \"\", $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 50:\n          yy.addPoint($$[$0 - 5], $$[$0 - 4], $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 51:\n          yy.setXAxisLeftText($$[$0 - 2]);\n          yy.setXAxisRightText($$[$0]);\n          break;\n        case 52:\n          $$[$0 - 1].text += \" \\u27F6 \";\n          yy.setXAxisLeftText($$[$0 - 1]);\n          break;\n        case 53:\n          yy.setXAxisLeftText($$[$0]);\n          break;\n        case 54:\n          yy.setYAxisBottomText($$[$0 - 2]);\n          yy.setYAxisTopText($$[$0]);\n          break;\n        case 55:\n          $$[$0 - 1].text += \" \\u27F6 \";\n          yy.setYAxisBottomText($$[$0 - 1]);\n          break;\n        case 56:\n          yy.setYAxisBottomText($$[$0]);\n          break;\n        case 57:\n          yy.setQuadrant1Text($$[$0]);\n          break;\n        case 58:\n          yy.setQuadrant2Text($$[$0]);\n          break;\n        case 59:\n          yy.setQuadrant3Text($$[$0]);\n          break;\n        case 60:\n          yy.setQuadrant4Text($$[$0]);\n          break;\n        case 64:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 65:\n          this.$ = { text: $$[$0 - 1].text + \"\" + $$[$0], type: $$[$0 - 1].type };\n          break;\n        case 66:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 67:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 68:\n          this.$ = $$[$0];\n          break;\n        case 69:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 18: $V0, 26: 1, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, { 1: [3] }, { 18: $V0, 26: 8, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, { 18: $V0, 26: 9, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, o($V5, [2, 33], { 29: 10 }), o($V6, [2, 61]), o($V6, [2, 62]), o($V6, [2, 63]), { 1: [2, 30] }, { 1: [2, 31] }, o($V7, $V8, { 30: 11, 31: 12, 24: 13, 32: 15, 33: 16, 34: 17, 43: 30, 58: 31, 1: [2, 32], 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $Vf, 25: $Vg, 35: $Vh, 37: $Vi, 39: $Vj, 41: $Vk, 42: $Vl, 48: $Vm, 50: $Vn, 51: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V5, [2, 34]), { 27: 45, 55: $V2, 56: $V3, 57: $V4 }, o($V7, [2, 37]), o($V7, $V8, { 24: 13, 32: 15, 33: 16, 34: 17, 43: 30, 58: 31, 31: 46, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $Vf, 25: $Vg, 35: $Vh, 37: $Vi, 39: $Vj, 41: $Vk, 42: $Vl, 48: $Vm, 50: $Vn, 51: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 39]), o($V7, [2, 40]), o($V7, [2, 41]), { 36: [1, 47] }, { 38: [1, 48] }, { 40: [1, 49] }, o($V7, [2, 45]), o($V7, [2, 46]), { 18: [1, 50] }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 51, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 52, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 53, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 54, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 55, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 56, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 44: [1, 57], 47: [1, 58], 58: 60, 59: 59, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, o($VB, [2, 64]), o($VB, [2, 66]), o($VB, [2, 67]), o($VB, [2, 70]), o($VB, [2, 71]), o($VB, [2, 72]), o($VB, [2, 73]), o($VB, [2, 74]), o($VB, [2, 75]), o($VB, [2, 76]), o($VB, [2, 77]), o($VB, [2, 78]), o($VB, [2, 79]), o($VB, [2, 80]), o($V5, [2, 35]), o($V7, [2, 38]), o($V7, [2, 42]), o($V7, [2, 43]), o($V7, [2, 44]), { 3: 64, 4: $VC, 5: $VD, 6: $VE, 7: $VF, 8: $VG, 9: $VH, 10: $VI, 11: $VJ, 12: $VK, 13: $VL, 14: $VM, 15: $VN, 21: 63 }, o($V7, [2, 53], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 49: [1, 77], 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 56], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 49: [1, 78], 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 57], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 58], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 59], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 60], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), { 45: [1, 79] }, { 44: [1, 80] }, o($VB, [2, 65]), o($VB, [2, 81]), o($VB, [2, 82]), o($VB, [2, 83]), { 3: 82, 4: $VC, 5: $VD, 6: $VE, 7: $VF, 8: $VG, 9: $VH, 10: $VI, 11: $VJ, 12: $VK, 13: $VL, 14: $VM, 15: $VN, 18: [1, 81] }, o($VO, [2, 23]), o($VO, [2, 1]), o($VO, [2, 2]), o($VO, [2, 3]), o($VO, [2, 4]), o($VO, [2, 5]), o($VO, [2, 6]), o($VO, [2, 7]), o($VO, [2, 8]), o($VO, [2, 9]), o($VO, [2, 10]), o($VO, [2, 11]), o($VO, [2, 12]), o($V7, [2, 52], { 58: 31, 43: 83, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 55], { 58: 31, 43: 84, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), { 46: [1, 85] }, { 45: [1, 86] }, { 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 16: 89, 17: $VV, 18: $VW, 19: $VX, 20: $VY, 22: 88, 23: 87 }, o($VO, [2, 24]), o($V7, [2, 51], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 54], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 47], { 22: 88, 16: 89, 23: 100, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), { 46: [1, 101] }, o($V7, [2, 29], { 10: $VZ }), o($V_, [2, 27], { 16: 103, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), o($V$, [2, 25]), o($V$, [2, 13]), o($V$, [2, 14]), o($V$, [2, 15]), o($V$, [2, 16]), o($V$, [2, 17]), o($V$, [2, 18]), o($V$, [2, 19]), o($V$, [2, 20]), o($V$, [2, 21]), o($V$, [2, 22]), o($V7, [2, 49], { 10: $VZ }), o($V7, [2, 48], { 22: 88, 16: 89, 23: 104, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), { 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 16: 89, 17: $VV, 18: $VW, 19: $VX, 20: $VY, 22: 105 }, o($V$, [2, 26]), o($V7, [2, 50], { 10: $VZ }), o($V_, [2, 28], { 16: 103, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY })],\n    defaultActions: { 8: [2, 30], 9: [2, 31] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 55;\n            break;\n          case 3:\n            break;\n          case 4:\n            this.begin(\"title\");\n            return 35;\n            break;\n          case 5:\n            this.popState();\n            return \"title_value\";\n            break;\n          case 6:\n            this.begin(\"acc_title\");\n            return 37;\n            break;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 8:\n            this.begin(\"acc_descr\");\n            return 39;\n            break;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 13:\n            return 48;\n            break;\n          case 14:\n            return 50;\n            break;\n          case 15:\n            return 49;\n            break;\n          case 16:\n            return 51;\n            break;\n          case 17:\n            return 52;\n            break;\n          case 18:\n            return 53;\n            break;\n          case 19:\n            return 54;\n            break;\n          case 20:\n            return 25;\n            break;\n          case 21:\n            this.begin(\"md_string\");\n            break;\n          case 22:\n            return \"MD_STR\";\n            break;\n          case 23:\n            this.popState();\n            break;\n          case 24:\n            this.begin(\"string\");\n            break;\n          case 25:\n            this.popState();\n            break;\n          case 26:\n            return \"STR\";\n            break;\n          case 27:\n            this.begin(\"class_name\");\n            break;\n          case 28:\n            this.popState();\n            return 47;\n            break;\n          case 29:\n            this.begin(\"point_start\");\n            return 44;\n            break;\n          case 30:\n            this.begin(\"point_x\");\n            return 45;\n            break;\n          case 31:\n            this.popState();\n            break;\n          case 32:\n            this.popState();\n            this.begin(\"point_y\");\n            break;\n          case 33:\n            this.popState();\n            return 46;\n            break;\n          case 34:\n            return 28;\n            break;\n          case 35:\n            return 4;\n            break;\n          case 36:\n            return 11;\n            break;\n          case 37:\n            return 64;\n            break;\n          case 38:\n            return 10;\n            break;\n          case 39:\n            return 65;\n            break;\n          case 40:\n            return 65;\n            break;\n          case 41:\n            return 14;\n            break;\n          case 42:\n            return 13;\n            break;\n          case 43:\n            return 67;\n            break;\n          case 44:\n            return 66;\n            break;\n          case 45:\n            return 12;\n            break;\n          case 46:\n            return 8;\n            break;\n          case 47:\n            return 5;\n            break;\n          case 48:\n            return 18;\n            break;\n          case 49:\n            return 56;\n            break;\n          case 50:\n            return 63;\n            break;\n          case 51:\n            return 57;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?: *x-axis *)/i, /^(?: *y-axis *)/i, /^(?: *--+> *)/i, /^(?: *quadrant-1 *)/i, /^(?: *quadrant-2 *)/i, /^(?: *quadrant-3 *)/i, /^(?: *quadrant-4 *)/i, /^(?:classDef\\b)/i, /^(?:[\"][`])/i, /^(?:[^`\"]+)/i, /^(?:[`][\"])/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?::::)/i, /^(?:^\\w+)/i, /^(?:\\s*:\\s*\\[\\s*)/i, /^(?:(1)|(0(.\\d+)?))/i, /^(?:\\s*\\] *)/i, /^(?:\\s*,\\s*)/i, /^(?:(1)|(0(.\\d+)?))/i, /^(?: *quadrantChart *)/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s)/i, /^(?:;)/i, /^(?:[!\"#$%&'*+,-.`?\\\\_/])/i, /^(?:$)/i],\n      conditions: { \"class_name\": { \"rules\": [28], \"inclusive\": false }, \"point_y\": { \"rules\": [33], \"inclusive\": false }, \"point_x\": { \"rules\": [32], \"inclusive\": false }, \"point_start\": { \"rules\": [30, 31], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"title\": { \"rules\": [5], \"inclusive\": false }, \"md_string\": { \"rules\": [22, 23], \"inclusive\": false }, \"string\": { \"rules\": [25, 26], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 24, 27, 29, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar quadrant_default = parser;\n\n// src/diagrams/quadrant-chart/quadrantBuilder.ts\nimport { scaleLinear } from \"d3\";\nvar defaultThemeVariables = getThemeVariables();\nvar QuadrantBuilder = class {\n  constructor() {\n    this.classes = /* @__PURE__ */ new Map();\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n  }\n  static {\n    __name(this, \"QuadrantBuilder\");\n  }\n  getDefaultData() {\n    return {\n      titleText: \"\",\n      quadrant1Text: \"\",\n      quadrant2Text: \"\",\n      quadrant3Text: \"\",\n      quadrant4Text: \"\",\n      xAxisLeftText: \"\",\n      xAxisRightText: \"\",\n      yAxisBottomText: \"\",\n      yAxisTopText: \"\",\n      points: []\n    };\n  }\n  getDefaultConfig() {\n    return {\n      showXAxis: true,\n      showYAxis: true,\n      showTitle: true,\n      chartHeight: defaultConfig_default.quadrantChart?.chartWidth || 500,\n      chartWidth: defaultConfig_default.quadrantChart?.chartHeight || 500,\n      titlePadding: defaultConfig_default.quadrantChart?.titlePadding || 10,\n      titleFontSize: defaultConfig_default.quadrantChart?.titleFontSize || 20,\n      quadrantPadding: defaultConfig_default.quadrantChart?.quadrantPadding || 5,\n      xAxisLabelPadding: defaultConfig_default.quadrantChart?.xAxisLabelPadding || 5,\n      yAxisLabelPadding: defaultConfig_default.quadrantChart?.yAxisLabelPadding || 5,\n      xAxisLabelFontSize: defaultConfig_default.quadrantChart?.xAxisLabelFontSize || 16,\n      yAxisLabelFontSize: defaultConfig_default.quadrantChart?.yAxisLabelFontSize || 16,\n      quadrantLabelFontSize: defaultConfig_default.quadrantChart?.quadrantLabelFontSize || 16,\n      quadrantTextTopPadding: defaultConfig_default.quadrantChart?.quadrantTextTopPadding || 5,\n      pointTextPadding: defaultConfig_default.quadrantChart?.pointTextPadding || 5,\n      pointLabelFontSize: defaultConfig_default.quadrantChart?.pointLabelFontSize || 12,\n      pointRadius: defaultConfig_default.quadrantChart?.pointRadius || 5,\n      xAxisPosition: defaultConfig_default.quadrantChart?.xAxisPosition || \"top\",\n      yAxisPosition: defaultConfig_default.quadrantChart?.yAxisPosition || \"left\",\n      quadrantInternalBorderStrokeWidth: defaultConfig_default.quadrantChart?.quadrantInternalBorderStrokeWidth || 1,\n      quadrantExternalBorderStrokeWidth: defaultConfig_default.quadrantChart?.quadrantExternalBorderStrokeWidth || 2\n    };\n  }\n  getDefaultThemeConfig() {\n    return {\n      quadrant1Fill: defaultThemeVariables.quadrant1Fill,\n      quadrant2Fill: defaultThemeVariables.quadrant2Fill,\n      quadrant3Fill: defaultThemeVariables.quadrant3Fill,\n      quadrant4Fill: defaultThemeVariables.quadrant4Fill,\n      quadrant1TextFill: defaultThemeVariables.quadrant1TextFill,\n      quadrant2TextFill: defaultThemeVariables.quadrant2TextFill,\n      quadrant3TextFill: defaultThemeVariables.quadrant3TextFill,\n      quadrant4TextFill: defaultThemeVariables.quadrant4TextFill,\n      quadrantPointFill: defaultThemeVariables.quadrantPointFill,\n      quadrantPointTextFill: defaultThemeVariables.quadrantPointTextFill,\n      quadrantXAxisTextFill: defaultThemeVariables.quadrantXAxisTextFill,\n      quadrantYAxisTextFill: defaultThemeVariables.quadrantYAxisTextFill,\n      quadrantTitleFill: defaultThemeVariables.quadrantTitleFill,\n      quadrantInternalBorderStrokeFill: defaultThemeVariables.quadrantInternalBorderStrokeFill,\n      quadrantExternalBorderStrokeFill: defaultThemeVariables.quadrantExternalBorderStrokeFill\n    };\n  }\n  clear() {\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n    this.classes = /* @__PURE__ */ new Map();\n    log.info(\"clear called\");\n  }\n  setData(data) {\n    this.data = { ...this.data, ...data };\n  }\n  addPoints(points) {\n    this.data.points = [...points, ...this.data.points];\n  }\n  addClass(className, styles) {\n    this.classes.set(className, styles);\n  }\n  setConfig(config2) {\n    log.trace(\"setConfig called with: \", config2);\n    this.config = { ...this.config, ...config2 };\n  }\n  setThemeConfig(themeConfig) {\n    log.trace(\"setThemeConfig called with: \", themeConfig);\n    this.themeConfig = { ...this.themeConfig, ...themeConfig };\n  }\n  calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle) {\n    const xAxisSpaceCalculation = this.config.xAxisLabelPadding * 2 + this.config.xAxisLabelFontSize;\n    const xAxisSpace = {\n      top: xAxisPosition === \"top\" && showXAxis ? xAxisSpaceCalculation : 0,\n      bottom: xAxisPosition === \"bottom\" && showXAxis ? xAxisSpaceCalculation : 0\n    };\n    const yAxisSpaceCalculation = this.config.yAxisLabelPadding * 2 + this.config.yAxisLabelFontSize;\n    const yAxisSpace = {\n      left: this.config.yAxisPosition === \"left\" && showYAxis ? yAxisSpaceCalculation : 0,\n      right: this.config.yAxisPosition === \"right\" && showYAxis ? yAxisSpaceCalculation : 0\n    };\n    const titleSpaceCalculation = this.config.titleFontSize + this.config.titlePadding * 2;\n    const titleSpace = {\n      top: showTitle ? titleSpaceCalculation : 0\n    };\n    const quadrantLeft = this.config.quadrantPadding + yAxisSpace.left;\n    const quadrantTop = this.config.quadrantPadding + xAxisSpace.top + titleSpace.top;\n    const quadrantWidth = this.config.chartWidth - this.config.quadrantPadding * 2 - yAxisSpace.left - yAxisSpace.right;\n    const quadrantHeight = this.config.chartHeight - this.config.quadrantPadding * 2 - xAxisSpace.top - xAxisSpace.bottom - titleSpace.top;\n    const quadrantHalfWidth = quadrantWidth / 2;\n    const quadrantHalfHeight = quadrantHeight / 2;\n    const quadrantSpace = {\n      quadrantLeft,\n      quadrantTop,\n      quadrantWidth,\n      quadrantHalfWidth,\n      quadrantHeight,\n      quadrantHalfHeight\n    };\n    return {\n      xAxisSpace,\n      yAxisSpace,\n      titleSpace,\n      quadrantSpace\n    };\n  }\n  getAxisLabels(xAxisPosition, showXAxis, showYAxis, spaceData) {\n    const { quadrantSpace, titleSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth\n    } = quadrantSpace;\n    const drawXAxisLabelsInMiddle = Boolean(this.data.xAxisRightText);\n    const drawYAxisLabelsInMiddle = Boolean(this.data.yAxisTopText);\n    const axisLabels = [];\n    if (this.data.xAxisLeftText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisLeftText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y: xAxisPosition === \"top\" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: 0\n      });\n    }\n    if (this.data.xAxisRightText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisRightText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + quadrantHalfWidth + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y: xAxisPosition === \"top\" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: 0\n      });\n    }\n    if (this.data.yAxisBottomText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisBottomText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x: this.config.yAxisPosition === \"left\" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,\n        y: quadrantTop + quadrantHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: -90\n      });\n    }\n    if (this.data.yAxisTopText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisTopText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x: this.config.yAxisPosition === \"left\" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,\n        y: quadrantTop + quadrantHalfHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: -90\n      });\n    }\n    return axisLabels;\n  }\n  getQuadrants(spaceData) {\n    const { quadrantSpace } = spaceData;\n    const { quadrantHalfHeight, quadrantLeft, quadrantHalfWidth, quadrantTop } = quadrantSpace;\n    const quadrants = [\n      {\n        text: {\n          text: this.data.quadrant1Text,\n          fill: this.themeConfig.quadrant1TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant1Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant2Text,\n          fill: this.themeConfig.quadrant2TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant2Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant3Text,\n          fill: this.themeConfig.quadrant3TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant3Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant4Text,\n          fill: this.themeConfig.quadrant4TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant4Fill\n      }\n    ];\n    for (const quadrant of quadrants) {\n      quadrant.text.x = quadrant.x + quadrant.width / 2;\n      if (this.data.points.length === 0) {\n        quadrant.text.y = quadrant.y + quadrant.height / 2;\n        quadrant.text.horizontalPos = \"middle\";\n      } else {\n        quadrant.text.y = quadrant.y + this.config.quadrantTextTopPadding;\n        quadrant.text.horizontalPos = \"top\";\n      }\n    }\n    return quadrants;\n  }\n  getQuadrantPoints(spaceData) {\n    const { quadrantSpace } = spaceData;\n    const { quadrantHeight, quadrantLeft, quadrantTop, quadrantWidth } = quadrantSpace;\n    const xAxis = scaleLinear().domain([0, 1]).range([quadrantLeft, quadrantWidth + quadrantLeft]);\n    const yAxis = scaleLinear().domain([0, 1]).range([quadrantHeight + quadrantTop, quadrantTop]);\n    const points = this.data.points.map((point) => {\n      const classStyles = this.classes.get(point.className);\n      if (classStyles) {\n        point = { ...classStyles, ...point };\n      }\n      const props = {\n        x: xAxis(point.x),\n        y: yAxis(point.y),\n        fill: point.color ?? this.themeConfig.quadrantPointFill,\n        radius: point.radius ?? this.config.pointRadius,\n        text: {\n          text: point.text,\n          fill: this.themeConfig.quadrantPointTextFill,\n          x: xAxis(point.x),\n          y: yAxis(point.y) + this.config.pointTextPadding,\n          verticalPos: \"center\",\n          horizontalPos: \"top\",\n          fontSize: this.config.pointLabelFontSize,\n          rotation: 0\n        },\n        strokeColor: point.strokeColor ?? this.themeConfig.quadrantPointFill,\n        strokeWidth: point.strokeWidth ?? \"0px\"\n      };\n      return props;\n    });\n    return points;\n  }\n  getBorders(spaceData) {\n    const halfExternalBorderWidth = this.config.quadrantExternalBorderStrokeWidth / 2;\n    const { quadrantSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth\n    } = quadrantSpace;\n    const borderLines = [\n      // top border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop\n      },\n      // right border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // bottom border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHeight,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHeight\n      },\n      // left border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // vertical inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantHalfWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantHalfWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // horizontal inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHalfHeight,\n        x2: quadrantLeft + quadrantWidth - halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHalfHeight\n      }\n    ];\n    return borderLines;\n  }\n  getTitle(showTitle) {\n    if (showTitle) {\n      return {\n        text: this.data.titleText,\n        fill: this.themeConfig.quadrantTitleFill,\n        fontSize: this.config.titleFontSize,\n        horizontalPos: \"top\",\n        verticalPos: \"center\",\n        rotation: 0,\n        y: this.config.titlePadding,\n        x: this.config.chartWidth / 2\n      };\n    }\n    return;\n  }\n  build() {\n    const showXAxis = this.config.showXAxis && !!(this.data.xAxisLeftText || this.data.xAxisRightText);\n    const showYAxis = this.config.showYAxis && !!(this.data.yAxisTopText || this.data.yAxisBottomText);\n    const showTitle = this.config.showTitle && !!this.data.titleText;\n    const xAxisPosition = this.data.points.length > 0 ? \"bottom\" : this.config.xAxisPosition;\n    const calculatedSpace = this.calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle);\n    return {\n      points: this.getQuadrantPoints(calculatedSpace),\n      quadrants: this.getQuadrants(calculatedSpace),\n      axisLabels: this.getAxisLabels(xAxisPosition, showXAxis, showYAxis, calculatedSpace),\n      borderLines: this.getBorders(calculatedSpace),\n      title: this.getTitle(showTitle)\n    };\n  }\n};\n\n// src/diagrams/quadrant-chart/utils.ts\nvar InvalidStyleError = class extends Error {\n  static {\n    __name(this, \"InvalidStyleError\");\n  }\n  constructor(style, value, type) {\n    super(`value for ${style} ${value} is invalid, please use a valid ${type}`);\n    this.name = \"InvalidStyleError\";\n  }\n};\nfunction validateHexCode(value) {\n  return !/^#?([\\dA-Fa-f]{6}|[\\dA-Fa-f]{3})$/.test(value);\n}\n__name(validateHexCode, \"validateHexCode\");\nfunction validateNumber(value) {\n  return !/^\\d+$/.test(value);\n}\n__name(validateNumber, \"validateNumber\");\nfunction validateSizeInPixels(value) {\n  return !/^\\d+px$/.test(value);\n}\n__name(validateSizeInPixels, \"validateSizeInPixels\");\n\n// src/diagrams/quadrant-chart/quadrantDb.ts\nvar config = getConfig();\nfunction textSanitizer(text) {\n  return sanitizeText(text.trim(), config);\n}\n__name(textSanitizer, \"textSanitizer\");\nvar quadrantBuilder = new QuadrantBuilder();\nfunction setQuadrant1Text(textObj) {\n  quadrantBuilder.setData({ quadrant1Text: textSanitizer(textObj.text) });\n}\n__name(setQuadrant1Text, \"setQuadrant1Text\");\nfunction setQuadrant2Text(textObj) {\n  quadrantBuilder.setData({ quadrant2Text: textSanitizer(textObj.text) });\n}\n__name(setQuadrant2Text, \"setQuadrant2Text\");\nfunction setQuadrant3Text(textObj) {\n  quadrantBuilder.setData({ quadrant3Text: textSanitizer(textObj.text) });\n}\n__name(setQuadrant3Text, \"setQuadrant3Text\");\nfunction setQuadrant4Text(textObj) {\n  quadrantBuilder.setData({ quadrant4Text: textSanitizer(textObj.text) });\n}\n__name(setQuadrant4Text, \"setQuadrant4Text\");\nfunction setXAxisLeftText(textObj) {\n  quadrantBuilder.setData({ xAxisLeftText: textSanitizer(textObj.text) });\n}\n__name(setXAxisLeftText, \"setXAxisLeftText\");\nfunction setXAxisRightText(textObj) {\n  quadrantBuilder.setData({ xAxisRightText: textSanitizer(textObj.text) });\n}\n__name(setXAxisRightText, \"setXAxisRightText\");\nfunction setYAxisTopText(textObj) {\n  quadrantBuilder.setData({ yAxisTopText: textSanitizer(textObj.text) });\n}\n__name(setYAxisTopText, \"setYAxisTopText\");\nfunction setYAxisBottomText(textObj) {\n  quadrantBuilder.setData({ yAxisBottomText: textSanitizer(textObj.text) });\n}\n__name(setYAxisBottomText, \"setYAxisBottomText\");\nfunction parseStyles(styles) {\n  const stylesObject = {};\n  for (const style of styles) {\n    const [key, value] = style.trim().split(/\\s*:\\s*/);\n    if (key === \"radius\") {\n      if (validateNumber(value)) {\n        throw new InvalidStyleError(key, value, \"number\");\n      }\n      stylesObject.radius = parseInt(value);\n    } else if (key === \"color\") {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, \"hex code\");\n      }\n      stylesObject.color = value;\n    } else if (key === \"stroke-color\") {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, \"hex code\");\n      }\n      stylesObject.strokeColor = value;\n    } else if (key === \"stroke-width\") {\n      if (validateSizeInPixels(value)) {\n        throw new InvalidStyleError(key, value, \"number of pixels (eg. 10px)\");\n      }\n      stylesObject.strokeWidth = value;\n    } else {\n      throw new Error(`style named ${key} is not supported.`);\n    }\n  }\n  return stylesObject;\n}\n__name(parseStyles, \"parseStyles\");\nfunction addPoint(textObj, className, x, y, styles) {\n  const stylesObject = parseStyles(styles);\n  quadrantBuilder.addPoints([\n    {\n      x,\n      y,\n      text: textSanitizer(textObj.text),\n      className,\n      ...stylesObject\n    }\n  ]);\n}\n__name(addPoint, \"addPoint\");\nfunction addClass(className, styles) {\n  quadrantBuilder.addClass(className, parseStyles(styles));\n}\n__name(addClass, \"addClass\");\nfunction setWidth(width) {\n  quadrantBuilder.setConfig({ chartWidth: width });\n}\n__name(setWidth, \"setWidth\");\nfunction setHeight(height) {\n  quadrantBuilder.setConfig({ chartHeight: height });\n}\n__name(setHeight, \"setHeight\");\nfunction getQuadrantData() {\n  const config2 = getConfig();\n  const { themeVariables, quadrantChart: quadrantChartConfig } = config2;\n  if (quadrantChartConfig) {\n    quadrantBuilder.setConfig(quadrantChartConfig);\n  }\n  quadrantBuilder.setThemeConfig({\n    quadrant1Fill: themeVariables.quadrant1Fill,\n    quadrant2Fill: themeVariables.quadrant2Fill,\n    quadrant3Fill: themeVariables.quadrant3Fill,\n    quadrant4Fill: themeVariables.quadrant4Fill,\n    quadrant1TextFill: themeVariables.quadrant1TextFill,\n    quadrant2TextFill: themeVariables.quadrant2TextFill,\n    quadrant3TextFill: themeVariables.quadrant3TextFill,\n    quadrant4TextFill: themeVariables.quadrant4TextFill,\n    quadrantPointFill: themeVariables.quadrantPointFill,\n    quadrantPointTextFill: themeVariables.quadrantPointTextFill,\n    quadrantXAxisTextFill: themeVariables.quadrantXAxisTextFill,\n    quadrantYAxisTextFill: themeVariables.quadrantYAxisTextFill,\n    quadrantExternalBorderStrokeFill: themeVariables.quadrantExternalBorderStrokeFill,\n    quadrantInternalBorderStrokeFill: themeVariables.quadrantInternalBorderStrokeFill,\n    quadrantTitleFill: themeVariables.quadrantTitleFill\n  });\n  quadrantBuilder.setData({ titleText: getDiagramTitle() });\n  return quadrantBuilder.build();\n}\n__name(getQuadrantData, \"getQuadrantData\");\nvar clear2 = /* @__PURE__ */ __name(function() {\n  quadrantBuilder.clear();\n  clear();\n}, \"clear\");\nvar quadrantDb_default = {\n  setWidth,\n  setHeight,\n  setQuadrant1Text,\n  setQuadrant2Text,\n  setQuadrant3Text,\n  setQuadrant4Text,\n  setXAxisLeftText,\n  setXAxisRightText,\n  setYAxisTopText,\n  setYAxisBottomText,\n  parseStyles,\n  addPoint,\n  addClass,\n  getQuadrantData,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription\n};\n\n// src/diagrams/quadrant-chart/quadrantRenderer.ts\nimport { select } from \"d3\";\nvar draw = /* @__PURE__ */ __name((txt, id, _version, diagObj) => {\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"hanging\" : \"middle\";\n  }\n  __name(getDominantBaseLine, \"getDominantBaseLine\");\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : \"middle\";\n  }\n  __name(getTextAnchor, \"getTextAnchor\");\n  function getTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  __name(getTransformation, \"getTransformation\");\n  const conf = getConfig();\n  log.debug(\"Rendering quadrant chart\\n\" + txt);\n  const securityLevel = conf.securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const width = conf.quadrantChart?.chartWidth ?? 500;\n  const height = conf.quadrantChart?.chartHeight ?? 500;\n  configureSvgSize(svg, height, width, conf.quadrantChart?.useMaxWidth ?? true);\n  svg.attr(\"viewBox\", \"0 0 \" + width + \" \" + height);\n  diagObj.db.setHeight(height);\n  diagObj.db.setWidth(width);\n  const quadrantData = diagObj.db.getQuadrantData();\n  const quadrantsGroup = group.append(\"g\").attr(\"class\", \"quadrants\");\n  const borderGroup = group.append(\"g\").attr(\"class\", \"border\");\n  const dataPointGroup = group.append(\"g\").attr(\"class\", \"data-points\");\n  const labelGroup = group.append(\"g\").attr(\"class\", \"labels\");\n  const titleGroup = group.append(\"g\").attr(\"class\", \"title\");\n  if (quadrantData.title) {\n    titleGroup.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", quadrantData.title.fill).attr(\"font-size\", quadrantData.title.fontSize).attr(\"dominant-baseline\", getDominantBaseLine(quadrantData.title.horizontalPos)).attr(\"text-anchor\", getTextAnchor(quadrantData.title.verticalPos)).attr(\"transform\", getTransformation(quadrantData.title)).text(quadrantData.title.text);\n  }\n  if (quadrantData.borderLines) {\n    borderGroup.selectAll(\"line\").data(quadrantData.borderLines).enter().append(\"line\").attr(\"x1\", (data) => data.x1).attr(\"y1\", (data) => data.y1).attr(\"x2\", (data) => data.x2).attr(\"y2\", (data) => data.y2).style(\"stroke\", (data) => data.strokeFill).style(\"stroke-width\", (data) => data.strokeWidth);\n  }\n  const quadrants = quadrantsGroup.selectAll(\"g.quadrant\").data(quadrantData.quadrants).enter().append(\"g\").attr(\"class\", \"quadrant\");\n  quadrants.append(\"rect\").attr(\"x\", (data) => data.x).attr(\"y\", (data) => data.y).attr(\"width\", (data) => data.width).attr(\"height\", (data) => data.height).attr(\"fill\", (data) => data.fill);\n  quadrants.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", (data) => data.text.fill).attr(\"font-size\", (data) => data.text.fontSize).attr(\n    \"dominant-baseline\",\n    (data) => getDominantBaseLine(data.text.horizontalPos)\n  ).attr(\"text-anchor\", (data) => getTextAnchor(data.text.verticalPos)).attr(\"transform\", (data) => getTransformation(data.text)).text((data) => data.text.text);\n  const labels = labelGroup.selectAll(\"g.label\").data(quadrantData.axisLabels).enter().append(\"g\").attr(\"class\", \"label\");\n  labels.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).text((data) => data.text).attr(\"fill\", (data) => data.fill).attr(\"font-size\", (data) => data.fontSize).attr(\"dominant-baseline\", (data) => getDominantBaseLine(data.horizontalPos)).attr(\"text-anchor\", (data) => getTextAnchor(data.verticalPos)).attr(\"transform\", (data) => getTransformation(data));\n  const dataPoints = dataPointGroup.selectAll(\"g.data-point\").data(quadrantData.points).enter().append(\"g\").attr(\"class\", \"data-point\");\n  dataPoints.append(\"circle\").attr(\"cx\", (data) => data.x).attr(\"cy\", (data) => data.y).attr(\"r\", (data) => data.radius).attr(\"fill\", (data) => data.fill).attr(\"stroke\", (data) => data.strokeColor).attr(\"stroke-width\", (data) => data.strokeWidth);\n  dataPoints.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).text((data) => data.text.text).attr(\"fill\", (data) => data.text.fill).attr(\"font-size\", (data) => data.text.fontSize).attr(\n    \"dominant-baseline\",\n    (data) => getDominantBaseLine(data.text.horizontalPos)\n  ).attr(\"text-anchor\", (data) => getTextAnchor(data.text.verticalPos)).attr(\"transform\", (data) => getTransformation(data.text));\n}, \"draw\");\nvar quadrantRenderer_default = {\n  draw\n};\n\n// src/diagrams/quadrant-chart/quadrantDiagram.ts\nvar diagram = {\n  parser: quadrant_default,\n  db: quadrantDb_default,\n  renderer: quadrantRenderer_default,\n  styles: /* @__PURE__ */ __name(() => \"\", \"styles\")\n};\nexport {\n  diagram\n};\n"], "names": ["parser", "o", "__name", "k", "v", "o2", "l", "length", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "$Vr", "$Vs", "$Vt", "$Vu", "$Vv", "$Vw", "$Vx", "$Vy", "$Vz", "$VA", "$VB", "$VC", "$VD", "$VE", "$VF", "$VG", "$VH", "$VI", "$VJ", "$VK", "$VL", "$VM", "$VN", "$VO", "$VP", "$VQ", "$VR", "$VS", "$VT", "$VU", "$VV", "$VW", "$VX", "$VY", "$VZ", "$V_", "$V$", "parser2", "trace", "yy", "symbols_", "terminals_", "productions_", "performAction", "yytext", "yyleng", "y<PERSON><PERSON>o", "yystate", "$$", "_$", "$0", "this", "$", "trim", "push", "addClass", "setDiagramTitle", "setAccTitle", "setAccDescription", "addSection", "substr", "addPoint", "setXAxisLeftText", "setXAxisRightText", "text", "setYAxisBottomText", "setYAxisTopText", "setQuadrant1Text", "setQuadrant2Text", "setQuadrant3Text", "setQuadrant4Text", "type", "table", "defaultActions", "parseError", "str", "hash", "recoverable", "error", "Error", "parse", "input", "self", "stack", "tstack", "vstack", "lstack", "recovering", "args", "slice", "call", "arguments", "lexer2", "Object", "create", "lexer", "sharedState", "prototype", "hasOwnProperty", "setInput", "yylloc", "yyloc", "ranges", "options", "lex", "token", "pop", "Array", "getPrototypeOf", "n", "symbol", "preErrorSymbol", "state", "action", "r", "p", "len", "newState", "expected", "yyval", "errStr", "showPosition", "join", "match", "line", "loc", "first_line", "last_line", "first_column", "last_column", "range", "apply", "concat", "EOF", "_input", "_more", "_backtrack", "done", "matched", "conditionStack", "offset", "ch", "unput", "lines", "split", "oldLines", "more", "reject", "backtrack_lexer", "less", "pastInput", "past", "replace", "upcomingInput", "next", "pre", "c", "test_match", "indexed_rule", "backup", "matches", "tempMatch", "index", "rules", "_currentRules", "i", "flex", "begin", "condition", "popState", "conditions", "topState", "Math", "abs", "pushState", "stateStackSize", "yy_", "$avoiding_name_collisions", "YY_START", "<PERSON><PERSON><PERSON>", "quadrant_default", "defaultThemeVariables", "getThemeVariables", "QuadrantBuilder", "constructor", "classes", "Map", "config", "getDefaultConfig", "themeConfig", "getDefaultThemeConfig", "data", "getDefaultData", "titleText", "quadrant1Text", "quadrant2Text", "quadrant3Text", "quadrant4Text", "xAxisLeftText", "xAxisRightText", "yAxisBottomText", "yAxisTopText", "points", "showXAxis", "showYAxis", "showTitle", "chartHeight", "defaultConfig_default", "quadrantChart", "chartWidth", "titlePadding", "titleFontSize", "quadrantPadding", "xAxisLabelPadding", "yAxisLabelPadding", "xAxisLabelFontSize", "yAxisLabelFontSize", "quadrantLabelFontSize", "quadrantTextTopPadding", "pointTextPadding", "pointLabelFontSize", "pointRadius", "xAxisPosition", "yAxisPosition", "quadrantInternalBorderStrokeWidth", "quadrantExternalBorderStrokeWidth", "quadrant1Fill", "quadrant2Fill", "quadrant3Fill", "quadrant4Fill", "quadrant1TextFill", "quadrant2TextFill", "quadrant3TextFill", "quadrant4TextFill", "quadrantPointFill", "quadrantPointTextFill", "quadrantXAxisTextFill", "quadrantYAxisTextFill", "quadrantTitleFill", "quadrantInternalBorderStrokeFill", "quadrantExternalBorderStrokeFill", "clear", "log", "info", "setData", "addPoints", "className", "styles", "set", "setConfig", "config2", "setThemeConfig", "calculateSpace", "xAxisSpaceCalculation", "xAxisSpace", "top", "bottom", "yAxisSpaceCalculation", "yAxisSpace", "left", "right", "titleSpaceCalculation", "titleSpace", "quadrantLeft", "quadrantTop", "quadrantWidth", "quadrantHeight", "quadrantSpace", "quadrantHalfWidth", "quadrantHalfHeight", "getAxisLabels", "spaceData", "drawXAxisLabelsInMiddle", "Boolean", "drawYAxisLabelsInMiddle", "axisLabels", "fill", "x", "y", "fontSize", "verticalPos", "horizontalPos", "rotation", "getQuadrants", "quadrants", "width", "height", "quadrant", "getQuadrantPoints", "xAxis", "scaleLinear", "domain", "yAxis", "map", "point", "classStyles", "get", "color", "radius", "strokeColor", "strokeWidth", "getBorders", "halfExternalBorderWidth", "strokeFill", "x1", "y1", "x2", "y2", "getTitle", "build", "calculatedSpace", "borderLines", "title", "InvalidStyleError", "style", "value", "super", "name", "validateHexCode", "test", "validateNumber", "validateSizeInPixels", "getConfig", "textSanitizer", "sanitizeText", "quadrantBuilder", "textObj", "parseStyles", "stylesObject", "key", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "setHeight", "getQuadrantData", "themeVariables", "quadrantChartConfig", "getDiagramTitle", "diagram", "db", "getAccTitle", "getAccDescription", "renderer", "draw", "txt", "id", "_version", "diagObj", "getDominantBaseLine", "getTextAnchor", "getTransformation", "conf", "debug", "securityLevel", "sandboxElement", "select", "svg", "nodes", "contentDocument", "body", "group", "append", "attr", "configureSvgSize", "useMaxWidth", "quadrantData", "quadrantsGroup", "borderGroup", "dataPointGroup", "labelGroup", "titleGroup", "selectAll", "enter", "dataPoints"], "sourceRoot": ""}