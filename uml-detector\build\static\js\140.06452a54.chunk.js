"use strict";(self.webpackChunkuml_detector=self.webpackChunkuml_detector||[]).push([[140],{888:(t,e,r)=>{r.d(e,{T:()=>s.T});var s=r(4472)},3140:(t,e,r)=>{r.d(e,{diagram:()=>fe});var s=r(594),a=r(4190),i=r(1984),n=r(6102),o=r(7551),l=r(4998),c=r(5710),d=r(6251),h=r(1804),g=r(888),u=function(){var t=(0,o.K2)((function(t,e,r,s){for(r=r||{},s=t.length;s--;r[t[s]]=e);return r}),"o"),e=[1,7],r=[1,13],s=[1,14],a=[1,15],i=[1,19],n=[1,16],l=[1,17],c=[1,18],d=[8,30],h=[8,21,28,29,30,31,32,40,44,47],g=[1,23],u=[1,24],p=[8,15,16,21,28,29,30,31,32,40,44,47],y=[8,15,16,21,27,28,29,30,31,32,40,44,47],b=[1,49],x={trace:(0,o.K2)((function(){}),"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,block:31,NODE_ID:32,nodeShapeNLabel:33,dirList:34,DIR:35,NODE_DSTART:36,NODE_DEND:37,BLOCK_ARROW_START:38,BLOCK_ARROW_END:39,classDef:40,CLASSDEF_ID:41,CLASSDEF_STYLEOPTS:42,DEFAULT:43,class:44,CLASSENTITY_IDS:45,STYLECLASS:46,style:47,STYLE_ENTITY_IDS:48,STYLE_DEFINITION_DATA:49,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"block",32:"NODE_ID",35:"DIR",36:"NODE_DSTART",37:"NODE_DEND",38:"BLOCK_ARROW_START",39:"BLOCK_ARROW_END",40:"classDef",41:"CLASSDEF_ID",42:"CLASSDEF_STYLEOPTS",43:"DEFAULT",44:"class",45:"CLASSENTITY_IDS",46:"STYLECLASS",47:"style",48:"STYLE_ENTITY_IDS",49:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],performAction:(0,o.K2)((function(t,e,r,s,a,i,n){var o=i.length-1;switch(a){case 4:s.getLogger().debug("Rule: separator (NL) ");break;case 5:s.getLogger().debug("Rule: separator (Space) ");break;case 6:s.getLogger().debug("Rule: separator (EOF) ");break;case 7:s.getLogger().debug("Rule: hierarchy: ",i[o-1]),s.setHierarchy(i[o-1]);break;case 8:s.getLogger().debug("Stop NL ");break;case 9:s.getLogger().debug("Stop EOF ");break;case 10:s.getLogger().debug("Stop NL2 ");break;case 11:s.getLogger().debug("Stop EOF2 ");break;case 12:s.getLogger().debug("Rule: statement: ",i[o]),"number"===typeof i[o].length?this.$=i[o]:this.$=[i[o]];break;case 13:s.getLogger().debug("Rule: statement #2: ",i[o-1]),this.$=[i[o-1]].concat(i[o]);break;case 14:s.getLogger().debug("Rule: link: ",i[o],t),this.$={edgeTypeStr:i[o],label:""};break;case 15:s.getLogger().debug("Rule: LABEL link: ",i[o-3],i[o-1],i[o]),this.$={edgeTypeStr:i[o],label:i[o-1]};break;case 18:const e=parseInt(i[o]),r=s.generateId();this.$={id:r,type:"space",label:"",width:e,children:[]};break;case 23:s.getLogger().debug("Rule: (nodeStatement link node) ",i[o-2],i[o-1],i[o]," typestr: ",i[o-1].edgeTypeStr);const a=s.edgeStrToEdgeData(i[o-1].edgeTypeStr);this.$=[{id:i[o-2].id,label:i[o-2].label,type:i[o-2].type,directions:i[o-2].directions},{id:i[o-2].id+"-"+i[o].id,start:i[o-2].id,end:i[o].id,label:i[o-1].label,type:"edge",directions:i[o].directions,arrowTypeEnd:a,arrowTypeStart:"arrow_open"},{id:i[o].id,label:i[o].label,type:s.typeStr2Type(i[o].typeStr),directions:i[o].directions}];break;case 24:s.getLogger().debug("Rule: nodeStatement (abc88 node size) ",i[o-1],i[o]),this.$={id:i[o-1].id,label:i[o-1].label,type:s.typeStr2Type(i[o-1].typeStr),directions:i[o-1].directions,widthInColumns:parseInt(i[o],10)};break;case 25:s.getLogger().debug("Rule: nodeStatement (node) ",i[o]),this.$={id:i[o].id,label:i[o].label,type:s.typeStr2Type(i[o].typeStr),directions:i[o].directions,widthInColumns:1};break;case 26:s.getLogger().debug("APA123",this?this:"na"),s.getLogger().debug("COLUMNS: ",i[o]),this.$={type:"column-setting",columns:"auto"===i[o]?-1:parseInt(i[o])};break;case 27:s.getLogger().debug("Rule: id-block statement : ",i[o-2],i[o-1]);s.generateId();this.$={...i[o-2],type:"composite",children:i[o-1]};break;case 28:s.getLogger().debug("Rule: blockStatement : ",i[o-2],i[o-1],i[o]);const n=s.generateId();this.$={id:n,type:"composite",label:"",children:i[o-1]};break;case 29:s.getLogger().debug("Rule: node (NODE_ID separator): ",i[o]),this.$={id:i[o]};break;case 30:s.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",i[o-1],i[o]),this.$={id:i[o-1],label:i[o].label,typeStr:i[o].typeStr,directions:i[o].directions};break;case 31:s.getLogger().debug("Rule: dirList: ",i[o]),this.$=[i[o]];break;case 32:s.getLogger().debug("Rule: dirList: ",i[o-1],i[o]),this.$=[i[o-1]].concat(i[o]);break;case 33:s.getLogger().debug("Rule: nodeShapeNLabel: ",i[o-2],i[o-1],i[o]),this.$={typeStr:i[o-2]+i[o],label:i[o-1]};break;case 34:s.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",i[o-3],i[o-2]," #3:",i[o-1],i[o]),this.$={typeStr:i[o-3]+i[o],label:i[o-2],directions:i[o-1]};break;case 35:case 36:this.$={type:"classDef",id:i[o-1].trim(),css:i[o].trim()};break;case 37:this.$={type:"applyClass",id:i[o-1].trim(),styleClass:i[o].trim()};break;case 38:this.$={type:"applyStyles",id:i[o-1].trim(),stylesStr:i[o].trim()}}}),"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:e,22:8,23:9,24:10,25:11,26:12,28:r,29:s,31:a,32:i,40:n,44:l,47:c},{8:[1,20]},t(d,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:e,28:r,29:s,31:a,32:i,40:n,44:l,47:c}),t(h,[2,16],{14:22,15:g,16:u}),t(h,[2,17]),t(h,[2,18]),t(h,[2,19]),t(h,[2,20]),t(h,[2,21]),t(h,[2,22]),t(p,[2,25],{27:[1,25]}),t(h,[2,26]),{19:26,26:12,32:i},{11:27,13:4,19:5,20:6,21:e,22:8,23:9,24:10,25:11,26:12,28:r,29:s,31:a,32:i,40:n,44:l,47:c},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},t(y,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},t(d,[2,13]),{26:35,32:i},{32:[2,14]},{17:[1,36]},t(p,[2,24]),{11:37,13:4,14:22,15:g,16:u,19:5,20:6,21:e,22:8,23:9,24:10,25:11,26:12,28:r,29:s,31:a,32:i,40:n,44:l,47:c},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},t(y,[2,30]),{18:[1,43]},{18:[1,44]},t(p,[2,23]),{18:[1,45]},{30:[1,46]},t(h,[2,28]),t(h,[2,35]),t(h,[2,36]),t(h,[2,37]),t(h,[2,38]),{37:[1,47]},{34:48,35:b},{15:[1,50]},t(h,[2,27]),t(y,[2,33]),{39:[1,51]},{34:52,35:b,39:[2,31]},{32:[2,15]},t(y,[2,34]),{39:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:(0,o.K2)((function(t,e){if(!e.recoverable){var r=new Error(t);throw r.hash=e,r}this.trace(t)}),"parseError"),parse:(0,o.K2)((function(t){var e=this,r=[0],s=[],a=[null],i=[],n=this.table,l="",c=0,d=0,h=0,g=i.slice.call(arguments,1),u=Object.create(this.lexer),p={yy:{}};for(var y in this.yy)Object.prototype.hasOwnProperty.call(this.yy,y)&&(p.yy[y]=this.yy[y]);u.setInput(t,p.yy),p.yy.lexer=u,p.yy.parser=this,"undefined"==typeof u.yylloc&&(u.yylloc={});var b=u.yylloc;i.push(b);var x=u.options&&u.options.ranges;function f(){var t;return"number"!==typeof(t=s.pop()||u.lex()||1)&&(t instanceof Array&&(t=(s=t).pop()),t=e.symbols_[t]||t),t}"function"===typeof p.yy.parseError?this.parseError=p.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,(0,o.K2)((function(t){r.length=r.length-2*t,a.length=a.length-t,i.length=i.length-t}),"popStack"),(0,o.K2)(f,"lex");for(var m,w,_,L,k,S,v,E,D,C={};;){if(_=r[r.length-1],this.defaultActions[_]?L=this.defaultActions[_]:(null!==m&&"undefined"!=typeof m||(m=f()),L=n[_]&&n[_][m]),"undefined"===typeof L||!L.length||!L[0]){var R="";for(S in D=[],n[_])this.terminals_[S]&&S>2&&D.push("'"+this.terminals_[S]+"'");R=u.showPosition?"Parse error on line "+(c+1)+":\n"+u.showPosition()+"\nExpecting "+D.join(", ")+", got '"+(this.terminals_[m]||m)+"'":"Parse error on line "+(c+1)+": Unexpected "+(1==m?"end of input":"'"+(this.terminals_[m]||m)+"'"),this.parseError(R,{text:u.match,token:this.terminals_[m]||m,line:u.yylineno,loc:b,expected:D})}if(L[0]instanceof Array&&L.length>1)throw new Error("Parse Error: multiple actions possible at state: "+_+", token: "+m);switch(L[0]){case 1:r.push(m),a.push(u.yytext),i.push(u.yylloc),r.push(L[1]),m=null,w?(m=w,w=null):(d=u.yyleng,l=u.yytext,c=u.yylineno,b=u.yylloc,h>0&&h--);break;case 2:if(v=this.productions_[L[1]][1],C.$=a[a.length-v],C._$={first_line:i[i.length-(v||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-(v||1)].first_column,last_column:i[i.length-1].last_column},x&&(C._$.range=[i[i.length-(v||1)].range[0],i[i.length-1].range[1]]),"undefined"!==typeof(k=this.performAction.apply(C,[l,d,c,p.yy,L[1],a,i].concat(g))))return k;v&&(r=r.slice(0,-1*v*2),a=a.slice(0,-1*v),i=i.slice(0,-1*v)),r.push(this.productions_[L[1]][0]),a.push(C.$),i.push(C._$),E=n[r[r.length-2]][r[r.length-1]],r.push(E);break;case 3:return!0}}return!0}),"parse")},f=function(){return{EOF:1,parseError:(0,o.K2)((function(t,e){if(!this.yy.parser)throw new Error(t);this.yy.parser.parseError(t,e)}),"parseError"),setInput:(0,o.K2)((function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this}),"setInput"),input:(0,o.K2)((function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t}),"input"),unput:(0,o.K2)((function(t){var e=t.length,r=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var s=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),r.length-1&&(this.yylineno-=r.length-1);var a=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:r?(r.length===s.length?this.yylloc.first_column:0)+s[s.length-r.length].length-r[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[a[0],a[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this}),"unput"),more:(0,o.K2)((function(){return this._more=!0,this}),"more"),reject:(0,o.K2)((function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}),"reject"),less:(0,o.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,o.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,o.K2)((function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,o.K2)((function(){var t=this.pastInput(),e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,o.K2)((function(t,e){var r,s,a;if(this.options.backtrack_lexer&&(a={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(a.yylloc.range=this.yylloc.range.slice(0))),(s=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=s.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:s?s[s.length-1].length-s[s.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],r=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),r)return r;if(this._backtrack){for(var i in a)this[i]=a[i];return!1}return!1}),"test_match"),next:(0,o.K2)((function(){if(this.done)return this.EOF;var t,e,r,s;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var a=this._currentRules(),i=0;i<a.length;i++)if((r=this._input.match(this.rules[a[i]]))&&(!e||r[0].length>e[0].length)){if(e=r,s=i,this.options.backtrack_lexer){if(!1!==(t=this.test_match(r,a[i])))return t;if(this._backtrack){e=!1;continue}return!1}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,a[s]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}),"next"),lex:(0,o.K2)((function(){var t=this.next();return t||this.lex()}),"lex"),begin:(0,o.K2)((function(t){this.conditionStack.push(t)}),"begin"),popState:(0,o.K2)((function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]}),"popState"),_currentRules:(0,o.K2)((function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules}),"_currentRules"),topState:(0,o.K2)((function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"}),"topState"),pushState:(0,o.K2)((function(t){this.begin(t)}),"pushState"),stateStackSize:(0,o.K2)((function(){return this.conditionStack.length}),"stateStackSize"),options:{},performAction:(0,o.K2)((function(t,e,r,s){switch(r){case 0:return 10;case 1:return t.getLogger().debug("Found space-block"),31;case 2:return t.getLogger().debug("Found nl-block"),31;case 3:return t.getLogger().debug("Found space-block"),29;case 4:t.getLogger().debug(".",e.yytext);break;case 5:t.getLogger().debug("_",e.yytext);break;case 6:return 5;case 7:return e.yytext=-1,28;case 8:return e.yytext=e.yytext.replace(/columns\s+/,""),t.getLogger().debug("COLUMNS (LEX)",e.yytext),28;case 9:case 77:case 78:case 100:this.pushState("md_string");break;case 10:return"MD_STR";case 11:case 35:case 80:this.popState();break;case 12:this.pushState("string");break;case 13:t.getLogger().debug("LEX: POPPING STR:",e.yytext),this.popState();break;case 14:return t.getLogger().debug("LEX: STR end:",e.yytext),"STR";case 15:return e.yytext=e.yytext.replace(/space\:/,""),t.getLogger().debug("SPACE NUM (LEX)",e.yytext),21;case 16:return e.yytext="1",t.getLogger().debug("COLUMNS (LEX)",e.yytext),21;case 17:return 43;case 18:return"LINKSTYLE";case 19:return"INTERPOLATE";case 20:return this.pushState("CLASSDEF"),40;case 21:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";case 22:return this.popState(),this.pushState("CLASSDEFID"),41;case 23:return this.popState(),42;case 24:return this.pushState("CLASS"),44;case 25:return this.popState(),this.pushState("CLASS_STYLE"),45;case 26:return this.popState(),46;case 27:return this.pushState("STYLE_STMNT"),47;case 28:return this.popState(),this.pushState("STYLE_DEFINITION"),48;case 29:return this.popState(),49;case 30:return this.pushState("acc_title"),"acc_title";case 31:return this.popState(),"acc_title_value";case 32:return this.pushState("acc_descr"),"acc_descr";case 33:return this.popState(),"acc_descr_value";case 34:this.pushState("acc_descr_multiline");break;case 36:return"acc_descr_multiline_value";case 37:return 30;case 38:case 39:case 41:case 42:case 45:return this.popState(),t.getLogger().debug("Lex: (("),"NODE_DEND";case 40:return this.popState(),t.getLogger().debug("Lex: ))"),"NODE_DEND";case 43:return this.popState(),t.getLogger().debug("Lex: (-"),"NODE_DEND";case 44:return this.popState(),t.getLogger().debug("Lex: -)"),"NODE_DEND";case 46:return this.popState(),t.getLogger().debug("Lex: ]]"),"NODE_DEND";case 47:return this.popState(),t.getLogger().debug("Lex: ("),"NODE_DEND";case 48:return this.popState(),t.getLogger().debug("Lex: ])"),"NODE_DEND";case 49:case 50:return this.popState(),t.getLogger().debug("Lex: /]"),"NODE_DEND";case 51:return this.popState(),t.getLogger().debug("Lex: )]"),"NODE_DEND";case 52:return this.popState(),t.getLogger().debug("Lex: )"),"NODE_DEND";case 53:return this.popState(),t.getLogger().debug("Lex: ]>"),"NODE_DEND";case 54:return this.popState(),t.getLogger().debug("Lex: ]"),"NODE_DEND";case 55:return t.getLogger().debug("Lexa: -)"),this.pushState("NODE"),36;case 56:return t.getLogger().debug("Lexa: (-"),this.pushState("NODE"),36;case 57:return t.getLogger().debug("Lexa: ))"),this.pushState("NODE"),36;case 58:case 60:case 61:case 62:case 65:return t.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 59:return t.getLogger().debug("Lex: ((("),this.pushState("NODE"),36;case 63:return t.getLogger().debug("Lexc: >"),this.pushState("NODE"),36;case 64:return t.getLogger().debug("Lexa: (["),this.pushState("NODE"),36;case 66:case 67:case 68:case 69:case 70:case 71:case 72:return this.pushState("NODE"),36;case 73:return t.getLogger().debug("Lexa: ["),this.pushState("NODE"),36;case 74:return this.pushState("BLOCK_ARROW"),t.getLogger().debug("LEX ARR START"),38;case 75:return t.getLogger().debug("Lex: NODE_ID",e.yytext),32;case 76:return t.getLogger().debug("Lex: EOF",e.yytext),8;case 79:return"NODE_DESCR";case 81:t.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 82:t.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 83:return t.getLogger().debug("LEX: NODE_DESCR:",e.yytext),"NODE_DESCR";case 84:t.getLogger().debug("LEX POPPING"),this.popState();break;case 85:t.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 86:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (right): dir:",e.yytext),"DIR";case 87:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (left):",e.yytext),"DIR";case 88:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (x):",e.yytext),"DIR";case 89:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (y):",e.yytext),"DIR";case 90:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (up):",e.yytext),"DIR";case 91:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (down):",e.yytext),"DIR";case 92:return e.yytext="]>",t.getLogger().debug("Lex (ARROW_DIR end):",e.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";case 93:return t.getLogger().debug("Lex: LINK","#"+e.yytext+"#"),15;case 94:case 95:case 96:return t.getLogger().debug("Lex: LINK",e.yytext),15;case 97:case 98:case 99:return t.getLogger().debug("Lex: START_LINK",e.yytext),this.pushState("LLABEL"),16;case 101:return t.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";case 102:return this.popState(),t.getLogger().debug("Lex: LINK","#"+e.yytext+"#"),15;case 103:case 104:return this.popState(),t.getLogger().debug("Lex: LINK",e.yytext),15;case 105:return t.getLogger().debug("Lex: COLON",e.yytext),e.yytext=e.yytext.slice(1),27}}),"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block\s+)/,/^(?:block\n+)/,/^(?:block:)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[29],inclusive:!1},STYLE_STMNT:{rules:[28],inclusive:!1},CLASSDEFID:{rules:[23],inclusive:!1},CLASSDEF:{rules:[21,22],inclusive:!1},CLASS_STYLE:{rules:[26],inclusive:!1},CLASS:{rules:[25],inclusive:!1},LLABEL:{rules:[100,101,102,103,104],inclusive:!1},ARROW_DIR:{rules:[86,87,88,89,90,91,92],inclusive:!1},BLOCK_ARROW:{rules:[77,82,85],inclusive:!1},NODE:{rules:[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],inclusive:!1},md_string:{rules:[10,11,79,80],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[13,14,83,84],inclusive:!1},acc_descr_multiline:{rules:[35,36],inclusive:!1},acc_descr:{rules:[33],inclusive:!1},acc_title:{rules:[31],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],inclusive:!0}}}}();function m(){this.yy={}}return x.lexer=f,(0,o.K2)(m,"Parser"),m.prototype=x,x.Parser=m,new m}();u.parser=u;var p=u,y=new Map,b=[],x=new Map,f="color",m="fill",w=(0,o.D7)(),_=new Map,L=(0,o.K2)((t=>o.Y2.sanitizeText(t,w)),"sanitizeText"),k=(0,o.K2)((function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=_.get(t);r||(r={id:t,styles:[],textStyles:[]},_.set(t,r)),void 0!==e&&null!==e&&e.split(",").forEach((t=>{const e=t.replace(/([^;]*);/,"$1").trim();if(RegExp(f).exec(t)){const t=e.replace(m,"bgFill").replace(f,m);r.textStyles.push(t)}r.styles.push(e)}))}),"addStyleClass"),S=(0,o.K2)((function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";const r=y.get(t);void 0!==e&&null!==e&&(r.styles=e.split(","))}),"addStyle2Node"),v=(0,o.K2)((function(t,e){t.split(",").forEach((function(t){let r=y.get(t);if(void 0===r){const e=t.trim();r={id:e,type:"na",children:[]},y.set(e,r)}r.classes||(r.classes=[]),r.classes.push(e)}))}),"setCssClass"),E=(0,o.K2)(((t,e)=>{const r=t.flat(),s=[];for(const a of r)if(a.label&&(a.label=L(a.label)),"classDef"!==a.type)if("applyClass"!==a.type)if("applyStyles"!==a.type)if("column-setting"===a.type)e.columns=a.columns??-1;else if("edge"===a.type){const t=(x.get(a.id)??0)+1;x.set(a.id,t),a.id=t+"-"+a.id,b.push(a)}else{a.label||("composite"===a.type?a.label="":a.label=a.id);const t=y.get(a.id);if(void 0===t?y.set(a.id,a):("na"!==a.type&&(t.type=a.type),a.label!==a.id&&(t.label=a.label)),a.children&&E(a.children,a),"space"===a.type){const t=a.width??1;for(let e=0;e<t;e++){const t=(0,l.A)(a);t.id=t.id+"-"+e,y.set(t.id,t),s.push(t)}}else void 0===t&&s.push(a)}else a?.stylesStr&&S(a.id,a?.stylesStr);else v(a.id,a?.styleClass??"");else k(a.id,a.css);e.children=s}),"populateBlockDatabase"),D=[],C={id:"root",type:"composite",children:[],columns:-1},R=(0,o.K2)((()=>{o.Rm.debug("Clear called"),(0,o.IU)(),C={id:"root",type:"composite",children:[],columns:-1},y=new Map([["root",C]]),D=[],_=new Map,b=[],x=new Map}),"clear");function K(t){switch(o.Rm.debug("typeStr2Type",t),t){case"[]":return"square";case"()":return o.Rm.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}function N(t){return o.Rm.debug("typeStr2Type",t),"=="===t?"thick":"normal"}function T(t){switch(t.trim()){case"--x":return"arrow_cross";case"--o":return"arrow_circle";default:return"arrow_point"}}(0,o.K2)(K,"typeStr2Type"),(0,o.K2)(N,"edgeTypeStr2Type"),(0,o.K2)(T,"edgeStrToEdgeData");var $=0,A=(0,o.K2)((()=>($++,"id-"+Math.random().toString(36).substr(2,12)+"-"+$)),"generateId"),I=(0,o.K2)((t=>{C.children=t,E(t,C),D=C.children}),"setHierarchy"),O=(0,o.K2)((t=>{const e=y.get(t);return e?e.columns?e.columns:e.children?e.children.length:-1:-1}),"getColumns"),B=(0,o.K2)((()=>[...y.values()]),"getBlocksFlat"),z=(0,o.K2)((()=>D||[]),"getBlocks"),M=(0,o.K2)((()=>b),"getEdges"),P=(0,o.K2)((t=>y.get(t)),"getBlock"),Y=(0,o.K2)((t=>{y.set(t.id,t)}),"setBlock"),F=(0,o.K2)((()=>console),"getLogger"),j=(0,o.K2)((function(){return _}),"getClasses"),W={getConfig:(0,o.K2)((()=>(0,o.zj)().block),"getConfig"),typeStr2Type:K,edgeTypeStr2Type:N,edgeStrToEdgeData:T,getLogger:F,getBlocksFlat:B,getBlocks:z,getEdges:M,setHierarchy:I,getBlock:P,setBlock:Y,getColumns:O,getClasses:j,clear:R,generateId:A},X=(0,o.K2)(((t,e)=>{const r=c.A,s=r(t,"r"),a=r(t,"g"),i=r(t,"b");return d.A(s,a,i,e)}),"fade"),H=(0,o.K2)((t=>`.label {\n    font-family: ${t.fontFamily};\n    color: ${t.nodeTextColor||t.textColor};\n  }\n  .cluster-label text {\n    fill: ${t.titleColor};\n  }\n  .cluster-label span,p {\n    color: ${t.titleColor};\n  }\n\n\n\n  .label text,span,p {\n    fill: ${t.nodeTextColor||t.textColor};\n    color: ${t.nodeTextColor||t.textColor};\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${t.mainBkg};\n    stroke: ${t.nodeBorder};\n    stroke-width: 1px;\n  }\n  .flowchart-label text {\n    text-anchor: middle;\n  }\n  // .flowchart-label .text-outer-tspan {\n  //   text-anchor: middle;\n  // }\n  // .flowchart-label .text-inner-tspan {\n  //   text-anchor: start;\n  // }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${t.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${t.lineColor};\n    stroke-width: 2.0px;\n  }\n\n  .flowchart-link {\n    stroke: ${t.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${t.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n      background-color: ${t.edgeLabelBackground};\n      fill: ${t.edgeLabelBackground};\n    }\n    text-align: center;\n  }\n\n  /* For html labels only */\n  .labelBkg {\n    background-color: ${X(t.edgeLabelBackground,.5)};\n    // background-color:\n  }\n\n  .node .cluster {\n    // fill: ${X(t.mainBkg,.5)};\n    fill: ${X(t.clusterBkg,.5)};\n    stroke: ${X(t.clusterBorder,.2)};\n    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;\n    stroke-width: 1px;\n  }\n\n  .cluster text {\n    fill: ${t.titleColor};\n  }\n\n  .cluster span,p {\n    color: ${t.titleColor};\n  }\n  /* .cluster div {\n    color: ${t.titleColor};\n  } */\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${t.fontFamily};\n    font-size: 12px;\n    background: ${t.tertiaryColor};\n    border: 1px solid ${t.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .flowchartTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${t.textColor};\n  }\n`),"getStyles"),U=(0,o.K2)(((t,e,r,s)=>{e.forEach((e=>{Z[e](t,r,s)}))}),"insertMarkers"),Z={extension:(0,o.K2)(((t,e,r)=>{o.Rm.trace("Making markers for ",r),t.append("defs").append("marker").attr("id",r+"_"+e+"-extensionStart").attr("class","marker extension "+e).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),t.append("defs").append("marker").attr("id",r+"_"+e+"-extensionEnd").attr("class","marker extension "+e).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")}),"extension"),composition:(0,o.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-compositionStart").attr("class","marker composition "+e).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),t.append("defs").append("marker").attr("id",r+"_"+e+"-compositionEnd").attr("class","marker composition "+e).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")}),"composition"),aggregation:(0,o.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-aggregationStart").attr("class","marker aggregation "+e).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),t.append("defs").append("marker").attr("id",r+"_"+e+"-aggregationEnd").attr("class","marker aggregation "+e).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")}),"aggregation"),dependency:(0,o.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-dependencyStart").attr("class","marker dependency "+e).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),t.append("defs").append("marker").attr("id",r+"_"+e+"-dependencyEnd").attr("class","marker dependency "+e).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")}),"dependency"),lollipop:(0,o.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-lollipopStart").attr("class","marker lollipop "+e).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),t.append("defs").append("marker").attr("id",r+"_"+e+"-lollipopEnd").attr("class","marker lollipop "+e).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)}),"lollipop"),point:(0,o.K2)(((t,e,r)=>{t.append("marker").attr("id",r+"_"+e+"-pointEnd").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),t.append("marker").attr("id",r+"_"+e+"-pointStart").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")}),"point"),circle:(0,o.K2)(((t,e,r)=>{t.append("marker").attr("id",r+"_"+e+"-circleEnd").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),t.append("marker").attr("id",r+"_"+e+"-circleStart").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")}),"circle"),cross:(0,o.K2)(((t,e,r)=>{t.append("marker").attr("id",r+"_"+e+"-crossEnd").attr("class","marker cross "+e).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),t.append("marker").attr("id",r+"_"+e+"-crossStart").attr("class","marker cross "+e).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")}),"cross"),barb:(0,o.K2)(((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")}),"barb")},q=U,G=(0,o.D7)()?.block?.padding??8;function J(t,e){if(0===t||!Number.isInteger(t))throw new Error("Columns must be an integer !== 0.");if(e<0||!Number.isInteger(e))throw new Error("Position must be a non-negative integer."+e);if(t<0)return{px:e,py:0};if(1===t)return{px:0,py:e};return{px:e%t,py:Math.floor(e/t)}}(0,o.K2)(J,"calculateBlockPosition");var V=(0,o.K2)((t=>{let e=0,r=0;for(const s of t.children){const{width:a,height:i,x:n,y:l}=s.size??{width:0,height:0,x:0,y:0};o.Rm.debug("getMaxChildSize abc95 child:",s.id,"width:",a,"height:",i,"x:",n,"y:",l,s.type),"space"!==s.type&&(a>e&&(e=a/(t.widthInColumns??1)),i>r&&(r=i))}return{width:e,height:r}}),"getMaxChildSize");function Q(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;o.Rm.debug("setBlockSizes abc95 (start)",t.id,t?.size?.x,"block width =",t?.size,"sieblingWidth",r),t?.size?.width||(t.size={width:r,height:s,x:0,y:0});let a=0,i=0;if(t.children?.length>0){for(const r of t.children)Q(r,e);const n=V(t);a=n.width,i=n.height,o.Rm.debug("setBlockSizes abc95 maxWidth of",t.id,":s children is ",a,i);for(const e of t.children)e.size&&(o.Rm.debug(`abc95 Setting size of children of ${t.id} id=${e.id} ${a} ${i} ${JSON.stringify(e.size)}`),e.size.width=a*(e.widthInColumns??1)+G*((e.widthInColumns??1)-1),e.size.height=i,e.size.x=0,e.size.y=0,o.Rm.debug(`abc95 updating size of ${t.id} children child:${e.id} maxWidth:${a} maxHeight:${i}`));for(const r of t.children)Q(r,e,a,i);const l=t.columns??-1;let c=0;for(const e of t.children)c+=e.widthInColumns??1;let d=t.children.length;l>0&&l<c&&(d=l);const h=Math.ceil(c/d);let g=d*(a+G)+G,u=h*(i+G)+G;if(g<r){o.Rm.debug(`Detected to small siebling: abc95 ${t.id} sieblingWidth ${r} sieblingHeight ${s} width ${g}`),g=r,u=s;const e=(r-d*G-G)/d,n=(s-h*G-G)/h;o.Rm.debug("Size indata abc88",t.id,"childWidth",e,"maxWidth",a),o.Rm.debug("Size indata abc88",t.id,"childHeight",n,"maxHeight",i),o.Rm.debug("Size indata abc88 xSize",d,"padding",G);for(const r of t.children)r.size&&(r.size.width=e,r.size.height=n,r.size.x=0,r.size.y=0)}if(o.Rm.debug(`abc95 (finale calc) ${t.id} xSize ${d} ySize ${h} columns ${l}${t.children.length} width=${Math.max(g,t.size?.width||0)}`),g<(t?.size?.width||0)){g=t?.size?.width||0;const e=l>0?Math.min(t.children.length,l):t.children.length;if(e>0){const r=(g-e*G-G)/e;o.Rm.debug("abc95 (growing to fit) width",t.id,g,t.size?.width,r);for(const e of t.children)e.size&&(e.size.width=r)}}t.size={width:g,height:u,x:0,y:0}}o.Rm.debug("setBlockSizes abc94 (done)",t.id,t?.size?.x,t?.size?.width,t?.size?.y,t?.size?.height)}function tt(t,e){o.Rm.debug(`abc85 layout blocks (=>layoutBlocks) ${t.id} x: ${t?.size?.x} y: ${t?.size?.y} width: ${t?.size?.width}`);const r=t.columns??-1;if(o.Rm.debug("layoutBlocks columns abc95",t.id,"=>",r,t),t.children&&t.children.length>0){const s=t?.children[0]?.size?.width??0,a=t.children.length*s+(t.children.length-1)*G;o.Rm.debug("widthOfChildren 88",a,"posX");let i=0;o.Rm.debug("abc91 block?.size?.x",t.id,t?.size?.x);let n=t?.size?.x?t?.size?.x+(-t?.size?.width/2||0):-G,l=0;for(const c of t.children){const s=t;if(!c.size)continue;const{width:a,height:d}=c.size,{px:h,py:g}=J(r,i);if(g!=l&&(l=g,n=t?.size?.x?t?.size?.x+(-t?.size?.width/2||0):-G,o.Rm.debug("New row in layout for block",t.id," and child ",c.id,l)),o.Rm.debug(`abc89 layout blocks (child) id: ${c.id} Pos: ${i} (px, py) ${h},${g} (${s?.size?.x},${s?.size?.y}) parent: ${s.id} width: ${a}${G}`),s.size){const t=a/2;c.size.x=n+G+t,o.Rm.debug(`abc91 layout blocks (calc) px, pyid:${c.id} startingPos=X${n} new startingPosX${c.size.x} ${t} padding=${G} width=${a} halfWidth=${t} => x:${c.size.x} y:${c.size.y} ${c.widthInColumns} (width * (child?.w || 1)) / 2 ${a*(c?.widthInColumns??1)/2}`),n=c.size.x+t,c.size.y=s.size.y-s.size.height/2+g*(d+G)+d/2+G,o.Rm.debug(`abc88 layout blocks (calc) px, pyid:${c.id}startingPosX${n}${G}${t}=>x:${c.size.x}y:${c.size.y}${c.widthInColumns}(width * (child?.w || 1)) / 2${a*(c?.widthInColumns??1)/2}`)}c.children&&tt(c,e),i+=c?.widthInColumns??1,o.Rm.debug("abc88 columnsPos",c,i)}}o.Rm.debug(`layout blocks (<==layoutBlocks) ${t.id} x: ${t?.size?.x} y: ${t?.size?.y} width: ${t?.size?.width}`)}function et(t){let{minX:e,minY:r,maxX:s,maxY:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{minX:0,minY:0,maxX:0,maxY:0};if(t.size&&"root"!==t.id){const{x:i,y:n,width:o,height:l}=t.size;i-o/2<e&&(e=i-o/2),n-l/2<r&&(r=n-l/2),i+o/2>s&&(s=i+o/2),n+l/2>a&&(a=n+l/2)}if(t.children)for(const i of t.children)({minX:e,minY:r,maxX:s,maxY:a}=et(i,{minX:e,minY:r,maxX:s,maxY:a}));return{minX:e,minY:r,maxX:s,maxY:a}}function rt(t){const e=t.getBlock("root");if(!e)return;Q(e,t,0,0),tt(e,t),o.Rm.debug("getBlocks",JSON.stringify(e,null,2));const{minX:r,minY:s,maxX:a,maxY:i}=et(e);return{x:r,y:s,width:a-r,height:i-s}}function st(t,e){e&&t.attr("style",e)}function at(t){const e=(0,h.Ltv)(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),r=e.append("xhtml:div"),s=t.label,a=t.isNode?"nodeLabel":"edgeLabel",i=r.append("span");return i.html(s),st(i,t.labelStyle),i.attr("class",a),st(r,t.labelStyle),r.style("display","inline-block"),r.style("white-space","nowrap"),r.attr("xmlns","http://www.w3.org/1999/xhtml"),e.node()}(0,o.K2)(Q,"setBlockSizes"),(0,o.K2)(tt,"layoutBlocks"),(0,o.K2)(et,"findBounds"),(0,o.K2)(rt,"layout"),(0,o.K2)(st,"applyStyle"),(0,o.K2)(at,"addHtmlLabel");var it=(0,o.K2)(((t,e,r,s)=>{let a=t||"";if("object"===typeof a&&(a=a[0]),(0,o._3)((0,o.D7)().flowchart.htmlLabels)){a=a.replace(/\\n|\n/g,"<br />"),o.Rm.debug("vertexText"+a);return at({isNode:s,label:(0,i.hE)((0,n.Sm)(a)),labelStyle:e.replace("fill:","color:")})}{const t=document.createElementNS("http://www.w3.org/2000/svg","text");t.setAttribute("style",e.replace("color:","fill:"));let s=[];s="string"===typeof a?a.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(a)?a:[];for(const e of s){const s=document.createElementNS("http://www.w3.org/2000/svg","tspan");s.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),s.setAttribute("dy","1em"),s.setAttribute("x","0"),r?s.setAttribute("class","title-row"):s.setAttribute("class","row"),s.textContent=e.trim(),t.appendChild(s)}return t}}),"createLabel"),nt=(0,o.K2)(((t,e,r,s,a)=>{e.arrowTypeStart&&lt(t,"start",e.arrowTypeStart,r,s,a),e.arrowTypeEnd&&lt(t,"end",e.arrowTypeEnd,r,s,a)}),"addEdgeMarkers"),ot={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},lt=(0,o.K2)(((t,e,r,s,a,i)=>{const n=ot[r];if(!n)return void o.Rm.warn(`Unknown arrow type: ${r}`);const l="start"===e?"Start":"End";t.attr(`marker-${e}`,`url(${s}#${a}_${i}-${n}${l})`)}),"addEdgeMarker"),ct={},dt={},ht=(0,o.K2)(((t,e)=>{const r=(0,o.D7)(),s=(0,o._3)(r.flowchart.htmlLabels),a="markdown"===e.labelType?(0,i.GZ)(t,e.label,{style:e.labelStyle,useHtmlLabels:s,addSvgBackground:!0},r):it(e.label,e.labelStyle),n=t.insert("g").attr("class","edgeLabel"),l=n.insert("g").attr("class","label");l.node().appendChild(a);let c,d=a.getBBox();if(s){const t=a.children[0],e=(0,h.Ltv)(a);d=t.getBoundingClientRect(),e.attr("width",d.width),e.attr("height",d.height)}if(l.attr("transform","translate("+-d.width/2+", "+-d.height/2+")"),ct[e.id]=n,e.width=d.width,e.height=d.height,e.startLabelLeft){const r=it(e.startLabelLeft,e.labelStyle),s=t.insert("g").attr("class","edgeTerminals"),a=s.insert("g").attr("class","inner");c=a.node().appendChild(r);const i=r.getBBox();a.attr("transform","translate("+-i.width/2+", "+-i.height/2+")"),dt[e.id]||(dt[e.id]={}),dt[e.id].startLeft=s,gt(c,e.startLabelLeft)}if(e.startLabelRight){const r=it(e.startLabelRight,e.labelStyle),s=t.insert("g").attr("class","edgeTerminals"),a=s.insert("g").attr("class","inner");c=s.node().appendChild(r),a.node().appendChild(r);const i=r.getBBox();a.attr("transform","translate("+-i.width/2+", "+-i.height/2+")"),dt[e.id]||(dt[e.id]={}),dt[e.id].startRight=s,gt(c,e.startLabelRight)}if(e.endLabelLeft){const r=it(e.endLabelLeft,e.labelStyle),s=t.insert("g").attr("class","edgeTerminals"),a=s.insert("g").attr("class","inner");c=a.node().appendChild(r);const i=r.getBBox();a.attr("transform","translate("+-i.width/2+", "+-i.height/2+")"),s.node().appendChild(r),dt[e.id]||(dt[e.id]={}),dt[e.id].endLeft=s,gt(c,e.endLabelLeft)}if(e.endLabelRight){const r=it(e.endLabelRight,e.labelStyle),s=t.insert("g").attr("class","edgeTerminals"),a=s.insert("g").attr("class","inner");c=a.node().appendChild(r);const i=r.getBBox();a.attr("transform","translate("+-i.width/2+", "+-i.height/2+")"),s.node().appendChild(r),dt[e.id]||(dt[e.id]={}),dt[e.id].endRight=s,gt(c,e.endLabelRight)}return a}),"insertEdgeLabel");function gt(t,e){(0,o.D7)().flowchart.htmlLabels&&t&&(t.style.width=9*e.length+"px",t.style.height="12px")}(0,o.K2)(gt,"setTerminalWidth");var ut=(0,o.K2)(((t,e)=>{o.Rm.debug("Moving label abc88 ",t.id,t.label,ct[t.id],e);let r=e.updatedPath?e.updatedPath:e.originalPath;const s=(0,o.D7)(),{subGraphTitleTotalMargin:i}=(0,a.O)(s);if(t.label){const s=ct[t.id];let a=t.x,l=t.y;if(r){const s=n._K.calcLabelPosition(r);o.Rm.debug("Moving label "+t.label+" from (",a,",",l,") to (",s.x,",",s.y,") abc88"),e.updatedPath&&(a=s.x,l=s.y)}s.attr("transform",`translate(${a}, ${l+i/2})`)}if(t.startLabelLeft){const e=dt[t.id].startLeft;let s=t.x,a=t.y;if(r){const e=n._K.calcTerminalLabelPosition(t.arrowTypeStart?10:0,"start_left",r);s=e.x,a=e.y}e.attr("transform",`translate(${s}, ${a})`)}if(t.startLabelRight){const e=dt[t.id].startRight;let s=t.x,a=t.y;if(r){const e=n._K.calcTerminalLabelPosition(t.arrowTypeStart?10:0,"start_right",r);s=e.x,a=e.y}e.attr("transform",`translate(${s}, ${a})`)}if(t.endLabelLeft){const e=dt[t.id].endLeft;let s=t.x,a=t.y;if(r){const e=n._K.calcTerminalLabelPosition(t.arrowTypeEnd?10:0,"end_left",r);s=e.x,a=e.y}e.attr("transform",`translate(${s}, ${a})`)}if(t.endLabelRight){const e=dt[t.id].endRight;let s=t.x,a=t.y;if(r){const e=n._K.calcTerminalLabelPosition(t.arrowTypeEnd?10:0,"end_right",r);s=e.x,a=e.y}e.attr("transform",`translate(${s}, ${a})`)}}),"positionEdgeLabel"),pt=(0,o.K2)(((t,e)=>{const r=t.x,s=t.y,a=Math.abs(e.x-r),i=Math.abs(e.y-s),n=t.width/2,o=t.height/2;return a>=n||i>=o}),"outsideNode"),yt=(0,o.K2)(((t,e,r)=>{o.Rm.debug(`intersection calc abc89:\n  outsidePoint: ${JSON.stringify(e)}\n  insidePoint : ${JSON.stringify(r)}\n  node        : x:${t.x} y:${t.y} w:${t.width} h:${t.height}`);const s=t.x,a=t.y,i=Math.abs(s-r.x),n=t.width/2;let l=r.x<e.x?n-i:n+i;const c=t.height/2,d=Math.abs(e.y-r.y),h=Math.abs(e.x-r.x);if(Math.abs(a-e.y)*n>Math.abs(s-e.x)*c){let t=r.y<e.y?e.y-c-a:a-c-e.y;l=h*t/d;const s={x:r.x<e.x?r.x+l:r.x-h+l,y:r.y<e.y?r.y+d-t:r.y-d+t};return 0===l&&(s.x=e.x,s.y=e.y),0===h&&(s.x=e.x),0===d&&(s.y=e.y),o.Rm.debug(`abc89 topp/bott calc, Q ${d}, q ${t}, R ${h}, r ${l}`,s),s}{l=r.x<e.x?e.x-n-s:s-n-e.x;let t=d*l/h,a=r.x<e.x?r.x+h-l:r.x-h+l,i=r.y<e.y?r.y+t:r.y-t;return o.Rm.debug(`sides calc abc89, Q ${d}, q ${t}, R ${h}, r ${l}`,{_x:a,_y:i}),0===l&&(a=e.x,i=e.y),0===h&&(a=e.x),0===d&&(i=e.y),{x:a,y:i}}}),"intersection"),bt=(0,o.K2)(((t,e)=>{o.Rm.debug("abc88 cutPathAtIntersect",t,e);let r=[],s=t[0],a=!1;return t.forEach((t=>{if(pt(e,t)||a)s=t,a||r.push(t);else{const i=yt(e,s,t);let n=!1;r.forEach((t=>{n=n||t.x===i.x&&t.y===i.y})),r.some((t=>t.x===i.x&&t.y===i.y))||r.push(i),a=!0}})),r}),"cutPathAtIntersect"),xt=(0,o.K2)((function(t,e,r,a,i,n,l){let c=r.points;o.Rm.debug("abc88 InsertEdge: edge=",r,"e=",e);let d=!1;const g=n.node(e.v);var u=n.node(e.w);u?.intersect&&g?.intersect&&(c=c.slice(1,r.points.length-1),c.unshift(g.intersect(c[0])),c.push(u.intersect(c[c.length-1]))),r.toCluster&&(o.Rm.debug("to cluster abc88",a[r.toCluster]),c=bt(r.points,a[r.toCluster].node),d=!0),r.fromCluster&&(o.Rm.debug("from cluster abc88",a[r.fromCluster]),c=bt(c.reverse(),a[r.fromCluster].node).reverse(),d=!0);const p=c.filter((t=>!Number.isNaN(t.y)));let y=h.qrM;!r.curve||"graph"!==i&&"flowchart"!==i||(y=r.curve);const{x:b,y:x}=(0,s.R)(r),f=(0,h.n8j)().x(b).y(x).curve(y);let m;switch(r.thickness){case"normal":m="edge-thickness-normal";break;case"thick":case"invisible":m="edge-thickness-thick";break;default:m=""}switch(r.pattern){case"solid":m+=" edge-pattern-solid";break;case"dotted":m+=" edge-pattern-dotted";break;case"dashed":m+=" edge-pattern-dashed"}const w=t.append("path").attr("d",f(p)).attr("id",r.id).attr("class"," "+m+(r.classes?" "+r.classes:"")).attr("style",r.style);let _="";((0,o.D7)().flowchart.arrowMarkerAbsolute||(0,o.D7)().state.arrowMarkerAbsolute)&&(_=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,_=_.replace(/\(/g,"\\("),_=_.replace(/\)/g,"\\)")),nt(w,r,_,l,i);let L={};return d&&(L.updatedPath=c),L.originalPath=r.points,L}),"insertEdge"),ft=(0,o.K2)((t=>{const e=new Set;for(const r of t)switch(r){case"x":e.add("right"),e.add("left");break;case"y":e.add("up"),e.add("down");break;default:e.add(r)}return e}),"expandAndDeduplicateDirections"),mt=(0,o.K2)(((t,e,r)=>{const s=ft(t),a=e.height+2*r.padding,i=a/2,n=e.width+2*i+r.padding,o=r.padding/2;return s.has("right")&&s.has("left")&&s.has("up")&&s.has("down")?[{x:0,y:0},{x:i,y:0},{x:n/2,y:2*o},{x:n-i,y:0},{x:n,y:0},{x:n,y:-a/3},{x:n+2*o,y:-a/2},{x:n,y:-2*a/3},{x:n,y:-a},{x:n-i,y:-a},{x:n/2,y:-a-2*o},{x:i,y:-a},{x:0,y:-a},{x:0,y:-2*a/3},{x:-2*o,y:-a/2},{x:0,y:-a/3}]:s.has("right")&&s.has("left")&&s.has("up")?[{x:i,y:0},{x:n-i,y:0},{x:n,y:-a/2},{x:n-i,y:-a},{x:i,y:-a},{x:0,y:-a/2}]:s.has("right")&&s.has("left")&&s.has("down")?[{x:0,y:0},{x:i,y:-a},{x:n-i,y:-a},{x:n,y:0}]:s.has("right")&&s.has("up")&&s.has("down")?[{x:0,y:0},{x:n,y:-i},{x:n,y:-a+i},{x:0,y:-a}]:s.has("left")&&s.has("up")&&s.has("down")?[{x:n,y:0},{x:0,y:-i},{x:0,y:-a+i},{x:n,y:-a}]:s.has("right")&&s.has("left")?[{x:i,y:0},{x:i,y:-o},{x:n-i,y:-o},{x:n-i,y:0},{x:n,y:-a/2},{x:n-i,y:-a},{x:n-i,y:-a+o},{x:i,y:-a+o},{x:i,y:-a},{x:0,y:-a/2}]:s.has("up")&&s.has("down")?[{x:n/2,y:0},{x:0,y:-o},{x:i,y:-o},{x:i,y:-a+o},{x:0,y:-a+o},{x:n/2,y:-a},{x:n,y:-a+o},{x:n-i,y:-a+o},{x:n-i,y:-o},{x:n,y:-o}]:s.has("right")&&s.has("up")?[{x:0,y:0},{x:n,y:-i},{x:0,y:-a}]:s.has("right")&&s.has("down")?[{x:0,y:0},{x:n,y:0},{x:0,y:-a}]:s.has("left")&&s.has("up")?[{x:n,y:0},{x:0,y:-i},{x:n,y:-a}]:s.has("left")&&s.has("down")?[{x:n,y:0},{x:0,y:0},{x:n,y:-a}]:s.has("right")?[{x:i,y:-o},{x:i,y:-o},{x:n-i,y:-o},{x:n-i,y:0},{x:n,y:-a/2},{x:n-i,y:-a},{x:n-i,y:-a+o},{x:i,y:-a+o},{x:i,y:-a+o}]:s.has("left")?[{x:i,y:0},{x:i,y:-o},{x:n-i,y:-o},{x:n-i,y:-a+o},{x:i,y:-a+o},{x:i,y:-a},{x:0,y:-a/2}]:s.has("up")?[{x:i,y:-o},{x:i,y:-a+o},{x:0,y:-a+o},{x:n/2,y:-a},{x:n,y:-a+o},{x:n-i,y:-a+o},{x:n-i,y:-o}]:s.has("down")?[{x:n/2,y:0},{x:0,y:-o},{x:i,y:-o},{x:i,y:-a+o},{x:n-i,y:-a+o},{x:n-i,y:-o},{x:n,y:-o}]:[{x:0,y:0}]}),"getArrowPoints");function wt(t,e){return t.intersect(e)}(0,o.K2)(wt,"intersectNode");var _t=wt;function Lt(t,e,r,s){var a=t.x,i=t.y,n=a-s.x,o=i-s.y,l=Math.sqrt(e*e*o*o+r*r*n*n),c=Math.abs(e*r*n/l);s.x<a&&(c=-c);var d=Math.abs(e*r*o/l);return s.y<i&&(d=-d),{x:a+c,y:i+d}}(0,o.K2)(Lt,"intersectEllipse");var kt=Lt;function St(t,e,r){return kt(t,e,e,r)}(0,o.K2)(St,"intersectCircle");var vt=St;function Et(t,e,r,s){var a,i,n,o,l,c,d,h,g,u,p,y,b;if(a=e.y-t.y,n=t.x-e.x,l=e.x*t.y-t.x*e.y,g=a*r.x+n*r.y+l,u=a*s.x+n*s.y+l,(0===g||0===u||!Dt(g,u))&&(i=s.y-r.y,o=r.x-s.x,c=s.x*r.y-r.x*s.y,d=i*t.x+o*t.y+c,h=i*e.x+o*e.y+c,(0===d||0===h||!Dt(d,h))&&0!==(p=a*o-i*n)))return y=Math.abs(p/2),{x:(b=n*c-o*l)<0?(b-y)/p:(b+y)/p,y:(b=i*l-a*c)<0?(b-y)/p:(b+y)/p}}function Dt(t,e){return t*e>0}(0,o.K2)(Et,"intersectLine"),(0,o.K2)(Dt,"sameSign");var Ct=Et,Rt=Kt;function Kt(t,e,r){var s=t.x,a=t.y,i=[],n=Number.POSITIVE_INFINITY,o=Number.POSITIVE_INFINITY;"function"===typeof e.forEach?e.forEach((function(t){n=Math.min(n,t.x),o=Math.min(o,t.y)})):(n=Math.min(n,e.x),o=Math.min(o,e.y));for(var l=s-t.width/2-n,c=a-t.height/2-o,d=0;d<e.length;d++){var h=e[d],g=e[d<e.length-1?d+1:0],u=Ct(t,r,{x:l+h.x,y:c+h.y},{x:l+g.x,y:c+g.y});u&&i.push(u)}return i.length?(i.length>1&&i.sort((function(t,e){var s=t.x-r.x,a=t.y-r.y,i=Math.sqrt(s*s+a*a),n=e.x-r.x,o=e.y-r.y,l=Math.sqrt(n*n+o*o);return i<l?-1:i===l?0:1})),i[0]):t}(0,o.K2)(Kt,"intersectPolygon");var Nt={node:_t,circle:vt,ellipse:kt,polygon:Rt,rect:(0,o.K2)(((t,e)=>{var r,s,a=t.x,i=t.y,n=e.x-a,o=e.y-i,l=t.width/2,c=t.height/2;return Math.abs(o)*l>Math.abs(n)*c?(o<0&&(c=-c),r=0===o?0:c*n/o,s=c):(n<0&&(l=-l),r=l,s=0===n?0:l*o/n),{x:a+r,y:i+s}}),"intersectRect")},Tt=(0,o.K2)((async(t,e,r,s)=>{const a=(0,o.D7)();let l;const c=e.useHtmlLabels||(0,o._3)(a.flowchart.htmlLabels);l=r||"node default";const d=t.insert("g").attr("class",l).attr("id",e.domId||e.id),g=d.insert("g").attr("class","label").attr("style",e.labelStyle);let u;u=void 0===e.labelText?"":"string"===typeof e.labelText?e.labelText:e.labelText[0];const p=g.node();let y;y="markdown"===e.labelType?(0,i.GZ)(g,(0,o.jZ)((0,n.Sm)(u),a),{useHtmlLabels:c,width:e.width||a.flowchart.wrappingWidth,classes:"markdown-node-label"},a):p.appendChild(it((0,o.jZ)((0,n.Sm)(u),a),e.labelStyle,!1,s));let b=y.getBBox();const x=e.padding/2;if((0,o._3)(a.flowchart.htmlLabels)){const t=y.children[0],e=(0,h.Ltv)(y),r=t.getElementsByTagName("img");if(r){const t=""===u.replace(/<img[^>]*>/g,"").trim();await Promise.all([...r].map((e=>new Promise((r=>{function s(){if(e.style.display="flex",e.style.flexDirection="column",t){const t=a.fontSize?a.fontSize:window.getComputedStyle(document.body).fontSize,r=5,s=parseInt(t,10)*r+"px";e.style.minWidth=s,e.style.maxWidth=s}else e.style.width="100%";r(e)}(0,o.K2)(s,"setupImage"),setTimeout((()=>{e.complete&&s()})),e.addEventListener("error",s),e.addEventListener("load",s)})))))}b=t.getBoundingClientRect(),e.attr("width",b.width),e.attr("height",b.height)}return c?g.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"):g.attr("transform","translate(0, "+-b.height/2+")"),e.centerLabel&&g.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),g.insert("rect",":first-child"),{shapeSvg:d,bbox:b,halfPadding:x,label:g}}),"labelHelper"),$t=(0,o.K2)(((t,e)=>{const r=e.node().getBBox();t.width=r.width,t.height=r.height}),"updateNodeBounds");function At(t,e,r,s){return t.insert("polygon",":first-child").attr("points",s.map((function(t){return t.x+","+t.y})).join(" ")).attr("class","label-container").attr("transform","translate("+-e/2+","+r/2+")")}(0,o.K2)(At,"insertPolygonShape");var It=(0,o.K2)((async(t,e)=>{e.useHtmlLabels||(0,o.D7)().flowchart.htmlLabels||(e.centerLabel=!0);const{shapeSvg:r,bbox:s,halfPadding:a}=await Tt(t,e,"node "+e.classes,!0);o.Rm.info("Classes = ",e.classes);const i=r.insert("rect",":first-child");return i.attr("rx",e.rx).attr("ry",e.ry).attr("x",-s.width/2-a).attr("y",-s.height/2-a).attr("width",s.width+e.padding).attr("height",s.height+e.padding),$t(e,i),e.intersect=function(t){return Nt.rect(e,t)},r}),"note"),Ot=(0,o.K2)((t=>t?" "+t:""),"formatClass"),Bt=(0,o.K2)(((t,e)=>`${e||"node default"}${Ot(t.classes)} ${Ot(t.class)}`),"getClassesFromNode"),zt=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,Bt(e,void 0),!0),a=s.width+e.padding+(s.height+e.padding),i=[{x:a/2,y:0},{x:a,y:-a/2},{x:a/2,y:-a},{x:0,y:-a/2}];o.Rm.info("Question main (Circle)");const n=At(r,a,a,i);return n.attr("style",e.style),$t(e,n),e.intersect=function(t){return o.Rm.warn("Intersect called"),Nt.polygon(e,i,t)},r}),"question"),Mt=(0,o.K2)(((t,e)=>{const r=t.insert("g").attr("class","node default").attr("id",e.domId||e.id),s=[{x:0,y:14},{x:14,y:0},{x:0,y:-14},{x:-14,y:0}];return r.insert("polygon",":first-child").attr("points",s.map((function(t){return t.x+","+t.y})).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),e.width=28,e.height=28,e.intersect=function(t){return Nt.circle(e,14,t)},r}),"choice"),Pt=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,Bt(e,void 0),!0),a=s.height+e.padding,i=a/4,n=s.width+2*i+e.padding,o=[{x:i,y:0},{x:n-i,y:0},{x:n,y:-a/2},{x:n-i,y:-a},{x:i,y:-a},{x:0,y:-a/2}],l=At(r,n,a,o);return l.attr("style",e.style),$t(e,l),e.intersect=function(t){return Nt.polygon(e,o,t)},r}),"hexagon"),Yt=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,void 0,!0),a=s.height+2*e.padding,i=a/2,n=s.width+2*i+e.padding,o=mt(e.directions,s,e),l=At(r,n,a,o);return l.attr("style",e.style),$t(e,l),e.intersect=function(t){return Nt.polygon(e,o,t)},r}),"block_arrow"),Ft=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,Bt(e,void 0),!0),a=s.width+e.padding,i=s.height+e.padding,n=[{x:-i/2,y:0},{x:a,y:0},{x:a,y:-i},{x:-i/2,y:-i},{x:0,y:-i/2}];return At(r,a,i,n).attr("style",e.style),e.width=a+i,e.height=i,e.intersect=function(t){return Nt.polygon(e,n,t)},r}),"rect_left_inv_arrow"),jt=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,Bt(e),!0),a=s.width+e.padding,i=s.height+e.padding,n=[{x:-2*i/6,y:0},{x:a-i/6,y:0},{x:a+2*i/6,y:-i},{x:i/6,y:-i}],o=At(r,a,i,n);return o.attr("style",e.style),$t(e,o),e.intersect=function(t){return Nt.polygon(e,n,t)},r}),"lean_right"),Wt=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,Bt(e,void 0),!0),a=s.width+e.padding,i=s.height+e.padding,n=[{x:2*i/6,y:0},{x:a+i/6,y:0},{x:a-2*i/6,y:-i},{x:-i/6,y:-i}],o=At(r,a,i,n);return o.attr("style",e.style),$t(e,o),e.intersect=function(t){return Nt.polygon(e,n,t)},r}),"lean_left"),Xt=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,Bt(e,void 0),!0),a=s.width+e.padding,i=s.height+e.padding,n=[{x:-2*i/6,y:0},{x:a+2*i/6,y:0},{x:a-i/6,y:-i},{x:i/6,y:-i}],o=At(r,a,i,n);return o.attr("style",e.style),$t(e,o),e.intersect=function(t){return Nt.polygon(e,n,t)},r}),"trapezoid"),Ht=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,Bt(e,void 0),!0),a=s.width+e.padding,i=s.height+e.padding,n=[{x:i/6,y:0},{x:a-i/6,y:0},{x:a+2*i/6,y:-i},{x:-2*i/6,y:-i}],o=At(r,a,i,n);return o.attr("style",e.style),$t(e,o),e.intersect=function(t){return Nt.polygon(e,n,t)},r}),"inv_trapezoid"),Ut=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,Bt(e,void 0),!0),a=s.width+e.padding,i=s.height+e.padding,n=[{x:0,y:0},{x:a+i/2,y:0},{x:a,y:-i/2},{x:a+i/2,y:-i},{x:0,y:-i}],o=At(r,a,i,n);return o.attr("style",e.style),$t(e,o),e.intersect=function(t){return Nt.polygon(e,n,t)},r}),"rect_right_inv_arrow"),Zt=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,Bt(e,void 0),!0),a=s.width+e.padding,i=a/2,n=i/(2.5+a/50),o=s.height+n+e.padding,l="M 0,"+n+" a "+i+","+n+" 0,0,0 "+a+" 0 a "+i+","+n+" 0,0,0 "+-a+" 0 l 0,"+o+" a "+i+","+n+" 0,0,0 "+a+" 0 l 0,"+-o,c=r.attr("label-offset-y",n).insert("path",":first-child").attr("style",e.style).attr("d",l).attr("transform","translate("+-a/2+","+-(o/2+n)+")");return $t(e,c),e.intersect=function(t){const r=Nt.rect(e,t),s=r.x-e.x;if(0!=i&&(Math.abs(s)<e.width/2||Math.abs(s)==e.width/2&&Math.abs(r.y-e.y)>e.height/2-n)){let a=n*n*(1-s*s/(i*i));0!=a&&(a=Math.sqrt(a)),a=n-a,t.y-e.y>0&&(a=-a),r.y+=a}return r},r}),"cylinder"),qt=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s,halfPadding:a}=await Tt(t,e,"node "+e.classes+" "+e.class,!0),i=r.insert("rect",":first-child"),n=e.positioned?e.width:s.width+e.padding,l=e.positioned?e.height:s.height+e.padding,c=e.positioned?-n/2:-s.width/2-a,d=e.positioned?-l/2:-s.height/2-a;if(i.attr("class","basic label-container").attr("style",e.style).attr("rx",e.rx).attr("ry",e.ry).attr("x",c).attr("y",d).attr("width",n).attr("height",l),e.props){const t=new Set(Object.keys(e.props));e.props.borders&&(Vt(i,e.props.borders,n,l),t.delete("borders")),t.forEach((t=>{o.Rm.warn(`Unknown node property ${t}`)}))}return $t(e,i),e.intersect=function(t){return Nt.rect(e,t)},r}),"rect"),Gt=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s,halfPadding:a}=await Tt(t,e,"node "+e.classes,!0),i=r.insert("rect",":first-child"),n=e.positioned?e.width:s.width+e.padding,l=e.positioned?e.height:s.height+e.padding,c=e.positioned?-n/2:-s.width/2-a,d=e.positioned?-l/2:-s.height/2-a;if(i.attr("class","basic cluster composite label-container").attr("style",e.style).attr("rx",e.rx).attr("ry",e.ry).attr("x",c).attr("y",d).attr("width",n).attr("height",l),e.props){const t=new Set(Object.keys(e.props));e.props.borders&&(Vt(i,e.props.borders,n,l),t.delete("borders")),t.forEach((t=>{o.Rm.warn(`Unknown node property ${t}`)}))}return $t(e,i),e.intersect=function(t){return Nt.rect(e,t)},r}),"composite"),Jt=(0,o.K2)((async(t,e)=>{const{shapeSvg:r}=await Tt(t,e,"label",!0);o.Rm.trace("Classes = ",e.class);const s=r.insert("rect",":first-child");if(s.attr("width",0).attr("height",0),r.attr("class","label edgeLabel"),e.props){const t=new Set(Object.keys(e.props));e.props.borders&&(Vt(s,e.props.borders,0,0),t.delete("borders")),t.forEach((t=>{o.Rm.warn(`Unknown node property ${t}`)}))}return $t(e,s),e.intersect=function(t){return Nt.rect(e,t)},r}),"labelRect");function Vt(t,e,r,s){const a=[],i=(0,o.K2)((t=>{a.push(t,0)}),"addBorder"),n=(0,o.K2)((t=>{a.push(0,t)}),"skipBorder");e.includes("t")?(o.Rm.debug("add top border"),i(r)):n(r),e.includes("r")?(o.Rm.debug("add right border"),i(s)):n(s),e.includes("b")?(o.Rm.debug("add bottom border"),i(r)):n(r),e.includes("l")?(o.Rm.debug("add left border"),i(s)):n(s),t.attr("stroke-dasharray",a.join(" "))}(0,o.K2)(Vt,"applyNodePropertyBorders");var Qt=(0,o.K2)(((t,e)=>{let r;r=e.classes?"node "+e.classes:"node default";const s=t.insert("g").attr("class",r).attr("id",e.domId||e.id),a=s.insert("rect",":first-child"),i=s.insert("line"),n=s.insert("g").attr("class","label"),l=e.labelText.flat?e.labelText.flat():e.labelText;let c="";c="object"===typeof l?l[0]:l,o.Rm.info("Label text abc79",c,l,"object"===typeof l);const d=n.node().appendChild(it(c,e.labelStyle,!0,!0));let g={width:0,height:0};if((0,o._3)((0,o.D7)().flowchart.htmlLabels)){const t=d.children[0],e=(0,h.Ltv)(d);g=t.getBoundingClientRect(),e.attr("width",g.width),e.attr("height",g.height)}o.Rm.info("Text 2",l);const u=l.slice(1,l.length);let p=d.getBBox();const y=n.node().appendChild(it(u.join?u.join("<br/>"):u,e.labelStyle,!0,!0));if((0,o._3)((0,o.D7)().flowchart.htmlLabels)){const t=y.children[0],e=(0,h.Ltv)(y);g=t.getBoundingClientRect(),e.attr("width",g.width),e.attr("height",g.height)}const b=e.padding/2;return(0,h.Ltv)(y).attr("transform","translate( "+(g.width>p.width?0:(p.width-g.width)/2)+", "+(p.height+b+5)+")"),(0,h.Ltv)(d).attr("transform","translate( "+(g.width<p.width?0:-(p.width-g.width)/2)+", 0)"),g=n.node().getBBox(),n.attr("transform","translate("+-g.width/2+", "+(-g.height/2-b+3)+")"),a.attr("class","outer title-state").attr("x",-g.width/2-b).attr("y",-g.height/2-b).attr("width",g.width+e.padding).attr("height",g.height+e.padding),i.attr("class","divider").attr("x1",-g.width/2-b).attr("x2",g.width/2+b).attr("y1",-g.height/2-b+p.height+b).attr("y2",-g.height/2-b+p.height+b),$t(e,a),e.intersect=function(t){return Nt.rect(e,t)},s}),"rectWithTitle"),te=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,Bt(e,void 0),!0),a=s.height+e.padding,i=s.width+a/4+e.padding,n=r.insert("rect",":first-child").attr("style",e.style).attr("rx",a/2).attr("ry",a/2).attr("x",-i/2).attr("y",-a/2).attr("width",i).attr("height",a);return $t(e,n),e.intersect=function(t){return Nt.rect(e,t)},r}),"stadium"),ee=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s,halfPadding:a}=await Tt(t,e,Bt(e,void 0),!0),i=r.insert("circle",":first-child");return i.attr("style",e.style).attr("rx",e.rx).attr("ry",e.ry).attr("r",s.width/2+a).attr("width",s.width+e.padding).attr("height",s.height+e.padding),o.Rm.info("Circle main"),$t(e,i),e.intersect=function(t){return o.Rm.info("Circle intersect",e,s.width/2+a,t),Nt.circle(e,s.width/2+a,t)},r}),"circle"),re=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s,halfPadding:a}=await Tt(t,e,Bt(e,void 0),!0),i=r.insert("g",":first-child"),n=i.insert("circle"),l=i.insert("circle");return i.attr("class",e.class),n.attr("style",e.style).attr("rx",e.rx).attr("ry",e.ry).attr("r",s.width/2+a+5).attr("width",s.width+e.padding+10).attr("height",s.height+e.padding+10),l.attr("style",e.style).attr("rx",e.rx).attr("ry",e.ry).attr("r",s.width/2+a).attr("width",s.width+e.padding).attr("height",s.height+e.padding),o.Rm.info("DoubleCircle main"),$t(e,n),e.intersect=function(t){return o.Rm.info("DoubleCircle intersect",e,s.width/2+a+5,t),Nt.circle(e,s.width/2+a+5,t)},r}),"doublecircle"),se=(0,o.K2)((async(t,e)=>{const{shapeSvg:r,bbox:s}=await Tt(t,e,Bt(e,void 0),!0),a=s.width+e.padding,i=s.height+e.padding,n=[{x:0,y:0},{x:a,y:0},{x:a,y:-i},{x:0,y:-i},{x:0,y:0},{x:-8,y:0},{x:a+8,y:0},{x:a+8,y:-i},{x:-8,y:-i},{x:-8,y:0}],o=At(r,a,i,n);return o.attr("style",e.style),$t(e,o),e.intersect=function(t){return Nt.polygon(e,n,t)},r}),"subroutine"),ae=(0,o.K2)(((t,e)=>{const r=t.insert("g").attr("class","node default").attr("id",e.domId||e.id),s=r.insert("circle",":first-child");return s.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),$t(e,s),e.intersect=function(t){return Nt.circle(e,7,t)},r}),"start"),ie=(0,o.K2)(((t,e,r)=>{const s=t.insert("g").attr("class","node default").attr("id",e.domId||e.id);let a=70,i=10;"LR"===r&&(a=10,i=70);const n=s.append("rect").attr("x",-1*a/2).attr("y",-1*i/2).attr("width",a).attr("height",i).attr("class","fork-join");return $t(e,n),e.height=e.height+e.padding/2,e.width=e.width+e.padding/2,e.intersect=function(t){return Nt.rect(e,t)},s}),"forkJoin"),ne={rhombus:zt,composite:Gt,question:zt,rect:qt,labelRect:Jt,rectWithTitle:Qt,choice:Mt,circle:ee,doublecircle:re,stadium:te,hexagon:Pt,block_arrow:Yt,rect_left_inv_arrow:Ft,lean_right:jt,lean_left:Wt,trapezoid:Xt,inv_trapezoid:Ht,rect_right_inv_arrow:Ut,cylinder:Zt,start:ae,end:(0,o.K2)(((t,e)=>{const r=t.insert("g").attr("class","node default").attr("id",e.domId||e.id),s=r.insert("circle",":first-child"),a=r.insert("circle",":first-child");return a.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),s.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),$t(e,a),e.intersect=function(t){return Nt.circle(e,7,t)},r}),"end"),note:It,subroutine:se,fork:ie,join:ie,class_box:(0,o.K2)(((t,e)=>{const r=e.padding/2;let s;s=e.classes?"node "+e.classes:"node default";const a=t.insert("g").attr("class",s).attr("id",e.domId||e.id),i=a.insert("rect",":first-child"),n=a.insert("line"),l=a.insert("line");let c=0,d=4;const g=a.insert("g").attr("class","label");let u=0;const p=e.classData.annotations?.[0],y=e.classData.annotations[0]?"\xab"+e.classData.annotations[0]+"\xbb":"",b=g.node().appendChild(it(y,e.labelStyle,!0,!0));let x=b.getBBox();if((0,o._3)((0,o.D7)().flowchart.htmlLabels)){const t=b.children[0],e=(0,h.Ltv)(b);x=t.getBoundingClientRect(),e.attr("width",x.width),e.attr("height",x.height)}e.classData.annotations[0]&&(d+=x.height+4,c+=x.width);let f=e.classData.label;void 0!==e.classData.type&&""!==e.classData.type&&((0,o.D7)().flowchart.htmlLabels?f+="&lt;"+e.classData.type+"&gt;":f+="<"+e.classData.type+">");const m=g.node().appendChild(it(f,e.labelStyle,!0,!0));(0,h.Ltv)(m).attr("class","classTitle");let w=m.getBBox();if((0,o._3)((0,o.D7)().flowchart.htmlLabels)){const t=m.children[0],e=(0,h.Ltv)(m);w=t.getBoundingClientRect(),e.attr("width",w.width),e.attr("height",w.height)}d+=w.height+4,w.width>c&&(c=w.width);const _=[];e.classData.members.forEach((t=>{const r=t.getDisplayDetails();let s=r.displayText;(0,o.D7)().flowchart.htmlLabels&&(s=s.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const a=g.node().appendChild(it(s,r.cssStyle?r.cssStyle:e.labelStyle,!0,!0));let i=a.getBBox();if((0,o._3)((0,o.D7)().flowchart.htmlLabels)){const t=a.children[0],e=(0,h.Ltv)(a);i=t.getBoundingClientRect(),e.attr("width",i.width),e.attr("height",i.height)}i.width>c&&(c=i.width),d+=i.height+4,_.push(a)})),d+=8;const L=[];if(e.classData.methods.forEach((t=>{const r=t.getDisplayDetails();let s=r.displayText;(0,o.D7)().flowchart.htmlLabels&&(s=s.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const a=g.node().appendChild(it(s,r.cssStyle?r.cssStyle:e.labelStyle,!0,!0));let i=a.getBBox();if((0,o._3)((0,o.D7)().flowchart.htmlLabels)){const t=a.children[0],e=(0,h.Ltv)(a);i=t.getBoundingClientRect(),e.attr("width",i.width),e.attr("height",i.height)}i.width>c&&(c=i.width),d+=i.height+4,L.push(a)})),d+=8,p){let t=(c-x.width)/2;(0,h.Ltv)(b).attr("transform","translate( "+(-1*c/2+t)+", "+-1*d/2+")"),u=x.height+4}let k=(c-w.width)/2;return(0,h.Ltv)(m).attr("transform","translate( "+(-1*c/2+k)+", "+(-1*d/2+u)+")"),u+=w.height+4,n.attr("class","divider").attr("x1",-c/2-r).attr("x2",c/2+r).attr("y1",-d/2-r+8+u).attr("y2",-d/2-r+8+u),u+=8,_.forEach((t=>{(0,h.Ltv)(t).attr("transform","translate( "+-c/2+", "+(-1*d/2+u+4)+")");const e=t?.getBBox();u+=(e?.height??0)+4})),u+=8,l.attr("class","divider").attr("x1",-c/2-r).attr("x2",c/2+r).attr("y1",-d/2-r+8+u).attr("y2",-d/2-r+8+u),u+=8,L.forEach((t=>{(0,h.Ltv)(t).attr("transform","translate( "+-c/2+", "+(-1*d/2+u)+")");const e=t?.getBBox();u+=(e?.height??0)+4})),i.attr("style",e.style).attr("class","outer title-state").attr("x",-c/2-r).attr("y",-d/2-r).attr("width",c+e.padding).attr("height",d+e.padding),$t(e,i),e.intersect=function(t){return Nt.rect(e,t)},a}),"class_box")},oe={},le=(0,o.K2)((async(t,e,r)=>{let s,a;if(e.link){let i;"sandbox"===(0,o.D7)().securityLevel?i="_top":e.linkTarget&&(i=e.linkTarget||"_blank"),s=t.insert("svg:a").attr("xlink:href",e.link).attr("target",i),a=await ne[e.shape](s,e,r)}else a=await ne[e.shape](t,e,r),s=a;return e.tooltip&&a.attr("title",e.tooltip),e.class&&a.attr("class","node default "+e.class),oe[e.id]=s,e.haveCallback&&oe[e.id].attr("class",oe[e.id].attr("class")+" clickable"),s}),"insertNode"),ce=(0,o.K2)((t=>{const e=oe[t.id];o.Rm.trace("Transforming node",t.diff,t,"translate("+(t.x-t.width/2-5)+", "+t.width/2+")");const r=t.diff||0;return t.clusterNode?e.attr("transform","translate("+(t.x+r-t.width/2)+", "+(t.y-t.height/2-8)+")"):e.attr("transform","translate("+t.x+", "+t.y+")"),r}),"positionNode");function de(t,e){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const s=t;let a="default";(s?.classes?.length||0)>0&&(a=(s?.classes??[]).join(" ")),a+=" flowchart-label";let i,l=0,c="";switch(s.type){case"round":l=5,c="rect";break;case"composite":l=0,c="composite",i=0;break;case"square":case"group":default:c="rect";break;case"diamond":c="question";break;case"hexagon":c="hexagon";break;case"block_arrow":c="block_arrow";break;case"odd":case"rect_left_inv_arrow":c="rect_left_inv_arrow";break;case"lean_right":c="lean_right";break;case"lean_left":c="lean_left";break;case"trapezoid":c="trapezoid";break;case"inv_trapezoid":c="inv_trapezoid";break;case"circle":c="circle";break;case"ellipse":c="ellipse";break;case"stadium":c="stadium";break;case"subroutine":c="subroutine";break;case"cylinder":c="cylinder";break;case"doublecircle":c="doublecircle"}const d=(0,n.sM)(s?.styles??[]),h=s.label,g=s.size??{width:0,height:0,x:0,y:0};return{labelStyle:d.labelStyle,shape:c,labelText:h,rx:l,ry:l,class:a,style:d.style,id:s.id,directions:s.directions,width:g.width,height:g.height,x:g.x,y:g.y,positioned:r,intersect:void 0,type:s.type,padding:i??(0,o.zj)()?.block?.padding??0}}async function he(t,e,r){const s=de(e,r,!1);if("group"===s.type)return;const a=(0,o.zj)(),i=await le(t,s,{config:a}),n=i.node().getBBox(),l=r.getBlock(s.id);l.size={width:n.width,height:n.height,x:0,y:0,node:i},r.setBlock(l),i.remove()}async function ge(t,e,r){const s=de(e,r,!0);if("space"!==r.getBlock(s.id).type){const r=(0,o.zj)();await le(t,s,{config:r}),e.intersect=s?.intersect,ce(s)}}async function ue(t,e,r,s){for(const a of e)await s(t,a,r),a.children&&await ue(t,a.children,r,s)}async function pe(t,e,r){await ue(t,e,r,he)}async function ye(t,e,r){await ue(t,e,r,ge)}async function be(t,e,r,s,a){const i=new g.T({multigraph:!0,compound:!0});i.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8});for(const n of r)n.size&&i.setNode(n.id,{width:n.size.width,height:n.size.height,intersect:n.intersect});for(const n of e)if(n.start&&n.end){const e=s.getBlock(n.start),r=s.getBlock(n.end);if(e?.size&&r?.size){const s=e.size,o=r.size,l=[{x:s.x,y:s.y},{x:s.x+(o.x-s.x)/2,y:s.y+(o.y-s.y)/2},{x:o.x,y:o.y}];xt(t,{v:n.start,w:n.end,name:n.id},{...n,arrowTypeEnd:n.arrowTypeEnd,arrowTypeStart:n.arrowTypeStart,points:l,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"},void 0,"block",i,a),n.label&&(await ht(t,{...n,label:n.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:n.arrowTypeEnd,arrowTypeStart:n.arrowTypeStart,points:l,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),ut({...n,x:l[1].x,y:l[1].y},{originalPath:l}))}}}(0,o.K2)(de,"getNodeFromBlock"),(0,o.K2)(he,"calculateBlockSize"),(0,o.K2)(ge,"insertBlockPositioned"),(0,o.K2)(ue,"performOperations"),(0,o.K2)(pe,"calculateBlockSizes"),(0,o.K2)(ye,"insertBlocks"),(0,o.K2)(be,"insertEdges");var xe=(0,o.K2)((function(t,e){return e.db.getClasses()}),"getClasses"),fe={parser:p,db:W,renderer:{draw:(0,o.K2)((async function(t,e,r,s){const{securityLevel:a,block:i}=(0,o.zj)(),n=s.db;let l;"sandbox"===a&&(l=(0,h.Ltv)("#i"+e));const c="sandbox"===a?(0,h.Ltv)(l.nodes()[0].contentDocument.body):(0,h.Ltv)("body"),d="sandbox"===a?c.select(`[id="${e}"]`):(0,h.Ltv)(`[id="${e}"]`);q(d,["point","circle","cross"],s.type,e);const g=n.getBlocks(),u=n.getBlocksFlat(),p=n.getEdges(),y=d.insert("g").attr("class","block");await pe(y,g,n);const b=rt(n);if(await ye(y,g,n),await be(y,p,u,n,e),b){const t=b,e=Math.max(1,Math.round(t.width/t.height*.125)),r=t.height+e+10,s=t.width+10,{useMaxWidth:a}=i;(0,o.a$)(d,r,s,!!a),o.Rm.debug("Here Bounds",b,t),d.attr("viewBox",`${t.x-5} ${t.y-5} ${t.width+10} ${t.height+10}`)}}),"draw"),getClasses:xe},styles:H}},4472:(t,e,r)=>{r.d(e,{T:()=>f});var s=r(6883),a=r(1919),i=r(5459),n=r(2373),o=r(5506),l=r(5665),c=r(9707),d=r(2461),h=r(3761),g=r(3909),u=r(3126);const p=(0,h.A)((function(t){return(0,g.A)((0,d.A)(t,1,u.A,!0))}));var y=r(2502),b=r(9638),x="\0";class f{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._isDirected=!Object.prototype.hasOwnProperty.call(t,"directed")||t.directed,this._isMultigraph=!!Object.prototype.hasOwnProperty.call(t,"multigraph")&&t.multigraph,this._isCompound=!!Object.prototype.hasOwnProperty.call(t,"compound")&&t.compound,this._label=void 0,this._defaultNodeLabelFn=s.A(void 0),this._defaultEdgeLabelFn=s.A(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children[x]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}isDirected(){return this._isDirected}isMultigraph(){return this._isMultigraph}isCompound(){return this._isCompound}setGraph(t){return this._label=t,this}graph(){return this._label}setDefaultNodeLabel(t){return a.A(t)||(t=s.A(t)),this._defaultNodeLabelFn=t,this}nodeCount(){return this._nodeCount}nodes(){return i.A(this._nodes)}sources(){var t=this;return n.A(this.nodes(),(function(e){return o.A(t._in[e])}))}sinks(){var t=this;return n.A(this.nodes(),(function(e){return o.A(t._out[e])}))}setNodes(t,e){var r=arguments,s=this;return l.A(t,(function(t){r.length>1?s.setNode(t,e):s.setNode(t)})),this}setNode(t,e){return Object.prototype.hasOwnProperty.call(this._nodes,t)?(arguments.length>1&&(this._nodes[t]=e),this):(this._nodes[t]=arguments.length>1?e:this._defaultNodeLabelFn(t),this._isCompound&&(this._parent[t]=x,this._children[t]={},this._children[x][t]=!0),this._in[t]={},this._preds[t]={},this._out[t]={},this._sucs[t]={},++this._nodeCount,this)}node(t){return this._nodes[t]}hasNode(t){return Object.prototype.hasOwnProperty.call(this._nodes,t)}removeNode(t){if(Object.prototype.hasOwnProperty.call(this._nodes,t)){var e=t=>this.removeEdge(this._edgeObjs[t]);delete this._nodes[t],this._isCompound&&(this._removeFromParentsChildList(t),delete this._parent[t],l.A(this.children(t),(t=>{this.setParent(t)})),delete this._children[t]),l.A(i.A(this._in[t]),e),delete this._in[t],delete this._preds[t],l.A(i.A(this._out[t]),e),delete this._out[t],delete this._sucs[t],--this._nodeCount}return this}setParent(t,e){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(c.A(e))e=x;else{for(var r=e+="";!c.A(r);r=this.parent(r))if(r===t)throw new Error("Setting "+e+" as parent of "+t+" would create a cycle");this.setNode(e)}return this.setNode(t),this._removeFromParentsChildList(t),this._parent[t]=e,this._children[e][t]=!0,this}_removeFromParentsChildList(t){delete this._children[this._parent[t]][t]}parent(t){if(this._isCompound){var e=this._parent[t];if(e!==x)return e}}children(t){if(c.A(t)&&(t=x),this._isCompound){var e=this._children[t];if(e)return i.A(e)}else{if(t===x)return this.nodes();if(this.hasNode(t))return[]}}predecessors(t){var e=this._preds[t];if(e)return i.A(e)}successors(t){var e=this._sucs[t];if(e)return i.A(e)}neighbors(t){var e=this.predecessors(t);if(e)return p(e,this.successors(t))}isLeaf(t){return 0===(this.isDirected()?this.successors(t):this.neighbors(t)).length}filterNodes(t){var e=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});e.setGraph(this.graph());var r=this;l.A(this._nodes,(function(r,s){t(s)&&e.setNode(s,r)})),l.A(this._edgeObjs,(function(t){e.hasNode(t.v)&&e.hasNode(t.w)&&e.setEdge(t,r.edge(t))}));var s={};function a(t){var i=r.parent(t);return void 0===i||e.hasNode(i)?(s[t]=i,i):i in s?s[i]:a(i)}return this._isCompound&&l.A(e.nodes(),(function(t){e.setParent(t,a(t))})),e}setDefaultEdgeLabel(t){return a.A(t)||(t=s.A(t)),this._defaultEdgeLabelFn=t,this}edgeCount(){return this._edgeCount}edges(){return y.A(this._edgeObjs)}setPath(t,e){var r=this,s=arguments;return b.A(t,(function(t,a){return s.length>1?r.setEdge(t,a,e):r.setEdge(t,a),a})),this}setEdge(){var t,e,r,s,a=!1,i=arguments[0];"object"===typeof i&&null!==i&&"v"in i?(t=i.v,e=i.w,r=i.name,2===arguments.length&&(s=arguments[1],a=!0)):(t=i,e=arguments[1],r=arguments[3],arguments.length>2&&(s=arguments[2],a=!0)),t=""+t,e=""+e,c.A(r)||(r=""+r);var n=_(this._isDirected,t,e,r);if(Object.prototype.hasOwnProperty.call(this._edgeLabels,n))return a&&(this._edgeLabels[n]=s),this;if(!c.A(r)&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(t),this.setNode(e),this._edgeLabels[n]=a?s:this._defaultEdgeLabelFn(t,e,r);var o=function(t,e,r,s){var a=""+e,i=""+r;if(!t&&a>i){var n=a;a=i,i=n}var o={v:a,w:i};s&&(o.name=s);return o}(this._isDirected,t,e,r);return t=o.v,e=o.w,Object.freeze(o),this._edgeObjs[n]=o,m(this._preds[e],t),m(this._sucs[t],e),this._in[e][n]=o,this._out[t][n]=o,this._edgeCount++,this}edge(t,e,r){var s=1===arguments.length?L(this._isDirected,arguments[0]):_(this._isDirected,t,e,r);return this._edgeLabels[s]}hasEdge(t,e,r){var s=1===arguments.length?L(this._isDirected,arguments[0]):_(this._isDirected,t,e,r);return Object.prototype.hasOwnProperty.call(this._edgeLabels,s)}removeEdge(t,e,r){var s=1===arguments.length?L(this._isDirected,arguments[0]):_(this._isDirected,t,e,r),a=this._edgeObjs[s];return a&&(t=a.v,e=a.w,delete this._edgeLabels[s],delete this._edgeObjs[s],w(this._preds[e],t),w(this._sucs[t],e),delete this._in[e][s],delete this._out[t][s],this._edgeCount--),this}inEdges(t,e){var r=this._in[t];if(r){var s=y.A(r);return e?n.A(s,(function(t){return t.v===e})):s}}outEdges(t,e){var r=this._out[t];if(r){var s=y.A(r);return e?n.A(s,(function(t){return t.w===e})):s}}nodeEdges(t,e){var r=this.inEdges(t,e);if(r)return r.concat(this.outEdges(t,e))}}function m(t,e){t[e]?t[e]++:t[e]=1}function w(t,e){--t[e]||delete t[e]}function _(t,e,r,s){var a=""+e,i=""+r;if(!t&&a>i){var n=a;a=i,i=n}return a+"\x01"+i+"\x01"+(c.A(s)?"\0":s)}function L(t,e){return _(t,e.v,e.w,e.name)}f.prototype._nodeCount=0,f.prototype._edgeCount=0},4998:(t,e,r)=>{r.d(e,{A:()=>a});var s=r(4827);const a=function(t){return(0,s.A)(t,4)}},5710:(t,e,r)=>{r.d(e,{A:()=>i});var s=r(4062),a=r(7420);const i=(t,e)=>s.A.lang.round(a.A.parse(t)[e])}}]);
//# sourceMappingURL=140.06452a54.chunk.js.map