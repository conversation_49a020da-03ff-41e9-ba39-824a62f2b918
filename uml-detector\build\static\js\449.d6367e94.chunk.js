"use strict";(self.webpackChunkuml_detector=self.webpackChunkuml_detector||[]).push([[449],{8449:(e,r,t)=>{t.d(r,{diagram:()=>c});var a=t(1605),n=t(113),s=t(7551),d=t(1106),i={parse:(0,s.K2)((async e=>{const r=await(0,d.qg)("info",e);s.Rm.debug(r)}),"parse")},o={version:a.n.version},c={parser:i,db:{getVersion:(0,s.K2)((()=>o.version),"getVersion")},renderer:{draw:(0,s.K2)(((e,r,t)=>{s.Rm.debug("rendering info diagram\n"+e);const a=(0,n.D)(r);(0,s.a$)(a,100,400,!0);a.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${t}`)}),"draw")}}}}]);
//# sourceMappingURL=449.d6367e94.chunk.js.map