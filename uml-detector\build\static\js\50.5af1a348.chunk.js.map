{"version": 3, "file": "static/js/50.5af1a348.chunk.js", "mappings": "0JAAe,SAASA,EAAIC,EAAQC,GAClC,IAAIF,EACJ,QAAgBG,IAAZD,EACF,IAAK,MAAME,KAASH,EACL,MAATG,IACIJ,EAAMI,QAAkBD,IAARH,GAAqBI,GAASA,KACpDJ,EAAMI,OAGL,CACL,IAAIC,GAAS,EACb,IAAK,IAAID,KAASH,EACiC,OAA5CG,EAAQF,EAAQE,IAASC,EAAOJ,MAC7BD,EAAMI,QAAkBD,IAARH,GAAqBI,GAASA,KACpDJ,EAAMI,EAGZ,CACA,OAAOJ,CACT,CCjBA,SAASM,EAAYC,GACnB,OAAOA,EAAEC,OAAOC,KAClB,CAUO,SAASC,EAAQC,EAAMC,GAC5B,OAAOD,EAAKE,YAAYC,OAASH,EAAKF,MAAQG,EAAI,CACpD,CChBe,SAASG,EAAId,EAAQC,GAClC,IAAIa,EAAM,EACV,QAAgBZ,IAAZD,EACF,IAAK,IAAIE,KAASH,GACZG,GAASA,KACXW,GAAOX,OAGN,CACL,IAAIC,GAAS,EACb,IAAK,IAAID,KAASH,GACZG,GAASF,EAAQE,IAASC,EAAOJ,MACnCc,GAAOX,EAGb,CACA,OAAOW,CACT,CCjBe,SAASC,EAAIf,EAAQC,GAClC,IAAIc,EACJ,QAAgBb,IAAZD,EACF,IAAK,MAAME,KAASH,EACL,MAATG,IACIY,EAAMZ,QAAkBD,IAARa,GAAqBZ,GAASA,KACpDY,EAAMZ,OAGL,CACL,IAAIC,GAAS,EACb,IAAK,IAAID,KAASH,EACiC,OAA5CG,EAAQF,EAAQE,IAASC,EAAOJ,MAC7Be,EAAMZ,QAAkBD,IAARa,GAAqBZ,GAASA,KACpDY,EAAMZ,EAGZ,CACA,OAAOY,CACT,CCnBe,SAASC,EAASC,GAC/B,OAAO,WACL,OAAOA,CACT,CACF,CCAA,SAASC,EAAuBC,EAAGC,GACjC,OAAOC,EAAiBF,EAAEG,OAAQF,EAAEE,SAAWH,EAAEf,MAAQgB,EAAEhB,KAC7D,CAEA,SAASmB,EAAuBJ,EAAGC,GACjC,OAAOC,EAAiBF,EAAEZ,OAAQa,EAAEb,SAAWY,EAAEf,MAAQgB,EAAEhB,KAC7D,CAEA,SAASiB,EAAiBF,EAAGC,GAC3B,OAAOD,EAAEK,GAAKJ,EAAEI,EAClB,CAEA,SAASrB,EAAMG,GACb,OAAOA,EAAEH,KACX,CAEA,SAASsB,EAAUnB,GACjB,OAAOA,EAAEF,KACX,CAEA,SAASsB,EAAaC,GACpB,OAAOA,EAAMC,KACf,CAEA,SAASC,EAAaF,GACpB,OAAOA,EAAMG,KACf,CAEA,SAASC,EAAKC,EAAUC,GACtB,MAAMvB,EAAOsB,EAASE,IAAID,GAC1B,IAAKvB,EAAM,MAAM,IAAIyB,MAAM,YAAcF,GACzC,OAAOvB,CACT,CAEA,SAAS0B,EAAmBC,GAAU,IAAT,MAACT,GAAMS,EAClC,IAAK,MAAM3B,KAAQkB,EAAO,CACxB,IAAIJ,EAAKd,EAAKc,GACVc,EAAKd,EACT,IAAK,MAAMe,KAAQ7B,EAAKE,YACtB2B,EAAKf,GAAKA,EAAKe,EAAKC,MAAQ,EAC5BhB,GAAMe,EAAKC,MAEb,IAAK,MAAMD,KAAQ7B,EAAK+B,YACtBF,EAAKD,GAAKA,EAAKC,EAAKC,MAAQ,EAC5BF,GAAMC,EAAKC,KAEf,CACF,CAEe,SAASE,IACtB,IAEYC,EAGRC,EACAC,EANAC,EAAK,EAAGtB,EAAK,EAAGuB,EAAK,EAAGT,EAAK,EAC7BU,EAAK,GACLC,EAAK,EACLhB,EAAKR,EACLyB,EAAQzC,EAGRmB,EAAQF,EACRI,EAAQD,EACRsB,EAAa,EAEjB,SAASC,IACP,MAAMzB,EAAQ,CAACC,MAAOA,EAAMyB,MAAM,KAAMC,WAAYxB,MAAOA,EAAMuB,MAAM,KAAMC,YAO7E,OAoDF,SAAyBC,GAAiB,IAAhB,MAAC3B,EAAK,MAAEE,GAAMyB,EACtC,IAAK,MAAOC,EAAG9C,KAASkB,EAAM6B,UAC5B/C,EAAKN,MAAQoD,EACb9C,EAAKE,YAAc,GACnBF,EAAK+B,YAAc,GAErB,MAAMT,EAAW,IAAI0B,IAAI9B,EAAM+B,KAAI,CAACrD,EAAGkD,IAAM,CAACvB,EAAG3B,EAAGkD,EAAG5B,GAAQtB,MAC/D,IAAK,MAAOkD,EAAGjB,KAAST,EAAM2B,UAAW,CACvClB,EAAKnC,MAAQoD,EACb,IAAI,OAAClC,EAAM,OAAEf,GAAUgC,EACD,kBAAXjB,IAAqBA,EAASiB,EAAKjB,OAASS,EAAKC,EAAUV,IAChD,kBAAXf,IAAqBA,EAASgC,EAAKhC,OAASwB,EAAKC,EAAUzB,IACtEe,EAAOV,YAAYgD,KAAKrB,GACxBhC,EAAOkC,YAAYmB,KAAKrB,EAC1B,CACA,GAAgB,MAAZM,EACF,IAAK,MAAM,YAACjC,EAAW,YAAE6B,KAAgBb,EACvChB,EAAYgC,KAAKC,GACjBJ,EAAYG,KAAKC,EAGvB,CA/EEgB,CAAiBlC,GAiFnB,SAA0BmC,GAAU,IAAT,MAAClC,GAAMkC,EAChC,IAAK,MAAMpD,KAAQkB,EACjBlB,EAAKP,WAA4BD,IAApBQ,EAAKqD,WACZC,KAAKjD,IAAID,EAAIJ,EAAKE,YAAaT,GAAQW,EAAIJ,EAAK+B,YAAatC,IAC7DO,EAAKqD,UAEf,CAtFEE,CAAkBtC,GAwFpB,SAA0BuC,GAAU,IAAT,MAACtC,GAAMsC,EAChC,MAAMvD,EAAIiB,EAAMf,OAChB,IAAIsD,EAAU,IAAIC,IAAIxC,GAClByC,EAAO,IAAID,IACXnD,EAAI,EACR,KAAOkD,EAAQG,MAAM,CACnB,IAAK,MAAM5D,KAAQyD,EAAS,CAC1BzD,EAAKF,MAAQS,EACb,IAAK,MAAM,OAACV,KAAWG,EAAKE,YAC1ByD,EAAKE,IAAIhE,EAEb,CACA,KAAMU,EAAIN,EAAG,MAAM,IAAIwB,MAAM,iBAC7BgC,EAAUE,EACVA,EAAO,IAAID,GACb,CACF,CAvGEI,CAAkB7C,GAyGpB,SAA2B8C,GAAU,IAAT,MAAC7C,GAAM6C,EACjC,MAAM9D,EAAIiB,EAAMf,OAChB,IAAIsD,EAAU,IAAIC,IAAIxC,GAClByC,EAAO,IAAID,IACXnD,EAAI,EACR,KAAOkD,EAAQG,MAAM,CACnB,IAAK,MAAM5D,KAAQyD,EAAS,CAC1BzD,EAAKgE,OAASzD,EACd,IAAK,MAAM,OAACK,KAAWZ,EAAK+B,YAC1B4B,EAAKE,IAAIjD,EAEb,CACA,KAAML,EAAIN,EAAG,MAAM,IAAIwB,MAAM,iBAC7BgC,EAAUE,EACVA,EAAO,IAAID,GACb,CACF,CAxHEO,CAAmBhD,GAkKrB,SAA6BA,GAC3B,MAAMiD,EAzCR,SAA0BC,GAAU,IAAT,MAACjD,GAAMiD,EAChC,MAAM5D,EAAIF,EAAIa,GAAOtB,GAAKA,EAAEE,QAAS,EAC/BsE,GAAM/B,EAAKD,EAAKE,IAAO/B,EAAI,GAC3B2D,EAAU,IAAIG,MAAM9D,GAC1B,IAAK,MAAMP,KAAQkB,EAAO,CACxB,MAAM4B,EAAIQ,KAAKjD,IAAI,EAAGiD,KAAKjE,IAAIkB,EAAI,EAAG+C,KAAKgB,MAAM9B,EAAM+B,KAAK,KAAMvE,EAAMO,MACxEP,EAAKwE,MAAQ1B,EACb9C,EAAKoC,GAAKA,EAAKU,EAAIsB,EACnBpE,EAAKqC,GAAKrC,EAAKoC,GAAKE,EAChB4B,EAAQpB,GAAIoB,EAAQpB,GAAGI,KAAKlD,GAC3BkE,EAAQpB,GAAK,CAAC9C,EACrB,CACA,GAAIkC,EAAM,IAAK,MAAMuC,KAAUP,EAC7BO,EAAOvC,KAAKA,GAEd,OAAOgC,CACT,CAyBkBQ,CAAkBzD,GAClCgB,EAAKqB,KAAKjE,IAAIkD,GAAKX,EAAKd,IAAOT,EAAI6D,GAASS,GAAKA,EAAExE,SAAU,IAxB/D,SAAgC+D,GAC9B,MAAMU,EAAKvF,EAAI6E,GAASS,IAAM/C,EAAKd,GAAM6D,EAAExE,OAAS,GAAK8B,GAAM7B,EAAIuE,EAAGlF,KACtE,IAAK,MAAMyB,KAASgD,EAAS,CAC3B,IAAIW,EAAI/D,EACR,IAAK,MAAMd,KAAQkB,EAAO,CACxBlB,EAAKc,GAAK+D,EACV7E,EAAK4B,GAAKiD,EAAI7E,EAAKP,MAAQmF,EAC3BC,EAAI7E,EAAK4B,GAAKK,EACd,IAAK,MAAMJ,KAAQ7B,EAAKE,YACtB2B,EAAKC,MAAQD,EAAKpC,MAAQmF,CAE9B,CACAC,GAAKjD,EAAKiD,EAAI5C,IAAOf,EAAMf,OAAS,GACpC,IAAK,IAAI2C,EAAI,EAAGA,EAAI5B,EAAMf,SAAU2C,EAAG,CACrC,MAAM9C,EAAOkB,EAAM4B,GACnB9C,EAAKc,IAAM+D,GAAK/B,EAAI,GACpB9C,EAAK4B,IAAMiD,GAAK/B,EAAI,EACtB,CACAgC,EAAa5D,EACf,CACF,CAKE6D,CAAuBb,GACvB,IAAK,IAAIpB,EAAI,EAAGA,EAAIL,IAAcK,EAAG,CACnC,MAAMkC,EAAQ1B,KAAK2B,IAAI,IAAMnC,GACvBoC,EAAO5B,KAAKjD,IAAI,EAAI2E,GAAQlC,EAAI,GAAKL,GAC3C0C,EAAiBjB,EAASc,EAAOE,GACjCE,EAAiBlB,EAASc,EAAOE,EACnC,CACF,CA3KEG,CAAoBpE,GACpBS,EAAoBT,GACbA,CACT,CA2KA,SAASmE,EAAiBlB,EAASc,EAAOE,GACxC,IAAK,IAAIpC,EAAI,EAAG7C,EAAIiE,EAAQ/D,OAAQ2C,EAAI7C,IAAK6C,EAAG,CAC9C,MAAM2B,EAASP,EAAQpB,GACvB,IAAK,MAAMjD,KAAU4E,EAAQ,CAC3B,IAAII,EAAI,EACJS,EAAI,EACR,IAAK,MAAM,OAAC1E,EAAM,MAAEnB,KAAUI,EAAOkC,YAAa,CAChD,IAAIwD,EAAI9F,GAASI,EAAO2E,MAAQ5D,EAAO4D,OACvCK,GAAKW,EAAU5E,EAAQf,GAAU0F,EACjCD,GAAKC,CACP,CACA,KAAMD,EAAI,GAAI,SACd,IAAI/C,GAAMsC,EAAIS,EAAIzF,EAAOiB,IAAMkE,EAC/BnF,EAAOiB,IAAMyB,EACb1C,EAAO+B,IAAMW,EACbkD,EAAiB5F,EACnB,MACaL,IAAT0C,GAAoBuC,EAAOvC,KAAKvB,GACpC+E,EAAkBjB,EAAQS,EAC5B,CACF,CAGA,SAASC,EAAiBjB,EAASc,EAAOE,GACxC,IAAK,IAAwBpC,EAAhBoB,EAAQ/D,OAAgB,EAAG2C,GAAK,IAAKA,EAAG,CACnD,MAAM2B,EAASP,EAAQpB,GACvB,IAAK,MAAMlC,KAAU6D,EAAQ,CAC3B,IAAII,EAAI,EACJS,EAAI,EACR,IAAK,MAAM,OAACzF,EAAM,MAAEJ,KAAUmB,EAAOV,YAAa,CAChD,IAAIqF,EAAI9F,GAASI,EAAO2E,MAAQ5D,EAAO4D,OACvCK,GAAKc,EAAU/E,EAAQf,GAAU0F,EACjCD,GAAKC,CACP,CACA,KAAMD,EAAI,GAAI,SACd,IAAI/C,GAAMsC,EAAIS,EAAI1E,EAAOE,IAAMkE,EAC/BpE,EAAOE,IAAMyB,EACb3B,EAAOgB,IAAMW,EACbkD,EAAiB7E,EACnB,MACapB,IAAT0C,GAAoBuC,EAAOvC,KAAKvB,GACpC+E,EAAkBjB,EAAQS,EAC5B,CACF,CAEA,SAASQ,EAAkBxE,EAAO8D,GAChC,MAAMlC,EAAI5B,EAAMf,QAAU,EACpByF,EAAU1E,EAAM4B,GACtB+C,EAA6B3E,EAAO0E,EAAQ9E,GAAKmB,EAAIa,EAAI,EAAGkC,GAC5Dc,EAA6B5E,EAAO0E,EAAQhE,GAAKK,EAAIa,EAAI,EAAGkC,GAC5Da,EAA6B3E,EAAOU,EAAIV,EAAMf,OAAS,EAAG6E,GAC1Dc,EAA6B5E,EAAOJ,EAAI,EAAGkE,EAC7C,CAGA,SAASc,EAA6B5E,EAAO2D,EAAG/B,EAAGkC,GACjD,KAAOlC,EAAI5B,EAAMf,SAAU2C,EAAG,CAC5B,MAAM9C,EAAOkB,EAAM4B,GACbP,GAAMsC,EAAI7E,EAAKc,IAAMkE,EACvBzC,EAAK,OAAMvC,EAAKc,IAAMyB,EAAIvC,EAAK4B,IAAMW,GACzCsC,EAAI7E,EAAK4B,GAAKK,CAChB,CACF,CAGA,SAAS4D,EAA6B3E,EAAO2D,EAAG/B,EAAGkC,GACjD,KAAOlC,GAAK,IAAKA,EAAG,CAClB,MAAM9C,EAAOkB,EAAM4B,GACbP,GAAMvC,EAAK4B,GAAKiD,GAAKG,EACvBzC,EAAK,OAAMvC,EAAKc,IAAMyB,EAAIvC,EAAK4B,IAAMW,GACzCsC,EAAI7E,EAAKc,GAAKmB,CAChB,CACF,CAEA,SAASwD,EAAgBM,GAA6B,IAA5B,YAAC7F,EAAW,YAAE6B,GAAYgE,EAClD,QAAiBvG,IAAb2C,EAAwB,CAC1B,IAAK,MAAOvB,QAAQ,YAACV,MAAiB6B,EACpC7B,EAAYgC,KAAKrB,GAEnB,IAAK,MAAOhB,QAAQ,YAACkC,MAAiB7B,EACpC6B,EAAYG,KAAK1B,EAErB,CACF,CAEA,SAASsE,EAAa5D,GACpB,QAAiB1B,IAAb2C,EACF,IAAK,MAAM,YAACjC,EAAW,YAAE6B,KAAgBb,EACvChB,EAAYgC,KAAKrB,GACjBkB,EAAYG,KAAK1B,EAGvB,CAGA,SAASgF,EAAU5E,EAAQf,GACzB,IAAIgF,EAAIjE,EAAOE,IAAMF,EAAOV,YAAYC,OAAS,GAAK8B,EAAK,EAC3D,IAAK,MAAOpC,OAAQG,EAAI,MAAE8B,KAAUlB,EAAOV,YAAa,CACtD,GAAIF,IAASH,EAAQ,MACrBgF,GAAK/C,EAAQG,CACf,CACA,IAAK,MAAOrB,OAAQZ,EAAI,MAAE8B,KAAUjC,EAAOkC,YAAa,CACtD,GAAI/B,IAASY,EAAQ,MACrBiE,GAAK/C,CACP,CACA,OAAO+C,CACT,CAGA,SAASc,EAAU/E,EAAQf,GACzB,IAAIgF,EAAIhF,EAAOiB,IAAMjB,EAAOkC,YAAY5B,OAAS,GAAK8B,EAAK,EAC3D,IAAK,MAAOrB,OAAQZ,EAAI,MAAE8B,KAAUjC,EAAOkC,YAAa,CACtD,GAAI/B,IAASY,EAAQ,MACrBiE,GAAK/C,EAAQG,CACf,CACA,IAAK,MAAOpC,OAAQG,EAAI,MAAE8B,KAAUlB,EAAOV,YAAa,CACtD,GAAIF,IAASH,EAAQ,MACrBgF,GAAK/C,CACP,CACA,OAAO+C,CACT,CAEA,OAnSAnC,EAAOsD,OAAS,SAAS/E,GAEvB,OADAS,EAAoBT,GACbA,CACT,EAEAyB,EAAOuD,OAAS,SAASC,GACvB,OAAOtD,UAAUzC,QAAUoB,EAAkB,oBAAN2E,EAAmBA,EAAI5F,EAAS4F,GAAIxD,GAAUnB,CACvF,EAEAmB,EAAOyD,UAAY,SAASD,GAC1B,OAAOtD,UAAUzC,QAAUqC,EAAqB,oBAAN0D,EAAmBA,EAAI5F,EAAS4F,GAAIxD,GAAUF,CAC1F,EAEAE,EAAO0D,SAAW,SAASF,GACzB,OAAOtD,UAAUzC,QAAU+B,EAAOgE,EAAGxD,GAAUR,CACjD,EAEAQ,EAAO2D,UAAY,SAASH,GAC1B,OAAOtD,UAAUzC,QAAUmC,GAAM4D,EAAGxD,GAAUJ,CAChD,EAEAI,EAAO4D,YAAc,SAASJ,GAC5B,OAAOtD,UAAUzC,QAAUoC,EAAKN,GAAMiE,EAAGxD,GAAUH,CACrD,EAEAG,EAAOxB,MAAQ,SAASgF,GACtB,OAAOtD,UAAUzC,QAAUe,EAAqB,oBAANgF,EAAmBA,EAAI5F,EAAS4F,GAAIxD,GAAUxB,CAC1F,EAEAwB,EAAOtB,MAAQ,SAAS8E,GACtB,OAAOtD,UAAUzC,QAAUiB,EAAqB,oBAAN8E,EAAmBA,EAAI5F,EAAS4F,GAAIxD,GAAUtB,CAC1F,EAEAsB,EAAOP,SAAW,SAAS+D,GACzB,OAAOtD,UAAUzC,QAAUgC,EAAW+D,EAAGxD,GAAUP,CACrD,EAEAO,EAAOkB,KAAO,SAASsC,GACrB,OAAOtD,UAAUzC,QAAUiC,EAAKtB,EAAK,EAAGuB,GAAM6D,EAAE,GAAItE,GAAMsE,EAAE,GAAIxD,GAAU,CAACL,EAAKD,EAAIR,EAAKd,EAC3F,EAEA4B,EAAO6D,OAAS,SAASL,GACvB,OAAOtD,UAAUzC,QAAUiC,GAAM8D,EAAE,GAAG,GAAI7D,GAAM6D,EAAE,GAAG,GAAIpF,GAAMoF,EAAE,GAAG,GAAItE,GAAMsE,EAAE,GAAG,GAAIxD,GAAU,CAAC,CAACN,EAAItB,GAAK,CAACuB,EAAIT,GACnH,EAEAc,EAAOD,WAAa,SAASyD,GAC3B,OAAOtD,UAAUzC,QAAUsC,GAAcyD,EAAGxD,GAAUD,CACxD,EAoPOC,CACT,CChXA,IAAI8D,EAAKlD,KAAKmD,GACVC,EAAM,EAAIF,EACVG,EAAU,KACVC,EAAaF,EAAMC,EAEvB,SAASE,IACPC,KAAKC,IAAMD,KAAKE,IAChBF,KAAKG,IAAMH,KAAKI,IAAM,KACtBJ,KAAKZ,EAAI,EACX,CAEA,SAASiB,IACP,OAAO,IAAIN,CACb,CAEAA,EAAKO,UAAYD,EAAKC,UAAY,CAChCC,YAAaR,EACbS,OAAQ,SAAS/G,EAAGsE,GAClBiC,KAAKZ,GAAK,KAAOY,KAAKC,IAAMD,KAAKG,KAAO1G,GAAK,KAAOuG,KAAKE,IAAMF,KAAKI,KAAOrC,EAC7E,EACA0C,UAAW,WACQ,OAAbT,KAAKG,MACPH,KAAKG,IAAMH,KAAKC,IAAKD,KAAKI,IAAMJ,KAAKE,IACrCF,KAAKZ,GAAK,IAEd,EACAsB,OAAQ,SAASjH,EAAGsE,GAClBiC,KAAKZ,GAAK,KAAOY,KAAKG,KAAO1G,GAAK,KAAOuG,KAAKI,KAAOrC,EACvD,EACA4C,iBAAkB,SAASpF,EAAIT,EAAIrB,EAAGsE,GACpCiC,KAAKZ,GAAK,MAAQ7D,EAAM,MAAQT,EAAM,KAAOkF,KAAKG,KAAO1G,GAAK,KAAOuG,KAAKI,KAAOrC,EACnF,EACA6C,cAAe,SAASrF,EAAIT,EAAI+F,EAAIC,EAAIrH,EAAGsE,GACzCiC,KAAKZ,GAAK,MAAQ7D,EAAM,MAAQT,EAAM,MAAQ+F,EAAM,MAAQC,EAAM,KAAOd,KAAKG,KAAO1G,GAAK,KAAOuG,KAAKI,KAAOrC,EAC/G,EACAgD,MAAO,SAASxF,EAAIT,EAAI+F,EAAIC,EAAIE,GAC9BzF,GAAMA,EAAIT,GAAMA,EAAI+F,GAAMA,EAAIC,GAAMA,EAAIE,GAAKA,EAC7C,IAAI1F,EAAK0E,KAAKG,IACVnG,EAAKgG,KAAKI,IACVa,EAAMJ,EAAKtF,EACX2F,EAAMJ,EAAKhG,EACXqG,EAAM7F,EAAKC,EACX6F,EAAMpH,EAAKc,EACXuG,EAAQF,EAAMA,EAAMC,EAAMA,EAG9B,GAAIJ,EAAI,EAAG,MAAM,IAAIrG,MAAM,oBAAsBqG,GAGjD,GAAiB,OAAbhB,KAAKG,IACPH,KAAKZ,GAAK,KAAOY,KAAKG,IAAM5E,GAAM,KAAOyE,KAAKI,IAAMtF,QAIjD,GAAMuG,EAAQxB,EAKd,GAAMrD,KAAK8E,IAAIF,EAAMH,EAAMC,EAAMC,GAAOtB,GAAamB,EAKrD,CACH,IAAIO,EAAMV,EAAKvF,EACXkG,EAAMV,EAAK9G,EACXyH,EAAQR,EAAMA,EAAMC,EAAMA,EAC1BQ,EAAQH,EAAMA,EAAMC,EAAMA,EAC1BG,EAAMnF,KAAKoF,KAAKH,GAChBI,EAAMrF,KAAKoF,KAAKP,GAChBS,EAAId,EAAIxE,KAAKuF,KAAKrC,EAAKlD,KAAKwF,MAAMP,EAAQJ,EAAQK,IAAU,EAAIC,EAAME,KAAS,GAC/EI,EAAMH,EAAID,EACVK,EAAMJ,EAAIH,EAGVnF,KAAK8E,IAAIW,EAAM,GAAKpC,IACtBG,KAAKZ,GAAK,KAAO7D,EAAK0G,EAAMd,GAAO,KAAOrG,EAAKmH,EAAMb,IAGvDpB,KAAKZ,GAAK,IAAM4B,EAAI,IAAMA,EAAI,WAAaI,EAAMG,EAAMJ,EAAMK,GAAQ,KAAOxB,KAAKG,IAAM5E,EAAK2G,EAAMjB,GAAO,KAAOjB,KAAKI,IAAMtF,EAAKoH,EAAMhB,EACxI,MArBElB,KAAKZ,GAAK,KAAOY,KAAKG,IAAM5E,GAAM,KAAOyE,KAAKI,IAAMtF,QAsBxD,EACAqH,IAAK,SAAS1I,EAAGsE,EAAGiD,EAAGoB,EAAIC,EAAIC,GAC7B7I,GAAKA,EAAGsE,GAAKA,EAAWuE,IAAQA,EAChC,IAAI9G,GADYwF,GAAKA,GACRxE,KAAK+F,IAAIH,GAClB3G,EAAKuF,EAAIxE,KAAKgG,IAAIJ,GAClB9G,EAAK7B,EAAI+B,EACTxB,EAAK+D,EAAItC,EACTgH,EAAK,EAAIH,EACTI,EAAKJ,EAAMF,EAAKC,EAAKA,EAAKD,EAG9B,GAAIpB,EAAI,EAAG,MAAM,IAAIrG,MAAM,oBAAsBqG,GAGhC,OAAbhB,KAAKG,IACPH,KAAKZ,GAAK,IAAM9D,EAAK,IAAMtB,GAIpBwC,KAAK8E,IAAItB,KAAKG,IAAM7E,GAAMuE,GAAWrD,KAAK8E,IAAItB,KAAKI,IAAMpG,GAAM6F,KACtEG,KAAKZ,GAAK,IAAM9D,EAAK,IAAMtB,GAIxBgH,IAGD0B,EAAK,IAAGA,EAAKA,EAAK9C,EAAMA,GAGxB8C,EAAK5C,EACPE,KAAKZ,GAAK,IAAM4B,EAAI,IAAMA,EAAI,QAAUyB,EAAK,KAAOhJ,EAAI+B,GAAM,KAAOuC,EAAItC,GAAM,IAAMuF,EAAI,IAAMA,EAAI,QAAUyB,EAAK,KAAOzC,KAAKG,IAAM7E,GAAM,KAAO0E,KAAKI,IAAMpG,GAIrJ0I,EAAK7C,IACZG,KAAKZ,GAAK,IAAM4B,EAAI,IAAMA,EAAI,SAAW0B,GAAMhD,GAAO,IAAM+C,EAAK,KAAOzC,KAAKG,IAAM1G,EAAIuH,EAAIxE,KAAK+F,IAAIF,IAAO,KAAOrC,KAAKI,IAAMrC,EAAIiD,EAAIxE,KAAKgG,IAAIH,KAElJ,EACAM,KAAM,SAASlJ,EAAGsE,EAAGS,EAAGoE,GACtB5C,KAAKZ,GAAK,KAAOY,KAAKC,IAAMD,KAAKG,KAAO1G,GAAK,KAAOuG,KAAKE,IAAMF,KAAKI,KAAOrC,GAAK,MAAQS,EAAK,MAAQoE,EAAK,KAAQpE,EAAK,GACzH,EACAqE,SAAU,WACR,OAAO7C,KAAKZ,CACd,GAGF,UCjIO,IAAI0D,EAAQvF,MAAM+C,UAAUwC,MCApB,WAASrJ,GACtB,OAAO,WACL,OAAOA,CACT,CACF,CCJO,SAASA,EAAEsJ,GAChB,OAAOA,EAAE,EACX,CAEO,SAAShF,EAAEgF,GAChB,OAAOA,EAAE,EACX,CCAA,SAASC,EAAWlK,GAClB,OAAOA,EAAEgB,MACX,CAEA,SAASmJ,EAAWnK,GAClB,OAAOA,EAAEC,MACX,CAEA,SAASgC,EAAKmI,GACZ,IAAIpJ,EAASkJ,EACTjK,EAASkK,EACTxJ,EAAI0J,EACJpF,EAAIqF,EACJC,EAAU,KAEd,SAAStI,IACP,IAAIuI,EAAQC,EAAOT,EAAMrF,KAAK3B,WAAY0H,EAAI1J,EAAO+B,MAAMmE,KAAMuD,GAAOE,EAAI1K,EAAO8C,MAAMmE,KAAMuD,GAG/F,GAFKF,IAASA,EAAUC,EAASjD,KACjC6C,EAAMG,GAAU5J,EAAEoC,MAAMmE,MAAOuD,EAAK,GAAKC,EAAGD,KAASxF,EAAElC,MAAMmE,KAAMuD,IAAQ9J,EAAEoC,MAAMmE,MAAOuD,EAAK,GAAKE,EAAGF,KAASxF,EAAElC,MAAMmE,KAAMuD,IAC1HD,EAAQ,OAAOD,EAAU,KAAMC,EAAS,IAAM,IACpD,CAsBA,OApBAvI,EAAKjB,OAAS,SAASsF,GACrB,OAAOtD,UAAUzC,QAAUS,EAASsF,EAAGrE,GAAQjB,CACjD,EAEAiB,EAAKhC,OAAS,SAASqG,GACrB,OAAOtD,UAAUzC,QAAUN,EAASqG,EAAGrE,GAAQhC,CACjD,EAEAgC,EAAKtB,EAAI,SAAS2F,GAChB,OAAOtD,UAAUzC,QAAUI,EAAiB,oBAAN2F,EAAmBA,EAAI5F,GAAU4F,GAAIrE,GAAQtB,CACrF,EAEAsB,EAAKgD,EAAI,SAASqB,GAChB,OAAOtD,UAAUzC,QAAU0E,EAAiB,oBAANqB,EAAmBA,EAAI5F,GAAU4F,GAAIrE,GAAQgD,CACrF,EAEAhD,EAAKsI,QAAU,SAASjE,GACtB,OAAOtD,UAAUzC,QAAWgK,EAAe,MAALjE,EAAY,KAAOA,EAAIrE,GAAQsI,CACvE,EAEOtI,CACT,CAEA,SAAS2I,EAAgBL,EAAS/H,EAAItB,EAAIuB,EAAIT,GAC5CuI,EAAQ7C,OAAOlF,EAAItB,GACnBqJ,EAAQzC,cAActF,GAAMA,EAAKC,GAAM,EAAGvB,EAAIsB,EAAIR,EAAIS,EAAIT,EAC5D,CCpDA,SAAS6I,EAAiB7K,GACxB,MAAO,CAACA,EAAEgB,OAAOyB,GAAIzC,EAAEkB,GACzB,CAEA,SAAS4J,EAAiB9K,GACxB,MAAO,CAACA,EAAEC,OAAOuC,GAAIxC,EAAEgC,GACzB,CAEe,aACb,OD4DOC,EAAK2I,GC3DP5J,OAAO6J,GACP5K,OAAO6K,EACd,CCEA,IAAIC,EAAS,WACX,IAAIC,GAAoBC,EAAAA,EAAAA,KAAO,SAASC,EAAGvF,EAAGwF,EAAInC,GAChD,IAAKmC,EAAKA,GAAM,CAAC,EAAGnC,EAAIkC,EAAE3K,OAAQyI,IAAKmC,EAAGD,EAAElC,IAAMrD,GAClD,OAAOwF,CACT,GAAG,KAAMC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,EAAG,GAAI,IACnDC,EAAU,CACZC,OAAuBP,EAAAA,EAAAA,KAAO,WAC9B,GAAG,SACHQ,GAAI,CAAC,EACLC,SAAU,CAAE,MAAS,EAAG,MAAS,EAAG,OAAU,EAAG,QAAW,EAAG,IAAO,EAAG,QAAW,EAAG,OAAU,EAAG,SAAY,EAAG,IAAO,GAAI,gBAAiB,GAAI,MAAS,GAAI,gBAAiB,GAAI,eAAgB,GAAI,MAAS,GAAI,QAAW,GAAI,YAAe,GAAI,OAAU,GAAI,aAAgB,GAAI,iBAAoB,GAAI,QAAW,EAAG,KAAQ,GACxUC,WAAY,CAAE,EAAG,QAAS,EAAG,SAAU,EAAG,UAAW,GAAI,MAAO,GAAI,gBAAiB,GAAI,QAAS,GAAI,gBAAiB,GAAI,eAAgB,GAAI,SAAU,GAAI,eAAgB,GAAI,oBACjLC,aAAc,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,IAC1GC,eAA+BZ,EAAAA,EAAAA,KAAO,SAAmBa,EAAQC,EAAQC,EAAUP,EAAIQ,EAASC,EAAIC,GAClG,IAAIC,EAAKF,EAAG3L,OAAS,EACrB,OAAQ0L,GACN,KAAK,EACH,MAAMjL,EAASyK,EAAGY,iBAAiBH,EAAGE,EAAK,GAAGE,OAAOC,WAAW,KAAM,MAChEtM,EAASwL,EAAGY,iBAAiBH,EAAGE,EAAK,GAAGE,OAAOC,WAAW,KAAM,MAChE1M,EAAQ2M,WAAWN,EAAGE,GAAIE,QAChCb,EAAGgB,QAAQzL,EAAQf,EAAQJ,GAC3B,MACF,KAAK,EACL,KAAK,EACL,KAAK,GACHqH,KAAKwF,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHlF,KAAKwF,EAAIR,EAAGE,EAAK,GAGvB,GAAG,aACHO,MAAO,CAAC,CAAE,EAAG,EAAG,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAIvB,EAAK,GAAIC,GAAO,CAAE,EAAG,CAAC,EAAG,GAAI,EAAG,GAAI,GAAI,CAAC,EAAG,KAAOL,EAAEK,EAAK,CAAC,EAAG,GAAI,CAAE,EAAG,GAAI,EAAG,CAAC,EAAG,MAAQ,CAAE,GAAI,CAAC,EAAG,KAAOL,EAAEM,EAAK,CAAC,EAAG,IAAKN,EAAEM,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,CAAC,EAAG,KAAON,EAAEM,EAAK,CAAC,EAAG,KAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,IAAMN,EAAEK,EAAK,CAAC,EAAG,IAAK,CAAE,EAAG,GAAI,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAID,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAID,EAAK,GAAIC,GAAO,CAAE,GAAI,CAAC,EAAG,KAAOL,EAAEK,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,CAAC,EAAG,KAAOL,EAAEM,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAIF,EAAK,GAAIC,GAAOL,EAAE,CAAC,EAAG,EAAG,IAAK,CAAC,EAAG,KAC/hB4B,eAAgB,CAAE,GAAI,CAAC,EAAG,GAAI,GAAI,CAAC,EAAG,IACtCC,YAA4B5B,EAAAA,EAAAA,KAAO,SAAoB6B,EAAKC,GAC1D,IAAIA,EAAKC,YAEF,CACL,IAAIC,EAAQ,IAAIpL,MAAMiL,GAEtB,MADAG,EAAMF,KAAOA,EACPE,CACR,CALE/F,KAAKsE,MAAMsB,EAMf,GAAG,cACHI,OAAuBjC,EAAAA,EAAAA,KAAO,SAAekC,GAC3C,IAAIC,EAAOlG,KAAMmG,EAAQ,CAAC,GAAIC,EAAS,GAAIC,EAAS,CAAC,MAAOC,EAAS,GAAIb,EAAQzF,KAAKyF,MAAOb,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAG0B,EAAa,EAC7IC,EAAOF,EAAOxD,MAAMrF,KAAK3B,UAAW,GACpC2K,EAASC,OAAOC,OAAO3G,KAAK4G,OAC5BC,EAAc,CAAEtC,GAAI,CAAC,GACzB,IAAK,IAAIP,KAAKhE,KAAKuE,GACbmC,OAAOpG,UAAUwG,eAAerJ,KAAKuC,KAAKuE,GAAIP,KAChD6C,EAAYtC,GAAGP,GAAKhE,KAAKuE,GAAGP,IAGhCyC,EAAOM,SAASd,EAAOY,EAAYtC,IACnCsC,EAAYtC,GAAGqC,MAAQH,EACvBI,EAAYtC,GAAGV,OAAS7D,KACI,oBAAjByG,EAAOO,SAChBP,EAAOO,OAAS,CAAC,GAEnB,IAAIC,EAAQR,EAAOO,OACnBV,EAAOlK,KAAK6K,GACZ,IAAIC,EAAST,EAAOU,SAAWV,EAAOU,QAAQD,OAY9C,SAASE,IACP,IAAIC,EASJ,MAPqB,kBADrBA,EAAQjB,EAAOkB,OAASb,EAAOW,OA/BqI,KAiC9JC,aAAiB9J,QAEnB8J,GADAjB,EAASiB,GACMC,OAEjBD,EAAQnB,EAAK1B,SAAS6C,IAAUA,GAE3BA,CACT,CAtByC,oBAA9BR,EAAYtC,GAAGoB,WACxB3F,KAAK2F,WAAakB,EAAYtC,GAAGoB,WAEjC3F,KAAK2F,WAAae,OAAOa,eAAevH,MAAM2F,YAOhD5B,EAAAA,EAAAA,KALA,SAAkB5K,GAChBgN,EAAM9M,OAAS8M,EAAM9M,OAAS,EAAIF,EAClCkN,EAAOhN,OAASgN,EAAOhN,OAASF,EAChCmN,EAAOjN,OAASiN,EAAOjN,OAASF,CAClC,GACiB,aAajB4K,EAAAA,EAAAA,IAAOqD,EAAK,OAEZ,IADA,IAAII,EAAQC,EAAgBC,EAAOC,EAAW3G,EAAe+B,EAAG6E,EAAKC,EAAUC,EAA9BC,EAAQ,CAAC,IAC7C,CAUX,GATAL,EAAQvB,EAAMA,EAAM9M,OAAS,GACzB2G,KAAK0F,eAAegC,GACtBC,EAAS3H,KAAK0F,eAAegC,IAEd,OAAXF,GAAoC,oBAAVA,IAC5BA,EAASJ,KAEXO,EAASlC,EAAMiC,IAAUjC,EAAMiC,GAAOF,IAElB,qBAAXG,IAA2BA,EAAOtO,SAAWsO,EAAO,GAAI,CACjE,IAAIK,EAAS,GAEb,IAAKjF,KADL+E,EAAW,GACDrC,EAAMiC,GACV1H,KAAKyE,WAAW1B,IAAMA,EAzD6H,GA0DrJ+E,EAAS1L,KAAK,IAAM4D,KAAKyE,WAAW1B,GAAK,KAI3CiF,EADEvB,EAAOwB,aACA,wBAA0BnD,EAAW,GAAK,MAAQ2B,EAAOwB,eAAiB,eAAiBH,EAASI,KAAK,MAAQ,WAAalI,KAAKyE,WAAW+C,IAAWA,GAAU,IAEnK,wBAA0B1C,EAAW,GAAK,iBAhE6G,GAgE1F0C,EAAgB,eAAiB,KAAOxH,KAAKyE,WAAW+C,IAAWA,GAAU,KAErJxH,KAAK2F,WAAWqC,EAAQ,CACtBG,KAAM1B,EAAO2B,MACbf,MAAOrH,KAAKyE,WAAW+C,IAAWA,EAClCa,KAAM5B,EAAO3B,SACbwD,IAAKrB,EACLa,YAEJ,CACA,GAAIH,EAAO,aAAcpK,OAASoK,EAAOtO,OAAS,EAChD,MAAM,IAAIsB,MAAM,oDAAsD+M,EAAQ,YAAcF,GAE9F,OAAQG,EAAO,IACb,KAAK,EACHxB,EAAM/J,KAAKoL,GACXnB,EAAOjK,KAAKqK,EAAO7B,QACnB0B,EAAOlK,KAAKqK,EAAOO,QACnBb,EAAM/J,KAAKuL,EAAO,IAClBH,EAAS,KACJC,GASHD,EAASC,EACTA,EAAiB,OATjB5C,EAAS4B,EAAO5B,OAChBD,EAAS6B,EAAO7B,OAChBE,EAAW2B,EAAO3B,SAClBmC,EAAQR,EAAOO,OACXT,EAAa,GACfA,KAMJ,MACF,KAAK,EAwBH,GAvBAqB,EAAM5H,KAAK0E,aAAaiD,EAAO,IAAI,GACnCI,EAAMvC,EAAIa,EAAOA,EAAOhN,OAASuO,GACjCG,EAAM9C,GAAK,CACTsD,WAAYjC,EAAOA,EAAOjN,QAAUuO,GAAO,IAAIW,WAC/CC,UAAWlC,EAAOA,EAAOjN,OAAS,GAAGmP,UACrCC,aAAcnC,EAAOA,EAAOjN,QAAUuO,GAAO,IAAIa,aACjDC,YAAapC,EAAOA,EAAOjN,OAAS,GAAGqP,aAErCxB,IACFa,EAAM9C,GAAG0D,MAAQ,CACfrC,EAAOA,EAAOjN,QAAUuO,GAAO,IAAIe,MAAM,GACzCrC,EAAOA,EAAOjN,OAAS,GAAGsP,MAAM,KAYnB,qBATjB3H,EAAIhB,KAAK2E,cAAc9I,MAAMkM,EAAO,CAClCnD,EACAC,EACAC,EACA+B,EAAYtC,GACZoD,EAAO,GACPtB,EACAC,GACAsC,OAAOpC,KAEP,OAAOxF,EAEL4G,IACFzB,EAAQA,EAAMrD,MAAM,GAAI,EAAI8E,EAAM,GAClCvB,EAASA,EAAOvD,MAAM,GAAI,EAAI8E,GAC9BtB,EAASA,EAAOxD,MAAM,GAAI,EAAI8E,IAEhCzB,EAAM/J,KAAK4D,KAAK0E,aAAaiD,EAAO,IAAI,IACxCtB,EAAOjK,KAAK2L,EAAMvC,GAClBc,EAAOlK,KAAK2L,EAAM9C,IAClB4C,EAAWpC,EAAMU,EAAMA,EAAM9M,OAAS,IAAI8M,EAAMA,EAAM9M,OAAS,IAC/D8M,EAAM/J,KAAKyL,GACX,MACF,KAAK,EACH,OAAO,EAEb,CACA,OAAO,CACT,GAAG,UAEDjB,EAAwB,WA2T1B,MA1Ta,CACXiC,IAAK,EACLlD,YAA4B5B,EAAAA,EAAAA,KAAO,SAAoB6B,EAAKC,GAC1D,IAAI7F,KAAKuE,GAAGV,OAGV,MAAM,IAAIlJ,MAAMiL,GAFhB5F,KAAKuE,GAAGV,OAAO8B,WAAWC,EAAKC,EAInC,GAAG,cAEHkB,UAA0BhD,EAAAA,EAAAA,KAAO,SAASkC,EAAO1B,GAiB/C,OAhBAvE,KAAKuE,GAAKA,GAAMvE,KAAKuE,IAAM,CAAC,EAC5BvE,KAAK8I,OAAS7C,EACdjG,KAAK+I,MAAQ/I,KAAKgJ,WAAahJ,KAAKiJ,MAAO,EAC3CjJ,KAAK8E,SAAW9E,KAAK6E,OAAS,EAC9B7E,KAAK4E,OAAS5E,KAAKkJ,QAAUlJ,KAAKoI,MAAQ,GAC1CpI,KAAKmJ,eAAiB,CAAC,WACvBnJ,KAAKgH,OAAS,CACZuB,WAAY,EACZE,aAAc,EACdD,UAAW,EACXE,YAAa,GAEX1I,KAAKmH,QAAQD,SACflH,KAAKgH,OAAO2B,MAAQ,CAAC,EAAG,IAE1B3I,KAAKoJ,OAAS,EACPpJ,IACT,GAAG,YAEHiG,OAAuBlC,EAAAA,EAAAA,KAAO,WAC5B,IAAIsF,EAAKrJ,KAAK8I,OAAO,GAiBrB,OAhBA9I,KAAK4E,QAAUyE,EACfrJ,KAAK6E,SACL7E,KAAKoJ,SACLpJ,KAAKoI,OAASiB,EACdrJ,KAAKkJ,SAAWG,EACJA,EAAGjB,MAAM,oBAEnBpI,KAAK8E,WACL9E,KAAKgH,OAAOwB,aAEZxI,KAAKgH,OAAO0B,cAEV1I,KAAKmH,QAAQD,QACflH,KAAKgH,OAAO2B,MAAM,KAEpB3I,KAAK8I,OAAS9I,KAAK8I,OAAOhG,MAAM,GACzBuG,CACT,GAAG,SAEHC,OAAuBvF,EAAAA,EAAAA,KAAO,SAASsF,GACrC,IAAIzB,EAAMyB,EAAGhQ,OACTkQ,EAAQF,EAAGG,MAAM,iBACrBxJ,KAAK8I,OAASO,EAAKrJ,KAAK8I,OACxB9I,KAAK4E,OAAS5E,KAAK4E,OAAO6E,OAAO,EAAGzJ,KAAK4E,OAAOvL,OAASuO,GACzD5H,KAAKoJ,QAAUxB,EACf,IAAI8B,EAAW1J,KAAKoI,MAAMoB,MAAM,iBAChCxJ,KAAKoI,MAAQpI,KAAKoI,MAAMqB,OAAO,EAAGzJ,KAAKoI,MAAM/O,OAAS,GACtD2G,KAAKkJ,QAAUlJ,KAAKkJ,QAAQO,OAAO,EAAGzJ,KAAKkJ,QAAQ7P,OAAS,GACxDkQ,EAAMlQ,OAAS,IACjB2G,KAAK8E,UAAYyE,EAAMlQ,OAAS,GAElC,IAAI2H,EAAIhB,KAAKgH,OAAO2B,MAWpB,OAVA3I,KAAKgH,OAAS,CACZuB,WAAYvI,KAAKgH,OAAOuB,WACxBC,UAAWxI,KAAK8E,SAAW,EAC3B2D,aAAczI,KAAKgH,OAAOyB,aAC1BC,YAAaa,GAASA,EAAMlQ,SAAWqQ,EAASrQ,OAAS2G,KAAKgH,OAAOyB,aAAe,GAAKiB,EAASA,EAASrQ,OAASkQ,EAAMlQ,QAAQA,OAASkQ,EAAM,GAAGlQ,OAAS2G,KAAKgH,OAAOyB,aAAeb,GAEtL5H,KAAKmH,QAAQD,SACflH,KAAKgH,OAAO2B,MAAQ,CAAC3H,EAAE,GAAIA,EAAE,GAAKhB,KAAK6E,OAAS+C,IAElD5H,KAAK6E,OAAS7E,KAAK4E,OAAOvL,OACnB2G,IACT,GAAG,SAEH2J,MAAsB5F,EAAAA,EAAAA,KAAO,WAE3B,OADA/D,KAAK+I,OAAQ,EACN/I,IACT,GAAG,QAEH4J,QAAwB7F,EAAAA,EAAAA,KAAO,WAC7B,OAAI/D,KAAKmH,QAAQ0C,iBACf7J,KAAKgJ,YAAa,EAQbhJ,MANEA,KAAK2F,WAAW,0BAA4B3F,KAAK8E,SAAW,GAAK,mIAAqI9E,KAAKiI,eAAgB,CAChOE,KAAM,GACNd,MAAO,KACPgB,KAAMrI,KAAK8E,UAIjB,GAAG,UAEHgF,MAAsB/F,EAAAA,EAAAA,KAAO,SAAS5K,GACpC6G,KAAKsJ,MAAMtJ,KAAKoI,MAAMtF,MAAM3J,GAC9B,GAAG,QAEH4Q,WAA2BhG,EAAAA,EAAAA,KAAO,WAChC,IAAIiG,EAAOhK,KAAKkJ,QAAQO,OAAO,EAAGzJ,KAAKkJ,QAAQ7P,OAAS2G,KAAKoI,MAAM/O,QACnE,OAAQ2Q,EAAK3Q,OAAS,GAAK,MAAQ,IAAM2Q,EAAKP,QAAQ,IAAIQ,QAAQ,MAAO,GAC3E,GAAG,aAEHC,eAA+BnG,EAAAA,EAAAA,KAAO,WACpC,IAAIlH,EAAOmD,KAAKoI,MAIhB,OAHIvL,EAAKxD,OAAS,KAChBwD,GAAQmD,KAAK8I,OAAOW,OAAO,EAAG,GAAK5M,EAAKxD,UAElCwD,EAAK4M,OAAO,EAAG,KAAO5M,EAAKxD,OAAS,GAAK,MAAQ,KAAK4Q,QAAQ,MAAO,GAC/E,GAAG,iBAEHhC,cAA8BlE,EAAAA,EAAAA,KAAO,WACnC,IAAIoG,EAAMnK,KAAK+J,YACXlM,EAAI,IAAIN,MAAM4M,EAAI9Q,OAAS,GAAG6O,KAAK,KACvC,OAAOiC,EAAMnK,KAAKkK,gBAAkB,KAAOrM,EAAI,GACjD,GAAG,gBAEHuM,YAA4BrG,EAAAA,EAAAA,KAAO,SAASqE,EAAOiC,GACjD,IAAIhD,EAAOkC,EAAOe,EAmDlB,GAlDItK,KAAKmH,QAAQ0C,kBACfS,EAAS,CACPxF,SAAU9E,KAAK8E,SACfkC,OAAQ,CACNuB,WAAYvI,KAAKgH,OAAOuB,WACxBC,UAAWxI,KAAKwI,UAChBC,aAAczI,KAAKgH,OAAOyB,aAC1BC,YAAa1I,KAAKgH,OAAO0B,aAE3B9D,OAAQ5E,KAAK4E,OACbwD,MAAOpI,KAAKoI,MACZmC,QAASvK,KAAKuK,QACdrB,QAASlJ,KAAKkJ,QACdrE,OAAQ7E,KAAK6E,OACbuE,OAAQpJ,KAAKoJ,OACbL,MAAO/I,KAAK+I,MACZD,OAAQ9I,KAAK8I,OACbvE,GAAIvE,KAAKuE,GACT4E,eAAgBnJ,KAAKmJ,eAAerG,MAAM,GAC1CmG,KAAMjJ,KAAKiJ,MAETjJ,KAAKmH,QAAQD,SACfoD,EAAOtD,OAAO2B,MAAQ3I,KAAKgH,OAAO2B,MAAM7F,MAAM,MAGlDyG,EAAQnB,EAAM,GAAGA,MAAM,sBAErBpI,KAAK8E,UAAYyE,EAAMlQ,QAEzB2G,KAAKgH,OAAS,CACZuB,WAAYvI,KAAKgH,OAAOwB,UACxBA,UAAWxI,KAAK8E,SAAW,EAC3B2D,aAAczI,KAAKgH,OAAO0B,YAC1BA,YAAaa,EAAQA,EAAMA,EAAMlQ,OAAS,GAAGA,OAASkQ,EAAMA,EAAMlQ,OAAS,GAAG+O,MAAM,UAAU,GAAG/O,OAAS2G,KAAKgH,OAAO0B,YAAcN,EAAM,GAAG/O,QAE/I2G,KAAK4E,QAAUwD,EAAM,GACrBpI,KAAKoI,OAASA,EAAM,GACpBpI,KAAKuK,QAAUnC,EACfpI,KAAK6E,OAAS7E,KAAK4E,OAAOvL,OACtB2G,KAAKmH,QAAQD,SACflH,KAAKgH,OAAO2B,MAAQ,CAAC3I,KAAKoJ,OAAQpJ,KAAKoJ,QAAUpJ,KAAK6E,SAExD7E,KAAK+I,OAAQ,EACb/I,KAAKgJ,YAAa,EAClBhJ,KAAK8I,OAAS9I,KAAK8I,OAAOhG,MAAMsF,EAAM,GAAG/O,QACzC2G,KAAKkJ,SAAWd,EAAM,GACtBf,EAAQrH,KAAK2E,cAAclH,KAAKuC,KAAMA,KAAKuE,GAAIvE,KAAMqK,EAAcrK,KAAKmJ,eAAenJ,KAAKmJ,eAAe9P,OAAS,IAChH2G,KAAKiJ,MAAQjJ,KAAK8I,SACpB9I,KAAKiJ,MAAO,GAEV5B,EACF,OAAOA,EACF,GAAIrH,KAAKgJ,WAAY,CAC1B,IAAK,IAAIhF,KAAKsG,EACZtK,KAAKgE,GAAKsG,EAAOtG,GAEnB,OAAO,CACT,CACA,OAAO,CACT,GAAG,cAEHnH,MAAsBkH,EAAAA,EAAAA,KAAO,WAC3B,GAAI/D,KAAKiJ,KACP,OAAOjJ,KAAK6I,IAKd,IAAIxB,EAAOe,EAAOoC,EAAW5R,EAHxBoH,KAAK8I,SACR9I,KAAKiJ,MAAO,GAGTjJ,KAAK+I,QACR/I,KAAK4E,OAAS,GACd5E,KAAKoI,MAAQ,IAGf,IADA,IAAIqC,EAAQzK,KAAK0K,gBACR1O,EAAI,EAAGA,EAAIyO,EAAMpR,OAAQ2C,IAEhC,IADAwO,EAAYxK,KAAK8I,OAAOV,MAAMpI,KAAKyK,MAAMA,EAAMzO,SAC5BoM,GAASoC,EAAU,GAAGnR,OAAS+O,EAAM,GAAG/O,QAAS,CAGlE,GAFA+O,EAAQoC,EACR5R,EAAQoD,EACJgE,KAAKmH,QAAQ0C,gBAAiB,CAEhC,IAAc,KADdxC,EAAQrH,KAAKoK,WAAWI,EAAWC,EAAMzO,KAEvC,OAAOqL,EACF,GAAIrH,KAAKgJ,WAAY,CAC1BZ,GAAQ,EACR,QACF,CACE,OAAO,CAEX,CAAO,IAAKpI,KAAKmH,QAAQwD,KACvB,KAEJ,CAEF,OAAIvC,GAEY,KADdf,EAAQrH,KAAKoK,WAAWhC,EAAOqC,EAAM7R,MAE5ByO,EAIS,KAAhBrH,KAAK8I,OACA9I,KAAK6I,IAEL7I,KAAK2F,WAAW,0BAA4B3F,KAAK8E,SAAW,GAAK,yBAA2B9E,KAAKiI,eAAgB,CACtHE,KAAM,GACNd,MAAO,KACPgB,KAAMrI,KAAK8E,UAGjB,GAAG,QAEHsC,KAAqBrD,EAAAA,EAAAA,KAAO,WAC1B,IAAI/C,EAAIhB,KAAKnD,OACb,OAAImE,GAGKhB,KAAKoH,KAEhB,GAAG,OAEHwD,OAAuB7G,EAAAA,EAAAA,KAAO,SAAe8G,GAC3C7K,KAAKmJ,eAAe/M,KAAKyO,EAC3B,GAAG,SAEHC,UAA0B/G,EAAAA,EAAAA,KAAO,WAE/B,OADQ/D,KAAKmJ,eAAe9P,OAAS,EAC7B,EACC2G,KAAKmJ,eAAe7B,MAEpBtH,KAAKmJ,eAAe,EAE/B,GAAG,YAEHuB,eAA+B3G,EAAAA,EAAAA,KAAO,WACpC,OAAI/D,KAAKmJ,eAAe9P,QAAU2G,KAAKmJ,eAAenJ,KAAKmJ,eAAe9P,OAAS,GAC1E2G,KAAK+K,WAAW/K,KAAKmJ,eAAenJ,KAAKmJ,eAAe9P,OAAS,IAAIoR,MAErEzK,KAAK+K,WAAoB,QAAEN,KAEtC,GAAG,iBAEHO,UAA0BjH,EAAAA,EAAAA,KAAO,SAAkB5K,GAEjD,OADAA,EAAI6G,KAAKmJ,eAAe9P,OAAS,EAAImD,KAAK8E,IAAInI,GAAK,KAC1C,EACA6G,KAAKmJ,eAAehQ,GAEpB,SAEX,GAAG,YAEH8R,WAA2BlH,EAAAA,EAAAA,KAAO,SAAmB8G,GACnD7K,KAAK4K,MAAMC,EACb,GAAG,aAEHK,gBAAgCnH,EAAAA,EAAAA,KAAO,WACrC,OAAO/D,KAAKmJ,eAAe9P,MAC7B,GAAG,kBACH8N,QAAS,CAAE,oBAAoB,GAC/BxC,eAA+BZ,EAAAA,EAAAA,KAAO,SAAmBQ,EAAI4G,EAAKC,EAA2BC,GAE3F,OAAQD,GACN,KAAK,EAEH,OADApL,KAAKiL,UAAU,OACR,EAET,KAAK,EACH,OAAO,GAET,KAAK,EACH,OAAO,EAET,KAAK,EACH,OAAO,GAET,KAAK,EAEH,OADAjL,KAAKiL,UAAU,gBACR,GAET,KAAK,EACH,OAAO,GAET,KAAK,EAEH,OADAjL,KAAK8K,SAAS,gBACP,GAET,KAAK,EACH,OAAO,GAGb,GAAG,aACHL,MAAO,CAAC,sBAAuB,UAAW,kCAAmC,iBAAkB,iBAAkB,qDAAsD,6BAA8B,oGACrMM,WAAY,CAAE,IAAO,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,WAAa,GAAS,aAAgB,CAAE,MAAS,CAAC,EAAG,GAAI,WAAa,GAAS,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,WAAa,IAGvM,CA5T4B,GA8T5B,SAASO,IACPtL,KAAKuE,GAAK,CAAC,CACb,CAIA,OAPAF,EAAQuC,MAAQA,GAIhB7C,EAAAA,EAAAA,IAAOuH,EAAQ,UACfA,EAAOhL,UAAY+D,EACnBA,EAAQiH,OAASA,EACV,IAAIA,CACb,CA9fa,GA+fbzH,EAAOA,OAASA,EAChB,IAAI0H,EAAiB1H,EAGjBvJ,EAAQ,GACRF,EAAQ,GACRoR,EAA2B,IAAItP,IAC/BuP,GAAyB1H,EAAAA,EAAAA,KAAO,KAClCzJ,EAAQ,GACRF,EAAQ,GACRoR,EAA2B,IAAItP,KAC/BwP,EAAAA,EAAAA,KAAO,GACN,SACCC,EAAa,MACfpL,WAAAA,CAAYzG,EAAQf,GAAmB,IAAXJ,EAAKmD,UAAAzC,OAAA,QAAAX,IAAAoD,UAAA,GAAAA,UAAA,GAAG,EAClCkE,KAAKlG,OAASA,EACdkG,KAAKjH,OAASA,EACdiH,KAAKrH,MAAQA,CACf,CAAC,eAECoL,EAAAA,EAAAA,IAAO/D,KAAM,cAFd,IAKCuF,GAA0BxB,EAAAA,EAAAA,KAAO,CAACjK,EAAQf,EAAQJ,KACpD2B,EAAM8B,KAAK,IAAIuP,EAAW7R,EAAQf,EAAQJ,GAAO,GAChD,WACCiT,EAAa,MACfrL,WAAAA,CAAYsL,GACV7L,KAAK6L,GAAKA,CACZ,CAAC,eAEC9H,EAAAA,EAAAA,IAAO/D,KAAM,cAFd,IAKCmF,GAAmCpB,EAAAA,EAAAA,KAAQ8H,IAC7CA,EAAKC,EAAAA,GAAeC,aAAaF,GAAIG,EAAAA,EAAAA,OACrC,IAAI9S,EAAOsS,EAAS9Q,IAAImR,GAMxB,YALa,IAAT3S,IACFA,EAAO,IAAI0S,EAAWC,GACtBL,EAASS,IAAIJ,EAAI3S,GACjBkB,EAAMgC,KAAKlD,IAENA,CAAI,GACV,oBACCgT,GAA2BnI,EAAAA,EAAAA,KAAO,IAAM3J,GAAO,YAC/C+R,GAA2BpI,EAAAA,EAAAA,KAAO,IAAMzJ,GAAO,YAC/C8R,GAA2BrI,EAAAA,EAAAA,KAAO,KAAM,CAC1C3J,MAAOA,EAAM+B,KAAKjD,IAAI,CAAQuB,GAAIvB,EAAK2S,OACvCvR,MAAOA,EAAM6B,KAAKpB,IAAI,CACpBjB,OAAQiB,EAAKjB,OAAO+R,GACpB9S,OAAQgC,EAAKhC,OAAO8S,GACpBlT,MAAOoC,EAAKpC,aAEZ,YACA0T,EAAmB,CACrBb,WACAQ,WAA2BjI,EAAAA,EAAAA,KAAO,KAAMiI,EAAAA,EAAAA,MAAYpQ,QAAQ,aAC5DsQ,WACAC,WACAC,WACA7G,UACAJ,mBACAmH,YAAW,KACXC,YAAW,KACXC,kBAAiB,KACjBC,kBAAiB,KACjBC,gBAAe,KACfC,gBAAe,KACfjB,MAAOD,GAmBLmB,EAAM,MAAMC,EAAK,eAEjB9I,EAAAA,EAAAA,IAAO/D,KAAM,OAFI,GAEE,cAGnBA,KAAK8M,MAAQ,EAHM,GAKrB,WAAOjQ,CAAKkQ,GACV,OAAO,IAAIF,EAAKE,KAASF,EAAKC,MAChC,CACAvM,WAAAA,CAAY9F,GACVuF,KAAKvF,GAAKA,EACVuF,KAAKgN,KAAO,IAAIvS,GAClB,CACAoI,QAAAA,GACE,MAAO,OAAS7C,KAAKgN,KAAO,GAC9B,GAIEC,GAAgB,CAClBC,KXrnBK,SAAchU,GACnB,OAAOA,EAAKF,KACd,EWonBEmU,MXlnBK,SAAejU,EAAMC,GAC1B,OAAOA,EAAI,EAAID,EAAKgE,MACtB,EWinBEkQ,OX3mBK,SAAgBlU,GACrB,OAAOA,EAAK+B,YAAY5B,OAASH,EAAKF,MAChCE,EAAKE,YAAYC,OAASd,EAAIW,EAAKE,YAAaP,GAAe,EAC/D,CACR,EWwmBEI,QAASoU,GAEPC,IAAuBvJ,EAAAA,EAAAA,KAAO,SAASoE,EAAM1N,EAAI8S,EAAUC,GAC7D,MAAM,cAAEC,EAAe7R,OAAQ8R,IAAS1B,EAAAA,EAAAA,MAClC2B,EAAsBC,EAAAA,GAAchS,OAC1C,IAAIiS,EACkB,YAAlBJ,IACFI,GAAiBC,EAAAA,EAAAA,KAAS,KAAOrT,IAEnC,MAAMsT,EAAyB,YAAlBN,GAA8BK,EAAAA,EAAAA,KAASD,EAAezT,QAAQ,GAAG4T,gBAAgBC,OAAQH,EAAAA,EAAAA,KAAS,QACzGI,EAAwB,YAAlBT,EAA8BM,EAAKI,OAAO,QAAQ1T,QAAUqT,EAAAA,EAAAA,KAAS,QAAQrT,OACnFO,EAAQ0S,GAAM1S,OAAS2S,EAAoB3S,MAC3CkC,EAASwQ,GAAMxQ,QAAUyQ,EAAoB3S,MAC7CoT,EAAcV,GAAMU,aAAeT,EAAoBS,YACvDC,EAAgBX,GAAMW,eAAiBV,EAAoBU,cAC3DC,EAASZ,GAAMY,QAAUX,EAAoBW,OAC7CC,EAASb,GAAMa,QAAUZ,EAAoBY,OAC7CC,EAAad,GAAMc,YAAcb,EAAoBa,WACrDrU,EAAQqT,EAAQiB,GAAGrC,WACnB/M,EAAY4N,GAAcoB,GAEjBK,IAAWvP,QAAQrG,GAAMA,EAAE2B,KAAI8E,UAD5B,IACiDC,YAAY,IAAMgP,EAAa,GAAK,IAAInP,UAAUA,GAAWI,OAAO,CACrI,CAAC,EAAG,GACJ,CAACzE,EAAOkC,IAEVtB,CAAOzB,GACP,MAAMwU,GAAcC,EAAAA,EAAAA,KAAeC,EAAAA,IACnCX,EAAIY,OAAO,KAAKC,KAAK,QAAS,SAASC,UAAU,SAASC,KAAK9U,EAAMC,OAAO8N,KAAK,KAAK6G,KAAK,QAAS,QAAQA,KAAK,MAAOjW,IAAOA,EAAEoW,IAAMtC,EAAI/P,KAAK,UAAUpC,KAAIsU,KAAK,aAAa,SAASjW,GACvL,MAAO,aAAeA,EAAEwC,GAAK,IAAMxC,EAAEkB,GAAK,GAC5C,IAAG+U,KAAK,KAAMjW,GAAMA,EAAEwC,KAAIyT,KAAK,KAAMjW,GAAMA,EAAEkB,KAAI8U,OAAO,QAAQC,KAAK,UAAWjW,GACvEA,EAAEgC,GAAKhC,EAAEkB,KACf+U,KAAK,SAAUjW,GAAMA,EAAEyC,GAAKzC,EAAEwC,KAAIyT,KAAK,QAASjW,GAAM6V,EAAY7V,EAAE2B,MACvE,MAAM0U,GAA0BpL,EAAAA,EAAAA,KAAOlJ,IAAwB,IAArBJ,GAAI2U,EAAG,MAAEzW,GAAOkC,EACxD,OAAK2T,EAGE,GAAGY,MACZd,IAAS9R,KAAK6S,MAAc,IAAR1W,GAAe,MAAM4V,IAH9Ba,CAGsC,GAC9C,WACHlB,EAAIY,OAAO,KAAKC,KAAK,QAAS,eAAeA,KAAK,YAAa,IAAIC,UAAU,QAAQC,KAAK9U,EAAMC,OAAO8N,KAAK,QAAQ6G,KAAK,KAAMjW,GAAMA,EAAEwC,GAAKN,EAAQ,EAAIlC,EAAEyC,GAAK,EAAIzC,EAAEwC,GAAK,IAAGyT,KAAK,KAAMjW,IAAOA,EAAEgC,GAAKhC,EAAEkB,IAAM,IAAG+U,KAAK,MAASP,EAAa,IAAM,QAAtB,MAAkCO,KAAK,eAAgBjW,GAAMA,EAAEwC,GAAKN,EAAQ,EAAI,QAAU,QAAOmN,KAAKgH,GAClU,MAAMpU,EAAOmT,EAAIY,OAAO,KAAKC,KAAK,QAAS,SAASA,KAAK,OAAQ,QAAQA,KAAK,iBAAkB,IAAKC,UAAU,SAASC,KAAK9U,EAAMG,OAAO4N,KAAK,KAAK6G,KAAK,QAAS,QAAQO,MAAM,iBAAkB,YAC5LC,EAAY7B,GAAM6B,WAAa,WACrC,GAAkB,aAAdA,EAA0B,CAC5B,MAAMC,EAAWzU,EAAK+T,OAAO,kBAAkBC,KAAK,MAAOjW,IAAOA,EAAEoW,IAAMtC,EAAI/P,KAAK,oBAAoBpC,KAAIsU,KAAK,gBAAiB,kBAAkBA,KAAK,MAAOjW,GAAMA,EAAEgB,OAAOyB,KAAIwT,KAAK,MAAOjW,GAAMA,EAAEC,OAAOuC,KAC7MkU,EAASV,OAAO,QAAQC,KAAK,SAAU,MAAMA,KAAK,cAAejW,GAAM6V,EAAY7V,EAAEgB,OAAOW,MAC5F+U,EAASV,OAAO,QAAQC,KAAK,SAAU,QAAQA,KAAK,cAAejW,GAAM6V,EAAY7V,EAAEC,OAAO0B,KAChG,CACA,IAAIgV,EACJ,OAAQF,GACN,IAAK,WACHE,GAA2B1L,EAAAA,EAAAA,KAAQjL,GAAMA,EAAEoW,KAAK,YAChD,MACF,IAAK,SACHO,GAA2B1L,EAAAA,EAAAA,KAAQjL,GAAM6V,EAAY7V,EAAEgB,OAAOW,KAAK,YACnE,MACF,IAAK,SACHgV,GAA2B1L,EAAAA,EAAAA,KAAQjL,GAAM6V,EAAY7V,EAAEC,OAAO0B,KAAK,YACnE,MACF,QACEgV,EAAWF,EAEfxU,EAAK+T,OAAO,QAAQC,KAAK,IAAKW,KAA0BX,KAAK,SAAUU,GAAUV,KAAK,gBAAiBjW,GAAM0D,KAAKjD,IAAI,EAAGT,EAAEkC,UAC3H2U,EAAAA,EAAAA,SAAkB,EAAQzB,EAAK,EAAGE,EACpC,GAAG,QACCwB,GAAyB,CAC3BtC,SAIEuC,IAAwC9L,EAAAA,EAAAA,KAAQoE,GAC9BA,EAAK9C,WAAW,2BAA4B,IAAIA,WAAW,aAAc,MAAMD,QAElG,yBAMC0K,IAH4B/L,EAAAA,EAAAA,KAAQoD,GAAY,gCAC/BA,EAAQ4I,sBACrB,aAIJC,GAAgBzE,EAAevF,MAAMiK,KAAK1E,GAC9CA,EAAevF,MAASmC,GAAS6H,GAAcH,GAAsB1H,IACrE,IAAI+H,GAAU,CACZC,OAAQL,GACRjM,OAAQ0H,EACRkD,GAAIpC,EACJ+D,SAAUR,G", "sources": ["../../node_modules/d3-sankey/node_modules/d3-array/src/min.js", "../../node_modules/d3-sankey/src/align.js", "../../node_modules/d3-sankey/node_modules/d3-array/src/sum.js", "../../node_modules/d3-sankey/node_modules/d3-array/src/max.js", "../../node_modules/d3-sankey/src/constant.js", "../../node_modules/d3-sankey/src/sankey.js", "../../node_modules/d3-sankey/node_modules/d3-path/src/path.js", "../../node_modules/d3-sankey/node_modules/d3-shape/src/array.js", "../../node_modules/d3-sankey/node_modules/d3-shape/src/constant.js", "../../node_modules/d3-sankey/node_modules/d3-shape/src/point.js", "../../node_modules/d3-sankey/node_modules/d3-shape/src/link/index.js", "../../node_modules/d3-sankey/src/sankeyLinkHorizontal.js", "../../node_modules/mermaid/dist/chunks/mermaid.core/sankeyDiagram-QLVOVGJD.mjs"], "sourcesContent": ["export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "import {min} from \"d3-array\";\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nexport function left(node) {\n  return node.depth;\n}\n\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\n\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nexport function center(node) {\n  return node.targetLinks.length ? node.depth\n      : node.sourceLinks.length ? min(node.sourceLinks, targetDepth) - 1\n      : 0;\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function constant(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {max, min, sum} from \"d3-array\";\nimport {justify} from \"./align.js\";\nimport constant from \"./constant.js\";\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error(\"missing: \" + id);\n  return node;\n}\n\nfunction computeLinkBreadths({nodes}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nexport default function Sankey() {\n  let x0 = 0, y0 = 0, x1 = 1, y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8, py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey() {\n    const graph = {nodes: nodes.apply(null, arguments), links: links.apply(null, arguments)};\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function(graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function(_) {\n    return arguments.length ? (id = typeof _ === \"function\" ? _ : constant(_), sankey) : id;\n  };\n\n  sankey.nodeAlign = function(_) {\n    return arguments.length ? (align = typeof _ === \"function\" ? _ : constant(_), sankey) : align;\n  };\n\n  sankey.nodeSort = function(_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n\n  sankey.nodeWidth = function(_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n\n  sankey.nodePadding = function(_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n\n  sankey.nodes = function(_) {\n    return arguments.length ? (nodes = typeof _ === \"function\" ? _ : constant(_), sankey) : nodes;\n  };\n\n  sankey.links = function(_) {\n    return arguments.length ? (links = typeof _ === \"function\" ? _ : constant(_), sankey) : links;\n  };\n\n  sankey.linkSort = function(_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n\n  sankey.size = function(_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function(_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n\n  sankey.iterations = function(_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n\n  function computeNodeLinks({nodes, links}) {\n    for (const [i, node] of nodes.entries()) {\n      node.index = i;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    }\n    const nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d]));\n    for (const [i, link] of links.entries()) {\n      link.index = i;\n      let {source, target} = link;\n      if (typeof source !== \"object\") source = link.source = find(nodeById, source);\n      if (typeof target !== \"object\") target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    }\n    if (linkSort != null) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({nodes}) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined\n          ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.depth = x;\n        for (const {target} of node.sourceLinks) {\n          next.add(target);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeHeights({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.height = x;\n        for (const {source} of node.targetLinks) {\n          next.add(source);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeLayers({nodes}) {\n    const x = max(nodes, d => d.depth) + 1;\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = min(columns, c => (y1 - y0 - (c.length - 1) * py) / sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {source, value} of target.targetLinks) {\n          let v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {target, value} of source.sourceLinks) {\n          let v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({sourceLinks, targetLinks}) {\n    if (linkSort === undefined) {\n      for (const {source: {sourceLinks}} of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {target: {targetLinks}} of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n", "var pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction Path() {\n  this._x0 = this._y0 = // start of current subpath\n  this._x1 = this._y1 = null; // end of current subpath\n  this._ = \"\";\n}\n\nfunction path() {\n  return new Path;\n}\n\nPath.prototype = path.prototype = {\n  constructor: Path,\n  moveTo: function(x, y) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y);\n  },\n  closePath: function() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  },\n  lineTo: function(x, y) {\n    this._ += \"L\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  quadraticCurveTo: function(x1, y1, x, y) {\n    this._ += \"Q\" + (+x1) + \",\" + (+y1) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) {\n    this._ += \"C\" + (+x1) + \",\" + (+y1) + \",\" + (+x2) + \",\" + (+y2) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  arcTo: function(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n    var x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._ += \"M\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._ += \"L\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      var x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._ += \"L\" + (x1 + t01 * x01) + \",\" + (y1 + t01 * y01);\n      }\n\n      this._ += \"A\" + r + \",\" + r + \",0,0,\" + (+(y01 * x20 > x01 * y20)) + \",\" + (this._x1 = x1 + t21 * x21) + \",\" + (this._y1 = y1 + t21 * y21);\n    }\n  },\n  arc: function(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n    var dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._ += \"M\" + x0 + \",\" + y0;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._ += \"L\" + x0 + \",\" + y0;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (x - dx) + \",\" + (y - dy) + \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (this._x1 = x0) + \",\" + (this._y1 = y0);\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,\" + (+(da >= pi)) + \",\" + cw + \",\" + (this._x1 = x + r * Math.cos(a1)) + \",\" + (this._y1 = y + r * Math.sin(a1));\n    }\n  },\n  rect: function(x, y, w, h) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y) + \"h\" + (+w) + \"v\" + (+h) + \"h\" + (-w) + \"Z\";\n  },\n  toString: function() {\n    return this._;\n  }\n};\n\nexport default path;\n", "export var slice = Array.prototype.slice;\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "import {path} from \"d3-path\";\nimport {slice} from \"../array.js\";\nimport constant from \"../constant.js\";\nimport {x as pointX, y as pointY} from \"../point.js\";\nimport pointRadial from \"../pointRadial.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nfunction link(curve) {\n  var source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null;\n\n  function link() {\n    var buffer, argv = slice.call(arguments), s = source.apply(this, argv), t = target.apply(this, argv);\n    if (!context) context = buffer = path();\n    curve(context, +x.apply(this, (argv[0] = s, argv)), +y.apply(this, argv), +x.apply(this, (argv[0] = t, argv)), +y.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), link) : context;\n  };\n\n  return link;\n}\n\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\n\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\n\nfunction curveRadial(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n      p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n      p2 = pointRadial(x1, y0),\n      p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\n\nexport function linkHorizontal() {\n  return link(curveHorizontal);\n}\n\nexport function linkVertical() {\n  return link(curveVertical);\n}\n\nexport function linkRadial() {\n  var l = link(curveRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n", "import {linkHorizontal} from \"d3-shape\";\n\nfunction horizontalSource(d) {\n  return [d.source.x1, d.y0];\n}\n\nfunction horizontalTarget(d) {\n  return [d.target.x0, d.y1];\n}\n\nexport default function() {\n  return linkHorizontal()\n      .source(horizontalSource)\n      .target(horizontalTarget);\n}\n", "import {\n  __name,\n  clear,\n  common_default,\n  defaultConfig2 as defaultConfig,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n  setupGraphViewbox\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/sankey/parser/sankey.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 9], $V1 = [1, 10], $V2 = [1, 5, 10, 12];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SANKEY\": 4, \"NEWLINE\": 5, \"csv\": 6, \"opt_eof\": 7, \"record\": 8, \"csv_tail\": 9, \"EOF\": 10, \"field[source]\": 11, \"COMMA\": 12, \"field[target]\": 13, \"field[value]\": 14, \"field\": 15, \"escaped\": 16, \"non_escaped\": 17, \"DQUOTE\": 18, \"ESCAPED_TEXT\": 19, \"NON_ESCAPED_TEXT\": 20, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SANKEY\", 5: \"NEWLINE\", 10: \"EOF\", 11: \"field[source]\", 12: \"COMMA\", 13: \"field[target]\", 14: \"field[value]\", 18: \"DQUOTE\", 19: \"ESCAPED_TEXT\", 20: \"NON_ESCAPED_TEXT\" },\n    productions_: [0, [3, 4], [6, 2], [9, 2], [9, 0], [7, 1], [7, 0], [8, 5], [15, 1], [15, 1], [16, 3], [17, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 7:\n          const source = yy.findOrCreateNode($$[$0 - 4].trim().replaceAll('\"\"', '\"'));\n          const target = yy.findOrCreateNode($$[$0 - 2].trim().replaceAll('\"\"', '\"'));\n          const value = parseFloat($$[$0].trim());\n          yy.addLink(source, target, value);\n          break;\n        case 8:\n        case 9:\n        case 11:\n          this.$ = $$[$0];\n          break;\n        case 10:\n          this.$ = $$[$0 - 1];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, { 5: [1, 3] }, { 6: 4, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 1: [2, 6], 7: 11, 10: [1, 12] }, o($V1, [2, 4], { 9: 13, 5: [1, 14] }), { 12: [1, 15] }, o($V2, [2, 8]), o($V2, [2, 9]), { 19: [1, 16] }, o($V2, [2, 11]), { 1: [2, 1] }, { 1: [2, 5] }, o($V1, [2, 2]), { 6: 17, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 15: 18, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 18: [1, 19] }, o($V1, [2, 3]), { 12: [1, 20] }, o($V2, [2, 10]), { 15: 21, 16: 7, 17: 8, 18: $V0, 20: $V1 }, o([1, 5, 10], [2, 7])],\n    defaultActions: { 11: [2, 1], 12: [2, 5] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.pushState(\"csv\");\n            return 4;\n            break;\n          case 1:\n            return 10;\n            break;\n          case 2:\n            return 5;\n            break;\n          case 3:\n            return 12;\n            break;\n          case 4:\n            this.pushState(\"escaped_text\");\n            return 18;\n            break;\n          case 5:\n            return 20;\n            break;\n          case 6:\n            this.popState(\"escaped_text\");\n            return 18;\n            break;\n          case 7:\n            return 19;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:sankey-beta\\b)/i, /^(?:$)/i, /^(?:((\\u000D\\u000A)|(\\u000A)))/i, /^(?:(\\u002C))/i, /^(?:(\\u0022))/i, /^(?:([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])*)/i, /^(?:(\\u0022)(?!(\\u0022)))/i, /^(?:(([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])|(\\u002C)|(\\u000D)|(\\u000A)|(\\u0022)(\\u0022))*)/i],\n      conditions: { \"csv\": { \"rules\": [1, 2, 3, 4, 5, 6, 7], \"inclusive\": false }, \"escaped_text\": { \"rules\": [6, 7], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar sankey_default = parser;\n\n// src/diagrams/sankey/sankeyDB.ts\nvar links = [];\nvar nodes = [];\nvar nodesMap = /* @__PURE__ */ new Map();\nvar clear2 = /* @__PURE__ */ __name(() => {\n  links = [];\n  nodes = [];\n  nodesMap = /* @__PURE__ */ new Map();\n  clear();\n}, \"clear\");\nvar SankeyLink = class {\n  constructor(source, target, value = 0) {\n    this.source = source;\n    this.target = target;\n    this.value = value;\n  }\n  static {\n    __name(this, \"SankeyLink\");\n  }\n};\nvar addLink = /* @__PURE__ */ __name((source, target, value) => {\n  links.push(new SankeyLink(source, target, value));\n}, \"addLink\");\nvar SankeyNode = class {\n  constructor(ID) {\n    this.ID = ID;\n  }\n  static {\n    __name(this, \"SankeyNode\");\n  }\n};\nvar findOrCreateNode = /* @__PURE__ */ __name((ID) => {\n  ID = common_default.sanitizeText(ID, getConfig());\n  let node = nodesMap.get(ID);\n  if (node === void 0) {\n    node = new SankeyNode(ID);\n    nodesMap.set(ID, node);\n    nodes.push(node);\n  }\n  return node;\n}, \"findOrCreateNode\");\nvar getNodes = /* @__PURE__ */ __name(() => nodes, \"getNodes\");\nvar getLinks = /* @__PURE__ */ __name(() => links, \"getLinks\");\nvar getGraph = /* @__PURE__ */ __name(() => ({\n  nodes: nodes.map((node) => ({ id: node.ID })),\n  links: links.map((link) => ({\n    source: link.source.ID,\n    target: link.target.ID,\n    value: link.value\n  }))\n}), \"getGraph\");\nvar sankeyDB_default = {\n  nodesMap,\n  getConfig: /* @__PURE__ */ __name(() => getConfig().sankey, \"getConfig\"),\n  getNodes,\n  getLinks,\n  getGraph,\n  addLink,\n  findOrCreateNode,\n  getAccTitle,\n  setAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getDiagramTitle,\n  setDiagramTitle,\n  clear: clear2\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nimport {\n  select as d3select,\n  scaleOrdinal as d3scaleOrdinal,\n  schemeTableau10 as d3schemeTableau10\n} from \"d3\";\nimport {\n  sankey as d3Sankey,\n  sankeyLinkHorizontal as d3SankeyLinkHorizontal,\n  sankeyLeft as d3SankeyLeft,\n  sankeyRight as d3SankeyRight,\n  sankeyCenter as d3SankeyCenter,\n  sankeyJustify as d3SankeyJustify\n} from \"d3-sankey\";\n\n// src/rendering-util/uid.ts\nvar Uid = class _Uid {\n  static {\n    __name(this, \"Uid\");\n  }\n  static {\n    this.count = 0;\n  }\n  static next(name) {\n    return new _Uid(name + ++_Uid.count);\n  }\n  constructor(id) {\n    this.id = id;\n    this.href = `#${id}`;\n  }\n  toString() {\n    return \"url(\" + this.href + \")\";\n  }\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nvar alignmentsMap = {\n  left: d3SankeyLeft,\n  right: d3SankeyRight,\n  center: d3SankeyCenter,\n  justify: d3SankeyJustify\n};\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  const { securityLevel, sankey: conf } = getConfig();\n  const defaultSankeyConfig = defaultConfig.sankey;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = d3select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? d3select(sandboxElement.nodes()[0].contentDocument.body) : d3select(\"body\");\n  const svg = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : d3select(`[id=\"${id}\"]`);\n  const width = conf?.width ?? defaultSankeyConfig.width;\n  const height = conf?.height ?? defaultSankeyConfig.width;\n  const useMaxWidth = conf?.useMaxWidth ?? defaultSankeyConfig.useMaxWidth;\n  const nodeAlignment = conf?.nodeAlignment ?? defaultSankeyConfig.nodeAlignment;\n  const prefix = conf?.prefix ?? defaultSankeyConfig.prefix;\n  const suffix = conf?.suffix ?? defaultSankeyConfig.suffix;\n  const showValues = conf?.showValues ?? defaultSankeyConfig.showValues;\n  const graph = diagObj.db.getGraph();\n  const nodeAlign = alignmentsMap[nodeAlignment];\n  const nodeWidth = 10;\n  const sankey = d3Sankey().nodeId((d) => d.id).nodeWidth(nodeWidth).nodePadding(10 + (showValues ? 15 : 0)).nodeAlign(nodeAlign).extent([\n    [0, 0],\n    [width, height]\n  ]);\n  sankey(graph);\n  const colorScheme = d3scaleOrdinal(d3schemeTableau10);\n  svg.append(\"g\").attr(\"class\", \"nodes\").selectAll(\".node\").data(graph.nodes).join(\"g\").attr(\"class\", \"node\").attr(\"id\", (d) => (d.uid = Uid.next(\"node-\")).id).attr(\"transform\", function(d) {\n    return \"translate(\" + d.x0 + \",\" + d.y0 + \")\";\n  }).attr(\"x\", (d) => d.x0).attr(\"y\", (d) => d.y0).append(\"rect\").attr(\"height\", (d) => {\n    return d.y1 - d.y0;\n  }).attr(\"width\", (d) => d.x1 - d.x0).attr(\"fill\", (d) => colorScheme(d.id));\n  const getText = /* @__PURE__ */ __name(({ id: id2, value }) => {\n    if (!showValues) {\n      return id2;\n    }\n    return `${id2}\n${prefix}${Math.round(value * 100) / 100}${suffix}`;\n  }, \"getText\");\n  svg.append(\"g\").attr(\"class\", \"node-labels\").attr(\"font-size\", 14).selectAll(\"text\").data(graph.nodes).join(\"text\").attr(\"x\", (d) => d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6).attr(\"y\", (d) => (d.y1 + d.y0) / 2).attr(\"dy\", `${showValues ? \"0\" : \"0.35\"}em`).attr(\"text-anchor\", (d) => d.x0 < width / 2 ? \"start\" : \"end\").text(getText);\n  const link = svg.append(\"g\").attr(\"class\", \"links\").attr(\"fill\", \"none\").attr(\"stroke-opacity\", 0.5).selectAll(\".link\").data(graph.links).join(\"g\").attr(\"class\", \"link\").style(\"mix-blend-mode\", \"multiply\");\n  const linkColor = conf?.linkColor ?? \"gradient\";\n  if (linkColor === \"gradient\") {\n    const gradient = link.append(\"linearGradient\").attr(\"id\", (d) => (d.uid = Uid.next(\"linearGradient-\")).id).attr(\"gradientUnits\", \"userSpaceOnUse\").attr(\"x1\", (d) => d.source.x1).attr(\"x2\", (d) => d.target.x0);\n    gradient.append(\"stop\").attr(\"offset\", \"0%\").attr(\"stop-color\", (d) => colorScheme(d.source.id));\n    gradient.append(\"stop\").attr(\"offset\", \"100%\").attr(\"stop-color\", (d) => colorScheme(d.target.id));\n  }\n  let coloring;\n  switch (linkColor) {\n    case \"gradient\":\n      coloring = /* @__PURE__ */ __name((d) => d.uid, \"coloring\");\n      break;\n    case \"source\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.source.id), \"coloring\");\n      break;\n    case \"target\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.target.id), \"coloring\");\n      break;\n    default:\n      coloring = linkColor;\n  }\n  link.append(\"path\").attr(\"d\", d3SankeyLinkHorizontal()).attr(\"stroke\", coloring).attr(\"stroke-width\", (d) => Math.max(1, d.width));\n  setupGraphViewbox(void 0, svg, 0, useMaxWidth);\n}, \"draw\");\nvar sankeyRenderer_default = {\n  draw\n};\n\n// src/diagrams/sankey/sankeyUtils.ts\nvar prepareTextForParsing = /* @__PURE__ */ __name((text) => {\n  const textToParse = text.replaceAll(/^[^\\S\\n\\r]+|[^\\S\\n\\r]+$/g, \"\").replaceAll(/([\\n\\r])+/g, \"\\n\").trim();\n  return textToParse;\n}, \"prepareTextForParsing\");\n\n// src/diagrams/sankey/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `.label {\n      font-family: ${options.fontFamily};\n    }`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/sankey/sankeyDiagram.ts\nvar originalParse = sankey_default.parse.bind(sankey_default);\nsankey_default.parse = (text) => originalParse(prepareTextForParsing(text));\nvar diagram = {\n  styles: styles_default,\n  parser: sankey_default,\n  db: sankeyDB_default,\n  renderer: sankeyRenderer_default\n};\nexport {\n  diagram\n};\n"], "names": ["min", "values", "valueof", "undefined", "value", "index", "targetDepth", "d", "target", "depth", "justify", "node", "n", "sourceLinks", "length", "sum", "max", "constant", "x", "ascendingSourceBreadth", "a", "b", "ascendingBreadth", "source", "ascending<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "y0", "defaultId", "defaultNodes", "graph", "nodes", "defaultLinks", "links", "find", "nodeById", "id", "get", "Error", "computeLinkBreadths", "_ref", "y1", "link", "width", "targetLinks", "<PERSON><PERSON>", "py", "sort", "linkSort", "x0", "x1", "dx", "dy", "align", "iterations", "sankey", "apply", "arguments", "_ref2", "i", "entries", "Map", "map", "push", "computeNodeLinks", "_ref3", "fixedValue", "Math", "computeNodeValues", "_ref4", "current", "Set", "next", "size", "add", "computeNodeDepths", "_ref5", "height", "computeNodeHeights", "columns", "_ref6", "kx", "Array", "floor", "call", "layer", "column", "computeNodeLayers", "c", "ky", "y", "reorderLinks", "initializeNodeBreadths", "alpha", "pow", "beta", "relaxRightToLeft", "relaxLeftToRight", "computeNodeBreadths", "w", "v", "targetTop", "reorderNodeLinks", "resolveCollisions", "sourceTop", "subject", "resolveCollisionsBottomToTop", "resolveCollisionsTopToBottom", "_ref7", "update", "nodeId", "_", "nodeAlign", "nodeSort", "nodeWidth", "nodePadding", "extent", "pi", "PI", "tau", "epsilon", "tauEpsilon", "Path", "this", "_x0", "_y0", "_x1", "_y1", "path", "prototype", "constructor", "moveTo", "closePath", "lineTo", "quadraticCurveTo", "bezierCurveTo", "x2", "y2", "arcTo", "r", "x21", "y21", "x01", "y01", "l01_2", "abs", "x20", "y20", "l21_2", "l20_2", "l21", "sqrt", "l01", "l", "tan", "acos", "t01", "t21", "arc", "a0", "a1", "ccw", "cos", "sin", "cw", "da", "rect", "h", "toString", "slice", "p", "linkSource", "linkTarget", "curve", "pointX", "pointY", "context", "buffer", "argv", "s", "t", "curveHorizontal", "horizontalSource", "horizontalTarget", "parser", "o", "__name", "k", "o2", "$V0", "$V1", "$V2", "parser2", "trace", "yy", "symbols_", "terminals_", "productions_", "performAction", "yytext", "yyleng", "y<PERSON><PERSON>o", "yystate", "$$", "_$", "$0", "findOrCreateNode", "trim", "replaceAll", "parseFloat", "addLink", "$", "table", "defaultActions", "parseError", "str", "hash", "recoverable", "error", "parse", "input", "self", "stack", "tstack", "vstack", "lstack", "recovering", "args", "lexer2", "Object", "create", "lexer", "sharedState", "hasOwnProperty", "setInput", "yylloc", "yyloc", "ranges", "options", "lex", "token", "pop", "getPrototypeOf", "symbol", "preErrorSymbol", "state", "action", "len", "newState", "expected", "yyval", "errStr", "showPosition", "join", "text", "match", "line", "loc", "first_line", "last_line", "first_column", "last_column", "range", "concat", "EOF", "_input", "_more", "_backtrack", "done", "matched", "conditionStack", "offset", "ch", "unput", "lines", "split", "substr", "oldLines", "more", "reject", "backtrack_lexer", "less", "pastInput", "past", "replace", "upcomingInput", "pre", "test_match", "indexed_rule", "backup", "matches", "tempMatch", "rules", "_currentRules", "flex", "begin", "condition", "popState", "conditions", "topState", "pushState", "stateStackSize", "yy_", "$avoiding_name_collisions", "YY_START", "<PERSON><PERSON><PERSON>", "sankey_default", "nodesMap", "clear2", "clear", "SankeyLink", "SankeyNode", "ID", "common_default", "sanitizeText", "getConfig", "set", "getNodes", "getLinks", "getGraph", "sankeyDB_default", "getAccTitle", "setAccTitle", "getAccDescription", "setAccDescription", "getDiagramTitle", "setDiagramTitle", "<PERSON><PERSON>", "_Uid", "count", "name", "href", "alignmentsMap", "left", "right", "center", "d3SankeyJustify", "draw", "_version", "diagObj", "securityLevel", "conf", "defaultSankeyConfig", "defaultConfig", "sandboxElement", "d3select", "root", "contentDocument", "body", "svg", "select", "useMaxWidth", "nodeAlignment", "prefix", "suffix", "showValues", "db", "d3Sankey", "colorScheme", "d3scaleOrdinal", "d3schemeTableau10", "append", "attr", "selectAll", "data", "uid", "getText", "id2", "round", "style", "linkColor", "gradient", "coloring", "d3SankeyLinkHorizontal", "setupGraphViewbox", "sankeyRenderer_default", "prepareTextForParsing", "styles_default", "fontFamily", "originalParse", "bind", "diagram", "styles", "renderer"], "sourceRoot": ""}