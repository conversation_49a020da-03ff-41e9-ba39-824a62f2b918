{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\imageUp\\\\ConflictResolutionModal.tsx\",\n  _s = $RefreshSig$();\n// ConflictResolutionModal.tsx - Modal pour résoudre les conflits lors de l'import\nimport React, { useState } from 'react';\nimport { AlertTriangle, Check, X, ArrowRight } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConflictResolutionModal = ({\n  darkMode,\n  isOpen,\n  currentClass,\n  importedClasses,\n  conflicts,\n  onResolve,\n  onCancel\n}) => {\n  _s();\n  const [conflictItems, setConflictItems] = useState(() => {\n    const items = [];\n\n    // Traiter les conflits d'attributs\n    conflicts.attributes.forEach(attrName => {\n      const current = currentClass.attributes.find(attr => attr.toLowerCase().includes(attrName.toLowerCase())) || attrName;\n      const imported = importedClasses.flatMap(cls => cls.attributes.filter(attr => attr.toLowerCase().includes(attrName.toLowerCase())));\n      items.push({\n        type: 'attribute',\n        name: attrName,\n        current,\n        imported,\n        resolution: 'merge'\n      });\n    });\n\n    // Traiter les conflits de méthodes\n    conflicts.methods.forEach(methodName => {\n      const current = currentClass.methods.find(method => method.toLowerCase().includes(methodName.toLowerCase())) || methodName;\n      const imported = importedClasses.flatMap(cls => cls.methods.filter(method => method.toLowerCase().includes(methodName.toLowerCase())));\n      items.push({\n        type: 'method',\n        name: methodName,\n        current,\n        imported,\n        resolution: 'merge'\n      });\n    });\n    return items;\n  });\n  const handleResolutionChange = (index, resolution) => {\n    const newItems = [...conflictItems];\n    newItems[index].resolution = resolution;\n    setConflictItems(newItems);\n  };\n  const handleResolve = () => {\n    // Appliquer les résolutions de conflits\n    const resolvedAttributes = [...currentClass.attributes];\n    const resolvedMethods = [...currentClass.methods];\n    conflictItems.forEach(item => {\n      if (item.type === 'attribute') {\n        const currentIndex = resolvedAttributes.findIndex(attr => attr.toLowerCase().includes(item.name.toLowerCase()));\n        switch (item.resolution) {\n          case 'keep':\n            // Ne rien faire, garder l'actuel\n            break;\n          case 'replace':\n            if (currentIndex !== -1 && item.imported.length > 0) {\n              resolvedAttributes[currentIndex] = item.imported[0];\n            }\n            break;\n          case 'merge':\n            // Ajouter tous les variants importés qui ne sont pas déjà présents\n            item.imported.forEach(importedAttr => {\n              if (!resolvedAttributes.some(existing => existing.toLowerCase() === importedAttr.toLowerCase())) {\n                resolvedAttributes.push(importedAttr);\n              }\n            });\n            break;\n        }\n      } else {\n        const currentIndex = resolvedMethods.findIndex(method => method.toLowerCase().includes(item.name.toLowerCase()));\n        switch (item.resolution) {\n          case 'keep':\n            // Ne rien faire, garder l'actuel\n            break;\n          case 'replace':\n            if (currentIndex !== -1 && item.imported.length > 0) {\n              resolvedMethods[currentIndex] = item.imported[0];\n            }\n            break;\n          case 'merge':\n            // Ajouter tous les variants importés qui ne sont pas déjà présents\n            item.imported.forEach(importedMethod => {\n              if (!resolvedMethods.some(existing => existing.toLowerCase() === importedMethod.toLowerCase())) {\n                resolvedMethods.push(importedMethod);\n              }\n            });\n            break;\n        }\n      }\n    });\n\n    // Ajouter les éléments non-conflictuels des classes importées\n    importedClasses.forEach(importedClass => {\n      importedClass.attributes.forEach(attr => {\n        const isConflict = conflicts.attributes.some(conflictAttr => attr.toLowerCase().includes(conflictAttr.toLowerCase()));\n        if (!isConflict && !resolvedAttributes.some(existing => existing.toLowerCase() === attr.toLowerCase())) {\n          resolvedAttributes.push(attr);\n        }\n      });\n      importedClass.methods.forEach(method => {\n        const isConflict = conflicts.methods.some(conflictMethod => method.toLowerCase().includes(conflictMethod.toLowerCase()));\n        if (!isConflict && !resolvedMethods.some(existing => existing.toLowerCase() === method.toLowerCase())) {\n          resolvedMethods.push(method);\n        }\n      });\n    });\n    const resolvedClass = {\n      name: currentClass.name,\n      attributes: resolvedAttributes,\n      methods: resolvedMethods\n    };\n    onResolve(resolvedClass);\n  };\n  const getModalStyles = () => ({\n    overlay: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0, 0, 0, 0.7)',\n      display: isOpen ? 'flex' : 'none',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1100,\n      backdropFilter: 'blur(8px)',\n      WebkitBackdropFilter: 'blur(8px)'\n    },\n    modal: {\n      backgroundColor: darkMode ? '#0a0f1c' : '#ffffff',\n      borderRadius: '16px',\n      boxShadow: darkMode ? '0 25px 50px -12px rgba(0, 0, 0, 0.8)' : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n      padding: '0',\n      minWidth: '600px',\n      maxWidth: '800px',\n      maxHeight: '80vh',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    header: {\n      padding: '24px 28px 20px',\n      borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px'\n    },\n    title: {\n      fontSize: '20px',\n      fontWeight: '700',\n      color: darkMode ? '#f8fafc' : '#0f172a',\n      margin: 0\n    },\n    content: {\n      padding: '24px 28px',\n      overflowY: 'auto',\n      flex: 1\n    },\n    conflictItem: {\n      marginBottom: '24px',\n      padding: '16px',\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.5)' : 'rgba(248, 250, 252, 0.8)',\n      borderRadius: '12px',\n      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`\n    },\n    conflictHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      marginBottom: '12px'\n    },\n    conflictType: {\n      fontSize: '14px',\n      fontWeight: '600',\n      color: darkMode ? '#fbbf24' : '#d97706',\n      textTransform: 'uppercase',\n      letterSpacing: '0.05em'\n    },\n    conflictName: {\n      fontSize: '16px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151'\n    },\n    comparisonContainer: {\n      display: 'grid',\n      gridTemplateColumns: '1fr auto 1fr',\n      gap: '16px',\n      alignItems: 'center',\n      marginBottom: '16px'\n    },\n    versionBox: {\n      padding: '12px',\n      backgroundColor: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(255, 255, 255, 0.9)',\n      borderRadius: '8px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`\n    },\n    versionLabel: {\n      fontSize: '12px',\n      fontWeight: '600',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      marginBottom: '8px',\n      textTransform: 'uppercase'\n    },\n    versionContent: {\n      fontSize: '14px',\n      color: darkMode ? '#cbd5e1' : '#4b5563',\n      fontFamily: 'monospace'\n    },\n    resolutionOptions: {\n      display: 'flex',\n      gap: '12px'\n    },\n    resolutionButton: {\n      padding: '8px 16px',\n      borderRadius: '8px',\n      border: 'none',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: 'pointer',\n      transition: 'all 0.2s ease',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '6px'\n    },\n    footer: {\n      padding: '20px 28px',\n      borderTop: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,\n      display: 'flex',\n      justifyContent: 'flex-end',\n      gap: '12px'\n    },\n    button: {\n      padding: '12px 24px',\n      borderRadius: '8px',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: 'pointer',\n      transition: 'all 0.2s ease',\n      border: 'none'\n    },\n    cancelButton: {\n      backgroundColor: 'transparent',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`\n    },\n    resolveButton: {\n      backgroundColor: '#3b82f6',\n      color: '#ffffff'\n    }\n  });\n  const getResolutionButtonStyle = (resolution, currentResolution) => {\n    const isSelected = resolution === currentResolution;\n    return {\n      ...getModalStyles().resolutionButton,\n      backgroundColor: isSelected ? '#3b82f6' : darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',\n      color: isSelected ? '#ffffff' : darkMode ? '#cbd5e1' : '#4b5563',\n      border: `1px solid ${isSelected ? '#3b82f6' : darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`\n    };\n  };\n  const styles = getModalStyles();\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.overlay,\n    onClick: onCancel,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.modal,\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.header,\n        children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n          size: 24,\n          color: \"#f59e0b\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          style: styles.title,\n          children: \"R\\xE9solution des Conflits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.content,\n        children: conflictItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.conflictItem,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.conflictHeader,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: styles.conflictType,\n              children: item.type === 'attribute' ? 'Attribut' : 'Méthode'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: styles.conflictName,\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.comparisonContainer,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.versionBox,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.versionLabel,\n                children: \"Version Actuelle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.versionContent,\n                children: item.current\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ArrowRight, {\n              size: 20,\n              color: darkMode ? '#64748b' : '#9ca3af'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.versionBox,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.versionLabel,\n                children: \"Versions Import\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this), item.imported.map((imported, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.versionContent,\n                children: imported\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.resolutionOptions,\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              style: getResolutionButtonStyle('keep', item.resolution),\n              onClick: () => handleResolutionChange(index, 'keep'),\n              children: [/*#__PURE__*/_jsxDEV(X, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this), \"Garder l'actuel\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: getResolutionButtonStyle('replace', item.resolution),\n              onClick: () => handleResolutionChange(index, 'replace'),\n              children: [/*#__PURE__*/_jsxDEV(ArrowRight, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this), \"Remplacer\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: getResolutionButtonStyle('merge', item.resolution),\n              onClick: () => handleResolutionChange(index, 'merge'),\n              children: [/*#__PURE__*/_jsxDEV(Check, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this), \"Fusionner\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this)]\n        }, `${item.type}-${item.name}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.footer,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.button,\n            ...styles.cancelButton\n          },\n          onClick: onCancel,\n          children: \"Annuler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: {\n            ...styles.button,\n            ...styles.resolveButton\n          },\n          onClick: handleResolve,\n          children: \"Appliquer les R\\xE9solutions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 343,\n    columnNumber: 5\n  }, this);\n};\n_s(ConflictResolutionModal, \"vpLSFf8sXyo8/M8nTJOfbPONFoY=\");\n_c = ConflictResolutionModal;\nexport default ConflictResolutionModal;\nvar _c;\n$RefreshReg$(_c, \"ConflictResolutionModal\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Check", "X", "ArrowRight", "jsxDEV", "_jsxDEV", "ConflictResolutionModal", "darkMode", "isOpen", "currentClass", "importedClasses", "conflicts", "onResolve", "onCancel", "_s", "conflictItems", "setConflictItems", "items", "attributes", "for<PERSON>ach", "attrName", "current", "find", "attr", "toLowerCase", "includes", "imported", "flatMap", "cls", "filter", "push", "type", "name", "resolution", "methods", "methodName", "method", "handleResolutionChange", "index", "newItems", "handleResolve", "resolvedAttributes", "resolvedMethods", "item", "currentIndex", "findIndex", "length", "importedAttr", "some", "existing", "importedMethod", "importedClass", "isConflict", "conflictAttr", "conflictMethod", "resolvedClass", "getModalStyles", "overlay", "position", "top", "left", "right", "bottom", "backgroundColor", "display", "alignItems", "justifyContent", "zIndex", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "modal", "borderRadius", "boxShadow", "padding", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "maxHeight", "border", "flexDirection", "header", "borderBottom", "gap", "title", "fontSize", "fontWeight", "color", "margin", "content", "overflowY", "flex", "conflictItem", "marginBottom", "<PERSON><PERSON><PERSON><PERSON>", "conflictType", "textTransform", "letterSpacing", "conflictName", "comparisonContainer", "gridTemplateColumns", "versionBox", "versionLabel", "versionContent", "fontFamily", "resolutionOptions", "resolution<PERSON>utton", "cursor", "transition", "footer", "borderTop", "button", "cancelButton", "resolveButton", "getResolutionButtonStyle", "currentResolution", "isSelected", "styles", "style", "onClick", "children", "e", "stopPropagation", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "idx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/imageUp/ConflictResolutionModal.tsx"], "sourcesContent": ["// ConflictResolutionModal.tsx - Modal pour résoudre les conflits lors de l'import\nimport React, { useState } from 'react';\nimport { ClassData } from '../../services/HistoryAnalysisService';\nimport { AlertTriangle, Check, X, ArrowRight } from 'lucide-react';\n\ninterface ConflictItem {\n  type: 'attribute' | 'method';\n  name: string;\n  current: string;\n  imported: string[];\n  resolution: 'keep' | 'replace' | 'merge';\n}\n\ninterface ConflictResolutionModalProps {\n  darkMode: boolean;\n  isOpen: boolean;\n  currentClass: ClassData;\n  importedClasses: ClassData[];\n  conflicts: { attributes: string[], methods: string[] };\n  onResolve: (resolvedData: ClassData) => void;\n  onCancel: () => void;\n}\n\nconst ConflictResolutionModal: React.FC<ConflictResolutionModalProps> = ({\n  darkMode,\n  isOpen,\n  currentClass,\n  importedClasses,\n  conflicts,\n  onResolve,\n  onCancel\n}) => {\n  const [conflictItems, setConflictItems] = useState<ConflictItem[]>(() => {\n    const items: ConflictItem[] = [];\n    \n    // Traiter les conflits d'attributs\n    conflicts.attributes.forEach(attrName => {\n      const current = currentClass.attributes.find(attr => \n        attr.toLowerCase().includes(attrName.toLowerCase())\n      ) || attrName;\n      \n      const imported = importedClasses.flatMap(cls => \n        cls.attributes.filter(attr => \n          attr.toLowerCase().includes(attrName.toLowerCase())\n        )\n      );\n      \n      items.push({\n        type: 'attribute',\n        name: attrName,\n        current,\n        imported,\n        resolution: 'merge'\n      });\n    });\n    \n    // Traiter les conflits de méthodes\n    conflicts.methods.forEach(methodName => {\n      const current = currentClass.methods.find(method => \n        method.toLowerCase().includes(methodName.toLowerCase())\n      ) || methodName;\n      \n      const imported = importedClasses.flatMap(cls => \n        cls.methods.filter(method => \n          method.toLowerCase().includes(methodName.toLowerCase())\n        )\n      );\n      \n      items.push({\n        type: 'method',\n        name: methodName,\n        current,\n        imported,\n        resolution: 'merge'\n      });\n    });\n    \n    return items;\n  });\n\n  const handleResolutionChange = (index: number, resolution: 'keep' | 'replace' | 'merge') => {\n    const newItems = [...conflictItems];\n    newItems[index].resolution = resolution;\n    setConflictItems(newItems);\n  };\n\n  const handleResolve = () => {\n    // Appliquer les résolutions de conflits\n    const resolvedAttributes = [...currentClass.attributes];\n    const resolvedMethods = [...currentClass.methods];\n    \n    conflictItems.forEach(item => {\n      if (item.type === 'attribute') {\n        const currentIndex = resolvedAttributes.findIndex(attr => \n          attr.toLowerCase().includes(item.name.toLowerCase())\n        );\n        \n        switch (item.resolution) {\n          case 'keep':\n            // Ne rien faire, garder l'actuel\n            break;\n          case 'replace':\n            if (currentIndex !== -1 && item.imported.length > 0) {\n              resolvedAttributes[currentIndex] = item.imported[0];\n            }\n            break;\n          case 'merge':\n            // Ajouter tous les variants importés qui ne sont pas déjà présents\n            item.imported.forEach(importedAttr => {\n              if (!resolvedAttributes.some(existing => \n                existing.toLowerCase() === importedAttr.toLowerCase()\n              )) {\n                resolvedAttributes.push(importedAttr);\n              }\n            });\n            break;\n        }\n      } else {\n        const currentIndex = resolvedMethods.findIndex(method => \n          method.toLowerCase().includes(item.name.toLowerCase())\n        );\n        \n        switch (item.resolution) {\n          case 'keep':\n            // Ne rien faire, garder l'actuel\n            break;\n          case 'replace':\n            if (currentIndex !== -1 && item.imported.length > 0) {\n              resolvedMethods[currentIndex] = item.imported[0];\n            }\n            break;\n          case 'merge':\n            // Ajouter tous les variants importés qui ne sont pas déjà présents\n            item.imported.forEach(importedMethod => {\n              if (!resolvedMethods.some(existing => \n                existing.toLowerCase() === importedMethod.toLowerCase()\n              )) {\n                resolvedMethods.push(importedMethod);\n              }\n            });\n            break;\n        }\n      }\n    });\n    \n    // Ajouter les éléments non-conflictuels des classes importées\n    importedClasses.forEach(importedClass => {\n      importedClass.attributes.forEach(attr => {\n        const isConflict = conflicts.attributes.some(conflictAttr => \n          attr.toLowerCase().includes(conflictAttr.toLowerCase())\n        );\n        \n        if (!isConflict && !resolvedAttributes.some(existing => \n          existing.toLowerCase() === attr.toLowerCase()\n        )) {\n          resolvedAttributes.push(attr);\n        }\n      });\n      \n      importedClass.methods.forEach(method => {\n        const isConflict = conflicts.methods.some(conflictMethod => \n          method.toLowerCase().includes(conflictMethod.toLowerCase())\n        );\n        \n        if (!isConflict && !resolvedMethods.some(existing => \n          existing.toLowerCase() === method.toLowerCase()\n        )) {\n          resolvedMethods.push(method);\n        }\n      });\n    });\n    \n    const resolvedClass: ClassData = {\n      name: currentClass.name,\n      attributes: resolvedAttributes,\n      methods: resolvedMethods\n    };\n    \n    onResolve(resolvedClass);\n  };\n\n  const getModalStyles = () => ({\n    overlay: {\n      position: 'fixed' as const,\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0, 0, 0, 0.7)',\n      display: isOpen ? 'flex' : 'none',\n      alignItems: 'center',\n      justifyContent: 'center',\n      zIndex: 1100,\n      backdropFilter: 'blur(8px)',\n      WebkitBackdropFilter: 'blur(8px)',\n    },\n    modal: {\n      backgroundColor: darkMode ? '#0a0f1c' : '#ffffff',\n      borderRadius: '16px',\n      boxShadow: darkMode \n        ? '0 25px 50px -12px rgba(0, 0, 0, 0.8)' \n        : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n      padding: '0',\n      minWidth: '600px',\n      maxWidth: '800px',\n      maxHeight: '80vh',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      display: 'flex',\n      flexDirection: 'column' as const,\n    },\n    header: {\n      padding: '24px 28px 20px',\n      borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n    },\n    title: {\n      fontSize: '20px',\n      fontWeight: '700',\n      color: darkMode ? '#f8fafc' : '#0f172a',\n      margin: 0,\n    },\n    content: {\n      padding: '24px 28px',\n      overflowY: 'auto' as const,\n      flex: 1,\n    },\n    conflictItem: {\n      marginBottom: '24px',\n      padding: '16px',\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.5)' : 'rgba(248, 250, 252, 0.8)',\n      borderRadius: '12px',\n      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`,\n    },\n    conflictHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      marginBottom: '12px',\n    },\n    conflictType: {\n      fontSize: '14px',\n      fontWeight: '600',\n      color: darkMode ? '#fbbf24' : '#d97706',\n      textTransform: 'uppercase' as const,\n      letterSpacing: '0.05em',\n    },\n    conflictName: {\n      fontSize: '16px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151',\n    },\n    comparisonContainer: {\n      display: 'grid',\n      gridTemplateColumns: '1fr auto 1fr',\n      gap: '16px',\n      alignItems: 'center',\n      marginBottom: '16px',\n    },\n    versionBox: {\n      padding: '12px',\n      backgroundColor: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(255, 255, 255, 0.9)',\n      borderRadius: '8px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n    },\n    versionLabel: {\n      fontSize: '12px',\n      fontWeight: '600',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      marginBottom: '8px',\n      textTransform: 'uppercase' as const,\n    },\n    versionContent: {\n      fontSize: '14px',\n      color: darkMode ? '#cbd5e1' : '#4b5563',\n      fontFamily: 'monospace',\n    },\n    resolutionOptions: {\n      display: 'flex',\n      gap: '12px',\n    },\n    resolutionButton: {\n      padding: '8px 16px',\n      borderRadius: '8px',\n      border: 'none',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: 'pointer',\n      transition: 'all 0.2s ease',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '6px',\n    },\n    footer: {\n      padding: '20px 28px',\n      borderTop: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,\n      display: 'flex',\n      justifyContent: 'flex-end',\n      gap: '12px',\n    },\n    button: {\n      padding: '12px 24px',\n      borderRadius: '8px',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: 'pointer',\n      transition: 'all 0.2s ease',\n      border: 'none',\n    },\n    cancelButton: {\n      backgroundColor: 'transparent',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n    },\n    resolveButton: {\n      backgroundColor: '#3b82f6',\n      color: '#ffffff',\n    }\n  });\n\n  const getResolutionButtonStyle = (resolution: string, currentResolution: string) => {\n    const isSelected = resolution === currentResolution;\n    return {\n      ...getModalStyles().resolutionButton,\n      backgroundColor: isSelected \n        ? '#3b82f6' \n        : darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',\n      color: isSelected \n        ? '#ffffff' \n        : darkMode ? '#cbd5e1' : '#4b5563',\n      border: `1px solid ${isSelected \n        ? '#3b82f6' \n        : darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`\n    };\n  };\n\n  const styles = getModalStyles();\n\n  if (!isOpen) return null;\n\n  return (\n    <div style={styles.overlay} onClick={onCancel}>\n      <div style={styles.modal} onClick={(e) => e.stopPropagation()}>\n        <div style={styles.header}>\n          <AlertTriangle size={24} color=\"#f59e0b\" />\n          <h3 style={styles.title}>Résolution des Conflits</h3>\n        </div>\n        \n        <div style={styles.content}>\n          {conflictItems.map((item, index) => (\n            <div key={`${item.type}-${item.name}`} style={styles.conflictItem}>\n              <div style={styles.conflictHeader}>\n                <span style={styles.conflictType}>{item.type === 'attribute' ? 'Attribut' : 'Méthode'}</span>\n                <span style={styles.conflictName}>{item.name}</span>\n              </div>\n              \n              <div style={styles.comparisonContainer}>\n                <div style={styles.versionBox}>\n                  <div style={styles.versionLabel}>Version Actuelle</div>\n                  <div style={styles.versionContent}>{item.current}</div>\n                </div>\n                \n                <ArrowRight size={20} color={darkMode ? '#64748b' : '#9ca3af'} />\n                \n                <div style={styles.versionBox}>\n                  <div style={styles.versionLabel}>Versions Importées</div>\n                  {item.imported.map((imported, idx) => (\n                    <div key={idx} style={styles.versionContent}>{imported}</div>\n                  ))}\n                </div>\n              </div>\n              \n              <div style={styles.resolutionOptions}>\n                <button\n                  style={getResolutionButtonStyle('keep', item.resolution)}\n                  onClick={() => handleResolutionChange(index, 'keep')}\n                >\n                  <X size={16} />\n                  Garder l'actuel\n                </button>\n                <button\n                  style={getResolutionButtonStyle('replace', item.resolution)}\n                  onClick={() => handleResolutionChange(index, 'replace')}\n                >\n                  <ArrowRight size={16} />\n                  Remplacer\n                </button>\n                <button\n                  style={getResolutionButtonStyle('merge', item.resolution)}\n                  onClick={() => handleResolutionChange(index, 'merge')}\n                >\n                  <Check size={16} />\n                  Fusionner\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n        \n        <div style={styles.footer}>\n          <button style={{...styles.button, ...styles.cancelButton}} onClick={onCancel}>\n            Annuler\n          </button>\n          <button style={{...styles.button, ...styles.resolveButton}} onClick={handleResolve}>\n            Appliquer les Résolutions\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConflictResolutionModal;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAEvC,SAASC,aAAa,EAAEC,KAAK,EAAEC,CAAC,EAAEC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBnE,MAAMC,uBAA+D,GAAGA,CAAC;EACvEC,QAAQ;EACRC,MAAM;EACNC,YAAY;EACZC,eAAe;EACfC,SAAS;EACTC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAiB,MAAM;IACvE,MAAMkB,KAAqB,GAAG,EAAE;;IAEhC;IACAN,SAAS,CAACO,UAAU,CAACC,OAAO,CAACC,QAAQ,IAAI;MACvC,MAAMC,OAAO,GAAGZ,YAAY,CAACS,UAAU,CAACI,IAAI,CAACC,IAAI,IAC/CA,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,QAAQ,CAACI,WAAW,CAAC,CAAC,CACpD,CAAC,IAAIJ,QAAQ;MAEb,MAAMM,QAAQ,GAAGhB,eAAe,CAACiB,OAAO,CAACC,GAAG,IAC1CA,GAAG,CAACV,UAAU,CAACW,MAAM,CAACN,IAAI,IACxBA,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,QAAQ,CAACI,WAAW,CAAC,CAAC,CACpD,CACF,CAAC;MAEDP,KAAK,CAACa,IAAI,CAAC;QACTC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAEZ,QAAQ;QACdC,OAAO;QACPK,QAAQ;QACRO,UAAU,EAAE;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAtB,SAAS,CAACuB,OAAO,CAACf,OAAO,CAACgB,UAAU,IAAI;MACtC,MAAMd,OAAO,GAAGZ,YAAY,CAACyB,OAAO,CAACZ,IAAI,CAACc,MAAM,IAC9CA,MAAM,CAACZ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACU,UAAU,CAACX,WAAW,CAAC,CAAC,CACxD,CAAC,IAAIW,UAAU;MAEf,MAAMT,QAAQ,GAAGhB,eAAe,CAACiB,OAAO,CAACC,GAAG,IAC1CA,GAAG,CAACM,OAAO,CAACL,MAAM,CAACO,MAAM,IACvBA,MAAM,CAACZ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACU,UAAU,CAACX,WAAW,CAAC,CAAC,CACxD,CACF,CAAC;MAEDP,KAAK,CAACa,IAAI,CAAC;QACTC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAEG,UAAU;QAChBd,OAAO;QACPK,QAAQ;QACRO,UAAU,EAAE;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOhB,KAAK;EACd,CAAC,CAAC;EAEF,MAAMoB,sBAAsB,GAAGA,CAACC,KAAa,EAAEL,UAAwC,KAAK;IAC1F,MAAMM,QAAQ,GAAG,CAAC,GAAGxB,aAAa,CAAC;IACnCwB,QAAQ,CAACD,KAAK,CAAC,CAACL,UAAU,GAAGA,UAAU;IACvCjB,gBAAgB,CAACuB,QAAQ,CAAC;EAC5B,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,MAAMC,kBAAkB,GAAG,CAAC,GAAGhC,YAAY,CAACS,UAAU,CAAC;IACvD,MAAMwB,eAAe,GAAG,CAAC,GAAGjC,YAAY,CAACyB,OAAO,CAAC;IAEjDnB,aAAa,CAACI,OAAO,CAACwB,IAAI,IAAI;MAC5B,IAAIA,IAAI,CAACZ,IAAI,KAAK,WAAW,EAAE;QAC7B,MAAMa,YAAY,GAAGH,kBAAkB,CAACI,SAAS,CAACtB,IAAI,IACpDA,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACkB,IAAI,CAACX,IAAI,CAACR,WAAW,CAAC,CAAC,CACrD,CAAC;QAED,QAAQmB,IAAI,CAACV,UAAU;UACrB,KAAK,MAAM;YACT;YACA;UACF,KAAK,SAAS;YACZ,IAAIW,YAAY,KAAK,CAAC,CAAC,IAAID,IAAI,CAACjB,QAAQ,CAACoB,MAAM,GAAG,CAAC,EAAE;cACnDL,kBAAkB,CAACG,YAAY,CAAC,GAAGD,IAAI,CAACjB,QAAQ,CAAC,CAAC,CAAC;YACrD;YACA;UACF,KAAK,OAAO;YACV;YACAiB,IAAI,CAACjB,QAAQ,CAACP,OAAO,CAAC4B,YAAY,IAAI;cACpC,IAAI,CAACN,kBAAkB,CAACO,IAAI,CAACC,QAAQ,IACnCA,QAAQ,CAACzB,WAAW,CAAC,CAAC,KAAKuB,YAAY,CAACvB,WAAW,CAAC,CACtD,CAAC,EAAE;gBACDiB,kBAAkB,CAACX,IAAI,CAACiB,YAAY,CAAC;cACvC;YACF,CAAC,CAAC;YACF;QACJ;MACF,CAAC,MAAM;QACL,MAAMH,YAAY,GAAGF,eAAe,CAACG,SAAS,CAACT,MAAM,IACnDA,MAAM,CAACZ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACkB,IAAI,CAACX,IAAI,CAACR,WAAW,CAAC,CAAC,CACvD,CAAC;QAED,QAAQmB,IAAI,CAACV,UAAU;UACrB,KAAK,MAAM;YACT;YACA;UACF,KAAK,SAAS;YACZ,IAAIW,YAAY,KAAK,CAAC,CAAC,IAAID,IAAI,CAACjB,QAAQ,CAACoB,MAAM,GAAG,CAAC,EAAE;cACnDJ,eAAe,CAACE,YAAY,CAAC,GAAGD,IAAI,CAACjB,QAAQ,CAAC,CAAC,CAAC;YAClD;YACA;UACF,KAAK,OAAO;YACV;YACAiB,IAAI,CAACjB,QAAQ,CAACP,OAAO,CAAC+B,cAAc,IAAI;cACtC,IAAI,CAACR,eAAe,CAACM,IAAI,CAACC,QAAQ,IAChCA,QAAQ,CAACzB,WAAW,CAAC,CAAC,KAAK0B,cAAc,CAAC1B,WAAW,CAAC,CACxD,CAAC,EAAE;gBACDkB,eAAe,CAACZ,IAAI,CAACoB,cAAc,CAAC;cACtC;YACF,CAAC,CAAC;YACF;QACJ;MACF;IACF,CAAC,CAAC;;IAEF;IACAxC,eAAe,CAACS,OAAO,CAACgC,aAAa,IAAI;MACvCA,aAAa,CAACjC,UAAU,CAACC,OAAO,CAACI,IAAI,IAAI;QACvC,MAAM6B,UAAU,GAAGzC,SAAS,CAACO,UAAU,CAAC8B,IAAI,CAACK,YAAY,IACvD9B,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC4B,YAAY,CAAC7B,WAAW,CAAC,CAAC,CACxD,CAAC;QAED,IAAI,CAAC4B,UAAU,IAAI,CAACX,kBAAkB,CAACO,IAAI,CAACC,QAAQ,IAClDA,QAAQ,CAACzB,WAAW,CAAC,CAAC,KAAKD,IAAI,CAACC,WAAW,CAAC,CAC9C,CAAC,EAAE;UACDiB,kBAAkB,CAACX,IAAI,CAACP,IAAI,CAAC;QAC/B;MACF,CAAC,CAAC;MAEF4B,aAAa,CAACjB,OAAO,CAACf,OAAO,CAACiB,MAAM,IAAI;QACtC,MAAMgB,UAAU,GAAGzC,SAAS,CAACuB,OAAO,CAACc,IAAI,CAACM,cAAc,IACtDlB,MAAM,CAACZ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC6B,cAAc,CAAC9B,WAAW,CAAC,CAAC,CAC5D,CAAC;QAED,IAAI,CAAC4B,UAAU,IAAI,CAACV,eAAe,CAACM,IAAI,CAACC,QAAQ,IAC/CA,QAAQ,CAACzB,WAAW,CAAC,CAAC,KAAKY,MAAM,CAACZ,WAAW,CAAC,CAChD,CAAC,EAAE;UACDkB,eAAe,CAACZ,IAAI,CAACM,MAAM,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAMmB,aAAwB,GAAG;MAC/BvB,IAAI,EAAEvB,YAAY,CAACuB,IAAI;MACvBd,UAAU,EAAEuB,kBAAkB;MAC9BP,OAAO,EAAEQ;IACX,CAAC;IAED9B,SAAS,CAAC2C,aAAa,CAAC;EAC1B,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,MAAO;IAC5BC,OAAO,EAAE;MACPC,QAAQ,EAAE,OAAgB;MAC1BC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,eAAe,EAAE,oBAAoB;MACrCC,OAAO,EAAExD,MAAM,GAAG,MAAM,GAAG,MAAM;MACjCyD,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,MAAM,EAAE,IAAI;MACZC,cAAc,EAAE,WAAW;MAC3BC,oBAAoB,EAAE;IACxB,CAAC;IACDC,KAAK,EAAE;MACLP,eAAe,EAAExD,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDgE,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAEjE,QAAQ,GACf,sCAAsC,GACtC,uCAAuC;MAC3CkE,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAE,aAAatE,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFyD,OAAO,EAAE,MAAM;MACfc,aAAa,EAAE;IACjB,CAAC;IACDC,MAAM,EAAE;MACNN,OAAO,EAAE,gBAAgB;MACzBO,YAAY,EAAE,aAAazE,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B,EAAE;MAC/FyD,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBgB,GAAG,EAAE;IACP,CAAC;IACDC,KAAK,EAAE;MACLC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBC,KAAK,EAAE9E,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC+E,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPd,OAAO,EAAE,WAAW;MACpBe,SAAS,EAAE,MAAe;MAC1BC,IAAI,EAAE;IACR,CAAC;IACDC,YAAY,EAAE;MACZC,YAAY,EAAE,MAAM;MACpBlB,OAAO,EAAE,MAAM;MACfV,eAAe,EAAExD,QAAQ,GAAG,uBAAuB,GAAG,0BAA0B;MAChFgE,YAAY,EAAE,MAAM;MACpBM,MAAM,EAAE,aAAatE,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB;IACvF,CAAC;IACDqF,cAAc,EAAE;MACd5B,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBgB,GAAG,EAAE,KAAK;MACVU,YAAY,EAAE;IAChB,CAAC;IACDE,YAAY,EAAE;MACZV,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBC,KAAK,EAAE9E,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCuF,aAAa,EAAE,WAAoB;MACnCC,aAAa,EAAE;IACjB,CAAC;IACDC,YAAY,EAAE;MACZb,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBC,KAAK,EAAE9E,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACD0F,mBAAmB,EAAE;MACnBjC,OAAO,EAAE,MAAM;MACfkC,mBAAmB,EAAE,cAAc;MACnCjB,GAAG,EAAE,MAAM;MACXhB,UAAU,EAAE,QAAQ;MACpB0B,YAAY,EAAE;IAChB,CAAC;IACDQ,UAAU,EAAE;MACV1B,OAAO,EAAE,MAAM;MACfV,eAAe,EAAExD,QAAQ,GAAG,uBAAuB,GAAG,0BAA0B;MAChFgE,YAAY,EAAE,KAAK;MACnBM,MAAM,EAAE,aAAatE,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB;IACvF,CAAC;IACD6F,YAAY,EAAE;MACZjB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBC,KAAK,EAAE9E,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCoF,YAAY,EAAE,KAAK;MACnBG,aAAa,EAAE;IACjB,CAAC;IACDO,cAAc,EAAE;MACdlB,QAAQ,EAAE,MAAM;MAChBE,KAAK,EAAE9E,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC+F,UAAU,EAAE;IACd,CAAC;IACDC,iBAAiB,EAAE;MACjBvC,OAAO,EAAE,MAAM;MACfiB,GAAG,EAAE;IACP,CAAC;IACDuB,gBAAgB,EAAE;MAChB/B,OAAO,EAAE,UAAU;MACnBF,YAAY,EAAE,KAAK;MACnBM,MAAM,EAAE,MAAM;MACdM,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBqB,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,eAAe;MAC3B1C,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBgB,GAAG,EAAE;IACP,CAAC;IACD0B,MAAM,EAAE;MACNlC,OAAO,EAAE,WAAW;MACpBmC,SAAS,EAAE,aAAarG,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B,EAAE;MAC5FyD,OAAO,EAAE,MAAM;MACfE,cAAc,EAAE,UAAU;MAC1Be,GAAG,EAAE;IACP,CAAC;IACD4B,MAAM,EAAE;MACNpC,OAAO,EAAE,WAAW;MACpBF,YAAY,EAAE,KAAK;MACnBY,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBqB,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,eAAe;MAC3B7B,MAAM,EAAE;IACV,CAAC;IACDiC,YAAY,EAAE;MACZ/C,eAAe,EAAE,aAAa;MAC9BsB,KAAK,EAAE9E,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCsE,MAAM,EAAE,aAAatE,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB;IACvF,CAAC;IACDwG,aAAa,EAAE;MACbhD,eAAe,EAAE,SAAS;MAC1BsB,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EAEF,MAAM2B,wBAAwB,GAAGA,CAAC/E,UAAkB,EAAEgF,iBAAyB,KAAK;IAClF,MAAMC,UAAU,GAAGjF,UAAU,KAAKgF,iBAAiB;IACnD,OAAO;MACL,GAAGzD,cAAc,CAAC,CAAC,CAACgD,gBAAgB;MACpCzC,eAAe,EAAEmD,UAAU,GACvB,SAAS,GACT3G,QAAQ,GAAG,uBAAuB,GAAG,0BAA0B;MACnE8E,KAAK,EAAE6B,UAAU,GACb,SAAS,GACT3G,QAAQ,GAAG,SAAS,GAAG,SAAS;MACpCsE,MAAM,EAAE,aAAaqC,UAAU,GAC3B,SAAS,GACT3G,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB;IACtE,CAAC;EACH,CAAC;EAED,MAAM4G,MAAM,GAAG3D,cAAc,CAAC,CAAC;EAE/B,IAAI,CAAChD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEH,OAAA;IAAK+G,KAAK,EAAED,MAAM,CAAC1D,OAAQ;IAAC4D,OAAO,EAAExG,QAAS;IAAAyG,QAAA,eAC5CjH,OAAA;MAAK+G,KAAK,EAAED,MAAM,CAAC7C,KAAM;MAAC+C,OAAO,EAAGE,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAF,QAAA,gBAC5DjH,OAAA;QAAK+G,KAAK,EAAED,MAAM,CAACpC,MAAO;QAAAuC,QAAA,gBACxBjH,OAAA,CAACL,aAAa;UAACyH,IAAI,EAAE,EAAG;UAACpC,KAAK,EAAC;QAAS;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CxH,OAAA;UAAI+G,KAAK,EAAED,MAAM,CAACjC,KAAM;UAAAoC,QAAA,EAAC;QAAuB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eAENxH,OAAA;QAAK+G,KAAK,EAAED,MAAM,CAAC5B,OAAQ;QAAA+B,QAAA,EACxBvG,aAAa,CAAC+G,GAAG,CAAC,CAACnF,IAAI,EAAEL,KAAK,kBAC7BjC,OAAA;UAAuC+G,KAAK,EAAED,MAAM,CAACzB,YAAa;UAAA4B,QAAA,gBAChEjH,OAAA;YAAK+G,KAAK,EAAED,MAAM,CAACvB,cAAe;YAAA0B,QAAA,gBAChCjH,OAAA;cAAM+G,KAAK,EAAED,MAAM,CAACtB,YAAa;cAAAyB,QAAA,EAAE3E,IAAI,CAACZ,IAAI,KAAK,WAAW,GAAG,UAAU,GAAG;YAAS;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7FxH,OAAA;cAAM+G,KAAK,EAAED,MAAM,CAACnB,YAAa;cAAAsB,QAAA,EAAE3E,IAAI,CAACX;YAAI;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAENxH,OAAA;YAAK+G,KAAK,EAAED,MAAM,CAAClB,mBAAoB;YAAAqB,QAAA,gBACrCjH,OAAA;cAAK+G,KAAK,EAAED,MAAM,CAAChB,UAAW;cAAAmB,QAAA,gBAC5BjH,OAAA;gBAAK+G,KAAK,EAAED,MAAM,CAACf,YAAa;gBAAAkB,QAAA,EAAC;cAAgB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDxH,OAAA;gBAAK+G,KAAK,EAAED,MAAM,CAACd,cAAe;gBAAAiB,QAAA,EAAE3E,IAAI,CAACtB;cAAO;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAENxH,OAAA,CAACF,UAAU;cAACsH,IAAI,EAAE,EAAG;cAACpC,KAAK,EAAE9E,QAAQ,GAAG,SAAS,GAAG;YAAU;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEjExH,OAAA;cAAK+G,KAAK,EAAED,MAAM,CAAChB,UAAW;cAAAmB,QAAA,gBAC5BjH,OAAA;gBAAK+G,KAAK,EAAED,MAAM,CAACf,YAAa;gBAAAkB,QAAA,EAAC;cAAkB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACxDlF,IAAI,CAACjB,QAAQ,CAACoG,GAAG,CAAC,CAACpG,QAAQ,EAAEqG,GAAG,kBAC/B1H,OAAA;gBAAe+G,KAAK,EAAED,MAAM,CAACd,cAAe;gBAAAiB,QAAA,EAAE5F;cAAQ,GAA5CqG,GAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA+C,CAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxH,OAAA;YAAK+G,KAAK,EAAED,MAAM,CAACZ,iBAAkB;YAAAe,QAAA,gBACnCjH,OAAA;cACE+G,KAAK,EAAEJ,wBAAwB,CAAC,MAAM,EAAErE,IAAI,CAACV,UAAU,CAAE;cACzDoF,OAAO,EAAEA,CAAA,KAAMhF,sBAAsB,CAACC,KAAK,EAAE,MAAM,CAAE;cAAAgF,QAAA,gBAErDjH,OAAA,CAACH,CAAC;gBAACuH,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxH,OAAA;cACE+G,KAAK,EAAEJ,wBAAwB,CAAC,SAAS,EAAErE,IAAI,CAACV,UAAU,CAAE;cAC5DoF,OAAO,EAAEA,CAAA,KAAMhF,sBAAsB,CAACC,KAAK,EAAE,SAAS,CAAE;cAAAgF,QAAA,gBAExDjH,OAAA,CAACF,UAAU;gBAACsH,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAE1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxH,OAAA;cACE+G,KAAK,EAAEJ,wBAAwB,CAAC,OAAO,EAAErE,IAAI,CAACV,UAAU,CAAE;cAC1DoF,OAAO,EAAEA,CAAA,KAAMhF,sBAAsB,CAACC,KAAK,EAAE,OAAO,CAAE;cAAAgF,QAAA,gBAEtDjH,OAAA,CAACJ,KAAK;gBAACwH,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GA5CE,GAAGlF,IAAI,CAACZ,IAAI,IAAIY,IAAI,CAACX,IAAI,EAAE;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6ChC,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENxH,OAAA;QAAK+G,KAAK,EAAED,MAAM,CAACR,MAAO;QAAAW,QAAA,gBACxBjH,OAAA;UAAQ+G,KAAK,EAAE;YAAC,GAAGD,MAAM,CAACN,MAAM;YAAE,GAAGM,MAAM,CAACL;UAAY,CAAE;UAACO,OAAO,EAAExG,QAAS;UAAAyG,QAAA,EAAC;QAE9E;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxH,OAAA;UAAQ+G,KAAK,EAAE;YAAC,GAAGD,MAAM,CAACN,MAAM;YAAE,GAAGM,MAAM,CAACJ;UAAa,CAAE;UAACM,OAAO,EAAE7E,aAAc;UAAA8E,QAAA,EAAC;QAEpF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/G,EAAA,CApYIR,uBAA+D;AAAA0H,EAAA,GAA/D1H,uBAA+D;AAsYrE,eAAeA,uBAAuB;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}