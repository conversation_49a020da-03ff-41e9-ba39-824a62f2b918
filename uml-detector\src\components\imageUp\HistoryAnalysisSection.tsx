// HistoryAnalysisSection.tsx - Composant pour l'analyse historique et l'import de classes
import React, { useState, useEffect } from 'react';
import { useHistory } from '../../context/HistoryContext';
import { useAuth } from '../../context/AuthContext';
import { HistoryAnalysisService, HistoryMatch, SortOption, ClassData } from '../../services/HistoryAnalysisService';
import { ChevronDown, ChevronUp, Eye, Clock, Zap, AlertTriangle } from 'lucide-react';

interface HistoryAnalysisSectionProps {
  darkMode: boolean;
  targetClassName: string;
  currentClassData: ClassData;
  onImport: (importedData: ClassData) => void;
}

const HistoryAnalysisSection: React.FC<HistoryAnalysisSectionProps> = ({
  darkMode,
  targetClassName,
  currentClassData,
  onImport
}) => {
  const { historyItems } = useHistory();
  const { currentUser } = useAuth();
  const [isExpanded, setIsExpanded] = useState(false);
  const [matches, setMatches] = useState<HistoryMatch[]>([]);
  const [selectedMatches, setSelectedMatches] = useState<Set<string>>(new Set());
  const [sortOption, setSortOption] = useState<SortOption>(HistoryAnalysisService.getSortOptions()[0]);
  const [showPreview, setShowPreview] = useState<string | null>(null);
  const [conflicts, setConflicts] = useState<{ attributes: string[], methods: string[] }>({ attributes: [], methods: [] });

  // Rechercher les correspondances lors du changement de classe cible
  useEffect(() => {
    if (!targetClassName || !currentUser) return;
    
    const foundMatches = HistoryAnalysisService.findMatchingDiagrams(
      targetClassName,
      historyItems,
      currentUser.uid
    );
    
    const sortedMatches = HistoryAnalysisService.sortMatches(foundMatches, sortOption);
    setMatches(sortedMatches);
  }, [targetClassName, historyItems, currentUser, sortOption]);

  // Détecter les conflits lors du changement de sélection
  useEffect(() => {
    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));
    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);
    
    if (selectedClasses.length > 0) {
      const detectedConflicts = HistoryAnalysisService.detectConflicts(currentClassData, selectedClasses);
      setConflicts(detectedConflicts);
    } else {
      setConflicts({ attributes: [], methods: [] });
    }
  }, [selectedMatches, matches, currentClassData]);

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleMatchSelection = (matchId: string, selected: boolean) => {
    const newSelection = new Set(selectedMatches);
    if (selected) {
      newSelection.add(matchId);
    } else {
      newSelection.delete(matchId);
    }
    setSelectedMatches(newSelection);
  };

  const handleImport = () => {
    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));
    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);
    
    if (selectedClasses.length > 0) {
      const mergedData = HistoryAnalysisService.mergeClassData(
        currentClassData,
        selectedClasses,
        'merge' // Par défaut, on fusionne sans écraser
      );
      onImport(mergedData);
      setSelectedMatches(new Set()); // Reset selection
    }
  };

  const handlePreview = (matchId: string) => {
    setShowPreview(showPreview === matchId ? null : matchId);
  };

  const getSectionStyles = () => ({
    container: {
      marginBottom: '24px',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
      borderRadius: '12px',
      background: darkMode 
        ? 'linear-gradient(135deg, rgba(10, 15, 28, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)' 
        : 'linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
    },
    header: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '16px 20px',
      cursor: 'pointer',
      borderBottom: isExpanded ? `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}` : 'none',
    },
    headerLeft: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
    },
    title: {
      fontSize: '16px',
      fontWeight: '600',
      color: darkMode ? '#f8fafc' : '#0f172a',
      margin: 0,
    },
    badge: {
      backgroundColor: matches.length > 0 ? '#3b82f6' : darkMode ? '#64748b' : '#94a3b8',
      color: '#ffffff',
      fontSize: '12px',
      fontWeight: '600',
      padding: '4px 8px',
      borderRadius: '12px',
      minWidth: '20px',
      textAlign: 'center' as const,
    },
    expandIcon: {
      color: darkMode ? '#94a3b8' : '#64748b',
      transition: 'transform 0.2s ease',
      transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
    },
    content: {
      padding: isExpanded ? '20px' : '0',
      maxHeight: isExpanded ? '400px' : '0',
      overflow: 'hidden' as const,
      transition: 'all 0.3s ease',
    },
    sortContainer: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      marginBottom: '16px',
    },
    sortLabel: {
      fontSize: '14px',
      color: darkMode ? '#cbd5e1' : '#64748b',
      fontWeight: '500',
    },
    sortSelect: {
      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
      borderRadius: '8px',
      padding: '6px 12px',
      fontSize: '14px',
      color: darkMode ? '#e2e8f0' : '#374151',
      cursor: 'pointer',
    },
    matchesList: {
      maxHeight: '250px',
      overflowY: 'auto' as const,
      marginBottom: '16px',
    },
    matchItem: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px',
      padding: '12px',
      marginBottom: '8px',
      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.4)' : 'rgba(248, 250, 252, 0.6)',
      borderRadius: '8px',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'}`,
      transition: 'all 0.2s ease',
    },
    checkbox: {
      width: '16px',
      height: '16px',
      accentColor: '#3b82f6',
      cursor: 'pointer',
    },
    matchInfo: {
      flex: 1,
    },
    matchTitle: {
      fontSize: '14px',
      fontWeight: '500',
      color: darkMode ? '#e2e8f0' : '#374151',
      marginBottom: '4px',
    },
    matchMeta: {
      fontSize: '12px',
      color: darkMode ? '#94a3b8' : '#64748b',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
    },
    similarityBadge: {
      backgroundColor: '#10b981',
      color: '#ffffff',
      fontSize: '11px',
      fontWeight: '600',
      padding: '2px 6px',
      borderRadius: '8px',
    },
    actionButtons: {
      display: 'flex',
      gap: '8px',
    },
    actionButton: {
      backgroundColor: 'transparent',
      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'}`,
      borderRadius: '6px',
      padding: '6px',
      cursor: 'pointer',
      color: darkMode ? '#60a5fa' : '#3b82f6',
      transition: 'all 0.2s ease',
    },
    conflictsWarning: {
      backgroundColor: darkMode ? 'rgba(245, 158, 11, 0.1)' : 'rgba(245, 158, 11, 0.05)',
      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`,
      borderRadius: '8px',
      padding: '12px',
      marginBottom: '16px',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
    },
    conflictsText: {
      fontSize: '13px',
      color: darkMode ? '#fbbf24' : '#d97706',
      fontWeight: '500',
    },
    importButton: {
      backgroundColor: selectedMatches.size > 0 ? '#3b82f6' : darkMode ? '#374151' : '#9ca3af',
      color: '#ffffff',
      border: 'none',
      borderRadius: '8px',
      padding: '10px 16px',
      fontSize: '14px',
      fontWeight: '600',
      cursor: selectedMatches.size > 0 ? 'pointer' : 'not-allowed',
      transition: 'all 0.2s ease',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
    },
    emptyState: {
      textAlign: 'center' as const,
      color: darkMode ? '#64748b' : '#9ca3af',
      fontSize: '14px',
      fontStyle: 'italic',
      padding: '20px',
    }
  });

  const styles = getSectionStyles();

  if (!currentUser) return null;

  return (
    <div style={styles.container}>
      <div style={styles.header} onClick={handleToggleExpand}>
        <div style={styles.headerLeft}>
          <Zap size={18} color={darkMode ? '#60a5fa' : '#3b82f6'} />
          <h4 style={styles.title}>Analyse Historique</h4>
          <span style={styles.badge}>{matches.length}</span>
        </div>
        <ChevronDown size={20} style={styles.expandIcon} />
      </div>
      
      {isExpanded && (
        <div style={styles.content}>
          {matches.length === 0 ? (
            <div style={styles.emptyState}>
              Aucun diagramme historique trouvé pour la classe "{targetClassName}"
            </div>
          ) : (
            <>
              <div style={styles.sortContainer}>
                <span style={styles.sortLabel}>Trier par:</span>
                <select 
                  style={styles.sortSelect}
                  value={`${sortOption.key}-${sortOption.direction}`}
                  onChange={(e) => {
                    const [key, direction] = e.target.value.split('-');
                    const option = HistoryAnalysisService.getSortOptions().find(
                      opt => opt.key === key && opt.direction === direction
                    );
                    if (option) setSortOption(option);
                  }}
                >
                  {HistoryAnalysisService.getSortOptions().map(option => (
                    <option key={`${option.key}-${option.direction}`} value={`${option.key}-${option.direction}`}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {conflicts.attributes.length > 0 || conflicts.methods.length > 0 ? (
                <div style={styles.conflictsWarning}>
                  <AlertTriangle size={16} />
                  <span style={styles.conflictsText}>
                    Conflits détectés: {conflicts.attributes.length} attribut(s), {conflicts.methods.length} méthode(s)
                  </span>
                </div>
              ) : null}

              <div style={styles.matchesList}>
                {matches.map(match => (
                  <div key={match.historyItem.id} style={styles.matchItem}>
                    <input
                      type="checkbox"
                      style={styles.checkbox}
                      checked={selectedMatches.has(match.historyItem.id)}
                      onChange={(e) => handleMatchSelection(match.historyItem.id, e.target.checked)}
                    />
                    <div style={styles.matchInfo}>
                      <div style={styles.matchTitle}>{match.historyItem.title}</div>
                      <div style={styles.matchMeta}>
                        <Clock size={12} />
                        {match.historyItem.createdAt.toLocaleDateString('fr-FR')}
                        <span style={styles.similarityBadge}>{Math.round(match.similarity)}%</span>
                        {match.isShared && <span>🔗 Partagé</span>}
                      </div>
                    </div>
                    <div style={styles.actionButtons}>
                      <button
                        style={styles.actionButton}
                        onClick={() => handlePreview(match.historyItem.id)}
                        title="Voir aperçu"
                      >
                        <Eye size={14} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              <button
                style={styles.importButton}
                onClick={handleImport}
                disabled={selectedMatches.size === 0}
              >
                <Zap size={16} />
                Importer ({selectedMatches.size} sélectionné{selectedMatches.size > 1 ? 's' : ''})
              </button>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default HistoryAnalysisSection;
