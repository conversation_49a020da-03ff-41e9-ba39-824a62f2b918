{"version": 3, "file": "static/js/886.5850109a.chunk.js", "mappings": "0MAOIA,GAA2BC,EAAAA,EAAAA,KAAO,CAACC,EAASC,KAC9C,MAAMC,EAAcF,EAAQG,OAAO,QAgBnC,GAfAD,EAAYE,KAAK,IAAKH,EAASI,GAC/BH,EAAYE,KAAK,IAAKH,EAASK,GAC/BJ,EAAYE,KAAK,OAAQH,EAASM,MAClCL,EAAYE,KAAK,SAAUH,EAASO,QACpCN,EAAYE,KAAK,QAASH,EAASQ,OACnCP,EAAYE,KAAK,SAAUH,EAASS,QAChCT,EAASU,MACXT,EAAYE,KAAK,OAAQH,EAASU,MAEhCV,EAASW,IACXV,EAAYE,KAAK,KAAMH,EAASW,IAE9BX,EAASY,IACXX,EAAYE,KAAK,KAAMH,EAASY,SAEX,IAAnBZ,EAASa,MACX,IAAK,MAAMC,KAAWd,EAASa,MAC7BZ,EAAYE,KAAKW,EAASd,EAASa,MAAMC,IAM7C,OAHId,EAASe,OACXd,EAAYE,KAAK,QAASH,EAASe,OAE9Bd,CAAW,GACjB,YACCe,GAAqClB,EAAAA,EAAAA,KAAO,CAACC,EAASkB,KACxD,MAAMjB,EAAW,CACfI,EAAGa,EAAOC,OACVb,EAAGY,EAAOE,OACVX,MAAOS,EAAOG,MAAQH,EAAOC,OAC7BT,OAAQQ,EAAOI,MAAQJ,EAAOE,OAC9Bb,KAAMW,EAAOX,KACbC,OAAQU,EAAOV,OACfQ,MAAO,QAEWlB,EAASE,EAASC,GAC1BsB,OAAO,GAClB,sBACCC,GAA2BzB,EAAAA,EAAAA,KAAO,CAACC,EAASyB,KAC9C,MAAMC,EAAQD,EAASE,KAAKC,QAAQC,EAAAA,GAAgB,KAC9CC,EAAW9B,EAAQG,OAAO,QAChC2B,EAAS1B,KAAK,IAAKqB,EAASpB,GAC5ByB,EAAS1B,KAAK,IAAKqB,EAASnB,GAC5BwB,EAAS1B,KAAK,QAAS,UACvB0B,EAASC,MAAM,cAAeN,EAASO,QACnCP,EAAST,OACXc,EAAS1B,KAAK,QAASqB,EAAST,OAElC,MAAMiB,EAAQH,EAAS3B,OAAO,SAG9B,OAFA8B,EAAM7B,KAAK,IAAKqB,EAASpB,EAA0B,EAAtBoB,EAASS,YACtCD,EAAMN,KAAKD,GACJI,CAAQ,GACd,YACCK,GAA4BpC,EAAAA,EAAAA,KAAO,CAACqC,EAAM/B,EAAGC,EAAG+B,KAClD,MAAMC,EAAeF,EAAKjC,OAAO,SACjCmC,EAAalC,KAAK,IAAKC,GACvBiC,EAAalC,KAAK,IAAKE,GACvB,MAAMiC,GAAgBC,EAAAA,EAAAA,GAAYH,GAClCC,EAAalC,KAAK,aAAcmC,EAAc,GAC7C,aACCE,GAAoC1C,EAAAA,EAAAA,KAAO,CAACC,EAASK,EAAGC,EAAG+B,KAC7D,MAAMC,EAAetC,EAAQG,OAAO,OACpCmC,EAAalC,KAAK,IAAKC,GACvBiC,EAAalC,KAAK,IAAKE,GACvB,MAAMiC,GAAgBC,EAAAA,EAAAA,GAAYH,GAClCC,EAAalC,KAAK,aAAc,IAAImC,IAAgB,GACnD,qBACCG,GAA8B3C,EAAAA,EAAAA,KAAO,KAClB,CACnBM,EAAG,EACHC,EAAG,EACHG,MAAO,IACPC,OAAQ,IACRH,KAAM,UACNC,OAAQ,OACRwB,OAAQ,QACRpB,GAAI,EACJC,GAAI,KAGL,eACC8B,GAA6B5C,EAAAA,EAAAA,KAAO,KACnB,CACjBM,EAAG,EACHC,EAAG,EACHG,MAAO,IACPC,OAAQ,IACR,cAAe,QACfqB,MAAO,OACPG,WAAY,EACZtB,GAAI,EACJC,GAAI,EACJoB,OAAO,KAGR,a,gDCnGCW,EAAkB,MAIpBC,WAAAA,CAAYC,GACVC,KAAKD,KAAOA,EACZC,KAAKC,QAAUD,KAAKD,MACtB,CAAC,eAEC/C,EAAAA,EAAAA,IAAOgD,KAAM,mBAFd,GAIDE,KAAAA,GACEF,KAAKC,QAAUD,KAAKD,MACtB,E,yGCqBEI,EAAS,WACX,IAAIC,GAAoBpD,EAAAA,EAAAA,KAAO,SAASqD,EAAGC,EAAGC,EAAIC,GAChD,IAAKD,EAAKA,GAAM,CAAC,EAAGC,EAAIH,EAAEI,OAAQD,IAAKD,EAAGF,EAAEG,IAAMF,GAClD,OAAOC,CACT,GAAG,KAAMG,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,EAAG,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,KAC5rCC,EAAU,CACZC,OAAuBpG,EAAAA,EAAAA,KAAO,WAC9B,GAAG,SACHqG,GAAI,CAAC,EACLC,SAAU,CAAE,MAAS,EAAG,MAAS,EAAG,MAAS,EAAG,QAAW,EAAG,GAAM,EAAG,SAAY,EAAG,KAAQ,EAAG,UAAa,EAAG,YAAe,GAAI,SAAY,GAAI,sBAAyB,GAAI,OAAU,GAAI,IAAO,GAAI,WAAc,GAAI,IAAO,GAAI,OAAU,GAAI,WAAc,GAAI,IAAO,GAAI,IAAO,GAAI,SAAY,GAAI,MAAS,GAAI,WAAc,GAAI,eAAkB,GAAI,gBAAmB,GAAI,eAAkB,GAAI,qBAAwB,GAAI,kBAAqB,GAAI,MAAS,GAAI,aAAgB,GAAI,UAAa,GAAI,gBAAmB,GAAI,UAAa,GAAI,gBAAmB,GAAI,0BAA6B,GAAI,KAAQ,GAAI,KAAQ,GAAI,IAAO,GAAI,IAAO,GAAI,cAAiB,GAAI,IAAO,GAAI,aAAgB,GAAI,SAAY,GAAI,SAAY,GAAI,gBAAmB,GAAI,MAAS,GAAI,OAAU,GAAI,IAAO,GAAI,KAAQ,GAAI,YAAe,GAAI,GAAM,GAAI,kBAAqB,GAAI,QAAW,GAAI,KAAQ,GAAI,UAAa,GAAI,MAAS,GAAI,KAAQ,GAAI,WAAc,GAAI,MAAS,GAAI,KAAQ,GAAI,WAAc,GAAI,QAAW,GAAI,UAAa,GAAI,IAAK,GAAI,QAAW,GAAI,SAAY,GAAI,WAAc,GAAI,IAAK,GAAI,IAAK,GAAI,MAAS,GAAI,iBAAoB,GAAI,kBAAqB,GAAI,YAAe,GAAI,0BAA6B,GAAI,aAAgB,GAAI,2BAA8B,GAAI,YAAe,GAAI,aAAgB,GAAI,YAAe,GAAI,aAAgB,GAAI,IAAO,GAAI,QAAW,EAAG,KAAQ,GAC51CC,WAAY,CAAE,EAAG,QAAS,EAAG,QAAS,EAAG,UAAW,EAAG,KAAM,GAAI,SAAU,GAAI,MAAO,GAAI,aAAc,GAAI,MAAO,GAAI,aAAc,GAAI,MAAO,GAAI,MAAO,GAAI,WAAY,GAAI,aAAc,GAAI,QAAS,GAAI,eAAgB,GAAI,YAAa,GAAI,kBAAmB,GAAI,YAAa,GAAI,kBAAmB,GAAI,4BAA6B,GAAI,OAAQ,GAAI,OAAQ,GAAI,MAAO,GAAI,MAAO,GAAI,MAAO,GAAI,WAAY,GAAI,WAAY,GAAI,QAAS,GAAI,SAAU,GAAI,MAAO,GAAI,OAAQ,GAAI,cAAe,GAAI,KAAM,GAAI,oBAAqB,GAAI,UAAW,GAAI,OAAQ,GAAI,OAAQ,GAAI,QAAS,GAAI,OAAQ,GAAI,aAAc,GAAI,UAAW,GAAI,IAAK,GAAI,UAAW,GAAI,WAAY,GAAI,IAAK,GAAI,IAAK,GAAI,QAAS,GAAI,mBAAoB,GAAI,oBAAqB,GAAI,cAAe,GAAI,4BAA6B,GAAI,eAAgB,GAAI,6BAA8B,GAAI,cAAe,GAAI,eAAgB,GAAI,cAAe,GAAI,eAAgB,GAAI,OACr6BC,aAAc,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,IACjrBC,eAA+BzG,EAAAA,EAAAA,KAAO,SAAmB0G,EAAQC,EAAQC,EAAUP,EAAIQ,EAASC,EAAIC,GAClG,IAAIC,EAAKF,EAAGrD,OAAS,EACrB,OAAQoD,GACN,KAAK,EAEH,OADAR,EAAGY,MAAMH,EAAGE,IACLF,EAAGE,GAEZ,KAAK,EACL,KAAK,EAcL,KAAK,EACL,KAAK,GACHhE,KAAKkE,EAAI,GACT,MAdF,KAAK,EACL,KAAK,GACHJ,EAAGE,EAAK,GAAGG,KAAKL,EAAGE,IACnBhE,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,EACL,KAAK,EACL,KAAK,GACL,KAAK,GAsJL,KAAK,GACHhE,KAAKkE,EAAIJ,EAAGE,GACZ,MAjJF,KAAK,GACHF,EAAGE,GAAII,KAAO,oBACdpE,KAAKkE,EAAIJ,EAAGE,GACZ,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGK,QAAQ,CAAED,KAAM,WAAYE,QAASjB,EAAGkB,aAAaT,EAAGE,EAAK,MACxEF,EAAGE,EAAK,GAAGG,KAAK,CAAEC,KAAM,SAAUI,QAASV,EAAGE,EAAK,KACnDhE,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHhE,KAAKkE,EAAI,CAAEE,KAAM,gBAAiBK,cAAeC,OAAOZ,EAAGE,EAAK,IAAKW,kBAAmBD,OAAOZ,EAAGE,EAAK,IAAKY,iBAAiB,EAAMC,WAAYxB,EAAGyB,SAASC,YAC3J,MACF,KAAK,GACH/E,KAAKkE,EAAI,CAAEE,KAAM,gBAAiBK,cAAeC,OAAOZ,EAAGE,EAAK,IAAKW,kBAAmB,EAAGC,iBAAiB,EAAMC,WAAYxB,EAAGyB,SAASC,YAC1I,MACF,KAAK,GACH/E,KAAKkE,EAAI,CAAEE,KAAM,gBAAiBQ,iBAAiB,EAAOC,WAAYxB,EAAGyB,SAASC,YAClF,MACF,KAAK,GACH/E,KAAKkE,EAAI,CAAEE,KAAM,gBAAiBQ,iBAAiB,EAAMC,WAAYxB,EAAGyB,SAASC,YACjF,MACF,KAAK,GACH/E,KAAKkE,EAAI,CAAEE,KAAM,cAAeS,WAAYxB,EAAGyB,SAASE,aAAcC,MAAOnB,EAAGE,EAAK,GAAGiB,OACxF,MACF,KAAK,GACHjF,KAAKkE,EAAI,CAAEE,KAAM,YAAaS,WAAYxB,EAAGyB,SAASI,WAAYD,MAAOnB,EAAGE,EAAK,GAAGiB,OACpF,MACF,KAAK,GACH5B,EAAG8B,gBAAgBrB,EAAGE,GAAIoB,UAAU,IACpCpF,KAAKkE,EAAIJ,EAAGE,GAAIoB,UAAU,GAC1B,MACF,KAAK,GACH/B,EAAG8B,gBAAgBrB,EAAGE,GAAIoB,UAAU,IACpCpF,KAAKkE,EAAIJ,EAAGE,GAAIoB,UAAU,GAC1B,MACF,KAAK,GACHpF,KAAKkE,EAAIJ,EAAGE,GAAIqB,OAChBhC,EAAGiC,YAAYtF,KAAKkE,GACpB,MACF,KAAK,GACL,KAAK,GACHlE,KAAKkE,EAAIJ,EAAGE,GAAIqB,OAChBhC,EAAGkC,kBAAkBvF,KAAKkE,GAC1B,MACF,KAAK,GACHJ,EAAGE,EAAK,GAAGK,QAAQ,CAAED,KAAM,YAAaoB,SAAUnC,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAASY,aACvG5B,EAAGE,EAAK,GAAGG,KAAK,CAAEC,KAAM,UAAWoB,SAAU1B,EAAGE,EAAK,GAAIa,WAAYxB,EAAGyB,SAASa,WACjF3F,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGK,QAAQ,CAAED,KAAM,YAAawB,MAAOvC,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAASe,aACpG/B,EAAGE,EAAK,GAAGG,KAAK,CAAEC,KAAM,UAAWwB,MAAOvC,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAASgB,WAC/F9F,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGK,QAAQ,CAAED,KAAM,WAAY2B,QAAS1C,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAASkB,YACrGlC,EAAGE,EAAK,GAAGG,KAAK,CAAEC,KAAM,SAAU2B,QAAS1C,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAASmB,UAChGjG,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGK,QAAQ,CAAED,KAAM,WAAY8B,QAAS7C,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAASqB,YACrGrC,EAAGE,EAAK,GAAGG,KAAK,CAAEC,KAAM,SAAUS,WAAYxB,EAAGyB,SAASsB,UAC1DpG,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGK,QAAQ,CAAED,KAAM,WAAYiC,QAAShD,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAASwB,YACrGxC,EAAGE,EAAK,GAAGG,KAAK,CAAEC,KAAM,SAAUS,WAAYxB,EAAGyB,SAASyB,UAC1DvG,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGK,QAAQ,CAAED,KAAM,WAAYiC,QAAShD,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAAS0B,iBACrG1C,EAAGE,EAAK,GAAGG,KAAK,CAAEC,KAAM,SAAUS,WAAYxB,EAAGyB,SAASyB,UAC1DvG,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGK,QAAQ,CAAED,KAAM,gBAAiBqC,aAAcpD,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAAS4B,iBAC/G5C,EAAGE,EAAK,GAAGG,KAAK,CAAEC,KAAM,cAAeS,WAAYxB,EAAGyB,SAAS6B,eAC/D3G,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGK,QAAQ,CAAED,KAAM,aAAcwC,UAAWvD,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAAS+B,cACzG/C,EAAGE,EAAK,GAAGG,KAAK,CAAEC,KAAM,WAAY2B,QAAS1C,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAASgC,YAClG9G,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHhE,KAAKkE,EAAIJ,EAAGE,EAAK,GAAG+C,OAAO,CAAC,CAAE3C,KAAM,SAAU4C,WAAY3D,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAASmC,iBAAmBnD,EAAGE,KACrI,MACF,KAAK,GACHhE,KAAKkE,EAAIJ,EAAGE,EAAK,GAAG+C,OAAO,CAAC,CAAE3C,KAAM,MAAOiC,QAAShD,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAASoC,SAAWpD,EAAGE,KACvH,MACF,KAAK,GACHhE,KAAKkE,EAAIJ,EAAGE,EAAK,GAAG+C,OAAO,CAAC,CAAE3C,KAAM,OAAQ8B,QAAS7C,EAAGoC,aAAa3B,EAAGE,EAAK,IAAKa,WAAYxB,EAAGyB,SAASqC,UAAYrD,EAAGE,KACzH,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGoD,KAAO,cAClBtD,EAAGE,EAAK,GAAGI,KAAO,iBAClBN,EAAGE,EAAK,GAAGqD,YAAchE,EAAGoC,aAAa3B,EAAGE,EAAK,IACjDhE,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGoD,KAAO,cAClBtD,EAAGE,EAAK,GAAGI,KAAO,iBAClBpE,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGoD,KAAO,QAClBtD,EAAGE,EAAK,GAAGI,KAAO,iBAClBN,EAAGE,EAAK,GAAGqD,YAAchE,EAAGoC,aAAa3B,EAAGE,EAAK,IACjDhE,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGoD,KAAO,QAClBtD,EAAGE,EAAK,GAAGI,KAAO,iBAClBpE,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHF,EAAGE,EAAK,GAAGI,KAAO,qBAClBpE,KAAKkE,EAAIJ,EAAGE,EAAK,GACjB,MACF,KAAK,GACHhE,KAAKkE,EAAI,CAACJ,EAAGE,EAAK,GAAI,CAAEI,KAAM,UAAWkD,UAAWxD,EAAGE,EAAK,GAAIiB,MAAOnB,EAAGE,EAAK,GAAGiB,MAAOrG,KAAMkF,EAAGE,KAClG,MACF,KAAK,GACHF,EAAGE,EAAK,GAAK,GAAG+C,OAAOjD,EAAGE,EAAK,GAAIF,EAAGE,EAAK,IAAIuD,MAAM,EAAG,GACxDzD,EAAGE,EAAK,GAAG,GAAKF,EAAGE,EAAK,GAAG,GAAGiB,MAC9BnB,EAAGE,EAAK,GAAG,GAAKF,EAAGE,EAAK,GAAG,GAAGiB,MAC9BjF,KAAKkE,EAAI,CAACJ,EAAGE,EAAK,GAAI,CAAEI,KAAM,UAAWkD,UAAWjE,EAAGmE,UAAUC,KAAMxC,MAAOnB,EAAGE,EAAK,GAAGuD,MAAM,EAAG,GAAI3I,KAAMkF,EAAGE,KAC/G,MACF,KAAK,GACHhE,KAAKkE,EAAI,CAACJ,EAAGE,EAAK,GAAI,CAAEI,KAAM,WAAYa,MAAOnB,EAAGE,EAAK,GAAGiB,MAAOrG,KAAMkF,EAAGE,KAC5E,MACF,KAAK,GACHhE,KAAKkE,EAAI,CAACJ,EAAGE,EAAK,GAAI,CAAEI,KAAM,WAAYa,MAAOnB,EAAGE,EAAK,GAAGiB,MAAOrG,KAAMkF,EAAGE,KAC5E,MACF,KAAK,GACHhE,KAAKkE,EAAI,CAACJ,EAAGE,EAAK,GAAI,CAAEI,KAAM,gBAAiBa,MAAOnB,EAAGE,EAAK,GAAGiB,MAAOrG,KAAMkF,EAAGE,KACjF,MACF,KAAK,GACHhE,KAAKkE,EAAI,CAACJ,EAAGE,EAAK,GAAI,CAAEI,KAAM,aAAca,MAAOnB,EAAGE,EAAK,GAAGiB,MAAOrG,KAAMkF,EAAGE,KAC9E,MACF,KAAK,GACHhE,KAAKkE,EAAI,CAACJ,EAAGE,EAAK,GAAIF,EAAGE,IACzB,MAIF,KAAK,GACHhE,KAAKkE,EAAIb,EAAGmE,UAAUE,OACtB,MACF,KAAK,GACH1H,KAAKkE,EAAIb,EAAGmE,UAAUG,QACtB,MACF,KAAK,GACH3H,KAAKkE,EAAI,CACPJ,EAAGE,EAAK,GACRF,EAAGE,EAAK,GACR,CAAEI,KAAM,aAAcwD,KAAM9D,EAAGE,EAAK,GAAGiB,MAAO4C,GAAI/D,EAAGE,EAAK,GAAGiB,MAAOJ,WAAYf,EAAGE,EAAK,GAAI8D,IAAKhE,EAAGE,GAAK+D,UAAU,GACnH,CAAE3D,KAAM,cAAeS,WAAYxB,EAAGyB,SAASE,aAAcC,MAAOnB,EAAGE,EAAK,GAAGiB,QAEjF,MACF,KAAK,GACHjF,KAAKkE,EAAI,CACPJ,EAAGE,EAAK,GACRF,EAAGE,EAAK,GACR,CAAEI,KAAM,aAAcwD,KAAM9D,EAAGE,EAAK,GAAGiB,MAAO4C,GAAI/D,EAAGE,EAAK,GAAGiB,MAAOJ,WAAYf,EAAGE,EAAK,GAAI8D,IAAKhE,EAAGE,IACpG,CAAEI,KAAM,YAAaS,WAAYxB,EAAGyB,SAASI,WAAYD,MAAOnB,EAAGE,EAAK,GAAGiB,QAE7E,MACF,KAAK,GACHjF,KAAKkE,EAAI,CAACJ,EAAGE,EAAK,GAAIF,EAAGE,EAAK,GAAI,CAAEI,KAAM,aAAcwD,KAAM9D,EAAGE,EAAK,GAAGiB,MAAO4C,GAAI/D,EAAGE,EAAK,GAAGiB,MAAOJ,WAAYf,EAAGE,EAAK,GAAI8D,IAAKhE,EAAGE,KACtI,MACF,KAAK,GACHhE,KAAKkE,EAAI,CAAEE,KAAM,iBAAkBa,MAAOnB,EAAGE,IAC7C,MACF,KAAK,GACHhE,KAAKkE,EAAIb,EAAGyB,SAASkD,WACrB,MACF,KAAK,GACHhI,KAAKkE,EAAIb,EAAGyB,SAASmD,YACrB,MACF,KAAK,GACHjI,KAAKkE,EAAIb,EAAGyB,SAASoD,MACrB,MACF,KAAK,GACHlI,KAAKkE,EAAIb,EAAGyB,SAASqD,oBACrB,MACF,KAAK,GACHnI,KAAKkE,EAAIb,EAAGyB,SAASsD,OACrB,MACF,KAAK,GACHpI,KAAKkE,EAAIb,EAAGyB,SAASuD,qBACrB,MACF,KAAK,GACHrI,KAAKkE,EAAIb,EAAGyB,SAASwD,YACrB,MACF,KAAK,GACHtI,KAAKkE,EAAIb,EAAGyB,SAASyD,aACrB,MACF,KAAK,GACHvI,KAAKkE,EAAIb,EAAGyB,SAAS0D,YACrB,MACF,KAAK,GACHxI,KAAKkE,EAAIb,EAAGyB,SAAS2D,aACrB,MACF,KAAK,GACHzI,KAAKkE,EAAIb,EAAGoC,aAAa3B,EAAGE,GAAIqB,OAAOD,UAAU,IAGvD,GAAG,aACHsD,MAAO,CAAC,CAAE,EAAG,EAAG,EAAGhI,EAAK,EAAGC,EAAK,EAAGC,GAAO,CAAE,EAAG,CAAC,IAAM,CAAE,EAAG,EAAG,EAAGF,EAAK,EAAGC,EAAK,EAAGC,GAAO,CAAE,EAAG,EAAG,EAAGF,EAAK,EAAGC,EAAK,EAAGC,GAAOR,EAAE,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKS,EAAK,CAAE,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,GAAI,EAAGC,EAAK,EAAGC,EAAK,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAOtC,EAAEuC,EAAK,CAAC,EAAG,IAAK,CAAE,EAAG,GAAI,GAAI,GAAI,GAAI3B,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAOtC,EAAEuC,EAAK,CAAC,EAAG,IAAKvC,EAAEuC,EAAK,CAAC,EAAG,IAAKvC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,GAAI,GAAIT,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,GAAI,GAAIM,GAAO,CAAE,GAAI,GAAI,GAAIA,GAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAOtC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAOvC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,GAAI,GAAID,GAAO,CAAE,GAAI,GAAI,GAAIA,GAAO,CAAE,GAAI,GAAI,GAAIA,GAAO,CAAE,GAAI,GAAI,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,GAAI,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,GAAI,GAAIA,GAAO,CAAE,GAAI,GAAI,GAAIA,GAAO,CAAE,GAAI,GAAI,GAAIA,GAAO,CAAE,GAAI,GAAI,GAAIA,GAAOtC,EAAE,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,CAAC,EAAG,KAAMA,EAAEuC,EAAK,CAAC,EAAG,IAAKvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEwC,EAAK,CAAC,EAAG,GAAI,CAAE,GAAI,KAAOxC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,EAAG,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAOvC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAOvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEyC,EAAKhC,EAAK,CAAE,EAAG,KAAOT,EAAEyC,EAAKhC,EAAK,CAAE,EAAG,MAAQT,EAAEyC,EAAKhC,EAAK,CAAE,EAAG,MAAQT,EAAE0C,EAAKjC,EAAK,CAAE,GAAI,IAAK,EAAG,MAAQT,EAAE2C,EAAKlC,EAAK,CAAE,GAAI,IAAK,EAAG,MAAQT,EAAE2C,EAAKlC,EAAK,CAAE,EAAG,IAAK,GAAI,MAAQT,EAAE4C,EAAKnC,EAAK,CAAE,GAAI,IAAK,EAAG,MAAQT,EAAEyC,EAAKhC,EAAK,CAAE,EAAG,MAAQ,CAAE,EAAG,CAAC,EAAG,KAAM,GAAI,CAAC,EAAG,MAAQ,CAAE,EAAG,CAAC,EAAG,KAAM,GAAI,CAAC,EAAG,MAAQ,CAAE,EAAG,CAAC,EAAG,MAAQ,CAAE,GAAI,IAAK,GAAI,CAAC,EAAG,KAAM,GAAI,CAAC,EAAG,KAAM,GAAI6B,GAAOtC,EAAE6C,EAAK,CAAC,EAAG,KAAM7C,EAAE6C,EAAK,CAAC,EAAG,KAAM7C,EAAE6C,EAAK,CAAC,EAAG,KAAM7C,EAAE6C,EAAK,CAAC,EAAG,KAAM7C,EAAE6C,EAAK,CAAC,EAAG,KAAM7C,EAAE6C,EAAK,CAAC,EAAG,KAAM7C,EAAE6C,EAAK,CAAC,EAAG,KAAM7C,EAAE6C,EAAK,CAAC,EAAG,KAAM7C,EAAE6C,EAAK,CAAC,EAAG,KAAM7C,EAAE6C,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,IAAK,GAAIP,GAAO,CAAE,GAAI,IAAK,GAAI,IAAK,GAAIA,GAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,IAAK,GAAIQ,GAAO,CAAE,GAAI,IAAK,GAAIA,GAAO,CAAE,GAAI,IAAK,GAAIA,GAAO,CAAE,GAAI,IAAK,GAAIA,GAAO,CAAE,EAAG,CAAC,EAAG,KAAM,EAAG,CAAC,EAAG,KAAM,GAAI,IAAK,GAAI,IAAK,GAAI,CAAC,EAAG,KAAM,GAAIhB,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAG,CAAC,EAAG,MAAQhC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,EAAG7B,EAAK,EAAGC,EAAK,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,KAAM,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAG5B,EAAK,EAAGC,EAAK,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,KAAM,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAG5B,EAAK,EAAGC,EAAK,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,KAAM,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,EAAG5B,EAAK,EAAGC,EAAK,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,IAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,KAAM,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,EAAG5B,EAAK,EAAGC,EAAK,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,IAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,KAAM,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,EAAG5B,EAAK,EAAGC,EAAK,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,IAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,KAAM,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,EAAG5B,EAAK,EAAGC,EAAK,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,CAAC,EAAG,KAAM,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,CAAC,EAAG,MAAQtC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,MAAQvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,IAAK,GAAID,GAAO,CAAE,GAAI,IAAK,GAAIA,GAAO,CAAE,GAAI,IAAK,GAAIQ,GAAO,CAAE,GAAI,IAAK,GAAIA,GAAO,CAAE,GAAI,IAAK,GAAIA,GAAO,CAAE,GAAI,CAAC,EAAG,KAAM,GAAI,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO9C,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEwC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,IAAK,GAAIV,EAAK,GAAIC,EAAK,GAAIC,GAAOhC,EAAEwC,EAAK,CAAC,EAAG,KAAMxC,EAAEwC,EAAK,CAAC,EAAG,KAAMxC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,MAAQvC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,MAAQvC,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,MAAQvC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,EAAG,CAAC,EAAG,MAAQ,CAAE,EAAG,CAAC,EAAG,MAAQ,CAAE,GAAI,IAAK,GAAIO,GAAO,CAAE,GAAI,IAAK,GAAIA,GAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,GAAI,IAAK,GAAIR,GAAOtC,EAAEwC,EAAK,CAAC,EAAG,KAAMxC,EAAE0C,EAAKjC,EAAK,CAAE,EAAG,IAAK,GAAI,MAAQT,EAAE2C,EAAKlC,EAAK,CAAE,EAAG,IAAK,GAAI,MAAQT,EAAE4C,EAAKnC,EAAK,CAAE,EAAG,IAAK,GAAI,MAAQT,EAAEuC,EAAK,CAAC,EAAG,KAAMvC,EAAEuC,EAAK,CAAC,EAAG,KAAM,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,MAC7gNgG,eAAgB,CAAE,EAAG,CAAC,EAAG,GAAI,EAAG,CAAC,EAAG,GAAI,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,KACjQC,YAA4B5L,EAAAA,EAAAA,KAAO,SAAoB6L,EAAKC,GAC1D,IAAIA,EAAKC,YAEF,CACL,IAAIC,EAAQ,IAAIC,MAAMJ,GAEtB,MADAG,EAAMF,KAAOA,EACPE,CACR,CALEhJ,KAAKoD,MAAMyF,EAMf,GAAG,cACHK,OAAuBlM,EAAAA,EAAAA,KAAO,SAAemM,GAC3C,IAAIC,EAAOpJ,KAAMqJ,EAAQ,CAAC,GAAIC,EAAS,GAAIC,EAAS,CAAC,MAAOC,EAAS,GAAId,EAAQ1I,KAAK0I,MAAOhF,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAG8F,EAAa,EAC7IC,EAAOF,EAAOjC,MAAMoC,KAAKC,UAAW,GACpCC,EAASC,OAAOC,OAAO/J,KAAKgK,OAC5BC,EAAc,CAAE5G,GAAI,CAAC,GACzB,IAAK,IAAIhD,KAAKL,KAAKqD,GACbyG,OAAOI,UAAUC,eAAeR,KAAK3J,KAAKqD,GAAIhD,KAChD4J,EAAY5G,GAAGhD,GAAKL,KAAKqD,GAAGhD,IAGhCwJ,EAAOO,SAASjB,EAAOc,EAAY5G,IACnC4G,EAAY5G,GAAG2G,MAAQH,EACvBI,EAAY5G,GAAGlD,OAASH,KACI,oBAAjB6J,EAAOQ,SAChBR,EAAOQ,OAAS,CAAC,GAEnB,IAAIC,EAAQT,EAAOQ,OACnBb,EAAOrF,KAAKmG,GACZ,IAAIC,EAASV,EAAOW,SAAWX,EAAOW,QAAQD,OAY9C,SAASE,IACP,IAAIC,EASJ,MAPqB,kBADrBA,EAAQpB,EAAOqB,OAASd,EAAOY,OA/BqI,KAiC9JC,aAAiBE,QAEnBF,GADApB,EAASoB,GACMC,OAEjBD,EAAQtB,EAAK9F,SAASoH,IAAUA,GAE3BA,CACT,CAtByC,oBAA9BT,EAAY5G,GAAGuF,WACxB5I,KAAK4I,WAAaqB,EAAY5G,GAAGuF,WAEjC5I,KAAK4I,WAAakB,OAAOe,eAAe7K,MAAM4I,YAOhD5L,EAAAA,EAAAA,KALA,SAAkB8N,GAChBzB,EAAM5I,OAAS4I,EAAM5I,OAAS,EAAIqK,EAClCvB,EAAO9I,OAAS8I,EAAO9I,OAASqK,EAChCtB,EAAO/I,OAAS+I,EAAO/I,OAASqK,CAClC,GACiB,aAajB9N,EAAAA,EAAAA,IAAOyN,EAAK,OAEZ,IADA,IAAIM,EAAQC,EAAgBC,EAAOC,EAAWC,EAAeC,EAAGC,EAAKC,EAAUC,EAA9BC,EAAQ,CAAC,IAC7C,CAUX,GATAP,EAAQ5B,EAAMA,EAAM5I,OAAS,GACzBT,KAAK2I,eAAesC,GACtBC,EAASlL,KAAK2I,eAAesC,IAEd,OAAXF,GAAoC,oBAAVA,IAC5BA,EAASN,KAEXS,EAASxC,EAAMuC,IAAUvC,EAAMuC,GAAOF,IAElB,qBAAXG,IAA2BA,EAAOzK,SAAWyK,EAAO,GAAI,CACjE,IAAIO,EAAS,GAEb,IAAKL,KADLG,EAAW,GACD7C,EAAMuC,GACVjL,KAAKuD,WAAW6H,IAAMA,EAzD6H,GA0DrJG,EAASpH,KAAK,IAAMnE,KAAKuD,WAAW6H,GAAK,KAI3CK,EADE5B,EAAO6B,aACA,wBAA0B9H,EAAW,GAAK,MAAQiG,EAAO6B,eAAiB,eAAiBH,EAASI,KAAK,MAAQ,WAAa3L,KAAKuD,WAAWwH,IAAWA,GAAU,IAEnK,wBAA0BnH,EAAW,GAAK,iBAhE6G,GAgE1FmH,EAAgB,eAAiB,KAAO/K,KAAKuD,WAAWwH,IAAWA,GAAU,KAErJ/K,KAAK4I,WAAW6C,EAAQ,CACtB7M,KAAMiL,EAAO+B,MACblB,MAAO1K,KAAKuD,WAAWwH,IAAWA,EAClCc,KAAMhC,EAAOjG,SACbkI,IAAKxB,EACLiB,YAEJ,CACA,GAAIL,EAAO,aAAcN,OAASM,EAAOzK,OAAS,EAChD,MAAM,IAAIwI,MAAM,oDAAsDgC,EAAQ,YAAcF,GAE9F,OAAQG,EAAO,IACb,KAAK,EACH7B,EAAMlF,KAAK4G,GACXxB,EAAOpF,KAAK0F,EAAOnG,QACnB8F,EAAOrF,KAAK0F,EAAOQ,QACnBhB,EAAMlF,KAAK+G,EAAO,IAClBH,EAAS,KACJC,GASHD,EAASC,EACTA,EAAiB,OATjBrH,EAASkG,EAAOlG,OAChBD,EAASmG,EAAOnG,OAChBE,EAAWiG,EAAOjG,SAClB0G,EAAQT,EAAOQ,OACXZ,EAAa,GACfA,KAMJ,MACF,KAAK,EAwBH,GAvBA4B,EAAMrL,KAAKwD,aAAa0H,EAAO,IAAI,GACnCM,EAAMtH,EAAIqF,EAAOA,EAAO9I,OAAS4K,GACjCG,EAAMzH,GAAK,CACTgI,WAAYvC,EAAOA,EAAO/I,QAAU4K,GAAO,IAAIU,WAC/CC,UAAWxC,EAAOA,EAAO/I,OAAS,GAAGuL,UACrCC,aAAczC,EAAOA,EAAO/I,QAAU4K,GAAO,IAAIY,aACjDC,YAAa1C,EAAOA,EAAO/I,OAAS,GAAGyL,aAErC3B,IACFiB,EAAMzH,GAAGoI,MAAQ,CACf3C,EAAOA,EAAO/I,QAAU4K,GAAO,IAAIc,MAAM,GACzC3C,EAAOA,EAAO/I,OAAS,GAAG0L,MAAM,KAYnB,qBATjBhB,EAAInL,KAAKyD,cAAcQ,MAAMuH,EAAO,CAClC9H,EACAC,EACAC,EACAqG,EAAY5G,GACZ6H,EAAO,GACP3B,EACAC,GACAzC,OAAO2C,KAEP,OAAOyB,EAELE,IACFhC,EAAQA,EAAM9B,MAAM,GAAI,EAAI8D,EAAM,GAClC9B,EAASA,EAAOhC,MAAM,GAAI,EAAI8D,GAC9B7B,EAASA,EAAOjC,MAAM,GAAI,EAAI8D,IAEhChC,EAAMlF,KAAKnE,KAAKwD,aAAa0H,EAAO,IAAI,IACxC3B,EAAOpF,KAAKqH,EAAMtH,GAClBsF,EAAOrF,KAAKqH,EAAMzH,IAClBuH,EAAW5C,EAAMW,EAAMA,EAAM5I,OAAS,IAAI4I,EAAMA,EAAM5I,OAAS,IAC/D4I,EAAMlF,KAAKmH,GACX,MACF,KAAK,EACH,OAAO,EAEb,CACA,OAAO,CACT,GAAG,UAEDtB,EAAwB,WAqgB1B,MApgBa,CACXoC,IAAK,EACLxD,YAA4B5L,EAAAA,EAAAA,KAAO,SAAoB6L,EAAKC,GAC1D,IAAI9I,KAAKqD,GAAGlD,OAGV,MAAM,IAAI8I,MAAMJ,GAFhB7I,KAAKqD,GAAGlD,OAAOyI,WAAWC,EAAKC,EAInC,GAAG,cAEHsB,UAA0BpN,EAAAA,EAAAA,KAAO,SAASmM,EAAO9F,GAiB/C,OAhBArD,KAAKqD,GAAKA,GAAMrD,KAAKqD,IAAM,CAAC,EAC5BrD,KAAKqM,OAASlD,EACdnJ,KAAKsM,MAAQtM,KAAKuM,WAAavM,KAAKwM,MAAO,EAC3CxM,KAAK4D,SAAW5D,KAAK2D,OAAS,EAC9B3D,KAAK0D,OAAS1D,KAAKyM,QAAUzM,KAAK4L,MAAQ,GAC1C5L,KAAK0M,eAAiB,CAAC,WACvB1M,KAAKqK,OAAS,CACZ0B,WAAY,EACZE,aAAc,EACdD,UAAW,EACXE,YAAa,GAEXlM,KAAKwK,QAAQD,SACfvK,KAAKqK,OAAO8B,MAAQ,CAAC,EAAG,IAE1BnM,KAAK2M,OAAS,EACP3M,IACT,GAAG,YAEHmJ,OAAuBnM,EAAAA,EAAAA,KAAO,WAC5B,IAAI4P,EAAK5M,KAAKqM,OAAO,GAiBrB,OAhBArM,KAAK0D,QAAUkJ,EACf5M,KAAK2D,SACL3D,KAAK2M,SACL3M,KAAK4L,OAASgB,EACd5M,KAAKyM,SAAWG,EACJA,EAAGhB,MAAM,oBAEnB5L,KAAK4D,WACL5D,KAAKqK,OAAO2B,aAEZhM,KAAKqK,OAAO6B,cAEVlM,KAAKwK,QAAQD,QACfvK,KAAKqK,OAAO8B,MAAM,KAEpBnM,KAAKqM,OAASrM,KAAKqM,OAAO9E,MAAM,GACzBqF,CACT,GAAG,SAEHC,OAAuB7P,EAAAA,EAAAA,KAAO,SAAS4P,GACrC,IAAIvB,EAAMuB,EAAGnM,OACTqM,EAAQF,EAAGG,MAAM,iBACrB/M,KAAKqM,OAASO,EAAK5M,KAAKqM,OACxBrM,KAAK0D,OAAS1D,KAAK0D,OAAOsJ,OAAO,EAAGhN,KAAK0D,OAAOjD,OAAS4K,GACzDrL,KAAK2M,QAAUtB,EACf,IAAI4B,EAAWjN,KAAK4L,MAAMmB,MAAM,iBAChC/M,KAAK4L,MAAQ5L,KAAK4L,MAAMoB,OAAO,EAAGhN,KAAK4L,MAAMnL,OAAS,GACtDT,KAAKyM,QAAUzM,KAAKyM,QAAQO,OAAO,EAAGhN,KAAKyM,QAAQhM,OAAS,GACxDqM,EAAMrM,OAAS,IACjBT,KAAK4D,UAAYkJ,EAAMrM,OAAS,GAElC,IAAI0K,EAAInL,KAAKqK,OAAO8B,MAWpB,OAVAnM,KAAKqK,OAAS,CACZ0B,WAAY/L,KAAKqK,OAAO0B,WACxBC,UAAWhM,KAAK4D,SAAW,EAC3BqI,aAAcjM,KAAKqK,OAAO4B,aAC1BC,YAAaY,GAASA,EAAMrM,SAAWwM,EAASxM,OAAST,KAAKqK,OAAO4B,aAAe,GAAKgB,EAASA,EAASxM,OAASqM,EAAMrM,QAAQA,OAASqM,EAAM,GAAGrM,OAAST,KAAKqK,OAAO4B,aAAeZ,GAEtLrL,KAAKwK,QAAQD,SACfvK,KAAKqK,OAAO8B,MAAQ,CAAChB,EAAE,GAAIA,EAAE,GAAKnL,KAAK2D,OAAS0H,IAElDrL,KAAK2D,OAAS3D,KAAK0D,OAAOjD,OACnBT,IACT,GAAG,SAEHkN,MAAsBlQ,EAAAA,EAAAA,KAAO,WAE3B,OADAgD,KAAKsM,OAAQ,EACNtM,IACT,GAAG,QAEHmN,QAAwBnQ,EAAAA,EAAAA,KAAO,WAC7B,OAAIgD,KAAKwK,QAAQ4C,iBACfpN,KAAKuM,YAAa,EAQbvM,MANEA,KAAK4I,WAAW,0BAA4B5I,KAAK4D,SAAW,GAAK,mIAAqI5D,KAAK0L,eAAgB,CAChO9M,KAAM,GACN8L,MAAO,KACPmB,KAAM7L,KAAK4D,UAIjB,GAAG,UAEHyJ,MAAsBrQ,EAAAA,EAAAA,KAAO,SAAS8N,GACpC9K,KAAK6M,MAAM7M,KAAK4L,MAAMrE,MAAMuD,GAC9B,GAAG,QAEHwC,WAA2BtQ,EAAAA,EAAAA,KAAO,WAChC,IAAIuQ,EAAOvN,KAAKyM,QAAQO,OAAO,EAAGhN,KAAKyM,QAAQhM,OAAST,KAAK4L,MAAMnL,QACnE,OAAQ8M,EAAK9M,OAAS,GAAK,MAAQ,IAAM8M,EAAKP,QAAQ,IAAInO,QAAQ,MAAO,GAC3E,GAAG,aAEH2O,eAA+BxQ,EAAAA,EAAAA,KAAO,WACpC,IAAIyQ,EAAOzN,KAAK4L,MAIhB,OAHI6B,EAAKhN,OAAS,KAChBgN,GAAQzN,KAAKqM,OAAOW,OAAO,EAAG,GAAKS,EAAKhN,UAElCgN,EAAKT,OAAO,EAAG,KAAOS,EAAKhN,OAAS,GAAK,MAAQ,KAAK5B,QAAQ,MAAO,GAC/E,GAAG,iBAEH6M,cAA8B1O,EAAAA,EAAAA,KAAO,WACnC,IAAI0Q,EAAM1N,KAAKsN,YACXK,EAAI,IAAI/C,MAAM8C,EAAIjN,OAAS,GAAGkL,KAAK,KACvC,OAAO+B,EAAM1N,KAAKwN,gBAAkB,KAAOG,EAAI,GACjD,GAAG,gBAEHC,YAA4B5Q,EAAAA,EAAAA,KAAO,SAAS4O,EAAOiC,GACjD,IAAInD,EAAOoC,EAAOgB,EAmDlB,GAlDI9N,KAAKwK,QAAQ4C,kBACfU,EAAS,CACPlK,SAAU5D,KAAK4D,SACfyG,OAAQ,CACN0B,WAAY/L,KAAKqK,OAAO0B,WACxBC,UAAWhM,KAAKgM,UAChBC,aAAcjM,KAAKqK,OAAO4B,aAC1BC,YAAalM,KAAKqK,OAAO6B,aAE3BxI,OAAQ1D,KAAK0D,OACbkI,MAAO5L,KAAK4L,MACZmC,QAAS/N,KAAK+N,QACdtB,QAASzM,KAAKyM,QACd9I,OAAQ3D,KAAK2D,OACbgJ,OAAQ3M,KAAK2M,OACbL,MAAOtM,KAAKsM,MACZD,OAAQrM,KAAKqM,OACbhJ,GAAIrD,KAAKqD,GACTqJ,eAAgB1M,KAAK0M,eAAenF,MAAM,GAC1CiF,KAAMxM,KAAKwM,MAETxM,KAAKwK,QAAQD,SACfuD,EAAOzD,OAAO8B,MAAQnM,KAAKqK,OAAO8B,MAAM5E,MAAM,MAGlDuF,EAAQlB,EAAM,GAAGA,MAAM,sBAErB5L,KAAK4D,UAAYkJ,EAAMrM,QAEzBT,KAAKqK,OAAS,CACZ0B,WAAY/L,KAAKqK,OAAO2B,UACxBA,UAAWhM,KAAK4D,SAAW,EAC3BqI,aAAcjM,KAAKqK,OAAO6B,YAC1BA,YAAaY,EAAQA,EAAMA,EAAMrM,OAAS,GAAGA,OAASqM,EAAMA,EAAMrM,OAAS,GAAGmL,MAAM,UAAU,GAAGnL,OAAST,KAAKqK,OAAO6B,YAAcN,EAAM,GAAGnL,QAE/IT,KAAK0D,QAAUkI,EAAM,GACrB5L,KAAK4L,OAASA,EAAM,GACpB5L,KAAK+N,QAAUnC,EACf5L,KAAK2D,OAAS3D,KAAK0D,OAAOjD,OACtBT,KAAKwK,QAAQD,SACfvK,KAAKqK,OAAO8B,MAAQ,CAACnM,KAAK2M,OAAQ3M,KAAK2M,QAAU3M,KAAK2D,SAExD3D,KAAKsM,OAAQ,EACbtM,KAAKuM,YAAa,EAClBvM,KAAKqM,OAASrM,KAAKqM,OAAO9E,MAAMqE,EAAM,GAAGnL,QACzCT,KAAKyM,SAAWb,EAAM,GACtBlB,EAAQ1K,KAAKyD,cAAckG,KAAK3J,KAAMA,KAAKqD,GAAIrD,KAAM6N,EAAc7N,KAAK0M,eAAe1M,KAAK0M,eAAejM,OAAS,IAChHT,KAAKwM,MAAQxM,KAAKqM,SACpBrM,KAAKwM,MAAO,GAEV9B,EACF,OAAOA,EACF,GAAI1K,KAAKuM,WAAY,CAC1B,IAAK,IAAIlM,KAAKyN,EACZ9N,KAAKK,GAAKyN,EAAOzN,GAEnB,OAAO,CACT,CACA,OAAO,CACT,GAAG,cAEHoN,MAAsBzQ,EAAAA,EAAAA,KAAO,WAC3B,GAAIgD,KAAKwM,KACP,OAAOxM,KAAKoM,IAKd,IAAI1B,EAAOkB,EAAOoC,EAAWC,EAHxBjO,KAAKqM,SACRrM,KAAKwM,MAAO,GAGTxM,KAAKsM,QACRtM,KAAK0D,OAAS,GACd1D,KAAK4L,MAAQ,IAGf,IADA,IAAIsC,EAAQlO,KAAKmO,gBACRC,EAAI,EAAGA,EAAIF,EAAMzN,OAAQ2N,IAEhC,IADAJ,EAAYhO,KAAKqM,OAAOT,MAAM5L,KAAKkO,MAAMA,EAAME,SAC5BxC,GAASoC,EAAU,GAAGvN,OAASmL,EAAM,GAAGnL,QAAS,CAGlE,GAFAmL,EAAQoC,EACRC,EAAQG,EACJpO,KAAKwK,QAAQ4C,gBAAiB,CAEhC,IAAc,KADd1C,EAAQ1K,KAAK4N,WAAWI,EAAWE,EAAME,KAEvC,OAAO1D,EACF,GAAI1K,KAAKuM,WAAY,CAC1BX,GAAQ,EACR,QACF,CACE,OAAO,CAEX,CAAO,IAAK5L,KAAKwK,QAAQ6D,KACvB,KAEJ,CAEF,OAAIzC,GAEY,KADdlB,EAAQ1K,KAAK4N,WAAWhC,EAAOsC,EAAMD,MAE5BvD,EAIS,KAAhB1K,KAAKqM,OACArM,KAAKoM,IAELpM,KAAK4I,WAAW,0BAA4B5I,KAAK4D,SAAW,GAAK,yBAA2B5D,KAAK0L,eAAgB,CACtH9M,KAAM,GACN8L,MAAO,KACPmB,KAAM7L,KAAK4D,UAGjB,GAAG,QAEH6G,KAAqBzN,EAAAA,EAAAA,KAAO,WAC1B,IAAImO,EAAInL,KAAKyN,OACb,OAAItC,GAGKnL,KAAKyK,KAEhB,GAAG,OAEH6D,OAAuBtR,EAAAA,EAAAA,KAAO,SAAeuR,GAC3CvO,KAAK0M,eAAevI,KAAKoK,EAC3B,GAAG,SAEHC,UAA0BxR,EAAAA,EAAAA,KAAO,WAE/B,OADQgD,KAAK0M,eAAejM,OAAS,EAC7B,EACCT,KAAK0M,eAAe/B,MAEpB3K,KAAK0M,eAAe,EAE/B,GAAG,YAEHyB,eAA+BnR,EAAAA,EAAAA,KAAO,WACpC,OAAIgD,KAAK0M,eAAejM,QAAUT,KAAK0M,eAAe1M,KAAK0M,eAAejM,OAAS,GAC1ET,KAAKyO,WAAWzO,KAAK0M,eAAe1M,KAAK0M,eAAejM,OAAS,IAAIyN,MAErElO,KAAKyO,WAAoB,QAAEP,KAEtC,GAAG,iBAEHQ,UAA0B1R,EAAAA,EAAAA,KAAO,SAAkB8N,GAEjD,OADAA,EAAI9K,KAAK0M,eAAejM,OAAS,EAAIkO,KAAKC,IAAI9D,GAAK,KAC1C,EACA9K,KAAK0M,eAAe5B,GAEpB,SAEX,GAAG,YAEH+D,WAA2B7R,EAAAA,EAAAA,KAAO,SAAmBuR,GACnDvO,KAAKsO,MAAMC,EACb,GAAG,aAEHO,gBAAgC9R,EAAAA,EAAAA,KAAO,WACrC,OAAOgD,KAAK0M,eAAejM,MAC7B,GAAG,kBACH+J,QAAS,CAAE,oBAAoB,GAC/B/G,eAA+BzG,EAAAA,EAAAA,KAAO,SAAmBqG,EAAI0L,EAAKC,EAA2BC,GAE3F,OAAQD,GACN,KAAK,EAiLL,KAAK,GA8CL,KAAK,GACH,OAAO,EA7NT,KAAK,EAEL,KAAK,EAEL,KAAK,EAEL,KAAK,EAEL,KAAK,EACH,MACF,KAAK,EACH,OAAO,GAET,KAAK,EAEH,OADAhP,KAAKsO,MAAM,QACJ,GAET,KAAK,EAEH,OADAtO,KAAKsO,MAAM,MACJ,GAET,KAAK,EAEH,OADAtO,KAAKsO,MAAM,MACJ,GAET,KAAK,GACH,OAAO,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,MACJ,GAET,KAAK,GAGH,OAFAS,EAAIrL,OAASqL,EAAIrL,OAAO2B,OACxBrF,KAAKsO,MAAM,SACJ,GAET,KAAK,GAIH,OAHAtO,KAAKwO,WACLxO,KAAKwO,WACLxO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAGH,OAFAtO,KAAKwO,WACLxO,KAAKwO,WACE,EAET,KAAK,GAEH,OADAxO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,QACJ,GAET,KAAK,GAEH,OADAtO,KAAKwO,WACE,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GAEH,OADAxO,KAAKsO,MAAM,MACJ,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,MACJ,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GAEH,OADAtO,KAAKsO,MAAM,aACJ,GAET,KAAK,GAEH,OADAtO,KAAKwO,WACE,kBAET,KAAK,GAEH,OADAxO,KAAKsO,MAAM,aACJ,GAET,KAAK,GAEH,OADAtO,KAAKwO,WACE,kBAET,KAAK,GACHxO,KAAKsO,MAAM,uBACX,MACF,KAAK,GACHtO,KAAKwO,WACL,MACF,KAAK,GACH,MAAO,4BAET,KAAK,GACH,OAAO,EAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAKT,KAAK,GAEH,OADAO,EAAIrL,OAASqL,EAAIrL,OAAO2B,OACjB,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAKT,KAAK,GACH,MAAO,UAGb,GAAG,aACH6I,MAAO,CAAC,cAAe,YAAa,oBAAqB,gBAAiB,sBAAuB,sBAAuB,yBAA0B,cAAe,sBAAuB,gBAAiB,iBAAkB,kBAAmB,sFAAuF,aAAc,aAAc,eAAgB,eAAgB,cAAe,cAAe,eAAgB,cAAe,mBAAoB,cAAe,mBAAoB,iBAAkB,gBAAiB,qCAAsC,cAAe,kBAAmB,mBAAoB,gBAAiB,eAAgB,qBAAsB,kBAAmB,eAAgB,eAAgB,mBAAoB,qBAAsB,wBAAyB,yBAA0B,wBAAyB,wBAAyB,wBAAyB,wBAAyB,yBAA0B,aAAc,eAAgB,0BAA2B,qBAAsB,cAAe,UAAW,UAAW,0EAA2E,YAAa,cAAe,aAAc,eAAgB,WAAY,YAAa,aAAc,cAAe,cAAe,eAAgB,kCAAmC,WAAY,UAAW,UAAW,WAC50CO,WAAY,CAAE,oBAAuB,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,GAAM,CAAE,MAAS,CAAC,EAAG,EAAG,IAAK,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,EAAG,EAAG,GAAI,IAAK,WAAa,GAAS,KAAQ,CAAE,MAAS,CAAC,EAAG,EAAG,IAAK,WAAa,GAAS,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,IAGtmB,CAtgB4B,GAwgB5B,SAASS,IACPlP,KAAKqD,GAAK,CAAC,CACb,CAIA,OAPAF,EAAQ6G,MAAQA,GAIhBhN,EAAAA,EAAAA,IAAOkS,EAAQ,UACfA,EAAOhF,UAAY/G,EACnBA,EAAQ+L,OAASA,EACV,IAAIA,CACb,CAh6Ba,GAi6Bb/O,EAAOA,OAASA,EAChB,IAAIgP,EAA0BhP,EAG1B2E,EAAW,CACboD,MAAO,EACPE,OAAQ,EACRgH,KAAM,EACN9G,YAAa,EACbC,aAAc,EACdP,WAAY,EACZC,YAAa,EACbvC,WAAY,GACZC,SAAU,GACVQ,UAAW,GACXgB,SAAU,GACVf,QAAS,GACTJ,UAAW,GACXC,QAAS,GACTjB,aAAc,GACdE,WAAY,GACZoB,UAAW,GACXY,QAAS,GACTX,QAAS,GACTV,WAAY,GACZC,SAAU,GACV0C,YAAa,GACbC,aAAc,GACd1D,WAAY,GACZ2B,eAAgB,GAChBO,gBAAiB,GACjBN,aAAc,GACdE,YAAa,GACbC,UAAW,GACXN,eAAgB,GAChB2B,oBAAqB,GACrBE,qBAAsB,IAEpBgH,EAAY,CACdC,OAAQ,EACRC,KAAM,GAEJ/H,EAAY,CACdE,OAAQ,EACRC,QAAS,EACTF,KAAM,GAEJ+H,EAAa,MACf1P,WAAAA,GACEE,KAAKiL,MAAQ,IAAIpL,EAAAA,GAAgB,KAAM,CACrC4P,eAAW,EACXC,OAAwB,IAAIC,IAC5BC,cAA+B,IAAID,IACnCE,gBAAiC,IAAIF,IACrCG,MAAO,GACPC,SAAU,GACVC,MAAO,GACPC,wBAAwB,EACxBC,iBAAa,EACbC,gBAAY,EACZC,iBAAa,EACbC,mBAAe,MAEjBrQ,KAAKsF,YAAcA,EAAAA,GACnBtF,KAAKuF,kBAAoBA,EAAAA,GACzBvF,KAAKmF,gBAAkBA,EAAAA,GACvBnF,KAAKsQ,YAAcA,EAAAA,GACnBtQ,KAAKuQ,kBAAoBA,EAAAA,GACzBvQ,KAAKwQ,gBAAkBA,EAAAA,GACvBxQ,KAAKiE,MAAQjE,KAAKiE,MAAMwM,KAAKzQ,MAC7BA,KAAKuE,aAAevE,KAAKuE,aAAakM,KAAKzQ,MAC3CA,KAAKyF,aAAezF,KAAKyF,aAAagL,KAAKzQ,MAC3CA,KAAK0Q,QACL1Q,KAAK2Q,SAAQC,EAAAA,EAAAA,MAAaC,MAC1B7Q,KAAK8E,SAAWA,EAChB9E,KAAKqP,UAAYA,EACjBrP,KAAKwH,UAAYA,CACnB,CAAC,eAECxK,EAAAA,EAAAA,IAAOgD,KAAM,cAFd,GAID8Q,MAAAA,CAAOC,GACL/Q,KAAKiL,MAAMhL,QAAQ6P,MAAM3L,KAAK,CAC5BvG,KAAMmT,EAAKnS,KACXiS,KAAME,EAAKF,MAAQ7Q,KAAKgR,WACxBxT,KAAMuT,EAAKnL,MACXqL,UAAW,KAEbjR,KAAKiL,MAAMhL,QAAQkQ,WAAanQ,KAAKiL,MAAMhL,QAAQ6P,MAAMvI,OAAO,GAAG,EACrE,CACA2J,QAAAA,CAASC,EAAIvT,EAAMyJ,EAAajD,GAC9B,IAAIgN,EAAcpR,KAAKiL,MAAMhL,QAAQkQ,WACrC,MAAMkB,EAAMrR,KAAKiL,MAAMhL,QAAQyP,OAAO4B,IAAIH,GAC1C,GAAIE,EAAK,CACP,GAAIrR,KAAKiL,MAAMhL,QAAQkQ,YAAckB,EAAIE,KAAOvR,KAAKiL,MAAMhL,QAAQkQ,aAAekB,EAAIE,IACpF,MAAM,IAAItI,MACR,yDAAyDoI,EAAIzT,qBAAqByT,EAAIE,IAAI3T,iBAAiBoC,KAAKiL,MAAMhL,QAAQkQ,WAAWvS,2BAK7I,GAFAwT,EAAcC,EAAIE,IAAMF,EAAIE,IAAMvR,KAAKiL,MAAMhL,QAAQkQ,WACrDkB,EAAIE,IAAMH,EACNC,GAAOzT,IAASyT,EAAIzT,MAAuB,MAAfyJ,EAC9B,MAEJ,CAmBA,GAlByB,MAArBA,GAAazI,OACfyI,EAAc,CAAEzI,KAAMhB,EAAMwG,SAElB,MAARA,GAAoC,MAApBiD,EAAYzI,OAC9ByI,EAAc,CAAEzI,KAAMhB,EAAMwG,SAE9BpE,KAAKiL,MAAMhL,QAAQyP,OAAO8B,IAAIL,EAAI,CAChCI,IAAKH,EACLxT,OACAyJ,YAAaA,EAAYzI,KACzBiS,KAAMxJ,EAAYwJ,MAAQ7Q,KAAKgR,WAC/BvB,UAAWzP,KAAKiL,MAAMhL,QAAQwP,UAC9BgC,MAAO,CAAC,EACRC,WAAY,CAAC,EACbC,SAAU,KACVzU,SAAU,KACVkH,KAAMA,GAAQ,gBAEZpE,KAAKiL,MAAMhL,QAAQwP,UAAW,CAChC,MAAMmC,EAAqB5R,KAAKiL,MAAMhL,QAAQyP,OAAO4B,IAAItR,KAAKiL,MAAMhL,QAAQwP,WACxEmC,IACFA,EAAmBC,UAAYV,EAEnC,CACInR,KAAKiL,MAAMhL,QAAQkQ,YACrBnQ,KAAKiL,MAAMhL,QAAQkQ,WAAWc,UAAU9M,KAAKgN,GAE/CnR,KAAKiL,MAAMhL,QAAQwP,UAAY0B,CACjC,CACAW,eAAAA,CAAgBC,GACd,IAAI3D,EACA4D,EAAQ,EACZ,IAAKD,EACH,OAAO,EAET,IAAK3D,EAAI,EAAGA,EAAIpO,KAAKiL,MAAMhL,QAAQ8P,SAAStP,OAAQ2N,IAC9CpO,KAAKiL,MAAMhL,QAAQ8P,SAAS3B,GAAGhK,OAASpE,KAAK8E,SAASE,cAAgBhF,KAAKiL,MAAMhL,QAAQ8P,SAAS3B,GAAGxG,OAASmK,GAChHC,IAEEhS,KAAKiL,MAAMhL,QAAQ8P,SAAS3B,GAAGhK,OAASpE,KAAK8E,SAASI,YAAclF,KAAKiL,MAAMhL,QAAQ8P,SAAS3B,GAAGxG,OAASmK,GAC9GC,IAGJ,OAAOA,CACT,CACAC,UAAAA,CAAWC,EAAQC,EAAMC,EAASC,GAChCrS,KAAKiL,MAAMhL,QAAQ8P,SAAS5L,KAAK,CAC/BgN,GAAInR,KAAKiL,MAAMhL,QAAQ8P,SAAStP,OAAO6R,WACvC1K,KAAMsK,EACNrK,GAAIsK,EACJC,QAASA,EAAQxT,KACjBiS,KAAMuB,EAAQvB,MAAQ7Q,KAAKgR,WAC3BqB,UAEJ,CACAE,SAAAA,CAAUL,EAAQC,EAAMC,EAASI,GAA+B,IAAlBzK,EAAQ6B,UAAAnJ,OAAA,QAAAgS,IAAA7I,UAAA,IAAAA,UAAA,GACpD,GAAI4I,IAAgBxS,KAAK8E,SAASI,WAAY,CAE5C,GADYlF,KAAK8R,gBAAgBI,GAAU,IACjC,EAAG,CACX,MAAMlJ,EAAQ,IAAIC,MAAM,iDAAmDiJ,EAAS,KAQpF,MAPAlJ,EAAMF,KAAO,CACXlK,KAAM,OACN8L,MAAO,OACPmB,KAAM,IACNC,IAAK,CAAEC,WAAY,EAAGC,UAAW,EAAGC,aAAc,EAAGC,YAAa,GAClEX,SAAU,CAAC,yBAEPvC,CACR,CACF,CAUA,OATAhJ,KAAKiL,MAAMhL,QAAQ8P,SAAS5L,KAAK,CAC/BgN,GAAInR,KAAKiL,MAAMhL,QAAQ8P,SAAStP,OAAO6R,WACvC1K,KAAMsK,EACNrK,GAAIsK,EACJC,QAASA,GAASxT,MAAQ,GAC1BiS,KAAMuB,GAASvB,MAAQ7Q,KAAKgR,WAC5B5M,KAAMoO,EACNzK,cAEK,CACT,CACA2K,gBAAAA,GACE,OAAO1S,KAAKiL,MAAMhL,QAAQ6P,MAAMrP,OAAS,CAC3C,CACAkS,yBAAAA,GACE,OAAO3S,KAAKiL,MAAMhL,QAAQ6P,MAAM8C,MAAMC,GAAMA,EAAEjV,MAChD,CACAkV,WAAAA,GACE,OAAO9S,KAAKiL,MAAMhL,QAAQ8P,QAC5B,CACAgD,QAAAA,GACE,OAAO/S,KAAKiL,MAAMhL,QAAQ6P,KAC5B,CACAkD,SAAAA,GACE,OAAOhT,KAAKiL,MAAMhL,QAAQyP,MAC5B,CACAuD,gBAAAA,GACE,OAAOjT,KAAKiL,MAAMhL,QAAQ2P,aAC5B,CACAsD,kBAAAA,GACE,OAAOlT,KAAKiL,MAAMhL,QAAQ4P,eAC5B,CACAsD,QAAAA,CAAShC,GACP,OAAOnR,KAAKiL,MAAMhL,QAAQyP,OAAO4B,IAAIH,EACvC,CACAiC,YAAAA,GACE,MAAO,IAAIpT,KAAKiL,MAAMhL,QAAQyP,OAAO2D,OACvC,CACAC,qBAAAA,GACEtT,KAAKiL,MAAMhL,QAAQgQ,wBAAyB,CAC9C,CACAsD,sBAAAA,GACEvT,KAAKiL,MAAMhL,QAAQgQ,wBAAyB,CAC9C,CACAuD,mBAAAA,GACE,OAAOxT,KAAKiL,MAAMhL,QAAQgQ,sBAC5B,CACAU,OAAAA,CAAQ8C,GACNzT,KAAKiL,MAAMhL,QAAQiQ,YAAcuD,CACnC,CACAC,WAAAA,CAAY9U,GACV,QAAa,IAATA,EACF,MAAO,CAAC,EAEVA,EAAOA,EAAKyG,OACZ,MAAMwL,EAAiC,OAA1B,WAAW8C,KAAK/U,IAAoD,OAA5B,aAAa+U,KAAK/U,SAAyB,EAEhG,MAAO,CAAEgV,kBADqB,IAAT/C,EAAkBjS,EAAOA,EAAKC,QAAQ,kBAAmB,KAAKwG,OAC7DwL,OACxB,CACAG,QAAAA,GACE,YAAuC,IAAnChR,KAAKiL,MAAMhL,QAAQiQ,YACdlQ,KAAKiL,MAAMhL,QAAQiQ,aAErBU,EAAAA,EAAAA,MAAaiD,UAAUhD,OAAQ,CACxC,CACAH,KAAAA,GACE1Q,KAAKiL,MAAM/K,SACXwQ,EAAAA,EAAAA,KACF,CACAjL,YAAAA,CAAaoD,GACX,MAAMiL,EAAajL,EAAIxD,QACjB,KAAEwL,EAAI,YAAE+C,GAAgB5T,KAAK0T,YAAYI,GACzC1B,EAAU,CACdxT,KAAMgV,EACN/C,QAGF,OADAkD,EAAAA,GAAIC,MAAM,iBAAiBC,KAAKC,UAAU9B,MACnCA,CACT,CAIA7N,YAAAA,CAAasE,GACX,MAAM+C,EAAQ,uCAAuC+H,KAAK9K,GAC1D,IAAIjD,EAAQgG,IAAQ,GAAKA,EAAM,GAAGvG,OAAS,cACvC8O,EAAQvI,IAAQ,GAAKA,EAAM,GAAGvG,YAAS,EAC3C,GAAI+O,QAAQC,IACLD,OAAOC,IAAIC,SAAS,QAAS1O,KAChCA,EAAQ,cACRuO,EAAQtL,EAAIxD,YAET,CACL,MAAMrG,GAAQ,IAAIuV,QAASvV,MAC3BA,EAAM4G,MAAQA,EACV5G,EAAM4G,QAAUA,IAClBA,EAAQ,cACRuO,EAAQtL,EAAIxD,OAEhB,CACA,MAAM,KAAEwL,EAAI,YAAE+C,GAAgB5T,KAAK0T,YAAYS,GAC/C,MAAO,CACLvV,KAAMgV,GAAcY,EAAAA,EAAAA,IAAaZ,GAAahD,EAAAA,EAAAA,YAAgB,EAC9DhL,QACAiL,OAEJ,CACA4D,OAAAA,CAAQxP,EAAOqC,EAAW8K,GACxB,MAAMsC,EAAO,CACXzP,QACAqC,YACA8K,QAASA,EAAQxT,KACjBiS,KAAMuB,EAAQvB,MAAQ7Q,KAAKgR,YAEvBtB,EAAS,GAAG3I,OAAO9B,EAAOA,GAChCjF,KAAKiL,MAAMhL,QAAQ+P,MAAM7L,KAAKuQ,GAC9B1U,KAAKiL,MAAMhL,QAAQ8P,SAAS5L,KAAK,CAC/BgN,GAAInR,KAAKiL,MAAMhL,QAAQ8P,SAAStP,OAAO6R,WACvC1K,KAAM8H,EAAO,GACb7H,GAAI6H,EAAO,GACX0C,QAASA,EAAQxT,KACjBiS,KAAMuB,EAAQvB,MAAQ7Q,KAAKgR,WAC3B5M,KAAMpE,KAAK8E,SAASsK,KACpB9H,aAEJ,CACAqN,QAAAA,CAASC,EAAShW,GAChB,MAAMqG,EAAQjF,KAAKmT,SAASyB,GAC5B,IACE,IAAIC,GAAgBL,EAAAA,EAAAA,IAAa5V,EAAKA,MAAMgS,EAAAA,EAAAA,OAC5CiE,EAAgBA,EAAchW,QAAQ,YAAa,KACnDgW,EAAgBA,EAAchW,QAAQ,SAAU,KAChD,MAAM4S,EAAQwC,KAAK/K,MAAM2L,GACzB7U,KAAK8U,YAAY7P,EAAOwM,EAC1B,CAAE,MAAOsD,GACPhB,EAAAA,GAAI/K,MAAM,sCAAuC+L,EACnD,CACF,CACAC,QAAAA,CAASJ,EAAShW,GAChB,MAAMqG,EAAQjF,KAAKmT,SAASyB,GAC5B,IACE,MAAMnD,EAAQ,CAAC,EACf,IAAIoD,GAAgBL,EAAAA,EAAAA,IAAa5V,EAAKA,MAAMgS,EAAAA,EAAAA,OAC5C,MAAMqE,EAAMJ,EAAcK,QAAQ,KAClCL,EAAgBA,EAAchW,QAAQ,YAAa,KACnDgW,EAAgBA,EAAchW,QAAQ,SAAU,KAChD,MAAMsW,EAAQN,EAActN,MAAM,EAAG0N,EAAM,GAAG5P,OACxC/F,EAAOuV,EAActN,MAAM0N,EAAM,GAAG5P,OAC1CoM,EAAM0D,GAAS7V,EACfU,KAAK8U,YAAY7P,EAAOwM,EAC1B,CAAE,MAAOsD,GACPhB,EAAAA,GAAI/K,MAAM,sCAAuC+L,EACnD,CACF,CACAD,WAAAA,CAAY7P,EAAOwM,GACjB,GAAmB,MAAfxM,EAAMwM,MACRxM,EAAMwM,MAAQA,OAEd,IAAK,MAAM2D,KAAO3D,EAChBxM,EAAMwM,MAAM2D,GAAO3D,EAAM2D,EAG/B,CACAC,aAAAA,CAAcT,EAAShW,GACrB,MAAMqG,EAAQjF,KAAKmT,SAASyB,GAC5B,IACE,MAAMC,GAAgBL,EAAAA,EAAAA,IAAa5V,EAAKA,MAAMgS,EAAAA,EAAAA,OACxCc,EAAauC,KAAK/K,MAAM2L,GAC9B7U,KAAKsV,iBAAiBrQ,EAAOyM,EAC/B,CAAE,MAAOqD,GACPhB,EAAAA,GAAI/K,MAAM,4CAA6C+L,EACzD,CACF,CACAO,gBAAAA,CAAiBrQ,EAAOyM,GACtB,GAAwB,MAApBzM,EAAMyM,WACRzM,EAAMyM,WAAaA,OAEnB,IAAK,MAAM0D,KAAO1D,EAChBzM,EAAMyM,WAAW0D,GAAO1D,EAAW0D,EAGzC,CACAG,MAAAA,GACEvV,KAAKiL,MAAMhL,QAAQkQ,gBAAa,CAClC,CACAqF,UAAAA,CAAWZ,EAAShW,GAClB,MAAMqG,EAAQjF,KAAKmT,SAASyB,GACtBvV,EAAOoW,SAASC,eAAe9W,EAAKA,MAC1C,IACE,MAAM+W,EAAQtW,EAAKuW,UACbC,EAAU5B,KAAK/K,MAAMyM,GACvBE,EAAQnE,YACV1R,KAAKsV,iBAAiBrQ,EAAO4Q,EAAQnE,YAEnCmE,EAAQpE,OACVzR,KAAK8U,YAAY7P,EAAO4Q,EAAQpE,MAEpC,CAAE,MAAOsD,GACPhB,EAAAA,GAAI/K,MAAM,yCAA0C+L,EACtD,CACF,CACAe,gBAAAA,CAAiB7Q,EAAOmQ,GACtB,QAA0B,IAAtBnQ,GAAOyM,WACT,OAAOzM,EAAMyM,WAAW0D,EAG5B,CAEAnR,KAAAA,CAAM8R,GACJ,GAAInL,MAAMoL,QAAQD,GAChBA,EAAME,SAASC,IACblW,KAAKiE,MAAMiS,EAAK,SAGlB,OAAQH,EAAM3R,MACZ,IAAK,gBACHpE,KAAKiL,MAAMhL,QAAQ8P,SAAS5L,KAAK,CAC/BgN,GAAInR,KAAKiL,MAAMhL,QAAQ8P,SAAStP,OAAO6R,WACvC1K,UAAM,EACNC,QAAI,EACJuK,QAAS,CACP+D,MAAOJ,EAAMtR,cACb2R,KAAML,EAAMpR,kBACZ0R,QAASN,EAAMnR,iBAEjBiM,MAAM,EACNzM,KAAM2R,EAAMlR,aAEd,MACF,IAAK,iBACH7E,KAAKkR,SAAS6E,EAAM9Q,MAAO8Q,EAAM9Q,MAAO8Q,EAAM1O,YAAa0O,EAAM3O,MACjE,MACF,IAAK,oBACH,GAAIpH,KAAKiL,MAAMhL,QAAQyP,OAAO4G,IAAIP,EAAM9Q,OACtC,MAAM,IAAIgE,MACR,sJAGJjJ,KAAKiL,MAAMhL,QAAQmQ,YAAc2F,EAAM9Q,MACvCjF,KAAKkR,SAAS6E,EAAM9Q,MAAO8Q,EAAM9Q,MAAO8Q,EAAM1O,YAAa0O,EAAM3O,MACjEpH,KAAKiL,MAAMhL,QAAQ2P,cAAc4B,IAAIuE,EAAM9Q,MAAOjF,KAAKiL,MAAMhL,QAAQ8P,SAAStP,QAC9E,MACF,IAAK,qBACHT,KAAKiL,MAAMhL,QAAQoQ,cAAgB0F,EAAM9Q,MACzCjF,KAAKiL,MAAMhL,QAAQ4P,gBAAgB2B,IAAIuE,EAAM9Q,MAAOjF,KAAKiL,MAAMhL,QAAQ8P,SAAStP,QAChF,MACF,IAAK,cAGL,IAAK,YACHT,KAAKuS,UAAUwD,EAAM9Q,WAAO,OAAQ,EAAQ8Q,EAAMlR,YAClD,MACF,IAAK,UACH7E,KAAKyU,QAAQsB,EAAM9Q,MAAO8Q,EAAMzO,UAAWyO,EAAMnX,MACjD,MACF,IAAK,WACHoB,KAAK2U,SAASoB,EAAM9Q,MAAO8Q,EAAMnX,MACjC,MACF,IAAK,WACHoB,KAAKgV,SAASe,EAAM9Q,MAAO8Q,EAAMnX,MACjC,MACF,IAAK,gBACHoB,KAAKqV,cAAcU,EAAM9Q,MAAO8Q,EAAMnX,MACtC,MACF,IAAK,aACHoB,KAAKwV,WAAWO,EAAM9Q,MAAO8Q,EAAMnX,MACnC,MACF,IAAK,aACH,GAAIoB,KAAKiL,MAAMhL,QAAQmQ,YAAa,CAClC,GAAI2F,EAAMlO,KAAO7H,KAAKiL,MAAMhL,QAAQmQ,YAClC,MAAM,IAAInH,MACR,2BAA6BjJ,KAAKiL,MAAMhL,QAAQmQ,YAAYxS,KAAO,2GAGrEoC,KAAKiL,MAAMhL,QAAQmQ,iBAAc,CAErC,MAAO,GAAIpQ,KAAKiL,MAAMhL,QAAQoQ,cAAe,CAC3C,GAAI0F,EAAMlO,KAAO7H,KAAKiL,MAAMhL,QAAQoQ,eAAiB0F,EAAMnO,OAAS5H,KAAKiL,MAAMhL,QAAQoQ,cACrF,MAAM,IAAIpH,MACR,6BAA+BjJ,KAAKiL,MAAMhL,QAAQoQ,cAAczS,KAAO,6GAGzEoC,KAAKiL,MAAMhL,QAAQoQ,mBAAgB,CAEvC,CACArQ,KAAKuS,UAAUwD,EAAMnO,KAAMmO,EAAMlO,GAAIkO,EAAMjO,IAAKiO,EAAMlR,WAAYkR,EAAMhO,UACxE,MACF,IAAK,WACH/H,KAAK8Q,OAAOiF,EAAMzR,SAClB,MACF,IAAK,SACHtE,KAAKuV,SACL,MACF,IAAK,YACHvV,KAAKuS,eAAU,OAAQ,EAAQwD,EAAMvQ,SAAUuQ,EAAMlR,YACrD,MACF,IAAK,UAML,IAAK,UAML,IAAK,SASL,IAAK,SAYL,IAAK,SASL,IAAK,cAML,IAAK,WACH7E,KAAKuS,eAAU,OAAQ,OAAQ,EAAQwD,EAAMlR,YAC7C,MA/CF,IAAK,YACH7E,KAAKuS,eAAU,OAAQ,EAAQwD,EAAMnQ,MAAOmQ,EAAMlR,YAClD,MAIF,IAAK,WACH7E,KAAKuS,eAAU,OAAQ,EAAQwD,EAAMhQ,QAASgQ,EAAMlR,YACpD,MAIF,IAAK,WAGL,IAAK,OACH7E,KAAKuS,eAAU,OAAQ,EAAQwD,EAAM7P,QAAS6P,EAAMlR,YACpD,MAIF,IAAK,eACHS,EAAAA,EAAAA,IAAYyQ,EAAMnX,MAClB,MACF,IAAK,WAGL,IAAK,MACHoB,KAAKuS,eAAU,OAAQ,EAAQwD,EAAM1P,QAAS0P,EAAMlR,YACpD,MAIF,IAAK,gBACH7E,KAAKuS,eAAU,OAAQ,EAAQwD,EAAMtP,aAAcsP,EAAMlR,YACzD,MACF,IAAK,SACH7E,KAAKuS,eAAU,OAAQ,EAAQwD,EAAM/O,WAAY+O,EAAMlR,YACvD,MAIF,IAAK,aACH7E,KAAKuS,eAAU,OAAQ,EAAQwD,EAAMnP,UAAWmP,EAAMlR,YAO9D,CACA0R,SAAAA,GACE,OAAO3F,EAAAA,EAAAA,MAAaiD,QACtB,GAyHE2C,GArH4BxZ,EAAAA,EAAAA,KAAQwN,GAAY,yBACtCA,EAAQiM,2BACVjM,EAAQkM,uDAIRlM,EAAQmM,2EAKNnM,EAAQoM,+GAMRpM,EAAQqM,4GAMRrM,EAAQqM,uDAIVrM,EAAQqM,6BACNrM,EAAQqM,uDAIVrM,EAAQsM,+DAIRtM,EAAQqM,uDAIRrM,EAAQqM,6BACNrM,EAAQqM,oDAIVrM,EAAQuM,0EAKNvM,EAAQwM,mCACVxM,EAAQyM,2EAIRzM,EAAQ0M,0FAKR1M,EAAQ2M,6HAON3M,EAAQwM,mCACVxM,EAAQwM,+EAKNxM,EAAQ4M,+BACV5M,EAAQ6M,qEAIR7M,EAAQ8M,yEAKR9M,EAAQ+M,oCACN/M,EAAQgN,8DAIVhN,EAAQ+M,oCACN/M,EAAQgN,8DAIVhN,EAAQ+M,oCACN/M,EAAQgN,oJASVhN,EAAQkM,6JAKNlM,EAAQiM,2BACVjM,EAAQkM,4DAGNlM,EAAQiM,2BACVjM,EAAQkM,4CAGjB,aASCe,EAAkB,YAClBC,EAAqB,eAErBC,EAAyB,YACzBC,GAA4B5a,EAAAA,EAAAA,KAAO,SAASqC,EAAMnC,GACpD,OAAOH,EAAAA,EAAAA,IAASsC,EAAMnC,EACxB,GAAG,YACC2a,GAA4B7a,EAAAA,EAAAA,KAAO,SAASqC,EAAM4F,EAAO6S,EAAcC,EAAWC,GACpF,QAAoB,IAAhB/S,EAAMwM,OAAoC,OAAhBxM,EAAMwM,OAAsD,IAApC3H,OAAOuJ,KAAKpO,EAAMwM,OAAOhR,OAC7E,MAAO,CAAE9C,OAAQ,EAAGD,MAAO,GAE7B,MAAM+T,EAAQxM,EAAMwM,MACdwG,EAAYhT,EAAM0M,SAClBzU,EAAW+H,EAAM/H,SACvB,IAAIgb,EAAe,OACfF,IACFE,EAAe,oBAEjB,MAAMC,EAAI9Y,EAAKjC,OAAO,KACtB+a,EAAE9a,KAAK,KAAM,QAAU4a,EAAY,UACnCE,EAAE9a,KAAK,QAAS,kBAChB8a,EAAE9a,KAAK,UAAW6a,GAClB,IAAIE,EAAa,QACM,IAAnBlb,EAASe,QACXma,EAAa,IAAMlb,EAASe,OAE9B,IAAIoa,EAAYnb,EAASQ,MAAQoa,EAAe5a,EAASQ,MAAQoa,EACjE,MAAMQ,EAAWH,EAAE/a,OAAO,QAU1B,GATAkb,EAASjb,KAAK,QAAS,sBAAwB+a,GAC/CE,EAASjb,KAAK,IAAKH,EAASI,GAC5Bgb,EAASjb,KAAK,IAAKH,EAASS,QAC5B2a,EAASjb,KAAK,OAAQH,EAASM,MAC/B8a,EAASjb,KAAK,SAAUH,EAASO,QACjC6a,EAASjb,KAAK,QAASgb,GACvBC,EAASjb,KAAK,SAAUH,EAASS,QACjC2a,EAASjb,KAAK,KAAMH,EAASW,IAC7Bya,EAASjb,KAAK,KAAMH,EAASY,IAChB,MAAT2T,EAAe,CACjB,IAAI8G,EAAQ,GACZ,IAAK,IAAInD,KAAO3D,EAAO,CACrB,IAAI+G,EAAWL,EAAE/a,OAAO,KACpBoC,GAAgBC,EAAAA,EAAAA,GAAYgS,EAAM2D,IACtCoD,EAASnb,KAAK,aAAcmC,GAC5BgZ,EAASnb,KAAK,SAAU,UACxBob,EAA+BV,EAA/BU,CACErD,EACAoD,EACAtb,EAASI,EAAI,GACbJ,EAASS,OAAS4a,EAClBF,EACA,GACA,CAAEpa,MAAO,SACT8Z,GAEFQ,GAAS,EACX,CACF,CAEA,OADAD,EAASjb,KAAK,SAAUkb,GACjB,CAAE5a,OAAQT,EAASS,OAAS4a,EAAO7a,MAAO2a,EACnD,GAAG,aACCK,GAAkC1b,EAAAA,EAAAA,KAAO,SAAS2b,GACpD,MAAO,qCAAuCA,EAAQ,4FACxD,GAAG,mBACCC,GAA4B5b,EAAAA,EAAAA,KAAO6b,eAAexZ,EAAMX,GAA2B,IAAjBoa,EAAQlP,UAAAnJ,OAAA,QAAAgS,IAAA7I,UAAA,GAAAA,UAAA,GAAG,KAC3E7K,EAAWM,EAAKjC,OAAO,iBAC3B,MAAM0P,QAAciM,EAAAA,EAAAA,IAAYra,EAASE,MAAM2X,EAAAA,EAAAA,OAEzCyC,EADUja,EAAS3B,OAAO,aAAaC,KAAK,QAAS,uBAAuBA,KAAK,QAAS,gCAAgC4b,KAAKnM,GACjHoM,OAAOC,wBAE3B,GADApa,EAAS1B,KAAK,SAAUsR,KAAKyK,MAAMJ,EAAIrb,SAASN,KAAK,QAASsR,KAAKyK,MAAMJ,EAAItb,QACtD,aAAnBgB,EAAST,MAAsB,CACjC,MAAMqa,EAAWjZ,EAAK6Z,OAAOG,WAC7Bf,EAASgB,aAAa,SAAUN,EAAIrb,OAAS,EAAIe,EAASS,YAC1D,MAAMoa,EAAUjB,EAASkB,UACzBza,EAAS1B,KAAK,IAAKsR,KAAKyK,MAAMG,EAAQjc,EAAIic,EAAQ7b,MAAQ,EAAIsb,EAAItb,MAAQ,IAAIL,KAAK,IAAKsR,KAAKyK,MAAMG,EAAQhc,EAAIgc,EAAQ5b,OAAS,EAAIqb,EAAIrb,OAAS,GACnJ,MAAO,GAAImb,EAAU,CACnB,IAAI,OAAE1a,EAAM,MAAEE,EAAK,OAAED,GAAWya,EAChC,GAAI1a,EAASE,EAAO,CAClB,MAAMmb,EAAOrb,EACbA,EAASE,EACTA,EAAQmb,CACV,CACA1a,EAAS1B,KAAK,IAAKsR,KAAKyK,MAAMhb,EAASuQ,KAAKC,IAAIxQ,EAASE,GAAS,EAAI0a,EAAItb,MAAQ,IAC3D,aAAnBgB,EAAST,MACXc,EAAS1B,KAAK,IAAKsR,KAAKyK,MAAM/a,IAE9BU,EAAS1B,KAAK,IAAKsR,KAAKyK,MAAM/a,EAAS2a,EAAIrb,QAE/C,CACA,MAAO,CAACoB,EACV,GAAG,aACCN,GAA2BzB,EAAAA,EAAAA,KAAO,SAASqC,EAAMX,GACnD,IAAIgb,EAAiB,EACjBC,EAAa,EACjB,MAAM7M,EAAQpO,EAASE,KAAKmO,MAAM6M,EAAAA,GAAe9a,iBAC1C+a,EAAeC,IAAmBC,EAAAA,EAAAA,IAAcrb,EAASsb,UAChE,IAAIC,EAAY,GACZC,EAAK,EACLC,GAAwBnd,EAAAA,EAAAA,KAAO,IAAM0B,EAASnB,GAAG,SACrD,QAAwB,IAApBmB,EAAS0b,aAA6C,IAAxB1b,EAASS,YAAyBT,EAASS,WAAa,EACxF,OAAQT,EAAS0b,QACf,IAAK,MACL,IAAK,QACHD,GAAwBnd,EAAAA,EAAAA,KAAO,IAAM2R,KAAKyK,MAAM1a,EAASnB,EAAImB,EAASS,aAAa,SACnF,MACF,IAAK,SACL,IAAK,SACHgb,GAAwBnd,EAAAA,EAAAA,KAAO,IAAM2R,KAAKyK,MAAM1a,EAASnB,GAAKmc,EAAiBC,EAAajb,EAASS,YAAc,IAAI,SACvH,MACF,IAAK,SACL,IAAK,MACHgb,GAAwBnd,EAAAA,EAAAA,KAAO,IAAM2R,KAAKyK,MACxC1a,EAASnB,GAAKmc,EAAiBC,EAAa,EAAIjb,EAASS,YAAcT,EAASS,aAC/E,SAIT,QAAwB,IAApBT,EAASO,aAA6C,IAAxBP,EAASS,iBAA4C,IAAnBT,EAAShB,MAC3E,OAAQgB,EAASO,QACf,IAAK,OACL,IAAK,QACHP,EAASpB,EAAIqR,KAAKyK,MAAM1a,EAASpB,EAAIoB,EAASS,YAC9CT,EAASO,OAAS,QAClBP,EAAS2b,iBAAmB,SAC5B3b,EAAS4b,kBAAoB,SAC7B,MACF,IAAK,SACL,IAAK,SACH5b,EAASpB,EAAIqR,KAAKyK,MAAM1a,EAASpB,EAAIoB,EAAShB,MAAQ,GACtDgB,EAASO,OAAS,SAClBP,EAAS2b,iBAAmB,SAC5B3b,EAAS4b,kBAAoB,SAC7B,MACF,IAAK,QACL,IAAK,MACH5b,EAASpB,EAAIqR,KAAKyK,MAAM1a,EAASpB,EAAIoB,EAAShB,MAAQgB,EAASS,YAC/DT,EAASO,OAAS,MAClBP,EAAS2b,iBAAmB,SAC5B3b,EAAS4b,kBAAoB,SAInC,IAAK,IAAKlM,EAAGvC,KAASiB,EAAMyN,UAAW,MACT,IAAxB7b,EAASS,YAAiD,IAAxBT,EAASS,iBAAsC,IAAlB0a,IACjEK,EAAK9L,EAAIyL,GAEX,MAAM9a,EAAWM,EAAKjC,OAAO,QAC7B2B,EAAS1B,KAAK,IAAKqB,EAASpB,GAC5ByB,EAAS1B,KAAK,IAAK8c,UACK,IAApBzb,EAASO,QACXF,EAAS1B,KAAK,cAAeqB,EAASO,QAAQ5B,KAAK,oBAAqBqB,EAAS2b,kBAAkBhd,KAAK,qBAAsBqB,EAAS4b,wBAE7G,IAAxB5b,EAAS8b,YACXzb,EAASC,MAAM,cAAeN,EAAS8b,iBAEjB,IAApBV,GACF/a,EAASC,MAAM,YAAa8a,QAEF,IAAxBpb,EAAS+b,YACX1b,EAASC,MAAM,cAAeN,EAAS+b,iBAEnB,IAAlB/b,EAASlB,MACXuB,EAAS1B,KAAK,OAAQqB,EAASlB,WAEV,IAAnBkB,EAAST,OACXc,EAAS1B,KAAK,QAASqB,EAAST,YAEd,IAAhBS,EAASwb,GACXnb,EAAS1B,KAAK,KAAMqB,EAASwb,IACb,IAAPA,GACTnb,EAAS1B,KAAK,KAAM6c,GAEtB,MAAMtb,EAAOiN,GAAQ6O,EAAAA,GACrB,GAAIhc,EAASQ,MAAO,CAClB,MAAMyb,EAAO5b,EAAS3B,OAAO,SAC7Bud,EAAKtd,KAAK,IAAKqB,EAASpB,QACF,IAAlBoB,EAASlB,MACXmd,EAAKtd,KAAK,OAAQqB,EAASlB,MAE7Bmd,EAAK/b,KAAKA,EACZ,MACEG,EAASH,KAAKA,QAEQ,IAApBF,EAAS0b,aAA6C,IAAxB1b,EAASS,YAAyBT,EAASS,WAAa,IACxFwa,IAAe5a,EAAS6b,SAAW7b,GAAU,GAAG,GAAGya,UAAU7b,OAC7D+b,EAAiBC,GAEnBM,EAAU9V,KAAKpF,EACjB,CACA,OAAOkb,CACT,GAAG,YACCY,GAA4B7d,EAAAA,EAAAA,KAAO,SAASqC,EAAMyb,GACpD,SAASC,EAAUzd,EAAGC,EAAGG,EAAOC,EAAQqd,GACtC,OAAO1d,EAAI,IAAMC,EAAI,KAAOD,EAAII,GAAS,IAAMH,EAAI,KAAOD,EAAII,GAAS,KAAOH,EAAII,EAASqd,GAAO,KAAO1d,EAAII,EAAc,IAANsd,GAAa,KAAOzd,EAAII,GAAU,IAAML,EAAI,KAAOC,EAAII,EAC9K,EACAX,EAAAA,EAAAA,IAAO+d,EAAW,aAClB,MAAME,EAAU5b,EAAKjC,OAAO,WAK5B,OAJA6d,EAAQ5d,KAAK,SAAU0d,EAAUD,EAAUxd,EAAGwd,EAAUvd,EAAGud,EAAUpd,MAAOod,EAAUnd,OAAQ,IAC9Fsd,EAAQ5d,KAAK,QAAS,YACtByd,EAAUvd,EAAIud,EAAUvd,EAAIud,EAAUnd,OAAS,EAC/Cc,EAASY,EAAMyb,GACRG,CACT,GAAG,aACCtJ,GAAY,EACZuJ,GAAqCle,EAAAA,EAAAA,KAAO,CAACme,EAAUzL,EAAQuB,EAAWmK,KACvED,EAASE,QAGdpK,EAAUgF,SAASqF,IACjB,MAAMrW,EAAQyK,EAAO4B,IAAIgK,GACnBC,EAAWJ,EAASE,OAAO,SAAWpW,EAAM0M,WAC7CyJ,EAAMI,cAAgBvW,EAAM1G,MAC/Bgd,EAASle,KAAK,KAAM4H,EAAM1G,MAAQ0G,EAAMtH,OAAS,GACxCyd,EAAMI,cACfD,EAASle,KAAK,KAAM4H,EAAM1G,MAC5B,GACA,GACD,sBACCkd,GAA2Cze,EAAAA,EAAAA,KAAO,SAASqC,EAAM4F,EAAOmW,EAAOM,GACjF,MAAMC,EAASD,EAAWzW,EAAM1G,MAAQ0G,EAAM5G,OACxCud,EAAS3W,EAAM3H,EAAI2H,EAAMvH,MAAQ,EACjCme,EAAUF,EAAS1W,EAAMtH,OACzBme,EAAmBzc,EAAKjC,OAAO,KAAKoB,QAC1C,IAAI2Z,EAAI2D,EACHJ,IACH/J,IACI7H,OAAOuJ,KAAKpO,EAAMwM,OAAS,CAAC,GAAGhR,SAAW2a,EAAMpD,YAClDG,EAAE9a,KAAK,UAAWqb,EAAgB,QAAQ/G,YAAmBtU,KAAK,SAAU,WAE9E8a,EAAE/a,OAAO,QAAQC,KAAK,KAAM,QAAUsU,GAAUtU,KAAK,KAAMue,GAAQve,KAAK,KAAMwe,GAASxe,KAAK,KAAMue,GAAQve,KAAK,KAAM,KAAKA,KAAK,QAAS,kBAAkBA,KAAK,eAAgB,SAASA,KAAK,SAAU,QAAQA,KAAK,OAAQ4H,EAAMrH,MAClOua,EAAI2D,EAAiB1e,OAAO,KAC5B6H,EAAM0M,SAAWA,EACE,MAAf1M,EAAMwM,OACR0G,EAAE9a,KAAK,KAAM,QAAUsU,IAG3B,MAAMoK,GAAOpc,EAAAA,EAAAA,MACb,IAAIqc,EAAW,QACX/W,EAAMyM,YAAYzT,MACpB+d,EAAW/W,EAAMyM,WAAWzT,MAE5B8d,EAAKve,KAAO,UAGZwe,GADEN,EACU,IAAIhE,IAEJ,IAAID,IAElBsE,EAAKze,EAAI2H,EAAM3H,EACfye,EAAKxe,EAAIoe,EACTI,EAAKre,MAAQuH,EAAMvH,MACnBqe,EAAKpe,OAASsH,EAAMtH,OACpBoe,EAAK9d,MAAQ+d,EACbD,EAAKle,GAAK,EACVke,EAAKje,GAAK,EACVie,EAAKne,KAAOqH,EAAMrH,KAClB,MAAM0a,EAAWV,EAAUO,EAAG4D,GAE9B,GADA9W,EAAM/H,SAAW6e,EACb9W,EAAMyM,YAAYuK,KAAM,CAC1B,MAAMC,EAAUjX,EAAMyM,WAAWuK,KAAK5W,OACZ,MAAtB6W,EAAQC,OAAO,IACjBzc,EAAAA,EAAAA,IAAkByY,EAAG4D,EAAKze,EAAIye,EAAKre,MAAQ,GAAIqe,EAAKxe,EAAI,GAAI2e,EAAQlP,OAAO,KAE3E5N,EAAAA,EAAAA,IAAU+Y,EAAG4D,EAAKze,EAAIye,EAAKre,MAAQ,GAAIqe,EAAKxe,EAAI,GAAI2e,EAExD,CACAE,EAAuBhB,GAAOiB,EAAAA,EAAAA,IAASpX,EAAMoC,aAA7C+U,CACEnX,EAAMoC,YACN8Q,EACA4D,EAAKze,EACLye,EAAKxe,EACLwe,EAAKre,MACLqe,EAAKpe,OACL,CAAEM,MAAO,mBACTmd,GAEF,IAAIzd,EAASsH,EAAMtH,OACnB,GAAI2a,EAASY,KAAM,CACjB,MAAMoD,EAAUhE,EAASY,OAAOM,UAChCvU,EAAMtH,OAAS2e,EAAQ3e,OACvBA,EAAS2e,EAAQ3e,MACnB,CACA,OAAOA,CACT,GAAG,4BACC4e,GAAqCvf,EAAAA,EAAAA,KAAO,SAASqC,EAAM4F,EAAOmW,EAAOM,GAC3E,MAAMC,EAASD,EAAWzW,EAAM1G,MAAQ0G,EAAM5G,OACxCud,EAAS3W,EAAM3H,EAAI2H,EAAMvH,MAAQ,EACjCme,EAAUF,EAAS,GACnB9P,EAAOxM,EAAKjC,OAAO,KAAKoB,QACzBkd,IACH/J,IACA9F,EAAKzO,OAAO,QAAQC,KAAK,KAAM,QAAUsU,GAAUtU,KAAK,KAAMue,GAAQve,KAAK,KAAMwe,GAASxe,KAAK,KAAMue,GAAQve,KAAK,KAAM,KAAKA,KAAK,QAAS,kBAAkBA,KAAK,eAAgB,SAASA,KAAK,SAAU,QAAQA,KAAK,OAAQ4H,EAAMrH,MACrOqH,EAAM0M,SAAWA,GAEnB,MAAM6K,EAAUnd,EAAKjC,OAAO,KAC5B,IAAIqf,EAAW9E,EAEb8E,GADEf,EACU,IAAIhE,IAEJ,IAAID,IAElB+E,EAAQnf,KAAK,QAASof,GACtBD,EAAQnf,KAAK,OAAQ4H,EAAMrH,MAC3B,MAAMme,GAAOpc,EAAAA,EAAAA,MACboc,EAAKze,EAAI2H,EAAM3H,EACfye,EAAKxe,EAAIoe,EACTI,EAAKve,KAAO,UACZue,EAAKre,MAAQuH,EAAMvH,MACnBqe,EAAKpe,OAASsH,EAAMtH,OACpBoe,EAAK9d,MAAQ,QACb8d,EAAKle,GAAK,EACVke,EAAKje,GAAK,EACV0e,EAAQpf,OAAO,QAAQC,KAAK,KAAM,kBAAoBsU,GAAUtU,KAAK,KAAMue,GAAQve,KAAK,KAAMse,EAAS,IAAIte,KAAK,KAAMue,GAAQve,KAAK,KAAMse,EAAS,IAClJa,EAAQpf,OAAO,QAAQC,KAAK,KAAM,iBAAmBsU,GAAUtU,KAAK,KAAMue,EAASc,IAAsBrf,KAAK,KAAMse,EAAS,IAAIte,KAAK,KAAMue,EAASc,IAAsBrf,KAAK,KAAMse,EAAS,IAC/La,EAAQpf,OAAO,QAAQC,KAAK,KAAMue,EAASc,IAAsBrf,KAAK,KAAMse,EAAS,IAAIte,KAAK,KAAMue,GAAQve,KAAK,KAAMse,EAAS,IAChIa,EAAQpf,OAAO,QAAQC,KAAK,KAAMue,GAAQve,KAAK,KAAMse,EAAS,IAAIte,KAAK,KAAMue,EAASc,GAAuB,GAAGrf,KAAK,KAAMse,EAAS,IACpI,MAAMgB,EAASH,EAAQpf,OAAO,UAC9Buf,EAAOtf,KAAK,KAAM4H,EAAM3H,EAAI2H,EAAMvH,MAAQ,GAC1Cif,EAAOtf,KAAK,KAAMse,EAAS,IAC3BgB,EAAOtf,KAAK,IAAK,IACjBsf,EAAOtf,KAAK,QAAS4H,EAAMvH,OAC3Bif,EAAOtf,KAAK,SAAU4H,EAAMtH,QAC5B,MAAM2e,EAAUE,EAAQtD,OAAOM,UAY/B,OAXAvU,EAAMtH,OAAS2e,EAAQ3e,OACvBye,EAAuBhB,GAAOiB,EAAAA,EAAAA,IAASpX,EAAMoC,aAA7C+U,CACEnX,EAAMoC,YACNmV,EACAT,EAAKze,EACLye,EAAKxe,EAAI,GACTwe,EAAKre,MACLqe,EAAKpe,OACL,CAAEM,MAAO,SAAS0Z,KAClByD,GAEKnW,EAAMtH,MACf,GAAG,sBACCif,GAA4B5f,EAAAA,EAAAA,KAAO6b,eAAexZ,EAAM4F,EAAOmW,EAAOM,GACxE,OAAQzW,EAAMb,MACZ,IAAK,QACH,aAAamY,EAAmBld,EAAM4F,EAAOmW,EAAOM,GACtD,IAAK,cACH,aAAaD,EAAyBpc,EAAM4F,EAAOmW,EAAOM,GAEhE,GAAG,aACCmB,GAA0B7f,EAAAA,EAAAA,KAAO,SAASqC,EAAMkS,EAAK6J,GACvD,MACMjD,EADmB9Y,EAAKjC,OAAO,KAErC0f,EAAoB3E,EAAG5G,GACnBA,EAAI3T,MACNwe,EAAuBhB,EAAvBgB,CACE7K,EAAI3T,KACJua,EACA5G,EAAIjU,EACJiU,EAAIhU,GAAKgU,EAAIwL,eAAiB,GAAK,EACnCxL,EAAI7T,MACJ,EACA,CAAEO,MAAO,QACTmd,GAGJjD,EAAE3Z,OACJ,GAAG,WACCwe,GAAgChgB,EAAAA,EAAAA,KAAO,SAASqC,GAClD,OAAOA,EAAKjC,OAAO,IACrB,GAAG,iBACC6f,GAAiCjgB,EAAAA,EAAAA,KAAO,SAASqC,EAAMid,EAASY,EAAa9B,EAAO+B,GACtF,MAAMpB,GAAOpc,EAAAA,EAAAA,MACPwY,EAAImE,EAAQc,SAClBrB,EAAKze,EAAIgf,EAAQle,OACjB2d,EAAKxe,EAAI+e,EAAQje,OACjB0d,EAAK9d,MAAQ,aAAekf,EAAoB,EAChDpB,EAAKre,MAAQ4e,EAAQhe,MAAQge,EAAQle,OACrC2d,EAAKpe,OAASuf,EAAcZ,EAAQje,OACpCuZ,EAAUO,EAAG4D,EACf,GAAG,kBACCsB,GAA2BrgB,EAAAA,EAAAA,KAAO6b,eAAexZ,EAAMie,EAAWC,EAAWnC,GAC/E,MAAM,UACJoC,EAAS,cACTC,EAAa,eACbC,EAAc,cACdC,EACAC,kBAAmBpD,EACnBqD,gBAAiB7D,EACjB8D,kBAAmBrD,GACjBW,EACEjD,EAAI9Y,EAAKjC,OAAO,KAChB2gB,GAA+B/gB,EAAAA,EAAAA,KAAO,SAASoB,EAAQC,EAAQC,EAAOC,GAC1E,OAAO4Z,EAAE/a,OAAO,QAAQC,KAAK,KAAMe,GAAQf,KAAK,KAAMgB,GAAQhB,KAAK,KAAMiB,GAAOjB,KAAK,KAAMkB,GAAOlB,KAAK,QAAS,WAClH,GAAG,gBACH0gB,EAAaT,EAAUlf,OAAQkf,EAAUjf,OAAQif,EAAUhf,MAAOgf,EAAUjf,QAC5E0f,EAAaT,EAAUhf,MAAOgf,EAAUjf,OAAQif,EAAUhf,MAAOgf,EAAU/e,OAC3Ewf,EAAaT,EAAUlf,OAAQkf,EAAU/e,MAAO+e,EAAUhf,MAAOgf,EAAU/e,OAC3Ewf,EAAaT,EAAUlf,OAAQkf,EAAUjf,OAAQif,EAAUlf,OAAQkf,EAAU/e,YAClD,IAAvB+e,EAAUU,UACZV,EAAUU,SAAS/H,SAAQ,SAASC,GAClC6H,EAAaT,EAAUlf,OAAQ8X,EAAK3Y,EAAG+f,EAAUhf,MAAO4X,EAAK3Y,GAAGyB,MAC9D,mBACA,OAEJ,IAEF,IAAIif,GAAMre,EAAAA,EAAAA,MACVqe,EAAIrf,KAAO2e,EACXU,EAAI3gB,EAAIggB,EAAUlf,OAClB6f,EAAI1gB,EAAI+f,EAAUjf,OAClB4f,EAAIzD,WAAaA,EACjByD,EAAIjE,SAAWA,EACfiE,EAAIxD,WAAaA,EACjBwD,EAAIhf,OAAS,SACbgf,EAAI7D,OAAS,SACb6D,EAAI/e,OAAQ,EACZ+e,EAAIvgB,MAAQigB,GAAiB,GAC7BM,EAAItgB,OAAS+f,GAAkB,GAC/BO,EAAI9e,WAAase,EACjBQ,EAAIhgB,MAAQ,YACZ4c,EAAU1C,EAAG8F,GACbA,EAAMC,IACND,EAAIrf,KAAO0e,EAAUnJ,MACrB8J,EAAI3gB,EAAIggB,EAAUlf,OAASuf,EAAgB,GAAKL,EAAUhf,MAAQgf,EAAUlf,QAAU,EACtF6f,EAAI1gB,EAAI+f,EAAUjf,OAASmf,EAAYC,EACvCQ,EAAIhf,OAAS,SACbgf,EAAI7D,OAAS,SACb6D,EAAI9e,WAAase,EACjBQ,EAAIhgB,MAAQ,WACZggB,EAAIzD,WAAaA,EACjByD,EAAIjE,SAAWA,EACfiE,EAAIxD,WAAaA,EACjBwD,EAAIpN,MAAO,EACX,IAAI9R,GAAWsd,EAAAA,EAAAA,IAAS4B,EAAIrf,YAAcga,EAAUT,EAAG8F,EAAKX,GAAa7e,EAAS0Z,EAAG8F,GACrF,QAAgC,IAA5BX,EAAUa,cACZ,IAAK,MAAOC,EAAKlI,KAASpM,OAAOyQ,QAAQ+C,EAAUa,eACjD,GAAIjI,EAAK9D,QAAS,CAChB6L,EAAIrf,KAAOsX,EAAK9D,QAChB6L,EAAI3gB,EAAIggB,EAAUlf,QAAUkf,EAAUhf,MAAQgf,EAAUlf,QAAU,EAClE6f,EAAI1gB,EAAI+f,EAAUU,SAASI,GAAK7gB,EAAIigB,EAAYC,EAChDQ,EAAIhgB,MAAQ,WACZggB,EAAIhf,OAAS,SACbgf,EAAI7D,OAAS,SACb6D,EAAI/e,OAAQ,EACZ+e,EAAIzD,WAAaA,EACjByD,EAAIjE,SAAWA,EACfiE,EAAIxD,WAAaA,EACjBwD,EAAIpN,KAAOyM,EAAUzM,MACjBwL,EAAAA,EAAAA,IAAS4B,EAAIrf,OACf0e,EAAUjf,OAASif,EAAUU,SAASI,GAAK7gB,QACrCqb,EAAUT,EAAG8F,EAAKX,IAExB7e,EAAS0Z,EAAG8F,GAEd,IAAII,EAAgB1P,KAAKyK,MACvBra,EAASuf,KAAKC,IAAQA,EAAG3D,SAAW2D,GAAI,GAAG,GAAG/E,UAAU7b,SAAQ6gB,QAAO,CAACC,EAAKC,IAASD,EAAMC,KAE9FpB,EAAUU,SAASI,GAAKzgB,QAAU0gB,GAAiBb,EAAYC,EACjE,CAIJ,OADAH,EAAU3f,OAASgR,KAAKyK,MAAMkE,EAAU/e,MAAQ+e,EAAUjf,QACnD8Z,CACT,GAAG,YACC2E,GAAsC9f,EAAAA,EAAAA,KAAO,SAASqC,EAAMid,IAC9Dpe,EAAAA,EAAAA,IAAmBmB,EAAMid,EAC3B,GAAG,sBACCqC,GAAqC3hB,EAAAA,EAAAA,KAAO,SAASqC,GACvDA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,YAAYA,KAAK,YAAa,WAAWA,KAAK,YAAa,WAAWD,OAAO,QAAQC,KAAK,YAAa,aAAaA,KAClK,IACA,k1ZAEJ,GAAG,sBACCuhB,GAAqC5hB,EAAAA,EAAAA,KAAO,SAASqC,GACvDA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,YAAYA,KAAK,QAAS,MAAMA,KAAK,SAAU,MAAMD,OAAO,QAAQC,KAAK,YAAa,aAAaA,KACjJ,IACA,2JAEJ,GAAG,sBACCwhB,GAAkC7hB,EAAAA,EAAAA,KAAO,SAASqC,GACpDA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,SAASA,KAAK,QAAS,MAAMA,KAAK,SAAU,MAAMD,OAAO,QAAQC,KAAK,YAAa,aAAaA,KAC9I,IACA,4UAEJ,GAAG,mBACCyhB,GAAkC9hB,EAAAA,EAAAA,KAAO,SAASqC,GACpDA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,aAAaA,KAAK,OAAQ,KAAKA,KAAK,OAAQ,GAAGA,KAAK,cAAe,kBAAkBA,KAAK,cAAe,IAAIA,KAAK,eAAgB,IAAIA,KAAK,SAAU,sBAAsBD,OAAO,QAAQC,KAAK,IAAK,yBACtP,GAAG,mBACC0hB,GAAwC/hB,EAAAA,EAAAA,KAAO,SAASqC,GAC1DA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,eAAeA,KAAK,OAAQ,MAAMA,KAAK,OAAQ,GAAGA,KAAK,cAAe,IAAIA,KAAK,eAAgB,IAAIA,KAAK,SAAU,QAAQD,OAAO,QAAQC,KAAK,IAAK,4BACrM,GAAG,yBACC2hB,GAAuChiB,EAAAA,EAAAA,KAAO,SAASqC,GACzDA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,kBAAkBA,KAAK,OAAQ,IAAIA,KAAK,OAAQ,IAAIA,KAAK,cAAe,IAAIA,KAAK,eAAgB,IAAIA,KAAK,SAAU,QAAQD,OAAO,UAAUC,KAAK,KAAM,IAAIA,KAAK,KAAM,IAAIA,KAAK,IAAK,EACvO,GAAG,wBACC4hB,GAAuCjiB,EAAAA,EAAAA,KAAO,SAASqC,GAC5CA,EAAKjC,OAAO,QACLA,OAAO,UAAUC,KAAK,KAAM,aAAaA,KAAK,cAAe,IAAIA,KAAK,eAAgB,GAAGA,KAAK,SAAU,QAAQA,KAAK,OAAQ,GAAGA,KAAK,OAAQ,KAC1JD,OAAO,QAAQC,KAAK,OAAQ,QAAQA,KAAK,SAAU,WAAW2B,MAAM,mBAAoB,QAAQ3B,KAAK,eAAgB,OAAOA,KAAK,IAAK,0BAC/I,GAAG,wBACC6gB,GAA8BlhB,EAAAA,EAAAA,KAAO,WACvC,MAAO,CACLM,EAAG,EACHC,EAAG,EACHC,UAAM,EACNyB,YAAQ,EACRD,MAAO,OACPtB,WAAO,EACPC,YAAQ,EACRwB,WAAY,EACZtB,GAAI,EACJC,GAAI,EACJoB,OAAO,EACPkb,YAAQ,EAEZ,GAAG,cACC8E,GAA+BliB,EAAAA,EAAAA,KAAO,WACxC,MAAO,CACLM,EAAG,EACHC,EAAG,EACHC,KAAM,UACNC,OAAQ,OACRC,MAAO,IACPuB,OAAQ,QACRtB,OAAQ,IACRE,GAAI,EACJC,GAAI,EAER,GAAG,eACCse,EAAyC,WAC3C,SAAS+C,EAAOC,EAASjH,EAAG7a,EAAGC,EAAGG,EAAOC,EAAQoa,GAE/CsH,EADalH,EAAE/a,OAAO,QAAQC,KAAK,IAAKC,EAAII,EAAQ,GAAGL,KAAK,IAAKE,EAAII,EAAS,EAAI,GAAGqB,MAAM,cAAe,UAAUJ,KAAKwgB,GACrGrH,EACtB,CAEA,SAASuH,EAAQF,EAASjH,EAAG7a,EAAGC,EAAGG,EAAOC,EAAQoa,EAAWqD,GAC3D,MAAM,cAAEmE,EAAa,gBAAEC,EAAe,gBAAEC,GAAoBrE,GACrDsE,EAAgBC,IAAoB5F,EAAAA,EAAAA,IAAcwF,GACnDzS,EAAQsS,EAAQrS,MAAM6M,EAAAA,GAAe9a,gBAC3C,IAAK,IAAIsP,EAAI,EAAGA,EAAItB,EAAMrM,OAAQ2N,IAAK,CACrC,MAAM8L,EAAK9L,EAAIsR,EAAiBA,GAAkB5S,EAAMrM,OAAS,GAAK,EAChE7B,EAAOuZ,EAAE/a,OAAO,QAAQC,KAAK,IAAKC,EAAII,EAAQ,GAAGL,KAAK,IAAKE,GAAGyB,MAAM,cAAe,UAAUA,MAAM,YAAa2gB,GAAkB3gB,MAAM,cAAeygB,GAAiBzgB,MAAM,cAAewgB,GACnM5gB,EAAKxB,OAAO,SAASC,KAAK,IAAKC,EAAII,EAAQ,GAAGL,KAAK,KAAM6c,GAAItb,KAAKkO,EAAMsB,IACxExP,EAAKvB,KAAK,IAAKE,EAAII,EAAS,GAAGN,KAAK,oBAAqB,WAAWA,KAAK,qBAAsB,WAC/FgiB,EAAczgB,EAAMmZ,EACtB,CACF,CAEA,SAAS6H,EAAKR,EAASjH,EAAG7a,EAAGC,EAAGG,EAAOC,EAAQoa,EAAWqD,GACxD,MAAMyE,EAAI1H,EAAE/a,OAAO,UAEbwB,EADIihB,EAAEziB,OAAO,iBAAiBC,KAAK,IAAKC,GAAGD,KAAK,IAAKE,GAAGF,KAAK,QAASK,GAAOL,KAAK,SAAUM,GACnFP,OAAO,aAAa4B,MAAM,UAAW,SAASA,MAAM,SAAU,QAAQA,MAAM,QAAS,QACpGJ,EAAKxB,OAAO,OAAO4B,MAAM,UAAW,cAAcA,MAAM,aAAc,UAAUA,MAAM,iBAAkB,UAAUJ,KAAKwgB,GACvHE,EAAQF,EAASS,EAAGviB,EAAGC,EAAGG,EAAOC,EAAQoa,EAAWqD,GACpDiE,EAAczgB,EAAMmZ,EACtB,CAEAc,eAAeiH,EAAQV,EAASjH,EAAG7a,EAAGC,EAAGG,EAAOC,EAAQoa,EAAWqD,GACjE,MAAMpC,QAAY+G,EAAAA,EAAAA,IAA0BX,GAAS7I,EAAAA,EAAAA,OAC/CsJ,EAAI1H,EAAE/a,OAAO,UAEbwB,EADIihB,EAAEziB,OAAO,iBAAiBC,KAAK,IAAKC,EAAII,EAAQ,EAAIsb,EAAItb,MAAQ,GAAGL,KAAK,IAAKE,EAAII,EAAS,EAAIqb,EAAIrb,OAAS,GAAGN,KAAK,QAAS2b,EAAItb,OAAOL,KAAK,SAAU2b,EAAIrb,QACrJP,OAAO,aAAa4B,MAAM,SAAU,QAAQA,MAAM,QAAS,QAC1EJ,EAAKxB,OAAO,OAAO4B,MAAM,aAAc,UAAUA,MAAM,iBAAkB,UAAUia,WAAWF,EAAAA,EAAAA,IAAYqG,GAAS7I,EAAAA,EAAAA,QACnH+I,EAAQF,EAASS,EAAGviB,EAAGC,EAAGG,EAAOC,EAAQoa,EAAWqD,GACpDiE,EAAczgB,EAAMmZ,EACtB,CAEA,SAASsH,EAAcW,EAAQC,GAC7B,IAAK,MAAM7K,KAAO6K,EACZA,EAAkB9V,eAAeiL,IACnC4K,EAAO3iB,KAAK+X,EAAK6K,EAAkB7K,GAGzC,CAEA,OAzCApY,EAAAA,EAAAA,IAAOmiB,EAAQ,WAafniB,EAAAA,EAAAA,IAAOsiB,EAAS,YAShBtiB,EAAAA,EAAAA,IAAO4iB,EAAM,SAUb5iB,EAAAA,EAAAA,IAAO8iB,EAAS,YAQhB9iB,EAAAA,EAAAA,IAAOqiB,EAAe,iBACf,SAASjE,GACd,OAD8BxR,UAAAnJ,OAAA,QAAAgS,IAAA7I,UAAA,IAAAA,UAAA,GAErBkW,EAEsB,OAAxB1E,EAAM8E,cAAyBN,EAA+B,QAAxBxE,EAAM8E,cAA0Bf,EAASG,CACxF,CACF,CApD6C,GAqDzC7G,EAAiD,WACnD,SAAS0G,EAAOC,EAASjH,EAAG7a,EAAGC,EAAGG,EAAOC,EAAQoa,GAE/CsH,EADalH,EAAE/a,OAAO,QAAQC,KAAK,IAAKC,GAAGD,KAAK,IAAKE,GAAGyB,MAAM,cAAe,SAASJ,KAAKwgB,GACvErH,EACtB,CAEA,SAASuH,EAAQF,EAASjH,EAAG7a,EAAGC,EAAGG,EAAOC,EAAQoa,EAAWqD,GAC3D,MAAM,cAAEmE,EAAa,gBAAEC,EAAe,gBAAEC,GAAoBrE,EACtDtO,EAAQsS,EAAQrS,MAAM6M,EAAAA,GAAe9a,gBAC3C,IAAK,IAAIsP,EAAI,EAAGA,EAAItB,EAAMrM,OAAQ2N,IAAK,CACrC,MAAM8L,EAAK9L,EAAImR,EAAgBA,GAAiBzS,EAAMrM,OAAS,GAAK,EAC9D7B,EAAOuZ,EAAE/a,OAAO,QAAQC,KAAK,IAAKC,GAAGD,KAAK,IAAKE,GAAGyB,MAAM,cAAe,SAASA,MAAM,YAAaugB,GAAevgB,MAAM,cAAeygB,GAAiBzgB,MAAM,cAAewgB,GACnL5gB,EAAKxB,OAAO,SAASC,KAAK,IAAKC,GAAGD,KAAK,KAAM6c,GAAItb,KAAKkO,EAAMsB,IAC5DxP,EAAKvB,KAAK,IAAKE,EAAII,EAAS,GAAGN,KAAK,oBAAqB,WAAWA,KAAK,qBAAsB,WAC/FgiB,EAAczgB,EAAMmZ,EACtB,CACF,CAEA,SAAS6H,EAAKR,EAASjH,EAAG7a,EAAGC,EAAGG,EAAOC,EAAQoa,EAAWqD,GACxD,MAAMyE,EAAI1H,EAAE/a,OAAO,UAEbwB,EADIihB,EAAEziB,OAAO,iBAAiBC,KAAK,IAAKC,GAAGD,KAAK,IAAKE,GAAGF,KAAK,QAASK,GAAOL,KAAK,SAAUM,GACnFP,OAAO,aAAa4B,MAAM,UAAW,SAASA,MAAM,SAAU,QAAQA,MAAM,QAAS,QACpGJ,EAAKxB,OAAO,OAAO4B,MAAM,UAAW,cAAcA,MAAM,aAAc,UAAUA,MAAM,iBAAkB,UAAUJ,KAAKwgB,GACvHE,EAAQF,EAASS,EAAGviB,EAAGC,EAAGG,EAAOC,EAAQoa,EAAWqD,GACpDiE,EAAczgB,EAAMmZ,EACtB,CAEA,SAASsH,EAAcW,EAAQC,GAC7B,IAAK,MAAM7K,KAAO6K,EACZA,EAAkB9V,eAAeiL,IACnC4K,EAAO3iB,KAAK+X,EAAK6K,EAAkB7K,GAGzC,CAEA,OA9BApY,EAAAA,EAAAA,IAAOmiB,EAAQ,WAYfniB,EAAAA,EAAAA,IAAOsiB,EAAS,YAShBtiB,EAAAA,EAAAA,IAAO4iB,EAAM,SAQb5iB,EAAAA,EAAAA,IAAOqiB,EAAe,iBACf,SAASjE,GACd,MAA+B,OAAxBA,EAAM8E,cAAyBN,EAA+B,QAAxBxE,EAAM8E,cAA0Bf,EAASG,CACxF,CACF,CAtCqD,GAuCjDa,EAAkB,CACpBpjB,SAAU6a,EACVnZ,WACAoc,YACA+B,YACAC,UACAhF,YACAmF,gBACAC,iBACAI,WACAnf,mBAAoB4e,EACpBgC,kBACAC,wBACAC,uBACAC,uBACAN,qBACAC,qBACAC,kBACAjf,WAAYse,EACZve,YAAauf,EACbhE,qBACAzb,YAAWA,EAAAA,GAIT2gB,EAAO,CAAC,EACRjiB,EAAS,CACX4S,KAAM,CACJ3S,YAAQ,EACRE,WAAO,EACPD,YAAQ,EACRE,WAAO,GAET2e,YAAa,EACbmD,cAAe,GACfC,YAAa,GACbC,OAAQ,CACNC,WAA2BxjB,EAAAA,EAAAA,KAAO,WAChC,OAAO2R,KAAK8R,IAAIxc,MACd,KACuB,IAAvBjE,KAAK0P,OAAOjP,OAAe,CAAC,GAAKT,KAAK0P,OAAO4O,KAAKrZ,GAAUA,EAAMtH,QAAU,MACnD,IAAtBqC,KAAK0gB,MAAMjgB,OAAe,EAAIT,KAAK0gB,MAAMpC,KAAKqC,GAAOA,EAAGhjB,QAAU,IAAG6gB,QAAO,CAACC,EAAKmC,IAAMnC,EAAMmC,MAAgC,IAAzB5gB,KAAK+P,SAAStP,OAAe,EAAIT,KAAK+P,SAASuO,KAAKqC,GAAOA,EAAGhjB,QAAU,IAAG6gB,QAAO,CAACC,EAAKmC,IAAMnC,EAAMmC,MAA6B,IAAtB5gB,KAAKgQ,MAAMvP,OAAe,EAAIT,KAAKgQ,MAAMsO,KAAKqC,GAAOA,EAAGhjB,QAAU,IAAG6gB,QAAO,CAACC,EAAKmC,IAAMnC,EAAMmC,IACrT,GAAG,aACHlQ,OAAuB1T,EAAAA,EAAAA,KAAO,WAC5BgD,KAAK0P,OAAS,GACd1P,KAAK8P,MAAQ,GACb9P,KAAK0gB,MAAQ,GACb1gB,KAAK+P,SAAW,GAChB/P,KAAKgQ,MAAQ,EACf,GAAG,SACHc,QAAwB9T,EAAAA,EAAAA,KAAO,SAAS6jB,GACtC7gB,KAAK8P,MAAM3L,KAAK0c,EAClB,GAAG,UACH3P,UAA0BlU,EAAAA,EAAAA,KAAO,SAAS8jB,GACxC9gB,KAAK0P,OAAOvL,KAAK2c,EACnB,GAAG,YACHC,SAAyB/jB,EAAAA,EAAAA,KAAO,SAASsgB,GACvCtd,KAAK0gB,MAAMvc,KAAKmZ,EAClB,GAAG,WACHrL,YAA4BjV,EAAAA,EAAAA,KAAO,SAAS8b,GAC1C9Y,KAAK+P,SAAS5L,KAAK2U,EACrB,GAAG,cACHrE,SAAyBzX,EAAAA,EAAAA,KAAO,SAASgkB,GACvChhB,KAAKgQ,MAAM7L,KAAK6c,EAClB,GAAG,WACHC,WAA2BjkB,EAAAA,EAAAA,KAAO,WAChC,OAAOgD,KAAK0P,OAAO1P,KAAK0P,OAAOjP,OAAS,EAC1C,GAAG,aACHygB,UAA0BlkB,EAAAA,EAAAA,KAAO,WAC/B,OAAOgD,KAAK0gB,MAAM1gB,KAAK0gB,MAAMjgB,OAAS,EACxC,GAAG,YACH0gB,aAA6BnkB,EAAAA,EAAAA,KAAO,WAClC,OAAOgD,KAAK+P,SAAS/P,KAAK+P,SAAStP,OAAS,EAC9C,GAAG,eACH2gB,UAA0BpkB,EAAAA,EAAAA,KAAO,WAC/B,OAAOgD,KAAKgQ,MAAMhQ,KAAKgQ,MAAMvP,OAAS,EACxC,GAAG,YACHiP,OAAQ,GACRI,MAAO,GACP4Q,MAAO,GACP3Q,SAAU,GACVC,MAAO,IAETjQ,MAAsB/C,EAAAA,EAAAA,KAAO,WAC3BgD,KAAKqgB,cAAgB,GACrBrgB,KAAKsgB,YAAc,GACnBtgB,KAAKugB,OAAO7P,QACZ1Q,KAAK+Q,KAAO,CACV3S,YAAQ,EACRE,WAAO,EACPD,YAAQ,EACRE,WAAO,GAETyB,KAAKkd,YAAc,EACnBmE,IAAQzQ,EAAAA,EAAAA,MACV,GAAG,QACH0Q,WAA2BtkB,EAAAA,EAAAA,KAAO,SAASukB,EAAKnM,EAAKoM,EAAKC,QACvC,IAAbF,EAAInM,GACNmM,EAAInM,GAAOoM,EAEXD,EAAInM,GAAOqM,EAAID,EAAKD,EAAInM,GAE5B,GAAG,aACHsM,cAA8B1kB,EAAAA,EAAAA,KAAO,SAASoB,EAAQC,EAAQC,EAAOC,GACnE,MAAMojB,EAAQ3hB,KACd,IAAI4hB,EAAM,EACV,SAASC,EAASzd,GAChB,OAAuBpH,EAAAA,EAAAA,KAAO,SAA0BkZ,GACtD0L,IACA,MAAM9W,EAAI6W,EAAMtB,cAAc5f,OAASmhB,EAAM,EAC7CD,EAAML,UAAUpL,EAAM,SAAU7X,EAASyM,EAAIsV,EAAK5C,UAAW7O,KAAKmT,KAClEH,EAAML,UAAUpL,EAAM,QAAS3X,EAAQuM,EAAIsV,EAAK5C,UAAW7O,KAAK8R,KAChEkB,EAAML,UAAUnjB,EAAO4S,KAAM,SAAU3S,EAAS0M,EAAIsV,EAAK5C,UAAW7O,KAAKmT,KACzEH,EAAML,UAAUnjB,EAAO4S,KAAM,QAASzS,EAAQwM,EAAIsV,EAAK5C,UAAW7O,KAAK8R,KACxD,eAATrc,IACJud,EAAML,UAAUpL,EAAM,SAAU9X,EAAS0M,EAAIsV,EAAK5C,UAAW7O,KAAKmT,KAClEH,EAAML,UAAUpL,EAAM,QAAS5X,EAAQwM,EAAIsV,EAAK5C,UAAW7O,KAAK8R,KAChEkB,EAAML,UAAUnjB,EAAO4S,KAAM,SAAU1S,EAASyM,EAAIsV,EAAK5C,UAAW7O,KAAKmT,KACzEH,EAAML,UAAUnjB,EAAO4S,KAAM,QAASxS,EAAQuM,EAAIsV,EAAK5C,UAAW7O,KAAK8R,KAE3E,GAAG,mBACL,EACAzjB,EAAAA,EAAAA,IAAO6kB,EAAU,YACjB7hB,KAAKqgB,cAAcpK,QAAQ4L,KAC3B7hB,KAAKsgB,YAAYrK,QAAQ4L,EAAS,cACpC,GAAG,gBACHE,QAAwB/kB,EAAAA,EAAAA,KAAO,SAASoB,EAAQC,EAAQC,EAAOC,GAC7D,MAAMyjB,EAAUpI,EAAAA,GAAeqI,OAAO7jB,EAAQE,GACxC4jB,EAAStI,EAAAA,GAAeuI,OAAO/jB,EAAQE,GACvC8jB,EAAUxI,EAAAA,GAAeqI,OAAO5jB,EAAQE,GACxC8jB,EAASzI,EAAAA,GAAeuI,OAAO9jB,EAAQE,GAC7CyB,KAAKshB,UAAUnjB,EAAO4S,KAAM,SAAUiR,EAASrT,KAAKmT,KACpD9hB,KAAKshB,UAAUnjB,EAAO4S,KAAM,SAAUqR,EAASzT,KAAKmT,KACpD9hB,KAAKshB,UAAUnjB,EAAO4S,KAAM,QAASmR,EAAQvT,KAAK8R,KAClDzgB,KAAKshB,UAAUnjB,EAAO4S,KAAM,QAASsR,EAAQ1T,KAAK8R,KAClDzgB,KAAK0hB,aAAaM,EAASI,EAASF,EAAQG,EAC9C,GAAG,UACHC,eAA+BtlB,EAAAA,EAAAA,KAAO,SAASoV,EAAS+I,EAAUzL,GAChE,MAAM6S,EAAY7S,EAAO4B,IAAIc,EAAQxK,MAC/B4a,EAAcC,GAAiBrQ,EAAQxK,MAAMnH,QAAU,EACvDnD,EAAIilB,EAAUjlB,EAAIilB,EAAU7kB,MAAQ,GAAK8kB,EAAc,GAAKpC,EAAKsC,gBAAkB,EACzF1iB,KAAKsgB,YAAYnc,KAAK,CACpB/F,OAAQd,EACRe,OAAQ2B,KAAKkd,YAAc,EAC3B5e,MAAOhB,EAAI8iB,EAAKsC,gBAChBnkB,WAAO,EACP0G,MAAOmN,EAAQxK,KACfwV,SAAU+C,EAAgBnD,cAAc7B,IAE5C,GAAG,iBACHwH,eAA+B3lB,EAAAA,EAAAA,KAAO,SAASoV,GAC7C,MAAMwQ,EAAyB5iB,KAAKsgB,YAAYhC,KAAI,SAASuE,GAC3D,OAAOA,EAAW5d,KACpB,IAAG6d,YAAY1Q,EAAQxK,MACvB,OAAO5H,KAAKsgB,YAAYyC,OAAOH,EAAwB,GAAG,EAC5D,GAAG,iBACHI,YAA4BhmB,EAAAA,EAAAA,KAAO,WAAwE,IAA/DmX,EAAKvK,UAAAnJ,OAAA,QAAAgS,IAAA7I,UAAA,GAAAA,UAAA,GAAG,CAAEwI,aAAS,EAAQvB,MAAM,EAAOnT,WAAO,GAAUF,EAAIoM,UAAAnJ,OAAA,EAAAmJ,UAAA,QAAA6I,EACvG,MAAO,CACLrU,YAAQ,EACRC,OAAQ2B,KAAKkd,YACb5e,WAAO,EACPC,WAAO,EACP4V,MAAOA,EAAM/B,QACbvB,KAAMsD,EAAMtD,KACZnT,MAAOyW,EAAMzW,MACbC,OAAQ,EACRH,OAEJ,GAAG,cACHylB,SAAyBjmB,EAAAA,EAAAA,KAAO,WAAwE,IAA/DmX,EAAKvK,UAAAnJ,OAAA,QAAAgS,IAAA7I,UAAA,GAAAA,UAAA,GAAG,CAAEwI,aAAS,EAAQvB,MAAM,EAAOnT,WAAO,GAAUF,EAAIoM,UAAAnJ,OAAA,EAAAmJ,UAAA,QAAA6I,EACpGzS,KAAKqgB,cAAclc,KAAKnE,KAAKgjB,WAAW7O,EAAO3W,GACjD,GAAG,WACH0lB,SAAyBlmB,EAAAA,EAAAA,KAAO,WAC9B,OAAOgD,KAAKqgB,cAAc1V,KAC5B,GAAG,WACHwY,eAA+BnmB,EAAAA,EAAAA,KAAO,WACpC,QAAOgD,KAAKqgB,cAAc5f,QAAST,KAAKqgB,cAAcrgB,KAAKqgB,cAAc5f,OAAS,GAAG2iB,OACvF,GAAG,iBACHC,kBAAkCrmB,EAAAA,EAAAA,KAAO,SAASoV,GAChD,MAAMkR,EAAOtjB,KAAKqgB,cAAc1V,MAChC2Y,EAAKtF,SAAWsF,EAAKtF,UAAY,GACjCsF,EAAKnF,cAAgBmF,EAAKnF,eAAiB,GAC3CmF,EAAKtF,SAAS7Z,KAAK,CAAE5G,EAAGY,EAAOolB,iBAAkB5lB,OAAQ,IACzD2lB,EAAKnF,cAAcha,KAAKiO,GACxBpS,KAAKqgB,cAAclc,KAAKmf,EAC1B,GAAG,oBACHE,iBAAiCxmB,EAAAA,EAAAA,KAAO,WAClCgD,KAAKmjB,kBACPnjB,KAAKyjB,iBAAmBzjB,KAAKkd,YAEjC,GAAG,mBACHwG,kBAAkC1mB,EAAAA,EAAAA,KAAO,WACnCgD,KAAKmjB,kBACPnjB,KAAKkd,YAAcld,KAAKyjB,iBAE5B,GAAG,oBACHE,iBAAiC3mB,EAAAA,EAAAA,KAAO,SAAS4mB,GAC/C5jB,KAAKkd,YAAcld,KAAKkd,YAAc0G,EACtC5jB,KAAK+Q,KAAKxS,MAAQqb,EAAAA,GAAeuI,OAAOniB,KAAK+Q,KAAKxS,MAAOyB,KAAKkd,YAChE,GAAG,mBACHqG,gBAAgCvmB,EAAAA,EAAAA,KAAO,WACrC,OAAOgD,KAAKkd,WACd,GAAG,kBACH2G,WAA2B7mB,EAAAA,EAAAA,KAAO,WAChC,MAAO,CAAEmB,OAAQ6B,KAAK+Q,KAAMwP,OAAQvgB,KAAKugB,OAC3C,GAAG,cAEDuD,GAA2B9mB,EAAAA,EAAAA,KAAO6b,eAAexZ,EAAM2hB,GACzD7iB,EAAOwlB,gBAAgBvD,EAAK5C,WAC5BwD,EAAUrjB,OAASyiB,EAAK5C,UACxBwD,EAAU3iB,OAASF,EAAOolB,iBAC1B,MAAMxH,GAAOpc,EAAAA,EAAAA,MACboc,EAAKze,EAAI0jB,EAAU5iB,OACnB2d,EAAKxe,EAAIyjB,EAAU3iB,OACnB0d,EAAKre,MAAQsjB,EAAUtjB,OAAS0iB,EAAK1iB,MACrCqe,EAAK9d,MAAQ,OACb,MAAMka,EAAI9Y,EAAKjC,OAAO,KAChBkb,EAAW6H,EAAgBpjB,SAASob,EAAG4D,GACvCgI,GAAUnkB,EAAAA,EAAAA,MAChBmkB,EAAQzmB,EAAI0jB,EAAU5iB,OACtB2lB,EAAQxmB,EAAIyjB,EAAU3iB,OACtB0lB,EAAQrmB,MAAQqe,EAAKre,MACrBqmB,EAAQ7J,GAAK,MACb6J,EAAQnlB,KAAOoiB,EAAU5O,QACzB2R,EAAQ9lB,MAAQ,WAChB8lB,EAAQvJ,WAAa4F,EAAK4D,eAC1BD,EAAQ/J,SAAWoG,EAAK6D,aACxBF,EAAQtJ,WAAa2F,EAAK8D,eAC1BH,EAAQ9kB,OAASmhB,EAAK+D,UACtBJ,EAAQ5kB,WAAaihB,EAAKgE,WAC1BL,EAAQ3J,OAAS,SACjB,MAAMrb,GAAWsd,EAAAA,EAAAA,IAAS0H,EAAQnlB,YAAcga,EAAUT,EAAG4L,GAAWtlB,EAAS0Z,EAAG4L,GAC9EpK,EAAahL,KAAKyK,MACtBra,EAASuf,KAAKC,IAAQA,EAAG3D,SAAW2D,GAAI,GAAG,GAAG/E,UAAU7b,SAAQ6gB,QAAO,CAACC,EAAKC,IAASD,EAAMC,KAE9FpG,EAASjb,KAAK,SAAUsc,EAAa,EAAIyG,EAAKgE,YAC9CpD,EAAUrjB,QAAUgc,EAAa,EAAIyG,EAAKgE,WAC1CjmB,EAAOwlB,gBAAgBhK,EAAa,EAAIyG,EAAKgE,YAC7CpD,EAAUziB,MAAQyiB,EAAU3iB,OAASsb,EAAa,EAAIyG,EAAKgE,WAC3DpD,EAAU1iB,MAAQ0iB,EAAU5iB,OAAS2d,EAAKre,MAC1CS,EAAO4jB,OAAOf,EAAU5iB,OAAQ4iB,EAAU3iB,OAAQ2iB,EAAU1iB,MAAO0iB,EAAUziB,OAC7EJ,EAAOoiB,OAAO9L,QAAQuM,EACxB,GAAG,YACCqD,GAA8BrnB,EAAAA,EAAAA,KAAQsnB,IACjC,CACL9J,WAAY8J,EAAI1G,kBAChB5D,SAAUsK,EAAIzG,gBACdpD,WAAY6J,EAAIxG,qBAEjB,eACCyG,GAA2BvnB,EAAAA,EAAAA,KAAQsnB,IAC9B,CACL9J,WAAY8J,EAAIN,eAChBhK,SAAUsK,EAAIL,aACdxJ,WAAY6J,EAAIJ,kBAEjB,YACCM,GAA4BxnB,EAAAA,EAAAA,KAAQsnB,IAC/B,CACL9J,WAAY8J,EAAI9E,gBAChBxF,SAAUsK,EAAI/E,cACd9E,WAAY6J,EAAI7E,mBAEjB,aACH5G,eAAe4L,EAAaC,EAAU5L,GACpC3a,EAAOwlB,gBAAgB,IACvB,MAAM,OAAEvlB,EAAM,MAAEE,EAAK,QAAE8T,GAAY0G,EAC7BhM,EAAQ8M,EAAAA,GAAe+K,YAAYvS,GAAS3R,OAC5CmkB,GAAavI,EAAAA,EAAAA,IAASjK,GACtByS,EAAWD,QAAmB7E,EAAAA,EAAAA,IAA0B3N,GAASxB,EAAAA,EAAAA,OAAgBkU,EAAAA,GAAcC,wBAAwB3S,EAASiS,EAAYjE,IAClJ,IAAKwE,EAAY,CACf,MAAMI,EAAaH,EAASlnB,OAASmP,EACrCgM,EAASnb,QAAUqnB,EACnB7mB,EAAOwlB,gBAAgBqB,EACzB,CACA,IAAIC,EACAC,EAAcL,EAASlnB,OAAS,GACpC,MAAMwnB,EAAYN,EAASnnB,MAC3B,GAAIU,IAAWE,EAAO,CACpB2mB,EAAa9mB,EAAOolB,iBAAmB2B,EAClC9E,EAAKgF,cACRF,GAAe9E,EAAK5C,UACpByH,EAAa9mB,EAAOolB,iBAAmB2B,GAEzCA,GAAe,GACf,MAAMG,EAAKzL,EAAAA,GAAeuI,OAAOgD,EAAY,EAAG/E,EAAK1iB,MAAQ,GAC7DS,EAAO4jB,OACL3jB,EAASinB,EACTlnB,EAAOolB,iBAAmB,GAAK2B,EAC/B5mB,EAAQ+mB,EACRlnB,EAAOolB,iBAAmB,GAAK2B,EAEnC,MACEA,GAAe9E,EAAK5C,UACpByH,EAAa9mB,EAAOolB,iBAAmB2B,EACvC/mB,EAAO4jB,OAAO3jB,EAAQ6mB,EAAa,GAAI3mB,EAAO2mB,GAMhD,OAJA9mB,EAAOwlB,gBAAgBuB,GACvBpM,EAASnb,QAAUunB,EACnBpM,EAASva,MAAQua,EAASza,OAASya,EAASnb,OAC5CQ,EAAO4jB,OAAOjJ,EAASwM,WAAYxM,EAASza,OAAQya,EAASyM,SAAUzM,EAASva,OACzE0mB,CACT,EACAjoB,EAAAA,EAAAA,IAAOynB,EAAc,gBACrB,IAAIe,IAA8BxoB,EAAAA,EAAAA,KAAO6b,eAAesC,EAAUrC,EAAUmM,EAAYQ,GACtF,MAAM,OAAErnB,EAAM,MAAEE,EAAK,OAAED,EAAM,QAAE+T,EAAO,KAAEhO,EAAI,cAAEK,EAAa,gBAAEG,GAAoBkU,EAC3E+L,EAAWC,EAAAA,GAAcC,wBAAwB3S,EAASiS,EAAYjE,IACtE2D,GAAUnkB,EAAAA,EAAAA,MAChBmkB,EAAQzmB,EAAIc,EACZ2lB,EAAQxmB,EAAIc,EAAS,GACrB0lB,EAAQrmB,MAAQY,EAAQF,EACxB2lB,EAAQ9lB,MAAQ,cAChB8lB,EAAQ7J,GAAK,MACb6J,EAAQnlB,KAAOwT,EACf2R,EAAQvJ,WAAa4F,EAAKxC,kBAC1BmG,EAAQ/J,SAAWoG,EAAKvC,gBACxBkG,EAAQtJ,WAAa2F,EAAKtC,kBAC1BiG,EAAQ9kB,OAASmhB,EAAKsF,aACtB3B,EAAQ3J,OAAS,SACjB2J,EAAQ5kB,WAAaihB,EAAKuF,YAC1B5B,EAAQ7kB,OAAQ,GACZmd,EAAAA,EAAAA,IAAS0H,EAAQnlB,YACbga,EAAUuC,EAAU4I,EAAS,CAAE3lB,SAAQE,QAAOD,OAAQ4mB,IAE5DxmB,EAAS0c,EAAU4I,GAErB,MAAMoB,EAAYN,EAASnnB,MAC3B,IAAImO,EACAzN,IAAWE,EAEXuN,EADEuU,EAAKgF,YACAjK,EAAS/d,OAAO,QAAQC,KAC7B,IACA,MAAMe,KAAU6mB,OAAgB7mB,EAASwb,EAAAA,GAAeuI,OAAO/B,EAAK1iB,MAAQ,EAAGynB,EAAY,QAAQF,EAAa,QAAQ7mB,KAGnH+c,EAAS/d,OAAO,QAAQC,KAC7B,IACA,KAAOe,EAAS,IAAM6mB,EAAa,OAAS7mB,EAAS,IAAM,KAAO6mB,EAAa,IAAM,KAAO7mB,EAAS,IAAM,KAAO6mB,EAAa,IAAM,IAAM7mB,EAAS,KAAO6mB,EAAa,MAI5KpZ,EAAOsP,EAAS/d,OAAO,QACvByO,EAAKxO,KAAK,KAAMe,GAChByN,EAAKxO,KAAK,KAAM4nB,GAChBpZ,EAAKxO,KAAK,KAAMiB,GAChBuN,EAAKxO,KAAK,KAAM4nB,IAEd7gB,IAASqhB,EAAQG,GAAG9gB,SAASsD,QAAUhE,IAASqhB,EAAQG,GAAG9gB,SAASyD,cAAgBnE,IAASqhB,EAAQG,GAAG9gB,SAAS2D,cAAgBrE,IAASqhB,EAAQG,GAAG9gB,SAASmD,aAAe7D,IAASqhB,EAAQG,GAAG9gB,SAASuD,sBAC5MwD,EAAK7M,MAAM,mBAAoB,QAC/B6M,EAAKxO,KAAK,QAAS,iBAEnBwO,EAAKxO,KAAK,QAAS,gBAErB,IAAIwoB,EAAM,GACNzF,EAAK0F,sBACPD,EAAMzR,OAAO2R,SAASC,SAAW,KAAO5R,OAAO2R,SAASE,KAAO7R,OAAO2R,SAASG,SAAW9R,OAAO2R,SAASI,OAC1GN,EAAMA,EAAIhnB,QAAQ,MAAO,OACzBgnB,EAAMA,EAAIhnB,QAAQ,MAAO,QAE3BgN,EAAKxO,KAAK,eAAgB,GAC1BwO,EAAKxO,KAAK,SAAU,QACpBwO,EAAK7M,MAAM,OAAQ,QACfoF,IAASqhB,EAAQG,GAAG9gB,SAASoD,OAAS9D,IAASqhB,EAAQG,GAAG9gB,SAASsD,QACrEyD,EAAKxO,KAAK,aAAc,OAASwoB,EAAM,eAErCzhB,IAASqhB,EAAQG,GAAG9gB,SAASqD,qBAAuB/D,IAASqhB,EAAQG,GAAG9gB,SAASuD,uBACnFwD,EAAKxO,KAAK,eAAgB,OAASwoB,EAAM,eACzCha,EAAKxO,KAAK,aAAc,OAASwoB,EAAM,gBAErCzhB,IAASqhB,EAAQG,GAAG9gB,SAAS0D,aAAepE,IAASqhB,EAAQG,GAAG9gB,SAAS2D,cAC3EoD,EAAKxO,KAAK,aAAc,OAASwoB,EAAM,iBAErCzhB,IAASqhB,EAAQG,GAAG9gB,SAASwD,aAAelE,IAASqhB,EAAQG,GAAG9gB,SAASyD,cAC3EsD,EAAKxO,KAAK,aAAc,OAASwoB,EAAM,gBAErCjhB,GAAmBwb,EAAK5M,uBAC1B3H,EAAKxO,KAAK,eAAgB,OAASwoB,EAAM,oBACzC1K,EAAS/d,OAAO,QAAQC,KAAK,IAAKe,GAAQf,KAAK,IAAK4nB,EAAa,GAAG5nB,KAAK,cAAe,cAAcA,KAAK,YAAa,QAAQA,KAAK,cAAe,UAAUA,KAAK,QAAS,kBAAkBuB,KAAK6F,GAEvM,GAAG,eACC2hB,IAAwCppB,EAAAA,EAAAA,KAAO,SAASme,EAAUzL,EAAQE,EAAeqB,EAAWiM,EAAanN,EAAU2L,GAC7H,IAEI2K,EAFAC,EAAY,EACZC,EAAa,EAEbC,EAAY,EAChB,IAAK,MAAMlL,KAAYrK,EAAW,CAChC,MAAMhM,EAAQyK,EAAO4B,IAAIgK,GACnB/J,EAAMtM,EAAMsM,IACd8U,GAAWA,GAAW9U,IACnBmK,GACHvd,EAAOoiB,OAAOzP,OAAOuV,GAEvBE,GAAcnG,EAAK5C,UAAY6I,EAAQI,QAErClV,GAAOA,GAAO8U,IACX3K,IACHnK,EAAIjU,EAAIgpB,EAAYC,EACpBhV,EAAIhU,EAAI2f,GAEVqJ,GAAchV,EAAIkV,QAEpBxhB,EAAMvH,MAAQuH,EAAMvH,OAAS0iB,EAAK1iB,MAClCuH,EAAMtH,OAASic,EAAAA,GAAeuI,OAAOld,EAAMtH,QAAUyiB,EAAKziB,OAAQyiB,EAAKziB,QACvEsH,EAAMwhB,OAASxhB,EAAMwhB,QAAUrG,EAAKsG,YACpCF,EAAY5M,EAAAA,GAAeuI,OAAOqE,EAAWvhB,EAAMtH,QAC/CiS,EAAc0B,IAAIrM,EAAMrH,QAC1B2oB,GAActhB,EAAMvH,MAAQ,GAE9BuH,EAAM3H,EAAIgpB,EAAYC,EACtBthB,EAAM5G,OAASF,EAAOolB,iBACtBplB,EAAO4jB,OAAO9c,EAAM3H,EAAG4f,EAAajY,EAAM3H,EAAI2H,EAAMvH,MAAOuH,EAAMtH,QACjE2oB,GAAarhB,EAAMvH,MAAQ6oB,EACvBthB,EAAMsM,MACRtM,EAAMsM,IAAI7T,MAAQ4oB,EAAY/U,EAAIkV,OAASxhB,EAAMsM,IAAIjU,GAEvDipB,EAAathB,EAAMwhB,OACnBJ,EAAUphB,EAAMsM,IAChBpT,EAAOoiB,OAAOrP,SAASjM,EACzB,CACIohB,IAAY3K,GACdvd,EAAOoiB,OAAOzP,OAAOuV,GAEvBloB,EAAOwlB,gBAAgB6C,EACzB,GAAG,yBACCG,IAA6B3pB,EAAAA,EAAAA,KAAO6b,eAAesC,EAAUzL,EAAQuB,EAAWyK,GAClF,GAAKA,EAKE,CACL,IAAI8K,EAAY,EAChBroB,EAAOwlB,gBAAiC,EAAjBvD,EAAK5C,WAC5B,IAAK,MAAMlC,KAAYrK,EAAW,CAChC,MAAMhM,EAAQyK,EAAO4B,IAAIgK,GACpBrW,EAAM1G,QACT0G,EAAM1G,MAAQJ,EAAOolB,kBAEvB,MAAM5lB,QAAewiB,EAAgBvD,UAAUzB,EAAUlW,EAAOmb,GAAM,GACtEoG,EAAY5M,EAAAA,GAAeuI,OAAOqE,EAAW7oB,EAC/C,CACAQ,EAAOwlB,gBAAgB6C,EAAYpG,EAAK5C,UAC1C,MAhBE,IAAK,MAAMlC,KAAYrK,EAAW,CAChC,MAAMhM,EAAQyK,EAAO4B,IAAIgK,SACnB6E,EAAgBvD,UAAUzB,EAAUlW,EAAOmb,GAAM,EACzD,CAcJ,GAAG,cACCwG,IAAkC5pB,EAAAA,EAAAA,KAAO,SAASme,EAAUzL,EAAQuB,EAAW4V,GACjF,IAAIL,EAAY,EACZM,EAAW,EACf,IAAK,MAAMxL,KAAYrK,EAAW,CAChC,MAAMhM,EAAQyK,EAAO4B,IAAIgK,GACnBxD,EAAeiP,GAAsB9hB,GACrC+hB,EAAiB7G,EAAgBtI,UACrCsD,EACAlW,EACA6S,EACAsI,EACAA,EAAKpI,WACL6O,GAEEG,EAAerpB,OAAS6oB,IAC1BA,EAAYQ,EAAerpB,QAEzBqpB,EAAetpB,MAAQuH,EAAM3H,EAAIwpB,IACnCA,EAAWE,EAAetpB,MAAQuH,EAAM3H,EAE5C,CACA,MAAO,CAAEkpB,YAAWM,WACtB,GAAG,mBACCzF,IAA0BrkB,EAAAA,EAAAA,KAAO,SAASsnB,IAC5C2C,EAAAA,EAAAA,IAAwB7G,EAAMkE,GAC1BA,EAAI9J,aACN4F,EAAKZ,gBAAkBY,EAAK4D,eAAiB5D,EAAKxC,kBAAoB0G,EAAI9J,YAExE8J,EAAItK,WACNoG,EAAKb,cAAgBa,EAAK6D,aAAe7D,EAAKvC,gBAAkByG,EAAItK,UAElEsK,EAAI7J,aACN2F,EAAKX,gBAAkBW,EAAK8D,eAAiB9D,EAAKtC,kBAAoBwG,EAAI7J,WAE9E,GAAG,WACCgI,IAAmCzlB,EAAAA,EAAAA,KAAO,SAASiI,GACrD,OAAO9G,EAAOmiB,YAAY4G,QAAO,SAASrE,GACxC,OAAOA,EAAW5d,QAAUA,CAC9B,GACF,GAAG,oBACCkiB,IAAmCnqB,EAAAA,EAAAA,KAAO,SAASiI,EAAOyK,GAC5D,MAAM0X,EAAW1X,EAAO4B,IAAIrM,GACtBqb,EAAcmC,GAAiBxd,GAarC,MAAO,CAZMqb,EAAY9B,QACvB,SAASC,EAAKoE,GACZ,OAAOjJ,EAAAA,GAAeqI,OAAOxD,EAAKoE,EAAWzkB,OAC/C,GACAgpB,EAAS9pB,EAAI8pB,EAAS1pB,MAAQ,EAAI,GAEtB4iB,EAAY9B,QACxB,SAASC,EAAKoE,GACZ,OAAOjJ,EAAAA,GAAeuI,OAAO1D,EAAKoE,EAAWvkB,MAC/C,GACA8oB,EAAS9pB,EAAI8pB,EAAS1pB,MAAQ,EAAI,GAGtC,GAAG,oBACH,SAAS2pB,GAAwBC,EAAYxf,EAAKyf,EAAWC,EAAYC,GACvEtpB,EAAOwlB,gBAAgB4D,GACvB,IAAIG,EAAeF,EACnB,GAAI1f,EAAIqJ,IAAMrJ,EAAIsK,SAAWkV,EAAWxf,EAAIqJ,IAAK,CAC/C,MAAMwW,EAAYL,EAAWxf,EAAIqJ,IAAIzT,MAC/BkqB,EAAWvD,EAAYjE,GAC7BtY,EAAIsK,QAAU0S,EAAAA,GAAc+C,UAAU,IAAI/f,EAAIsK,WAAYuV,EAAY,EAAIvH,EAAKuF,YAAaiC,GAC5F9f,EAAIpK,MAAQiqB,EACZ7f,EAAI+I,MAAO,EACX,MAAMgU,EAAWC,EAAAA,GAAcC,wBAAwBjd,EAAIsK,QAASwV,GAC9D1C,EAActL,EAAAA,GAAeuI,OAAO0C,EAASlnB,OAAQyiB,EAAK1C,gBAChEgK,EAAeF,EAAatC,EAC5BnR,EAAAA,GAAIC,MAAM,GAAGkR,OAAiBpd,EAAIsK,UACpC,CACAqV,EAAU3f,GACV3J,EAAOwlB,gBAAgB+D,EACzB,CAEA,SAASI,GAA2BhgB,EAAKgR,EAAUmM,EAAYhX,EAAOyB,EAAQE,EAAeC,GAC3F,SAASkY,EAAmB9iB,EAAO+iB,GAC7B/iB,EAAM3H,EAAIoS,EAAO4B,IAAIxJ,EAAIF,MAAMtK,GACjCa,EAAO4jB,OACLjJ,EAASxa,MAAQ0pB,EACjBlP,EAASza,OACTya,EAAS1a,OACT0a,EAASva,MAAQ0G,EAAMtH,OAAS,EAAIyiB,EAAKgE,YAE3CtL,EAASxa,MAAQwa,EAASxa,MAAQ0pB,IAElC7pB,EAAO4jB,OACLjJ,EAAS1a,OACT0a,EAASza,OACTya,EAASxa,MAAQ0pB,EACjBlP,EAASva,MAAQ0G,EAAMtH,OAAS,EAAIyiB,EAAKgE,YAE3CtL,EAASxa,MAAQwa,EAASxa,MAAQ0pB,EAEtC,CAEA,SAASC,EAAiBhjB,EAAO+iB,GAC3B/iB,EAAM3H,EAAIoS,EAAO4B,IAAIxJ,EAAID,IAAIvK,GAC/Ba,EAAO4jB,OACLjJ,EAAS1a,OAAS4pB,EAClBlP,EAASza,OACTya,EAASxa,MACTwa,EAASva,MAAQ0G,EAAMtH,OAAS,EAAIyiB,EAAKgE,YAE3CtL,EAAS1a,OAAS0a,EAAS1a,OAAS4pB,IAEpC7pB,EAAO4jB,OACLjJ,EAASxa,MACTwa,EAASza,OACTya,EAAS1a,OAAS4pB,EAClBlP,EAASva,MAAQ0G,EAAMtH,OAAS,EAAIyiB,EAAKgE,YAE3CtL,EAAS1a,OAAS0a,EAAS1a,OAAS4pB,EAExC,CAEA,IArBAhrB,EAAAA,EAAAA,IAAO+qB,EAAoB,uBAoB3B/qB,EAAAA,EAAAA,IAAOirB,EAAkB,oBACrBrY,EAAc0B,IAAIxJ,EAAID,KAAOoG,EAAO,CACtC,MAAMhJ,EAAQyK,EAAO4B,IAAIxJ,EAAID,IAE7BkgB,EAAmB9iB,EADc,SAAdA,EAAMb,KAAkBsY,GAA2BzX,EAAMvH,MAAQ,EAAI,GAExFuH,EAAM5G,OAAS4mB,EAAahgB,EAAMtH,OAAS,EAC3CQ,EAAOwlB,gBAAgB1e,EAAMtH,OAAS,EACxC,MAAO,GAAIkS,EAAgByB,IAAIxJ,EAAIF,OAASqG,EAAO,CACjD,MAAMhJ,EAAQyK,EAAO4B,IAAIxJ,EAAIF,MAC7B,GAAIwY,EAAK5E,aAAc,CAErByM,EAAiBhjB,EADgB,SAAdA,EAAMb,KAAkBsY,GAAuBzX,EAAMvH,MAAQ,EAElF,CACAuH,EAAM1G,MAAQ0mB,EAAahgB,EAAMtH,OAAS,EAC1CQ,EAAOwlB,gBAAgB1e,EAAMtH,OAAS,EACxC,MAAO,GAAIkS,EAAgByB,IAAIxJ,EAAID,KAAOoG,EAAO,CAC/C,MAAMhJ,EAAQyK,EAAO4B,IAAIxJ,EAAID,IAC7B,GAAIuY,EAAK5E,aAAc,CAErBuM,EAAmB9iB,EADc,SAAdA,EAAMb,KAAkBsY,GAA2BzX,EAAMvH,MAAQ,EAAI,EAE1F,CACAuH,EAAM1G,MAAQ0mB,EAAahgB,EAAMtH,OAAS,EAC1CQ,EAAOwlB,gBAAgB1e,EAAMtH,OAAS,EACxC,CACF,EAjEAX,EAAAA,EAAAA,IAAOqqB,GAAyB,4BAkEhCrqB,EAAAA,EAAAA,IAAO8qB,GAA4B,8BACnC,IAAI1gB,IAAuBpK,EAAAA,EAAAA,KAAO6b,eAAeqP,EAAO/W,EAAIgX,EAAU1C,GACpE,MAAM,cAAE2C,EAAa,SAAEvU,IAAajD,EAAAA,EAAAA,MAEpC,IAAIyX,EADJjI,EAAOvM,EAEe,YAAlBuU,IACFC,GAAiBhN,EAAAA,EAAAA,KAAO,KAAOlK,IAEjC,MAAMmX,EAAyB,YAAlBF,GAA8B/M,EAAAA,EAAAA,KAAOgN,EAAeE,QAAQ,GAAGC,gBAAgBC,OAAQpN,EAAAA,EAAAA,KAAO,QACrGwL,EAAwB,YAAlBuB,EAA8BC,EAAeE,QAAQ,GAAGC,gBAAkB/S,SACtFtX,EAAO4B,OACPgU,EAAAA,GAAIC,MAAMyR,EAAQG,IAClB,MAAMzK,EAA6B,YAAlBiN,EAA8BE,EAAKjN,OAAO,QAAQlK,QAAUkK,EAAAA,EAAAA,KAAO,QAAQlK,OACtFzB,EAAS+V,EAAQG,GAAG5S,YACpBpD,EAAgB6V,EAAQG,GAAG3S,mBAC3BpD,EAAkB4V,EAAQG,GAAG1S,qBAC7BpD,EAAQ2V,EAAQG,GAAG7S,WACzB,IAAI9B,EAAYwU,EAAQG,GAAGxS,eAC3B,MAAMrD,EAAW0V,EAAQG,GAAG9S,cACtBqB,EAAQsR,EAAQG,GAAGpV,kBACnBkY,EAAWjD,EAAQG,GAAGlT,mBACtBiW,EAAelD,EAAQG,GAAGjT,4BAC1BiW,QAAgCC,GAA2BnZ,EAAQK,EAAU0V,GAWnF,GAVArF,EAAKziB,aAAemrB,GAAsBpZ,EAAQkZ,EAAyB9Y,GAC3EqQ,EAAgBvB,mBAAmBzD,GACnCgF,EAAgBxB,mBAAmBxD,GACnCgF,EAAgBtB,gBAAgB1D,GAC5BuN,IACFvqB,EAAOwlB,gBAAgBvD,EAAK5C,WACxBmL,GACFxqB,EAAOwlB,gBAAgB7T,EAAM,GAAGiN,iBAGA,IAAhCqD,EAAK2I,uBAAiC,CACxC,MAAMC,EAA4B,IAAIC,IACtClZ,EAASkG,SAAS7D,IAChB4W,EAAUE,IAAI9W,EAAQxK,MACtBohB,EAAUE,IAAI9W,EAAQvK,GAAG,IAE3BoJ,EAAYA,EAAUiW,QAAQ5L,GAAa0N,EAAU1S,IAAIgF,IAC3D,CACA8K,GAAsBjL,EAAUzL,EAAQE,EAAeqB,EAAW,EAAGlB,GAAU,GAC/E,MAAMuX,QAAmB6B,GAAoBpZ,EAAUL,EAAQkZ,EAAyBnD,GAKxF,SAAS2D,EAAUthB,EAAKoV,GACtB,MAAMmM,EAAiBlrB,EAAOwkB,cAAc7a,GACxCuhB,EAAehrB,OAAS,GAAK6e,IAC/BmM,EAAehrB,OAAS6e,EAAc,EACtCA,GAAe,IAEjBiD,EAAgBlD,eACd9B,EACAkO,EACAnM,EACAkD,EACAqC,GAAiB3a,EAAIF,MAAMnH,QAE7BtC,EAAO4jB,OAAOsH,EAAejrB,OAAQ8e,EAAc,GAAImM,EAAe/qB,MAAO4e,EAC/E,CAlBAiD,EAAgBrB,gBAAgB3D,GAChCgF,EAAgBlB,qBAAqB9D,GACrCgF,EAAgBpB,sBAAsB5D,GACtCgF,EAAgBnB,qBAAqB7D,IAgBrCne,EAAAA,EAAAA,IAAOosB,EAAW,aAClB,IAAI3kB,EAAgB,EAChBE,EAAoB,EACxB,MAAM2kB,EAAiB,GACjBC,EAAc,GACpB,IAAItb,EAAQ,EACZ,IAAK,MAAMnG,KAAOiI,EAAU,CAC1B,IAAIuN,EAAW0D,EAAWlI,EAC1B,OAAQhR,EAAI1D,MACV,KAAKqhB,EAAQG,GAAG9gB,SAASsK,KACvBjR,EAAOulB,mBACP1C,EAAYlZ,EAAIkZ,gBACV8C,EAAS3I,EAAU6F,GACzB,MACF,KAAKyE,EAAQG,GAAG9gB,SAASE,aACvB7G,EAAOmkB,cAAcxa,EAAKqT,EAAUzL,GACpC,MACF,KAAK+V,EAAQG,GAAG9gB,SAASI,WACvBkkB,EAAUthB,EAAK3J,EAAOolB,kBACtB,MACF,KAAKkC,EAAQG,GAAG9gB,SAASY,WACvB2hB,GACEC,EACAxf,EACAsY,EAAK5C,UACL4C,EAAK5C,UAAY4C,EAAK3C,eACrBrL,GAAYjU,EAAO8kB,QAAQ7Q,KAE9B,MACF,KAAKqT,EAAQG,GAAG9gB,SAASa,SACvB2X,EAAYnf,EAAO+kB,gBACb/C,EAAgB9C,SAASlC,EAAUmC,EAAW,OAAQ8C,GAC5DjiB,EAAOwlB,gBAAgBrG,EAAU/e,MAAQJ,EAAOolB,kBAChDplB,EAAOoiB,OAAOQ,QAAQzD,GACtB,MACF,KAAKmI,EAAQG,GAAG9gB,SAASe,WACvBwhB,GACEC,EACAxf,EACAsY,EAAK5C,UACL4C,EAAK5C,WACJpL,GAAYjU,EAAO8kB,aAAQ,EAAQ7Q,EAAQA,WAE9C,MACF,KAAKqT,EAAQG,GAAG9gB,SAASgB,SACvBwX,EAAYnf,EAAO+kB,UACnBqG,EAAYplB,KAAKmZ,GACjBnf,EAAOoiB,OAAOQ,QAAQzD,GACtBnf,EAAOwlB,gBAAgBrG,EAAU/e,MAAQJ,EAAOolB,kBAChD,MACF,KAAKkC,EAAQG,GAAG9gB,SAASkB,UACvBqhB,GACEC,EACAxf,EACAsY,EAAK5C,UACL4C,EAAK5C,UAAY4C,EAAK3C,eACrBrL,GAAYjU,EAAO8kB,QAAQ7Q,KAE9B,MACF,KAAKqT,EAAQG,GAAG9gB,SAASmB,QACvBqX,EAAYnf,EAAO+kB,gBACb/C,EAAgB9C,SAASlC,EAAUmC,EAAW,MAAO8C,GAC3DjiB,EAAOwlB,gBAAgBrG,EAAU/e,MAAQJ,EAAOolB,kBAChDplB,EAAOoiB,OAAOQ,QAAQzD,GACtB,MACF,KAAKmI,EAAQG,GAAG9gB,SAASqB,UACvBkhB,GACEC,EACAxf,EACAsY,EAAK5C,UACL4C,EAAK5C,UAAY4C,EAAK3C,eACrBrL,GAAYjU,EAAO8kB,QAAQ7Q,KAE9B,MACF,KAAKqT,EAAQG,GAAG9gB,SAASqC,SACvBkgB,GACEC,EACAxf,EACAsY,EAAK5C,UAAY4C,EAAK3C,cACtB2C,EAAK5C,WACJpL,GAAYjU,EAAOklB,iBAAiBjR,KAEvC,MACF,KAAKqT,EAAQG,GAAG9gB,SAASsB,QACvBkX,EAAYnf,EAAO+kB,gBACb/C,EAAgB9C,SAASlC,EAAUmC,EAAW,MAAO8C,GAC3DjiB,EAAOwlB,gBAAgBrG,EAAU/e,MAAQJ,EAAOolB,kBAChDplB,EAAOoiB,OAAOQ,QAAQzD,GACtB,MACF,KAAKmI,EAAQG,GAAG9gB,SAASwB,UACzB,KAAKmf,EAAQG,GAAG9gB,SAAS0B,eACvB6gB,GACEC,EACAxf,EACAsY,EAAK5C,UACL4C,EAAK5C,UAAY4C,EAAK3C,eACrBrL,GAAYjU,EAAO8kB,QAAQ7Q,KAE9BjU,EAAOqlB,kBACP,MACF,KAAKiC,EAAQG,GAAG9gB,SAASoC,QACvBmgB,GACEC,EACAxf,EACAsY,EAAK5C,UAAY4C,EAAK3C,cACtB2C,EAAK5C,WACJpL,GAAYjU,EAAOklB,iBAAiBjR,KAEvC,MACF,KAAKqT,EAAQG,GAAG9gB,SAASyB,QACvB+W,EAAYnf,EAAO+kB,gBACb/C,EAAgB9C,SAASlC,EAAUmC,EAAW,MAAO8C,GAC3DjiB,EAAOwlB,gBAAgBrG,EAAU/e,MAAQJ,EAAOolB,kBAChDplB,EAAOoiB,OAAOQ,QAAQzD,GACtB,MACF,KAAKmI,EAAQG,GAAG9gB,SAASC,WACvBN,EAAgBqD,EAAIsK,QAAQ+D,OAAS1R,EACrCE,EAAoBmD,EAAIsK,QAAQgE,MAAQzR,EACpCmD,EAAIsK,QAAQiE,QACdoP,EAAQG,GAAGtS,wBAEXmS,EAAQG,GAAGrS,yBAEb,MACF,KAAKkS,EAAQG,GAAG9gB,SAAS4B,eACvB2gB,GACEC,EACAxf,EACAsY,EAAK5C,UACL4C,EAAK5C,UAAY4C,EAAK3C,eACrBrL,GAAYjU,EAAO8kB,QAAQ7Q,KAE9B,MACF,KAAKqT,EAAQG,GAAG9gB,SAASmC,gBACvBogB,GACEC,EACAxf,EACAsY,EAAK5C,UAAY4C,EAAK3C,cACtB2C,EAAK5C,WACJpL,GAAYjU,EAAOklB,iBAAiBjR,KAEvC,MACF,KAAKqT,EAAQG,GAAG9gB,SAAS6B,aACvB2W,EAAYnf,EAAO+kB,gBACb/C,EAAgB9C,SAASlC,EAAUmC,EAAW,WAAY8C,GAChEjiB,EAAOwlB,gBAAgBrG,EAAU/e,MAAQJ,EAAOolB,kBAChDplB,EAAOoiB,OAAOQ,QAAQzD,GACtB,MACF,KAAKmI,EAAQG,GAAG9gB,SAAS+B,YACvBwgB,GACEC,EACAxf,EACAsY,EAAK5C,UACL4C,EAAK5C,UAAY4C,EAAK3C,eACrBrL,GAAYjU,EAAO8kB,QAAQ7Q,KAE9B,MACF,KAAKqT,EAAQG,GAAG9gB,SAASgC,UACvBwW,EAAYnf,EAAO+kB,gBACb/C,EAAgB9C,SAASlC,EAAUmC,EAAW,QAAS8C,GAC7DjiB,EAAOwlB,gBAAgBrG,EAAU/e,MAAQJ,EAAOolB,kBAChDplB,EAAOoiB,OAAOQ,QAAQzD,GACtB,MACF,QACE,IACExE,EAAWhR,EAAIgR,SACfA,EAASza,OAASF,EAAOolB,iBACzBzK,EAASrU,cAAgBA,EACzBqU,EAASlU,gBAAkB6gB,EAAQG,GAAGpS,sBACtC,MAAMyR,QAAmBR,EAAatJ,EAAUrC,GAChDgP,GACEhgB,EACAgR,EACAmM,EACAhX,EACAyB,EACAE,EACAC,GAEFyZ,EAAenlB,KAAK,CAAEqlB,aAAc1Q,EAAUmM,eAC9C9mB,EAAOoiB,OAAOtO,WAAW6G,EAC3B,CAAE,MAAO/D,GACPhB,EAAAA,GAAI/K,MAAM,8BAA+B+L,EAC3C,EAEA,CACF0Q,EAAQG,GAAG9gB,SAASkD,WACpByd,EAAQG,GAAG9gB,SAASmD,YACpBwd,EAAQG,GAAG9gB,SAASoD,MACpBud,EAAQG,GAAG9gB,SAASsD,OACpBqd,EAAQG,GAAG9gB,SAASwD,YACpBmd,EAAQG,GAAG9gB,SAASyD,aACpBkd,EAAQG,GAAG9gB,SAAS0D,YACpBid,EAAQG,GAAG9gB,SAAS2D,aACpBgd,EAAQG,GAAG9gB,SAASqD,oBACpBsd,EAAQG,GAAG9gB,SAASuD,sBACpBohB,SAAS3hB,EAAI1D,QACbK,GAAgCE,GAElCsJ,GACF,CACA8F,EAAAA,GAAIC,MAAM,gBAAiBpE,GAC3BmE,EAAAA,GAAIC,MAAM,kBAAmBnE,SACvB8W,GAAWxL,EAAUzL,EAAQuB,GAAW,GAC9C,IAAK,MAAM8D,KAAKuU,QACR9D,GAAYrK,EAAUpG,EAAEyU,aAAczU,EAAEkQ,WAAYQ,GAExDrF,EAAK5E,oBACDmL,GAAWxL,EAAUzL,EAAQuB,GAAW,GAEhDsY,EAAYtT,SAASlB,GAAMoL,EAAgBjiB,mBAAmBid,EAAUpG,KACxEmG,EAAmBC,EAAUzL,EAAQuB,EAAWmP,GAChD,IAAK,MAAMsJ,KAAQvrB,EAAOoiB,OAAOzQ,MAC/B4Z,EAAK/rB,OAASQ,EAAOolB,iBAAmBmG,EAAKnsB,EAC7CY,EAAO4jB,OAAO2H,EAAKpsB,EAAGosB,EAAKnsB,EAAGmsB,EAAKpsB,EAAIosB,EAAKhsB,MAAOgsB,EAAK/rB,QACxD+rB,EAAKtrB,OAASsrB,EAAKpsB,EACnBosB,EAAKrrB,OAASqrB,EAAKnsB,EACnBmsB,EAAKprB,MAAQorB,EAAKtrB,OAASsrB,EAAKhsB,MAChCgsB,EAAKnrB,MAAQmrB,EAAKrrB,OAASqrB,EAAK/rB,OAChC+rB,EAAKjsB,OAAS,kBACd0iB,EAAgBtD,QAAQ1B,EAAUuO,EAAMtJ,GAEtCsI,GACFvqB,EAAOwlB,gBAAgBvD,EAAK5C,WAE9B,MAAMmM,EAAkB/C,GAAgBzL,EAAUzL,EAAQuB,EAAW4V,IAC7D1oB,OAAQoT,GAAQpT,EAAO0lB,iBACZ,IAAftS,EAAInT,SACNmT,EAAInT,OAAS,QAEI,IAAfmT,EAAIlT,SACNkT,EAAIlT,OAAS,QAEG,IAAdkT,EAAIjT,QACNiT,EAAIjT,MAAQ,QAEI,IAAdiT,EAAIhT,QACNgT,EAAIhT,MAAQ,GAEd,IAAIqrB,EAAYrY,EAAIhT,MAAQgT,EAAIlT,OAC5BurB,EAAYD,EAAgBnD,YAC9BoD,EAAYD,EAAgBnD,WAE9B,IAAI7oB,EAASisB,EAAY,EAAIxJ,EAAKyJ,eAC9BzJ,EAAK5E,eACP7d,EAASA,EAASyiB,EAAK5C,UAAY4C,EAAK0J,iBAE1C,IAAIC,EAAWxY,EAAIjT,MAAQiT,EAAInT,OAC3B2rB,EAAWJ,EAAgB7C,WAC7BiD,EAAWJ,EAAgB7C,UAE7B,MAAMppB,EAAQqsB,EAAW,EAAI3J,EAAK4J,eAC9B7V,GACFgH,EAAS/d,OAAO,QAAQwB,KAAKuV,GAAO9W,KAAK,KAAMkU,EAAIjT,MAAQiT,EAAInT,QAAU,EAAI,EAAIgiB,EAAK4J,gBAAgB3sB,KAAK,KAAM,KAEnH4sB,EAAAA,EAAAA,IAAiB9O,EAAUxd,EAAQD,EAAO0iB,EAAK8J,aAC/C,MAAMC,EAAoBhW,EAAQ,GAAK,EACvCgH,EAAS9d,KACP,UACAkU,EAAInT,OAASgiB,EAAK4J,eAAiB,MAAQ5J,EAAKyJ,eAAiBM,GAAqB,IAAMzsB,EAAQ,KAAOC,EAASwsB,IAEtHpW,EAAAA,GAAIC,MAAM,UAAW7V,EAAOoiB,OAC9B,GAAG,QACH1H,eAAegQ,GAA2BnZ,EAAQK,EAAU0V,GAC1D,MAAMmD,EAA0B,CAAC,EACjC,IAAK,MAAM9gB,KAAOiI,EAChB,GAAIL,EAAO4B,IAAIxJ,EAAID,KAAO6H,EAAO4B,IAAIxJ,EAAIF,MAAO,CAC9C,MAAM3C,EAAQyK,EAAO4B,IAAIxJ,EAAID,IAC7B,GAAIC,EAAIR,YAAcme,EAAQG,GAAGpe,UAAUE,SAAWzC,EAAMwK,UAC1D,SAEF,GAAI3H,EAAIR,YAAcme,EAAQG,GAAGpe,UAAUG,UAAY1C,EAAM4M,UAC3D,SAEF,MAAMuY,OAA2B,IAAlBtiB,EAAIR,UACb+iB,GAAaD,EACbE,EAAWF,EAAS7F,EAASnE,GAAQiE,EAAYjE,GACjDmK,EAAiBziB,EAAI+I,KAAOiU,EAAAA,GAAc+C,UAAU/f,EAAIsK,QAASgO,EAAK1iB,MAAQ,EAAI0iB,EAAKuF,YAAa2E,GAAYxiB,EAAIsK,QAEpHoY,IADoBnO,EAAAA,EAAAA,IAASkO,SAAwBxK,EAAAA,EAAAA,IAA0BjY,EAAIsK,SAASxB,EAAAA,EAAAA,OAAgBkU,EAAAA,GAAcC,wBAAwBwF,EAAgBD,IACjI5sB,MAAQ,EAAI0iB,EAAKuF,YACpD0E,GAAaviB,EAAIF,OAAS3C,EAAM4M,UAClC+W,EAAwB9gB,EAAID,IAAM+R,EAAAA,GAAeuI,OAC/CyG,EAAwB9gB,EAAID,KAAO,EACnC2iB,GAEOH,GAAaviB,EAAIF,OAAS3C,EAAMwK,UACzCmZ,EAAwB9gB,EAAIF,MAAQgS,EAAAA,GAAeuI,OACjDyG,EAAwB9gB,EAAIF,OAAS,EACrC4iB,GAEOH,GAAaviB,EAAIF,OAASE,EAAID,IACvC+gB,EAAwB9gB,EAAIF,MAAQgS,EAAAA,GAAeuI,OACjDyG,EAAwB9gB,EAAIF,OAAS,EACrC4iB,EAAe,GAEjB5B,EAAwB9gB,EAAID,IAAM+R,EAAAA,GAAeuI,OAC/CyG,EAAwB9gB,EAAID,KAAO,EACnC2iB,EAAe,IAER1iB,EAAIR,YAAcme,EAAQG,GAAGpe,UAAUG,QAChDihB,EAAwB9gB,EAAIF,MAAQgS,EAAAA,GAAeuI,OACjDyG,EAAwB9gB,EAAIF,OAAS,EACrC4iB,GAEO1iB,EAAIR,YAAcme,EAAQG,GAAGpe,UAAUE,OAChDkhB,EAAwB3jB,EAAMwK,WAAamK,EAAAA,GAAeuI,OACxDyG,EAAwB3jB,EAAMwK,YAAc,EAC5C+a,GAEO1iB,EAAIR,YAAcme,EAAQG,GAAGpe,UAAUC,OAC5CxC,EAAMwK,YACRmZ,EAAwB3jB,EAAMwK,WAAamK,EAAAA,GAAeuI,OACxDyG,EAAwB3jB,EAAMwK,YAAc,EAC5C+a,EAAe,IAGfvlB,EAAM4M,YACR+W,EAAwB9gB,EAAIF,MAAQgS,EAAAA,GAAeuI,OACjDyG,EAAwB9gB,EAAIF,OAAS,EACrC4iB,EAAe,IAIvB,CAGF,OADAzW,EAAAA,GAAIC,MAAM,2BAA4B4U,GAC/BA,CACT,EACA5rB,EAAAA,EAAAA,IAAO6rB,GAA4B,8BACnC,IAAI9B,IAAwC/pB,EAAAA,EAAAA,KAAO,SAASiI,GAC1D,IAAIwlB,EAAqB,EACzB,MAAMH,EAAW9F,EAAUpE,GAC3B,IAAK,MAAMhL,KAAOnQ,EAAMwM,MAAO,CAC7B,MACMiZ,EADkB5F,EAAAA,GAAcC,wBAAwB3P,EAAKkV,GAChC5sB,MAAQ,EAAI0iB,EAAKuF,YAAc,EAAIvF,EAAK5C,UACvEiN,EAAqBC,IACvBD,EAAqBC,EAEzB,CACA,OAAOD,CACT,GAAG,yBACH5R,eAAeiQ,GAAsBpZ,EAAQib,EAAqB7a,GAChE,IAAI0W,EAAY,EAChB,IAAK,MAAMoE,KAAQlb,EAAO2D,OAAQ,CAChC,MAAMpO,EAAQyK,EAAO4B,IAAIsZ,GACrB3lB,EAAM4L,OACR5L,EAAMoC,YAAcyd,EAAAA,GAAc+C,UAChC5iB,EAAMoC,YACN+Y,EAAK1iB,MAAQ,EAAI0iB,EAAKuF,YACtBnB,EAAUpE,KAGd,MAAMyK,GAAUxO,EAAAA,EAAAA,IAASpX,EAAMoC,mBAAqB0Y,EAAAA,EAAAA,IAA0B9a,EAAMoC,aAAauJ,EAAAA,EAAAA,OAAgBkU,EAAAA,GAAcC,wBAAwB9f,EAAMoC,YAAamd,EAAUpE,IACpLnb,EAAMvH,MAAQuH,EAAM4L,KAAOuP,EAAK1iB,MAAQkc,EAAAA,GAAeuI,OAAO/B,EAAK1iB,MAAOmtB,EAAQntB,MAAQ,EAAI0iB,EAAKuF,aACnG1gB,EAAMtH,OAASsH,EAAM4L,KAAO+I,EAAAA,GAAeuI,OAAO0I,EAAQltB,OAAQyiB,EAAKziB,QAAUyiB,EAAKziB,OACtF6oB,EAAY5M,EAAAA,GAAeuI,OAAOqE,EAAWvhB,EAAMtH,OACrD,CACA,IAAK,MAAM2d,KAAYqP,EAAqB,CAC1C,MAAM1lB,EAAQyK,EAAO4B,IAAIgK,GACzB,IAAKrW,EACH,SAEF,MAAM4M,EAAYnC,EAAO4B,IAAIrM,EAAM4M,WACnC,IAAKA,EAAW,CACd,MACMiZ,EADgBH,EAAoBrP,GACN8E,EAAKsG,YAAczhB,EAAMvH,MAAQ,EACrEuH,EAAMwhB,OAAS7M,EAAAA,GAAeuI,OAAO2I,EAAa1K,EAAKsG,aACvD,QACF,CACA,MACMqE,EADeJ,EAAoBrP,GACP8E,EAAKsG,YAAczhB,EAAMvH,MAAQ,EAAImU,EAAUnU,MAAQ,EACzFuH,EAAMwhB,OAAS7M,EAAAA,GAAeuI,OAAO4I,EAAY3K,EAAKsG,YACxD,CACA,IAAIsE,EAAe,EAoBnB,OAnBAlb,EAAMmG,SAAS1E,IACb,MAAM+Y,EAAWjG,EAAYjE,GAC7B,IAAI6K,EAAa1Z,EAAIN,UAAUuN,QAAO,CAAC0M,EAAOC,IACrCD,GAASxb,EAAO4B,IAAI6Z,GAAMztB,OAASgS,EAAO4B,IAAI6Z,GAAM1E,QAAU,KACpE,GACHwE,GAAc,EAAI7K,EAAK3C,cACnBlM,EAAIV,OACNU,EAAI3T,KAAOknB,EAAAA,GAAc+C,UAAUtW,EAAI3T,KAAMqtB,EAAa,EAAI7K,EAAKuF,YAAa2E,IAElF,MAAMc,EAAmBtG,EAAAA,GAAcC,wBAAwBxT,EAAI3T,KAAM0sB,GACzEU,EAAepR,EAAAA,GAAeuI,OAAOiJ,EAAiBztB,OAAQqtB,GAC9D,MAAMK,EAAWzR,EAAAA,GAAeuI,OAAO8I,EAAYG,EAAiB1tB,MAAQ,EAAI0iB,EAAKuF,aAErF,GADApU,EAAIkV,OAASrG,EAAK3C,cACdwN,EAAaI,EAAU,CACzB,MAAMC,GAAWD,EAAWJ,GAAc,EAC1C1Z,EAAIkV,QAAU6E,CAChB,KAEFxb,EAAMmG,SAAS1E,GAAQA,EAAIwL,cAAgBiO,IACpCpR,EAAAA,GAAeuI,OAAOqE,EAAWpG,EAAKziB,OAC/C,EACAX,EAAAA,EAAAA,IAAO8rB,GAAuB,yBAC9B,IAAIyC,IAAiCvuB,EAAAA,EAAAA,KAAO6b,eAAe/Q,EAAK4H,EAAQ+V,GACtE,MAAM+F,EAAY9b,EAAO4B,IAAIxJ,EAAIF,MAC3B6jB,EAAU/b,EAAO4B,IAAIxJ,EAAID,IACzBzJ,EAASotB,EAAUluB,EACnBgB,EAAQmtB,EAAQnuB,EAChBouB,EAAa5jB,EAAI+I,MAAQ/I,EAAIsK,QACnC,IAAIuZ,GAAiBtP,EAAAA,EAAAA,IAASvU,EAAIsK,eAAiB2N,EAAAA,EAAAA,IAA0BjY,EAAIsK,SAASxB,EAAAA,EAAAA,OAAgBkU,EAAAA,GAAcC,wBACtH2G,EAAa5G,EAAAA,GAAc+C,UAAU/f,EAAIsK,QAASgO,EAAK1iB,MAAO6mB,EAASnE,IAAStY,EAAIsK,QACpFmS,EAASnE,IAEX,MAAMY,EAAY,CAChBtjB,MAAOguB,EAAatL,EAAK1iB,MAAQkc,EAAAA,GAAeuI,OAAO/B,EAAK1iB,MAAOiuB,EAAejuB,MAAQ,EAAI0iB,EAAKgE,YACnGzmB,OAAQ,EACRS,OAAQotB,EAAUluB,EAClBgB,MAAO,EACPD,OAAQ,EACRE,MAAO,EACP6T,QAAStK,EAAIsK,SAmCf,OAjCItK,EAAIR,YAAcme,EAAQG,GAAGpe,UAAUG,SACzCqZ,EAAUtjB,MAAQguB,EAAa9R,EAAAA,GAAeuI,OAAO/B,EAAK1iB,MAAOiuB,EAAejuB,OAASkc,EAAAA,GAAeuI,OACtGqJ,EAAU9tB,MAAQ,EAAI+tB,EAAQ/tB,MAAQ,EACtCiuB,EAAejuB,MAAQ,EAAI0iB,EAAKgE,YAElCpD,EAAU5iB,OAASA,GAAUotB,EAAU9tB,MAAQ0iB,EAAKsG,aAAe,GAC1D5e,EAAIR,YAAcme,EAAQG,GAAGpe,UAAUE,QAChDsZ,EAAUtjB,MAAQguB,EAAa9R,EAAAA,GAAeuI,OAAO/B,EAAK1iB,MAAOiuB,EAAejuB,MAAQ,EAAI0iB,EAAKgE,YAAcxK,EAAAA,GAAeuI,OAC5HqJ,EAAU9tB,MAAQ,EAAI+tB,EAAQ/tB,MAAQ,EACtCiuB,EAAejuB,MAAQ,EAAI0iB,EAAKgE,YAElCpD,EAAU5iB,OAASA,EAAS4iB,EAAUtjB,OAAS8tB,EAAU9tB,MAAQ0iB,EAAKsG,aAAe,GAC5E5e,EAAID,KAAOC,EAAIF,MACxB+jB,EAAiB7G,EAAAA,GAAcC,wBAC7B2G,EAAa5G,EAAAA,GAAc+C,UAAU/f,EAAIsK,QAASwH,EAAAA,GAAeuI,OAAO/B,EAAK1iB,MAAO8tB,EAAU9tB,OAAQ6mB,EAASnE,IAAStY,EAAIsK,QAC5HmS,EAASnE,IAEXY,EAAUtjB,MAAQguB,EAAa9R,EAAAA,GAAeuI,OAAO/B,EAAK1iB,MAAO8tB,EAAU9tB,OAASkc,EAAAA,GAAeuI,OAAOqJ,EAAU9tB,MAAO0iB,EAAK1iB,MAAOiuB,EAAejuB,MAAQ,EAAI0iB,EAAKgE,YACvKpD,EAAU5iB,OAASA,GAAUotB,EAAU9tB,MAAQsjB,EAAUtjB,OAAS,IAElEsjB,EAAUtjB,MAAQiR,KAAKC,IAAIxQ,EAASotB,EAAU9tB,MAAQ,GAAKY,EAAQmtB,EAAQ/tB,MAAQ,IAAM0iB,EAAKsG,YAC9F1F,EAAU5iB,OAASA,EAASE,EAAQF,EAASotB,EAAU9tB,MAAQ,EAAI0iB,EAAKsG,YAAc,EAAIpoB,EAAQmtB,EAAQ/tB,MAAQ,EAAI0iB,EAAKsG,YAAc,GAEvIgF,IACF1K,EAAU5O,QAAU0S,EAAAA,GAAc+C,UAChC/f,EAAIsK,QACJ4O,EAAUtjB,MAAQ,EAAI0iB,EAAKuF,YAC3BpB,EAASnE,KAGbrM,EAAAA,GAAIC,MACF,OAAOgN,EAAU5iB,UAAU4iB,EAAU1iB,SAAS0iB,EAAU3iB,UAAU2iB,EAAUziB,SAASyiB,EAAUtjB,SAASsjB,EAAUrjB,UAAUmK,EAAIsK,YAE3H4O,CACT,GAAG,kBACC4K,IAAoC5uB,EAAAA,EAAAA,KAAO,SAAS8K,EAAK4H,EAAQ+V,GACnE,IAAK,CACHA,EAAQG,GAAG9gB,SAASkD,WACpByd,EAAQG,GAAG9gB,SAASmD,YACpBwd,EAAQG,GAAG9gB,SAASoD,MACpBud,EAAQG,GAAG9gB,SAASsD,OACpBqd,EAAQG,GAAG9gB,SAASwD,YACpBmd,EAAQG,GAAG9gB,SAASyD,aACpBkd,EAAQG,GAAG9gB,SAAS0D,YACpBid,EAAQG,GAAG9gB,SAAS2D,aACpBgd,EAAQG,GAAG9gB,SAASqD,oBACpBsd,EAAQG,GAAG9gB,SAASuD,sBACpBohB,SAAS3hB,EAAI1D,MACb,MAAO,CAAC,EAEV,MAAOynB,EAAUC,GAAa3E,GAAiBrf,EAAIF,KAAM8H,IAClDqc,EAAQC,GAAW7E,GAAiBrf,EAAID,GAAI6H,GAC7Cuc,EAAiBJ,GAAYE,EACnC,IAAI3tB,EAAS6tB,EAAiBH,EAAYD,EACtCvtB,EAAQ2tB,EAAiBF,EAASC,EACtC,MAAME,EAAsBvd,KAAKC,IAAImd,EAASC,GAAW,EACnDG,GAA8BnvB,EAAAA,EAAAA,KAAQovB,GACnCH,GAAkBG,EAAQA,GAChC,eACCtkB,EAAIF,OAASE,EAAID,GACnBvJ,EAAQF,GAEJ0J,EAAIC,WAAamkB,IACnB5tB,GAAS6tB,EAAY/L,EAAKsC,gBAAkB,EAAI,IAE7C,CAAC+C,EAAQG,GAAG9gB,SAASkD,WAAYyd,EAAQG,GAAG9gB,SAASmD,aAAawhB,SAAS3hB,EAAI1D,QAClF9F,GAAS6tB,EAAY,IAEnB,CAAC1G,EAAQG,GAAG9gB,SAASqD,oBAAqBsd,EAAQG,GAAG9gB,SAASuD,sBAAsBohB,SACtF3hB,EAAI1D,QAEJhG,GAAU+tB,EAAY,KAG1B,MAAME,EAAY,CAACR,EAAUC,EAAWC,EAAQC,GAC1CM,EAAe3d,KAAKC,IAAIxQ,EAASE,GACnCwJ,EAAI+I,MAAQ/I,EAAIsK,UAClBtK,EAAIsK,QAAU0S,EAAAA,GAAc+C,UAC1B/f,EAAIsK,QACJwH,EAAAA,GAAeuI,OAAOmK,EAAe,EAAIlM,EAAKuF,YAAavF,EAAK1iB,OAChE2mB,EAAYjE,KAGhB,MAAMmM,EAAUzH,EAAAA,GAAcC,wBAAwBjd,EAAIsK,QAASiS,EAAYjE,IAC/E,MAAO,CACL1iB,MAAOkc,EAAAA,GAAeuI,OACpBra,EAAI+I,KAAO,EAAI0b,EAAQ7uB,MAAQ,EAAI0iB,EAAKuF,YACxC2G,EAAe,EAAIlM,EAAKuF,YACxBvF,EAAK1iB,OAEPC,OAAQ,EACRS,SACAE,QACAD,OAAQ,EACRE,MAAO,EACP6T,QAAStK,EAAIsK,QACbhO,KAAM0D,EAAI1D,KACVyM,KAAM/I,EAAI+I,KACVyU,WAAY3W,KAAKmT,IAAI7d,MAAM,KAAMooB,GACjC9G,SAAU5W,KAAK8R,IAAIxc,MAAM,KAAMooB,GAEnC,GAAG,qBACClD,IAAsCnsB,EAAAA,EAAAA,KAAO6b,eAAe9I,EAAUL,EAAQ8c,EAAmB/G,GACnG,MAAM/E,EAAQ,CAAC,EACTrX,EAAQ,GACd,IAAIojB,EAASzL,EAAWlI,EACxB,IAAK,MAAMhR,KAAOiI,EAAU,CAC1B,OAAQjI,EAAI1D,MACV,KAAKqhB,EAAQG,GAAG9gB,SAASY,WACzB,KAAK+f,EAAQG,GAAG9gB,SAASqB,UACzB,KAAKsf,EAAQG,GAAG9gB,SAASkB,UACzB,KAAKyf,EAAQG,GAAG9gB,SAASwB,UACzB,KAAKmf,EAAQG,GAAG9gB,SAAS0B,eACzB,KAAKif,EAAQG,GAAG9gB,SAAS4B,eACzB,KAAK+e,EAAQG,GAAG9gB,SAAS+B,YACvBwC,EAAMlF,KAAK,CACTgN,GAAIrJ,EAAIqJ,GACRrJ,IAAKA,EAAIsK,QACTxK,KAAMlD,OAAOgoB,iBACb7kB,GAAInD,OAAOioB,iBACXjvB,MAAO,IAET,MACF,KAAK+nB,EAAQG,GAAG9gB,SAASqC,SACzB,KAAKse,EAAQG,GAAG9gB,SAASoC,QACzB,KAAKue,EAAQG,GAAG9gB,SAASmC,gBACnBa,EAAIsK,UACNqa,EAAUpjB,EAAMsB,MAChB+V,EAAM+L,EAAQtb,IAAMsb,EACpB/L,EAAM5Y,EAAIqJ,IAAMsb,EAChBpjB,EAAMlF,KAAKsoB,IAEb,MACF,KAAKhH,EAAQG,GAAG9gB,SAASa,SACzB,KAAK8f,EAAQG,GAAG9gB,SAASsB,QACzB,KAAKqf,EAAQG,GAAG9gB,SAASmB,QACzB,KAAKwf,EAAQG,GAAG9gB,SAASyB,QACzB,KAAKkf,EAAQG,GAAG9gB,SAAS6B,aACzB,KAAK8e,EAAQG,GAAG9gB,SAASgC,UACvB2lB,EAAUpjB,EAAMsB,MAChB+V,EAAM+L,EAAQtb,IAAMsb,EACpB,MACF,KAAKhH,EAAQG,GAAG9gB,SAASE,aACvB,CACE,MAAMud,EAAY7S,EAAO4B,IAAIxJ,EAAIF,KAAOE,EAAIF,KAAOE,EAAID,GAAG5C,OACpDud,EAAcC,GAAiB3a,EAAIF,KAAOE,EAAIF,KAAOE,EAAID,GAAG5C,OAAOxE,OACnEnD,EAAIilB,EAAUjlB,EAAIilB,EAAU7kB,MAAQ,GAAK8kB,EAAc,GAAKpC,EAAKsC,gBAAkB,EACnFkK,EAAQ,CACZxuB,OAAQd,EACRgB,MAAOhB,EAAI8iB,EAAKsC,gBAChBzd,MAAO6C,EAAIF,KACXilB,SAAS,GAEX1uB,EAAOmiB,YAAYnc,KAAKyoB,EAC1B,CACA,MACF,KAAKnH,EAAQG,GAAG9gB,SAASI,WACvB,CACE,MAAM0d,EAAyBzkB,EAAOmiB,YAAYhC,KAAKwO,GAAMA,EAAE7nB,QAAO6d,YAAYhb,EAAIF,MACtFzJ,EAAOmiB,YAAYyC,OAAOH,EAAwB,GAAGG,OAAO,EAAG,EACjE,OAG6B,IAAlBjb,EAAIR,WAEjB0Z,QAAkBuK,GAAezjB,EAAK4H,EAAQ+V,GAC9C3d,EAAIkZ,UAAYA,EAChB3X,EAAM4M,SAAS8W,IACbN,EAAUM,EACVN,EAAQ7kB,KAAOgS,EAAAA,GAAeqI,OAAOwK,EAAQ7kB,KAAMoZ,EAAU5iB,QAC7DquB,EAAQ5kB,GAAK+R,EAAAA,GAAeuI,OAAOsK,EAAQ5kB,GAAImZ,EAAU5iB,OAAS4iB,EAAUtjB,OAC5E+uB,EAAQ/uB,MAAQkc,EAAAA,GAAeuI,OAAOsK,EAAQ/uB,MAAOiR,KAAKC,IAAI6d,EAAQ7kB,KAAO6kB,EAAQ5kB,KAAOuY,EAAKzC,aAAa,MAGhH7E,EAAW8S,GAAkB9jB,EAAK4H,EAAQ+V,GAC1C3d,EAAIgR,SAAWA,EACXA,EAAS1a,QAAU0a,EAASxa,OAAS+K,EAAM5I,OAAS,GACtD4I,EAAM4M,SAAS8W,IAEb,GADAN,EAAUM,EACNjU,EAAS1a,SAAW0a,EAASxa,MAAO,CACtC,MAAMsJ,EAAO8H,EAAO4B,IAAIxJ,EAAIF,MACtBC,EAAK6H,EAAO4B,IAAIxJ,EAAID,IAC1B4kB,EAAQ7kB,KAAOgS,EAAAA,GAAeqI,OAC5Bra,EAAKtK,EAAIwb,EAASpb,MAAQ,EAC1BkK,EAAKtK,EAAIsK,EAAKlK,MAAQ,EACtB+uB,EAAQ7kB,MAEV6kB,EAAQ5kB,GAAK+R,EAAAA,GAAeuI,OAC1Bta,EAAGvK,EAAIwb,EAASpb,MAAQ,EACxBmK,EAAGvK,EAAIsK,EAAKlK,MAAQ,EACpB+uB,EAAQ5kB,IAEV4kB,EAAQ/uB,MAAQkc,EAAAA,GAAeuI,OAAOsK,EAAQ/uB,MAAOiR,KAAKC,IAAI6d,EAAQ5kB,GAAK4kB,EAAQ7kB,OAASwY,EAAKzC,aACnG,MACE8O,EAAQ7kB,KAAOgS,EAAAA,GAAeqI,OAAOnJ,EAAS1a,OAAQquB,EAAQ7kB,MAC9D6kB,EAAQ5kB,GAAK+R,EAAAA,GAAeuI,OAAOrJ,EAASxa,MAAOmuB,EAAQ5kB,IAC3D4kB,EAAQ/uB,MAAQkc,EAAAA,GAAeuI,OAAOsK,EAAQ/uB,MAAOob,EAASpb,OAAS0iB,EAAKzC,aAC9E,IAIR,CAGA,OAFAxf,EAAOmiB,YAAc,GACrBvM,EAAAA,GAAIC,MAAM,oBAAqB0M,GACxBA,CACT,GAAG,uBACCsM,GAA2B,CAC7B7uB,SACAwoB,cACAC,mBACAvF,WACAja,SAIE6lB,GAAU,CACZ9sB,OAAQgP,EACR,MAAIyW,GACF,OAAO,IAAIpW,CACb,EACA0d,SAAUF,GACVG,OAAQ3W,EACRzW,MAAsB/C,EAAAA,EAAAA,KAAQsnB,IACvBA,EAAIzQ,WACPyQ,EAAIzQ,SAAW,CAAC,GAEdyQ,EAAIzT,OACNyT,EAAIzQ,SAAShD,KAAOyT,EAAIzT,MACxBuc,EAAAA,EAAAA,IAAU,CAAEvZ,SAAU,CAAEhD,KAAMyT,EAAIzT,QACpC,GACC,Q", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-D6G4REZN.mjs", "../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-XZIHB7SX.mjs", "../../node_modules/mermaid/dist/chunks/mermaid.core/sequenceDiagram-X6HHIX6F.mjs"], "sourcesContent": ["import {\n  __name,\n  lineBreakRegex\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/common/svgDrawCommon.ts\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nvar drawRect = /* @__PURE__ */ __name((element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr(\"rx\", rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr(\"ry\", rectData.ry);\n  }\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  if (rectData.class) {\n    rectElement.attr(\"class\", rectData.class);\n  }\n  return rectElement;\n}, \"drawRect\");\nvar drawBackgroundRect = /* @__PURE__ */ __name((element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n}, \"drawBackgroundRect\");\nvar drawText = /* @__PURE__ */ __name((element, textData) => {\n  const nText = textData.text.replace(lineBreakRegex, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawImage = /* @__PURE__ */ __name((elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = sanitizeUrl(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawEmbeddedImage = /* @__PURE__ */ __name((element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = sanitizeUrl(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n}, \"drawEmbeddedImage\");\nvar getNoteRect = /* @__PURE__ */ __name(() => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n}, \"getNoteRect\");\nvar getTextObj = /* @__PURE__ */ __name(() => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n}, \"getTextObj\");\n\nexport {\n  drawRect,\n  drawBackgroundRect,\n  drawText,\n  drawImage,\n  drawEmbeddedImage,\n  getNoteRect,\n  getTextObj\n};\n", "import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/utils/imperativeState.ts\nvar ImperativeState = class {\n  /**\n   * @param init - Function that creates the default state.\n   */\n  constructor(init) {\n    this.init = init;\n    this.records = this.init();\n  }\n  static {\n    __name(this, \"ImperativeState\");\n  }\n  reset() {\n    this.records = this.init();\n  }\n};\n\nexport {\n  ImperativeState\n};\n", "import {\n  drawBackgroundRect,\n  drawEmbeddedImage,\n  drawImage,\n  drawRect,\n  getNoteRect,\n  getTextObj\n} from \"./chunk-D6G4REZN.mjs\";\nimport {\n  ImperativeState\n} from \"./chunk-XZIHB7SX.mjs\";\nimport {\n  ZERO_WIDTH_SPACE,\n  parseFontSize,\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  assignWithDepth_default,\n  calculateMathMLDimensions,\n  clear,\n  common_default,\n  configureSvgSize,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getConfig2,\n  getDiagramTitle,\n  hasKatex,\n  log,\n  renderKatex,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle,\n  setConfig2 as setConfig,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/sequence/parser/sequenceDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 2], $V1 = [1, 3], $V2 = [1, 4], $V3 = [2, 4], $V4 = [1, 9], $V5 = [1, 11], $V6 = [1, 13], $V7 = [1, 14], $V8 = [1, 16], $V9 = [1, 17], $Va = [1, 18], $Vb = [1, 24], $Vc = [1, 25], $Vd = [1, 26], $Ve = [1, 27], $Vf = [1, 28], $Vg = [1, 29], $Vh = [1, 30], $Vi = [1, 31], $Vj = [1, 32], $Vk = [1, 33], $Vl = [1, 34], $Vm = [1, 35], $Vn = [1, 36], $Vo = [1, 37], $Vp = [1, 38], $Vq = [1, 39], $Vr = [1, 41], $Vs = [1, 42], $Vt = [1, 43], $Vu = [1, 44], $Vv = [1, 45], $Vw = [1, 46], $Vx = [1, 4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 47, 48, 49, 50, 52, 53, 54, 59, 60, 61, 62, 70], $Vy = [4, 5, 16, 50, 52, 53], $Vz = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VA = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 49, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VB = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 48, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VC = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 47, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VD = [68, 69, 70], $VE = [1, 122];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SPACE\": 4, \"NEWLINE\": 5, \"SD\": 6, \"document\": 7, \"line\": 8, \"statement\": 9, \"box_section\": 10, \"box_line\": 11, \"participant_statement\": 12, \"create\": 13, \"box\": 14, \"restOfLine\": 15, \"end\": 16, \"signal\": 17, \"autonumber\": 18, \"NUM\": 19, \"off\": 20, \"activate\": 21, \"actor\": 22, \"deactivate\": 23, \"note_statement\": 24, \"links_statement\": 25, \"link_statement\": 26, \"properties_statement\": 27, \"details_statement\": 28, \"title\": 29, \"legacy_title\": 30, \"acc_title\": 31, \"acc_title_value\": 32, \"acc_descr\": 33, \"acc_descr_value\": 34, \"acc_descr_multiline_value\": 35, \"loop\": 36, \"rect\": 37, \"opt\": 38, \"alt\": 39, \"else_sections\": 40, \"par\": 41, \"par_sections\": 42, \"par_over\": 43, \"critical\": 44, \"option_sections\": 45, \"break\": 46, \"option\": 47, \"and\": 48, \"else\": 49, \"participant\": 50, \"AS\": 51, \"participant_actor\": 52, \"destroy\": 53, \"note\": 54, \"placement\": 55, \"text2\": 56, \"over\": 57, \"actor_pair\": 58, \"links\": 59, \"link\": 60, \"properties\": 61, \"details\": 62, \"spaceList\": 63, \",\": 64, \"left_of\": 65, \"right_of\": 66, \"signaltype\": 67, \"+\": 68, \"-\": 69, \"ACTOR\": 70, \"SOLID_OPEN_ARROW\": 71, \"DOTTED_OPEN_ARROW\": 72, \"SOLID_ARROW\": 73, \"BIDIRECTIONAL_SOLID_ARROW\": 74, \"DOTTED_ARROW\": 75, \"BIDIRECTIONAL_DOTTED_ARROW\": 76, \"SOLID_CROSS\": 77, \"DOTTED_CROSS\": 78, \"SOLID_POINT\": 79, \"DOTTED_POINT\": 80, \"TXT\": 81, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SPACE\", 5: \"NEWLINE\", 6: \"SD\", 13: \"create\", 14: \"box\", 15: \"restOfLine\", 16: \"end\", 18: \"autonumber\", 19: \"NUM\", 20: \"off\", 21: \"activate\", 23: \"deactivate\", 29: \"title\", 30: \"legacy_title\", 31: \"acc_title\", 32: \"acc_title_value\", 33: \"acc_descr\", 34: \"acc_descr_value\", 35: \"acc_descr_multiline_value\", 36: \"loop\", 37: \"rect\", 38: \"opt\", 39: \"alt\", 41: \"par\", 43: \"par_over\", 44: \"critical\", 46: \"break\", 47: \"option\", 48: \"and\", 49: \"else\", 50: \"participant\", 51: \"AS\", 52: \"participant_actor\", 53: \"destroy\", 54: \"note\", 57: \"over\", 59: \"links\", 60: \"link\", 61: \"properties\", 62: \"details\", 64: \",\", 65: \"left_of\", 66: \"right_of\", 68: \"+\", 69: \"-\", 70: \"ACTOR\", 71: \"SOLID_OPEN_ARROW\", 72: \"DOTTED_OPEN_ARROW\", 73: \"SOLID_ARROW\", 74: \"BIDIRECTIONAL_SOLID_ARROW\", 75: \"DOTTED_ARROW\", 76: \"BIDIRECTIONAL_DOTTED_ARROW\", 77: \"SOLID_CROSS\", 78: \"DOTTED_CROSS\", 79: \"SOLID_POINT\", 80: \"DOTTED_POINT\", 81: \"TXT\" },\n    productions_: [0, [3, 2], [3, 2], [3, 2], [7, 0], [7, 2], [8, 2], [8, 1], [8, 1], [10, 0], [10, 2], [11, 2], [11, 1], [11, 1], [9, 1], [9, 2], [9, 4], [9, 2], [9, 4], [9, 3], [9, 3], [9, 2], [9, 3], [9, 3], [9, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [45, 1], [45, 4], [42, 1], [42, 4], [40, 1], [40, 4], [12, 5], [12, 3], [12, 5], [12, 3], [12, 3], [24, 4], [24, 4], [25, 3], [26, 3], [27, 3], [28, 3], [63, 2], [63, 1], [58, 3], [58, 1], [55, 1], [55, 1], [17, 5], [17, 5], [17, 4], [22, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [56, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.apply($$[$0]);\n          return $$[$0];\n          break;\n        case 4:\n        case 9:\n          this.$ = [];\n          break;\n        case 5:\n        case 10:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 6:\n        case 7:\n        case 11:\n        case 12:\n          this.$ = $$[$0];\n          break;\n        case 8:\n        case 13:\n          this.$ = [];\n          break;\n        case 15:\n          $$[$0].type = \"createParticipant\";\n          this.$ = $$[$0];\n          break;\n        case 16:\n          $$[$0 - 1].unshift({ type: \"boxStart\", boxData: yy.parseBoxData($$[$0 - 2]) });\n          $$[$0 - 1].push({ type: \"boxEnd\", boxText: $$[$0 - 2] });\n          this.$ = $$[$0 - 1];\n          break;\n        case 18:\n          this.$ = { type: \"sequenceIndex\", sequenceIndex: Number($$[$0 - 2]), sequenceIndexStep: Number($$[$0 - 1]), sequenceVisible: true, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 19:\n          this.$ = { type: \"sequenceIndex\", sequenceIndex: Number($$[$0 - 1]), sequenceIndexStep: 1, sequenceVisible: true, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 20:\n          this.$ = { type: \"sequenceIndex\", sequenceVisible: false, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 21:\n          this.$ = { type: \"sequenceIndex\", sequenceVisible: true, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 22:\n          this.$ = { type: \"activeStart\", signalType: yy.LINETYPE.ACTIVE_START, actor: $$[$0 - 1].actor };\n          break;\n        case 23:\n          this.$ = { type: \"activeEnd\", signalType: yy.LINETYPE.ACTIVE_END, actor: $$[$0 - 1].actor };\n          break;\n        case 29:\n          yy.setDiagramTitle($$[$0].substring(6));\n          this.$ = $$[$0].substring(6);\n          break;\n        case 30:\n          yy.setDiagramTitle($$[$0].substring(7));\n          this.$ = $$[$0].substring(7);\n          break;\n        case 31:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 32:\n        case 33:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 34:\n          $$[$0 - 1].unshift({ type: \"loopStart\", loopText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.LOOP_START });\n          $$[$0 - 1].push({ type: \"loopEnd\", loopText: $$[$0 - 2], signalType: yy.LINETYPE.LOOP_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 35:\n          $$[$0 - 1].unshift({ type: \"rectStart\", color: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.RECT_START });\n          $$[$0 - 1].push({ type: \"rectEnd\", color: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.RECT_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 36:\n          $$[$0 - 1].unshift({ type: \"optStart\", optText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.OPT_START });\n          $$[$0 - 1].push({ type: \"optEnd\", optText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.OPT_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 37:\n          $$[$0 - 1].unshift({ type: \"altStart\", altText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.ALT_START });\n          $$[$0 - 1].push({ type: \"altEnd\", signalType: yy.LINETYPE.ALT_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 38:\n          $$[$0 - 1].unshift({ type: \"parStart\", parText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.PAR_START });\n          $$[$0 - 1].push({ type: \"parEnd\", signalType: yy.LINETYPE.PAR_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 39:\n          $$[$0 - 1].unshift({ type: \"parStart\", parText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.PAR_OVER_START });\n          $$[$0 - 1].push({ type: \"parEnd\", signalType: yy.LINETYPE.PAR_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 40:\n          $$[$0 - 1].unshift({ type: \"criticalStart\", criticalText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.CRITICAL_START });\n          $$[$0 - 1].push({ type: \"criticalEnd\", signalType: yy.LINETYPE.CRITICAL_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 41:\n          $$[$0 - 1].unshift({ type: \"breakStart\", breakText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.BREAK_START });\n          $$[$0 - 1].push({ type: \"breakEnd\", optText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.BREAK_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 43:\n          this.$ = $$[$0 - 3].concat([{ type: \"option\", optionText: yy.parseMessage($$[$0 - 1]), signalType: yy.LINETYPE.CRITICAL_OPTION }, $$[$0]]);\n          break;\n        case 45:\n          this.$ = $$[$0 - 3].concat([{ type: \"and\", parText: yy.parseMessage($$[$0 - 1]), signalType: yy.LINETYPE.PAR_AND }, $$[$0]]);\n          break;\n        case 47:\n          this.$ = $$[$0 - 3].concat([{ type: \"else\", altText: yy.parseMessage($$[$0 - 1]), signalType: yy.LINETYPE.ALT_ELSE }, $$[$0]]);\n          break;\n        case 48:\n          $$[$0 - 3].draw = \"participant\";\n          $$[$0 - 3].type = \"addParticipant\";\n          $$[$0 - 3].description = yy.parseMessage($$[$0 - 1]);\n          this.$ = $$[$0 - 3];\n          break;\n        case 49:\n          $$[$0 - 1].draw = \"participant\";\n          $$[$0 - 1].type = \"addParticipant\";\n          this.$ = $$[$0 - 1];\n          break;\n        case 50:\n          $$[$0 - 3].draw = \"actor\";\n          $$[$0 - 3].type = \"addParticipant\";\n          $$[$0 - 3].description = yy.parseMessage($$[$0 - 1]);\n          this.$ = $$[$0 - 3];\n          break;\n        case 51:\n          $$[$0 - 1].draw = \"actor\";\n          $$[$0 - 1].type = \"addParticipant\";\n          this.$ = $$[$0 - 1];\n          break;\n        case 52:\n          $$[$0 - 1].type = \"destroyParticipant\";\n          this.$ = $$[$0 - 1];\n          break;\n        case 53:\n          this.$ = [$$[$0 - 1], { type: \"addNote\", placement: $$[$0 - 2], actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 54:\n          $$[$0 - 2] = [].concat($$[$0 - 1], $$[$0 - 1]).slice(0, 2);\n          $$[$0 - 2][0] = $$[$0 - 2][0].actor;\n          $$[$0 - 2][1] = $$[$0 - 2][1].actor;\n          this.$ = [$$[$0 - 1], { type: \"addNote\", placement: yy.PLACEMENT.OVER, actor: $$[$0 - 2].slice(0, 2), text: $$[$0] }];\n          break;\n        case 55:\n          this.$ = [$$[$0 - 1], { type: \"addLinks\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 56:\n          this.$ = [$$[$0 - 1], { type: \"addALink\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 57:\n          this.$ = [$$[$0 - 1], { type: \"addProperties\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 58:\n          this.$ = [$$[$0 - 1], { type: \"addDetails\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 61:\n          this.$ = [$$[$0 - 2], $$[$0]];\n          break;\n        case 62:\n          this.$ = $$[$0];\n          break;\n        case 63:\n          this.$ = yy.PLACEMENT.LEFTOF;\n          break;\n        case 64:\n          this.$ = yy.PLACEMENT.RIGHTOF;\n          break;\n        case 65:\n          this.$ = [\n            $$[$0 - 4],\n            $$[$0 - 1],\n            { type: \"addMessage\", from: $$[$0 - 4].actor, to: $$[$0 - 1].actor, signalType: $$[$0 - 3], msg: $$[$0], activate: true },\n            { type: \"activeStart\", signalType: yy.LINETYPE.ACTIVE_START, actor: $$[$0 - 1].actor }\n          ];\n          break;\n        case 66:\n          this.$ = [\n            $$[$0 - 4],\n            $$[$0 - 1],\n            { type: \"addMessage\", from: $$[$0 - 4].actor, to: $$[$0 - 1].actor, signalType: $$[$0 - 3], msg: $$[$0] },\n            { type: \"activeEnd\", signalType: yy.LINETYPE.ACTIVE_END, actor: $$[$0 - 4].actor }\n          ];\n          break;\n        case 67:\n          this.$ = [$$[$0 - 3], $$[$0 - 1], { type: \"addMessage\", from: $$[$0 - 3].actor, to: $$[$0 - 1].actor, signalType: $$[$0 - 2], msg: $$[$0] }];\n          break;\n        case 68:\n          this.$ = { type: \"addParticipant\", actor: $$[$0] };\n          break;\n        case 69:\n          this.$ = yy.LINETYPE.SOLID_OPEN;\n          break;\n        case 70:\n          this.$ = yy.LINETYPE.DOTTED_OPEN;\n          break;\n        case 71:\n          this.$ = yy.LINETYPE.SOLID;\n          break;\n        case 72:\n          this.$ = yy.LINETYPE.BIDIRECTIONAL_SOLID;\n          break;\n        case 73:\n          this.$ = yy.LINETYPE.DOTTED;\n          break;\n        case 74:\n          this.$ = yy.LINETYPE.BIDIRECTIONAL_DOTTED;\n          break;\n        case 75:\n          this.$ = yy.LINETYPE.SOLID_CROSS;\n          break;\n        case 76:\n          this.$ = yy.LINETYPE.DOTTED_CROSS;\n          break;\n        case 77:\n          this.$ = yy.LINETYPE.SOLID_POINT;\n          break;\n        case 78:\n          this.$ = yy.LINETYPE.DOTTED_POINT;\n          break;\n        case 79:\n          this.$ = yy.parseMessage($$[$0].trim().substring(1));\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: $V0, 5: $V1, 6: $V2 }, { 1: [3] }, { 3: 5, 4: $V0, 5: $V1, 6: $V2 }, { 3: 6, 4: $V0, 5: $V1, 6: $V2 }, o([1, 4, 5, 13, 14, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 50, 52, 53, 54, 59, 60, 61, 62, 70], $V3, { 7: 7 }), { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 3], 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, o($Vx, [2, 5]), { 9: 47, 12: 12, 13: $V6, 14: $V7, 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, o($Vx, [2, 7]), o($Vx, [2, 8]), o($Vx, [2, 14]), { 12: 48, 50: $Vo, 52: $Vp, 53: $Vq }, { 15: [1, 49] }, { 5: [1, 50] }, { 5: [1, 53], 19: [1, 51], 20: [1, 52] }, { 22: 54, 70: $Vw }, { 22: 55, 70: $Vw }, { 5: [1, 56] }, { 5: [1, 57] }, { 5: [1, 58] }, { 5: [1, 59] }, { 5: [1, 60] }, o($Vx, [2, 29]), o($Vx, [2, 30]), { 32: [1, 61] }, { 34: [1, 62] }, o($Vx, [2, 33]), { 15: [1, 63] }, { 15: [1, 64] }, { 15: [1, 65] }, { 15: [1, 66] }, { 15: [1, 67] }, { 15: [1, 68] }, { 15: [1, 69] }, { 15: [1, 70] }, { 22: 71, 70: $Vw }, { 22: 72, 70: $Vw }, { 22: 73, 70: $Vw }, { 67: 74, 71: [1, 75], 72: [1, 76], 73: [1, 77], 74: [1, 78], 75: [1, 79], 76: [1, 80], 77: [1, 81], 78: [1, 82], 79: [1, 83], 80: [1, 84] }, { 55: 85, 57: [1, 86], 65: [1, 87], 66: [1, 88] }, { 22: 89, 70: $Vw }, { 22: 90, 70: $Vw }, { 22: 91, 70: $Vw }, { 22: 92, 70: $Vw }, o([5, 51, 64, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81], [2, 68]), o($Vx, [2, 6]), o($Vx, [2, 15]), o($Vy, [2, 9], { 10: 93 }), o($Vx, [2, 17]), { 5: [1, 95], 19: [1, 94] }, { 5: [1, 96] }, o($Vx, [2, 21]), { 5: [1, 97] }, { 5: [1, 98] }, o($Vx, [2, 24]), o($Vx, [2, 25]), o($Vx, [2, 26]), o($Vx, [2, 27]), o($Vx, [2, 28]), o($Vx, [2, 31]), o($Vx, [2, 32]), o($Vz, $V3, { 7: 99 }), o($Vz, $V3, { 7: 100 }), o($Vz, $V3, { 7: 101 }), o($VA, $V3, { 40: 102, 7: 103 }), o($VB, $V3, { 42: 104, 7: 105 }), o($VB, $V3, { 7: 105, 42: 106 }), o($VC, $V3, { 45: 107, 7: 108 }), o($Vz, $V3, { 7: 109 }), { 5: [1, 111], 51: [1, 110] }, { 5: [1, 113], 51: [1, 112] }, { 5: [1, 114] }, { 22: 117, 68: [1, 115], 69: [1, 116], 70: $Vw }, o($VD, [2, 69]), o($VD, [2, 70]), o($VD, [2, 71]), o($VD, [2, 72]), o($VD, [2, 73]), o($VD, [2, 74]), o($VD, [2, 75]), o($VD, [2, 76]), o($VD, [2, 77]), o($VD, [2, 78]), { 22: 118, 70: $Vw }, { 22: 120, 58: 119, 70: $Vw }, { 70: [2, 63] }, { 70: [2, 64] }, { 56: 121, 81: $VE }, { 56: 123, 81: $VE }, { 56: 124, 81: $VE }, { 56: 125, 81: $VE }, { 4: [1, 128], 5: [1, 130], 11: 127, 12: 129, 16: [1, 126], 50: $Vo, 52: $Vp, 53: $Vq }, { 5: [1, 131] }, o($Vx, [2, 19]), o($Vx, [2, 20]), o($Vx, [2, 22]), o($Vx, [2, 23]), { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 132], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 133], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 134], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 16: [1, 135] }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [2, 46], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 49: [1, 136], 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 16: [1, 137] }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [2, 44], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 48: [1, 138], 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 16: [1, 139] }, { 16: [1, 140] }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [2, 42], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 47: [1, 141], 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 142], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 15: [1, 143] }, o($Vx, [2, 49]), { 15: [1, 144] }, o($Vx, [2, 51]), o($Vx, [2, 52]), { 22: 145, 70: $Vw }, { 22: 146, 70: $Vw }, { 56: 147, 81: $VE }, { 56: 148, 81: $VE }, { 56: 149, 81: $VE }, { 64: [1, 150], 81: [2, 62] }, { 5: [2, 55] }, { 5: [2, 79] }, { 5: [2, 56] }, { 5: [2, 57] }, { 5: [2, 58] }, o($Vx, [2, 16]), o($Vy, [2, 10]), { 12: 151, 50: $Vo, 52: $Vp, 53: $Vq }, o($Vy, [2, 12]), o($Vy, [2, 13]), o($Vx, [2, 18]), o($Vx, [2, 34]), o($Vx, [2, 35]), o($Vx, [2, 36]), o($Vx, [2, 37]), { 15: [1, 152] }, o($Vx, [2, 38]), { 15: [1, 153] }, o($Vx, [2, 39]), o($Vx, [2, 40]), { 15: [1, 154] }, o($Vx, [2, 41]), { 5: [1, 155] }, { 5: [1, 156] }, { 56: 157, 81: $VE }, { 56: 158, 81: $VE }, { 5: [2, 67] }, { 5: [2, 53] }, { 5: [2, 54] }, { 22: 159, 70: $Vw }, o($Vy, [2, 11]), o($VA, $V3, { 7: 103, 40: 160 }), o($VB, $V3, { 7: 105, 42: 161 }), o($VC, $V3, { 7: 108, 45: 162 }), o($Vx, [2, 48]), o($Vx, [2, 50]), { 5: [2, 65] }, { 5: [2, 66] }, { 81: [2, 61] }, { 16: [2, 47] }, { 16: [2, 45] }, { 16: [2, 43] }],\n    defaultActions: { 5: [2, 1], 6: [2, 2], 87: [2, 63], 88: [2, 64], 121: [2, 55], 122: [2, 79], 123: [2, 56], 124: [2, 57], 125: [2, 58], 147: [2, 67], 148: [2, 53], 149: [2, 54], 157: [2, 65], 158: [2, 66], 159: [2, 61], 160: [2, 47], 161: [2, 45], 162: [2, 43] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 5;\n            break;\n          case 1:\n            break;\n          case 2:\n            break;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            break;\n          case 6:\n            return 19;\n            break;\n          case 7:\n            this.begin(\"LINE\");\n            return 14;\n            break;\n          case 8:\n            this.begin(\"ID\");\n            return 50;\n            break;\n          case 9:\n            this.begin(\"ID\");\n            return 52;\n            break;\n          case 10:\n            return 13;\n            break;\n          case 11:\n            this.begin(\"ID\");\n            return 53;\n            break;\n          case 12:\n            yy_.yytext = yy_.yytext.trim();\n            this.begin(\"ALIAS\");\n            return 70;\n            break;\n          case 13:\n            this.popState();\n            this.popState();\n            this.begin(\"LINE\");\n            return 51;\n            break;\n          case 14:\n            this.popState();\n            this.popState();\n            return 5;\n            break;\n          case 15:\n            this.begin(\"LINE\");\n            return 36;\n            break;\n          case 16:\n            this.begin(\"LINE\");\n            return 37;\n            break;\n          case 17:\n            this.begin(\"LINE\");\n            return 38;\n            break;\n          case 18:\n            this.begin(\"LINE\");\n            return 39;\n            break;\n          case 19:\n            this.begin(\"LINE\");\n            return 49;\n            break;\n          case 20:\n            this.begin(\"LINE\");\n            return 41;\n            break;\n          case 21:\n            this.begin(\"LINE\");\n            return 43;\n            break;\n          case 22:\n            this.begin(\"LINE\");\n            return 48;\n            break;\n          case 23:\n            this.begin(\"LINE\");\n            return 44;\n            break;\n          case 24:\n            this.begin(\"LINE\");\n            return 47;\n            break;\n          case 25:\n            this.begin(\"LINE\");\n            return 46;\n            break;\n          case 26:\n            this.popState();\n            return 15;\n            break;\n          case 27:\n            return 16;\n            break;\n          case 28:\n            return 65;\n            break;\n          case 29:\n            return 66;\n            break;\n          case 30:\n            return 59;\n            break;\n          case 31:\n            return 60;\n            break;\n          case 32:\n            return 61;\n            break;\n          case 33:\n            return 62;\n            break;\n          case 34:\n            return 57;\n            break;\n          case 35:\n            return 54;\n            break;\n          case 36:\n            this.begin(\"ID\");\n            return 21;\n            break;\n          case 37:\n            this.begin(\"ID\");\n            return 23;\n            break;\n          case 38:\n            return 29;\n            break;\n          case 39:\n            return 30;\n            break;\n          case 40:\n            this.begin(\"acc_title\");\n            return 31;\n            break;\n          case 41:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 42:\n            this.begin(\"acc_descr\");\n            return 33;\n            break;\n          case 43:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 44:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 45:\n            this.popState();\n            break;\n          case 46:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 47:\n            return 6;\n            break;\n          case 48:\n            return 18;\n            break;\n          case 49:\n            return 20;\n            break;\n          case 50:\n            return 64;\n            break;\n          case 51:\n            return 5;\n            break;\n          case 52:\n            yy_.yytext = yy_.yytext.trim();\n            return 70;\n            break;\n          case 53:\n            return 73;\n            break;\n          case 54:\n            return 74;\n            break;\n          case 55:\n            return 75;\n            break;\n          case 56:\n            return 76;\n            break;\n          case 57:\n            return 71;\n            break;\n          case 58:\n            return 72;\n            break;\n          case 59:\n            return 77;\n            break;\n          case 60:\n            return 78;\n            break;\n          case 61:\n            return 79;\n            break;\n          case 62:\n            return 80;\n            break;\n          case 63:\n            return 81;\n            break;\n          case 64:\n            return 68;\n            break;\n          case 65:\n            return 69;\n            break;\n          case 66:\n            return 5;\n            break;\n          case 67:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:((?!\\n)\\s)+)/i, /^(?:#[^\\n]*)/i, /^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[0-9]+(?=[ \\n]+))/i, /^(?:box\\b)/i, /^(?:participant\\b)/i, /^(?:actor\\b)/i, /^(?:create\\b)/i, /^(?:destroy\\b)/i, /^(?:[^\\<->\\->:\\n,;]+?([\\-]*[^\\<->\\->:\\n,;]+?)*?(?=((?!\\n)\\s)+as(?!\\n)\\s|[#\\n;]|$))/i, /^(?:as\\b)/i, /^(?:(?:))/i, /^(?:loop\\b)/i, /^(?:rect\\b)/i, /^(?:opt\\b)/i, /^(?:alt\\b)/i, /^(?:else\\b)/i, /^(?:par\\b)/i, /^(?:par_over\\b)/i, /^(?:and\\b)/i, /^(?:critical\\b)/i, /^(?:option\\b)/i, /^(?:break\\b)/i, /^(?:(?:[:]?(?:no)?wrap)?[^#\\n;]*)/i, /^(?:end\\b)/i, /^(?:left of\\b)/i, /^(?:right of\\b)/i, /^(?:links\\b)/i, /^(?:link\\b)/i, /^(?:properties\\b)/i, /^(?:details\\b)/i, /^(?:over\\b)/i, /^(?:note\\b)/i, /^(?:activate\\b)/i, /^(?:deactivate\\b)/i, /^(?:title\\s[^#\\n;]+)/i, /^(?:title:\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:sequenceDiagram\\b)/i, /^(?:autonumber\\b)/i, /^(?:off\\b)/i, /^(?:,)/i, /^(?:;)/i, /^(?:[^\\+\\<->\\->:\\n,;]+((?!(-x|--x|-\\)|--\\)))[\\-]*[^\\+\\<->\\->:\\n,;]+)*)/i, /^(?:->>)/i, /^(?:<<->>)/i, /^(?:-->>)/i, /^(?:<<-->>)/i, /^(?:->)/i, /^(?:-->)/i, /^(?:-[x])/i, /^(?:--[x])/i, /^(?:-[\\)])/i, /^(?:--[\\)])/i, /^(?::(?:(?:no)?wrap)?[^#\\n;]+)/i, /^(?:\\+)/i, /^(?:-)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [45, 46], \"inclusive\": false }, \"acc_descr\": { \"rules\": [43], \"inclusive\": false }, \"acc_title\": { \"rules\": [41], \"inclusive\": false }, \"ID\": { \"rules\": [2, 3, 12], \"inclusive\": false }, \"ALIAS\": { \"rules\": [2, 3, 13, 14], \"inclusive\": false }, \"LINE\": { \"rules\": [2, 3, 26], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 44, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar sequenceDiagram_default = parser;\n\n// src/diagrams/sequence/sequenceDb.ts\nvar LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25,\n  AUTONUMBER: 26,\n  CRITICAL_START: 27,\n  CRITICAL_OPTION: 28,\n  CRITICAL_END: 29,\n  BREAK_START: 30,\n  BREAK_END: 31,\n  PAR_OVER_START: 32,\n  BIDIRECTIONAL_SOLID: 33,\n  BIDIRECTIONAL_DOTTED: 34\n};\nvar ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1\n};\nvar PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2\n};\nvar SequenceDB = class {\n  constructor() {\n    this.state = new ImperativeState(() => ({\n      prevActor: void 0,\n      actors: /* @__PURE__ */ new Map(),\n      createdActors: /* @__PURE__ */ new Map(),\n      destroyedActors: /* @__PURE__ */ new Map(),\n      boxes: [],\n      messages: [],\n      notes: [],\n      sequenceNumbersEnabled: false,\n      wrapEnabled: void 0,\n      currentBox: void 0,\n      lastCreated: void 0,\n      lastDestroyed: void 0\n    }));\n    this.setAccTitle = setAccTitle;\n    this.setAccDescription = setAccDescription;\n    this.setDiagramTitle = setDiagramTitle;\n    this.getAccTitle = getAccTitle;\n    this.getAccDescription = getAccDescription;\n    this.getDiagramTitle = getDiagramTitle;\n    this.apply = this.apply.bind(this);\n    this.parseBoxData = this.parseBoxData.bind(this);\n    this.parseMessage = this.parseMessage.bind(this);\n    this.clear();\n    this.setWrap(getConfig2().wrap);\n    this.LINETYPE = LINETYPE;\n    this.ARROWTYPE = ARROWTYPE;\n    this.PLACEMENT = PLACEMENT;\n  }\n  static {\n    __name(this, \"SequenceDB\");\n  }\n  addBox(data) {\n    this.state.records.boxes.push({\n      name: data.text,\n      wrap: data.wrap ?? this.autoWrap(),\n      fill: data.color,\n      actorKeys: []\n    });\n    this.state.records.currentBox = this.state.records.boxes.slice(-1)[0];\n  }\n  addActor(id, name, description, type) {\n    let assignedBox = this.state.records.currentBox;\n    const old = this.state.records.actors.get(id);\n    if (old) {\n      if (this.state.records.currentBox && old.box && this.state.records.currentBox !== old.box) {\n        throw new Error(\n          `A same participant should only be defined in one Box: ${old.name} can't be in '${old.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`\n        );\n      }\n      assignedBox = old.box ? old.box : this.state.records.currentBox;\n      old.box = assignedBox;\n      if (old && name === old.name && description == null) {\n        return;\n      }\n    }\n    if (description?.text == null) {\n      description = { text: name, type };\n    }\n    if (type == null || description.text == null) {\n      description = { text: name, type };\n    }\n    this.state.records.actors.set(id, {\n      box: assignedBox,\n      name,\n      description: description.text,\n      wrap: description.wrap ?? this.autoWrap(),\n      prevActor: this.state.records.prevActor,\n      links: {},\n      properties: {},\n      actorCnt: null,\n      rectData: null,\n      type: type ?? \"participant\"\n    });\n    if (this.state.records.prevActor) {\n      const prevActorInRecords = this.state.records.actors.get(this.state.records.prevActor);\n      if (prevActorInRecords) {\n        prevActorInRecords.nextActor = id;\n      }\n    }\n    if (this.state.records.currentBox) {\n      this.state.records.currentBox.actorKeys.push(id);\n    }\n    this.state.records.prevActor = id;\n  }\n  activationCount(part) {\n    let i;\n    let count = 0;\n    if (!part) {\n      return 0;\n    }\n    for (i = 0; i < this.state.records.messages.length; i++) {\n      if (this.state.records.messages[i].type === this.LINETYPE.ACTIVE_START && this.state.records.messages[i].from === part) {\n        count++;\n      }\n      if (this.state.records.messages[i].type === this.LINETYPE.ACTIVE_END && this.state.records.messages[i].from === part) {\n        count--;\n      }\n    }\n    return count;\n  }\n  addMessage(idFrom, idTo, message, answer) {\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: idFrom,\n      to: idTo,\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n      answer\n    });\n  }\n  addSignal(idFrom, idTo, message, messageType, activate = false) {\n    if (messageType === this.LINETYPE.ACTIVE_END) {\n      const cnt = this.activationCount(idFrom ?? \"\");\n      if (cnt < 1) {\n        const error = new Error(\"Trying to inactivate an inactive participant (\" + idFrom + \")\");\n        error.hash = {\n          text: \"->>-\",\n          token: \"->>-\",\n          line: \"1\",\n          loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n          expected: [\"'ACTIVE_PARTICIPANT'\"]\n        };\n        throw error;\n      }\n    }\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: idFrom,\n      to: idTo,\n      message: message?.text ?? \"\",\n      wrap: message?.wrap ?? this.autoWrap(),\n      type: messageType,\n      activate\n    });\n    return true;\n  }\n  hasAtLeastOneBox() {\n    return this.state.records.boxes.length > 0;\n  }\n  hasAtLeastOneBoxWithTitle() {\n    return this.state.records.boxes.some((b) => b.name);\n  }\n  getMessages() {\n    return this.state.records.messages;\n  }\n  getBoxes() {\n    return this.state.records.boxes;\n  }\n  getActors() {\n    return this.state.records.actors;\n  }\n  getCreatedActors() {\n    return this.state.records.createdActors;\n  }\n  getDestroyedActors() {\n    return this.state.records.destroyedActors;\n  }\n  getActor(id) {\n    return this.state.records.actors.get(id);\n  }\n  getActorKeys() {\n    return [...this.state.records.actors.keys()];\n  }\n  enableSequenceNumbers() {\n    this.state.records.sequenceNumbersEnabled = true;\n  }\n  disableSequenceNumbers() {\n    this.state.records.sequenceNumbersEnabled = false;\n  }\n  showSequenceNumbers() {\n    return this.state.records.sequenceNumbersEnabled;\n  }\n  setWrap(wrapSetting) {\n    this.state.records.wrapEnabled = wrapSetting;\n  }\n  extractWrap(text) {\n    if (text === void 0) {\n      return {};\n    }\n    text = text.trim();\n    const wrap = /^:?wrap:/.exec(text) !== null ? true : /^:?nowrap:/.exec(text) !== null ? false : void 0;\n    const cleanedText = (wrap === void 0 ? text : text.replace(/^:?(?:no)?wrap:/, \"\")).trim();\n    return { cleanedText, wrap };\n  }\n  autoWrap() {\n    if (this.state.records.wrapEnabled !== void 0) {\n      return this.state.records.wrapEnabled;\n    }\n    return getConfig2().sequence?.wrap ?? false;\n  }\n  clear() {\n    this.state.reset();\n    clear();\n  }\n  parseMessage(str) {\n    const trimmedStr = str.trim();\n    const { wrap, cleanedText } = this.extractWrap(trimmedStr);\n    const message = {\n      text: cleanedText,\n      wrap\n    };\n    log.debug(`parseMessage: ${JSON.stringify(message)}`);\n    return message;\n  }\n  // We expect the box statement to be color first then description\n  // The color can be rgb,rgba,hsl,hsla, or css code names  #hex codes are not supported for now because of the way the char # is handled\n  // We extract first segment as color, the rest of the line is considered as text\n  parseBoxData(str) {\n    const match = /^((?:rgba?|hsla?)\\s*\\(.*\\)|\\w*)(.*)$/.exec(str);\n    let color = match?.[1] ? match[1].trim() : \"transparent\";\n    let title = match?.[2] ? match[2].trim() : void 0;\n    if (window?.CSS) {\n      if (!window.CSS.supports(\"color\", color)) {\n        color = \"transparent\";\n        title = str.trim();\n      }\n    } else {\n      const style = new Option().style;\n      style.color = color;\n      if (style.color !== color) {\n        color = \"transparent\";\n        title = str.trim();\n      }\n    }\n    const { wrap, cleanedText } = this.extractWrap(title);\n    return {\n      text: cleanedText ? sanitizeText(cleanedText, getConfig2()) : void 0,\n      color,\n      wrap\n    };\n  }\n  addNote(actor, placement, message) {\n    const note = {\n      actor,\n      placement,\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap()\n    };\n    const actors = [].concat(actor, actor);\n    this.state.records.notes.push(note);\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: actors[0],\n      to: actors[1],\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n      type: this.LINETYPE.NOTE,\n      placement\n    });\n  }\n  addLinks(actorId, text) {\n    const actor = this.getActor(actorId);\n    try {\n      let sanitizedText = sanitizeText(text.text, getConfig2());\n      sanitizedText = sanitizedText.replace(/&equals;/g, \"=\");\n      sanitizedText = sanitizedText.replace(/&amp;/g, \"&\");\n      const links = JSON.parse(sanitizedText);\n      this.insertLinks(actor, links);\n    } catch (e) {\n      log.error(\"error while parsing actor link text\", e);\n    }\n  }\n  addALink(actorId, text) {\n    const actor = this.getActor(actorId);\n    try {\n      const links = {};\n      let sanitizedText = sanitizeText(text.text, getConfig2());\n      const sep = sanitizedText.indexOf(\"@\");\n      sanitizedText = sanitizedText.replace(/&equals;/g, \"=\");\n      sanitizedText = sanitizedText.replace(/&amp;/g, \"&\");\n      const label = sanitizedText.slice(0, sep - 1).trim();\n      const link = sanitizedText.slice(sep + 1).trim();\n      links[label] = link;\n      this.insertLinks(actor, links);\n    } catch (e) {\n      log.error(\"error while parsing actor link text\", e);\n    }\n  }\n  insertLinks(actor, links) {\n    if (actor.links == null) {\n      actor.links = links;\n    } else {\n      for (const key in links) {\n        actor.links[key] = links[key];\n      }\n    }\n  }\n  addProperties(actorId, text) {\n    const actor = this.getActor(actorId);\n    try {\n      const sanitizedText = sanitizeText(text.text, getConfig2());\n      const properties = JSON.parse(sanitizedText);\n      this.insertProperties(actor, properties);\n    } catch (e) {\n      log.error(\"error while parsing actor properties text\", e);\n    }\n  }\n  insertProperties(actor, properties) {\n    if (actor.properties == null) {\n      actor.properties = properties;\n    } else {\n      for (const key in properties) {\n        actor.properties[key] = properties[key];\n      }\n    }\n  }\n  boxEnd() {\n    this.state.records.currentBox = void 0;\n  }\n  addDetails(actorId, text) {\n    const actor = this.getActor(actorId);\n    const elem = document.getElementById(text.text);\n    try {\n      const text2 = elem.innerHTML;\n      const details = JSON.parse(text2);\n      if (details.properties) {\n        this.insertProperties(actor, details.properties);\n      }\n      if (details.links) {\n        this.insertLinks(actor, details.links);\n      }\n    } catch (e) {\n      log.error(\"error while parsing actor details text\", e);\n    }\n  }\n  getActorProperty(actor, key) {\n    if (actor?.properties !== void 0) {\n      return actor.properties[key];\n    }\n    return void 0;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-redundant-type-constituents\n  apply(param) {\n    if (Array.isArray(param)) {\n      param.forEach((item) => {\n        this.apply(item);\n      });\n    } else {\n      switch (param.type) {\n        case \"sequenceIndex\":\n          this.state.records.messages.push({\n            id: this.state.records.messages.length.toString(),\n            from: void 0,\n            to: void 0,\n            message: {\n              start: param.sequenceIndex,\n              step: param.sequenceIndexStep,\n              visible: param.sequenceVisible\n            },\n            wrap: false,\n            type: param.signalType\n          });\n          break;\n        case \"addParticipant\":\n          this.addActor(param.actor, param.actor, param.description, param.draw);\n          break;\n        case \"createParticipant\":\n          if (this.state.records.actors.has(param.actor)) {\n            throw new Error(\n              \"It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior\"\n            );\n          }\n          this.state.records.lastCreated = param.actor;\n          this.addActor(param.actor, param.actor, param.description, param.draw);\n          this.state.records.createdActors.set(param.actor, this.state.records.messages.length);\n          break;\n        case \"destroyParticipant\":\n          this.state.records.lastDestroyed = param.actor;\n          this.state.records.destroyedActors.set(param.actor, this.state.records.messages.length);\n          break;\n        case \"activeStart\":\n          this.addSignal(param.actor, void 0, void 0, param.signalType);\n          break;\n        case \"activeEnd\":\n          this.addSignal(param.actor, void 0, void 0, param.signalType);\n          break;\n        case \"addNote\":\n          this.addNote(param.actor, param.placement, param.text);\n          break;\n        case \"addLinks\":\n          this.addLinks(param.actor, param.text);\n          break;\n        case \"addALink\":\n          this.addALink(param.actor, param.text);\n          break;\n        case \"addProperties\":\n          this.addProperties(param.actor, param.text);\n          break;\n        case \"addDetails\":\n          this.addDetails(param.actor, param.text);\n          break;\n        case \"addMessage\":\n          if (this.state.records.lastCreated) {\n            if (param.to !== this.state.records.lastCreated) {\n              throw new Error(\n                \"The created participant \" + this.state.records.lastCreated.name + \" does not have an associated creating message after its declaration. Please check the sequence diagram.\"\n              );\n            } else {\n              this.state.records.lastCreated = void 0;\n            }\n          } else if (this.state.records.lastDestroyed) {\n            if (param.to !== this.state.records.lastDestroyed && param.from !== this.state.records.lastDestroyed) {\n              throw new Error(\n                \"The destroyed participant \" + this.state.records.lastDestroyed.name + \" does not have an associated destroying message after its declaration. Please check the sequence diagram.\"\n              );\n            } else {\n              this.state.records.lastDestroyed = void 0;\n            }\n          }\n          this.addSignal(param.from, param.to, param.msg, param.signalType, param.activate);\n          break;\n        case \"boxStart\":\n          this.addBox(param.boxData);\n          break;\n        case \"boxEnd\":\n          this.boxEnd();\n          break;\n        case \"loopStart\":\n          this.addSignal(void 0, void 0, param.loopText, param.signalType);\n          break;\n        case \"loopEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"rectStart\":\n          this.addSignal(void 0, void 0, param.color, param.signalType);\n          break;\n        case \"rectEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"optStart\":\n          this.addSignal(void 0, void 0, param.optText, param.signalType);\n          break;\n        case \"optEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"altStart\":\n          this.addSignal(void 0, void 0, param.altText, param.signalType);\n          break;\n        case \"else\":\n          this.addSignal(void 0, void 0, param.altText, param.signalType);\n          break;\n        case \"altEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"setAccTitle\":\n          setAccTitle(param.text);\n          break;\n        case \"parStart\":\n          this.addSignal(void 0, void 0, param.parText, param.signalType);\n          break;\n        case \"and\":\n          this.addSignal(void 0, void 0, param.parText, param.signalType);\n          break;\n        case \"parEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"criticalStart\":\n          this.addSignal(void 0, void 0, param.criticalText, param.signalType);\n          break;\n        case \"option\":\n          this.addSignal(void 0, void 0, param.optionText, param.signalType);\n          break;\n        case \"criticalEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"breakStart\":\n          this.addSignal(void 0, void 0, param.breakText, param.signalType);\n          break;\n        case \"breakEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n      }\n    }\n  }\n  getConfig() {\n    return getConfig2().sequence;\n  }\n};\n\n// src/diagrams/sequence/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `.actor {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n  }\n\n  text.actor > tspan {\n    fill: ${options.actorTextColor};\n    stroke: none;\n  }\n\n  .actor-line {\n    stroke: ${options.actorLineColor};\n  }\n\n  .messageLine0 {\n    stroke-width: 1.5;\n    stroke-dasharray: none;\n    stroke: ${options.signalColor};\n  }\n\n  .messageLine1 {\n    stroke-width: 1.5;\n    stroke-dasharray: 2, 2;\n    stroke: ${options.signalColor};\n  }\n\n  #arrowhead path {\n    fill: ${options.signalColor};\n    stroke: ${options.signalColor};\n  }\n\n  .sequenceNumber {\n    fill: ${options.sequenceNumberColor};\n  }\n\n  #sequencenumber {\n    fill: ${options.signalColor};\n  }\n\n  #crosshead path {\n    fill: ${options.signalColor};\n    stroke: ${options.signalColor};\n  }\n\n  .messageText {\n    fill: ${options.signalTextColor};\n    stroke: none;\n  }\n\n  .labelBox {\n    stroke: ${options.labelBoxBorderColor};\n    fill: ${options.labelBoxBkgColor};\n  }\n\n  .labelText, .labelText > tspan {\n    fill: ${options.labelTextColor};\n    stroke: none;\n  }\n\n  .loopText, .loopText > tspan {\n    fill: ${options.loopTextColor};\n    stroke: none;\n  }\n\n  .loopLine {\n    stroke-width: 2px;\n    stroke-dasharray: 2, 2;\n    stroke: ${options.labelBoxBorderColor};\n    fill: ${options.labelBoxBorderColor};\n  }\n\n  .note {\n    //stroke: #decc93;\n    stroke: ${options.noteBorderColor};\n    fill: ${options.noteBkgColor};\n  }\n\n  .noteText, .noteText > tspan {\n    fill: ${options.noteTextColor};\n    stroke: none;\n  }\n\n  .activation0 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .activation1 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .activation2 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .actorPopupMenu {\n    position: absolute;\n  }\n\n  .actorPopupMenuPanel {\n    position: absolute;\n    fill: ${options.actorBkg};\n    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));\n}\n  .actor-man line {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n  }\n  .actor-man circle, line {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n    stroke-width: 2px;\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/sequence/sequenceRenderer.ts\nimport { select } from \"d3\";\n\n// src/diagrams/sequence/svgDraw.js\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nvar ACTOR_TYPE_WIDTH = 18 * 2;\nvar TOP_ACTOR_CLASS = \"actor-top\";\nvar BOTTOM_ACTOR_CLASS = \"actor-bottom\";\nvar ACTOR_BOX_CLASS = \"actor-box\";\nvar ACTOR_MAN_FIGURE_CLASS = \"actor-man\";\nvar drawRect2 = /* @__PURE__ */ __name(function(elem, rectData) {\n  return drawRect(elem, rectData);\n}, \"drawRect\");\nvar drawPopup = /* @__PURE__ */ __name(function(elem, actor, minMenuWidth, textAttrs, forceMenus) {\n  if (actor.links === void 0 || actor.links === null || Object.keys(actor.links).length === 0) {\n    return { height: 0, width: 0 };\n  }\n  const links = actor.links;\n  const actorCnt2 = actor.actorCnt;\n  const rectData = actor.rectData;\n  var displayValue = \"none\";\n  if (forceMenus) {\n    displayValue = \"block !important\";\n  }\n  const g = elem.append(\"g\");\n  g.attr(\"id\", \"actor\" + actorCnt2 + \"_popup\");\n  g.attr(\"class\", \"actorPopupMenu\");\n  g.attr(\"display\", displayValue);\n  var actorClass = \"\";\n  if (rectData.class !== void 0) {\n    actorClass = \" \" + rectData.class;\n  }\n  let menuWidth = rectData.width > minMenuWidth ? rectData.width : minMenuWidth;\n  const rectElem = g.append(\"rect\");\n  rectElem.attr(\"class\", \"actorPopupMenuPanel\" + actorClass);\n  rectElem.attr(\"x\", rectData.x);\n  rectElem.attr(\"y\", rectData.height);\n  rectElem.attr(\"fill\", rectData.fill);\n  rectElem.attr(\"stroke\", rectData.stroke);\n  rectElem.attr(\"width\", menuWidth);\n  rectElem.attr(\"height\", rectData.height);\n  rectElem.attr(\"rx\", rectData.rx);\n  rectElem.attr(\"ry\", rectData.ry);\n  if (links != null) {\n    var linkY = 20;\n    for (let key in links) {\n      var linkElem = g.append(\"a\");\n      var sanitizedLink = sanitizeUrl(links[key]);\n      linkElem.attr(\"xlink:href\", sanitizedLink);\n      linkElem.attr(\"target\", \"_blank\");\n      _drawMenuItemTextCandidateFunc(textAttrs)(\n        key,\n        linkElem,\n        rectData.x + 10,\n        rectData.height + linkY,\n        menuWidth,\n        20,\n        { class: \"actor\" },\n        textAttrs\n      );\n      linkY += 30;\n    }\n  }\n  rectElem.attr(\"height\", linkY);\n  return { height: rectData.height + linkY, width: menuWidth };\n}, \"drawPopup\");\nvar popupMenuToggle = /* @__PURE__ */ __name(function(popId) {\n  return \"var pu = document.getElementById('\" + popId + \"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }\";\n}, \"popupMenuToggle\");\nvar drawKatex = /* @__PURE__ */ __name(async function(elem, textData, msgModel = null) {\n  let textElem = elem.append(\"foreignObject\");\n  const lines = await renderKatex(textData.text, getConfig());\n  const divElem = textElem.append(\"xhtml:div\").attr(\"style\", \"width: fit-content;\").attr(\"xmlns\", \"http://www.w3.org/1999/xhtml\").html(lines);\n  const dim = divElem.node().getBoundingClientRect();\n  textElem.attr(\"height\", Math.round(dim.height)).attr(\"width\", Math.round(dim.width));\n  if (textData.class === \"noteText\") {\n    const rectElem = elem.node().firstChild;\n    rectElem.setAttribute(\"height\", dim.height + 2 * textData.textMargin);\n    const rectDim = rectElem.getBBox();\n    textElem.attr(\"x\", Math.round(rectDim.x + rectDim.width / 2 - dim.width / 2)).attr(\"y\", Math.round(rectDim.y + rectDim.height / 2 - dim.height / 2));\n  } else if (msgModel) {\n    let { startx, stopx, starty } = msgModel;\n    if (startx > stopx) {\n      const temp = startx;\n      startx = stopx;\n      stopx = temp;\n    }\n    textElem.attr(\"x\", Math.round(startx + Math.abs(startx - stopx) / 2 - dim.width / 2));\n    if (textData.class === \"loopText\") {\n      textElem.attr(\"y\", Math.round(starty));\n    } else {\n      textElem.attr(\"y\", Math.round(starty - dim.height));\n    }\n  }\n  return [textElem];\n}, \"drawKatex\");\nvar drawText = /* @__PURE__ */ __name(function(elem, textData) {\n  let prevTextHeight = 0;\n  let textHeight = 0;\n  const lines = textData.text.split(common_default.lineBreakRegex);\n  const [_textFontSize, _textFontSizePx] = parseFontSize(textData.fontSize);\n  let textElems = [];\n  let dy = 0;\n  let yfunc = /* @__PURE__ */ __name(() => textData.y, \"yfunc\");\n  if (textData.valign !== void 0 && textData.textMargin !== void 0 && textData.textMargin > 0) {\n    switch (textData.valign) {\n      case \"top\":\n      case \"start\":\n        yfunc = /* @__PURE__ */ __name(() => Math.round(textData.y + textData.textMargin), \"yfunc\");\n        break;\n      case \"middle\":\n      case \"center\":\n        yfunc = /* @__PURE__ */ __name(() => Math.round(textData.y + (prevTextHeight + textHeight + textData.textMargin) / 2), \"yfunc\");\n        break;\n      case \"bottom\":\n      case \"end\":\n        yfunc = /* @__PURE__ */ __name(() => Math.round(\n          textData.y + (prevTextHeight + textHeight + 2 * textData.textMargin) - textData.textMargin\n        ), \"yfunc\");\n        break;\n    }\n  }\n  if (textData.anchor !== void 0 && textData.textMargin !== void 0 && textData.width !== void 0) {\n    switch (textData.anchor) {\n      case \"left\":\n      case \"start\":\n        textData.x = Math.round(textData.x + textData.textMargin);\n        textData.anchor = \"start\";\n        textData.dominantBaseline = \"middle\";\n        textData.alignmentBaseline = \"middle\";\n        break;\n      case \"middle\":\n      case \"center\":\n        textData.x = Math.round(textData.x + textData.width / 2);\n        textData.anchor = \"middle\";\n        textData.dominantBaseline = \"middle\";\n        textData.alignmentBaseline = \"middle\";\n        break;\n      case \"right\":\n      case \"end\":\n        textData.x = Math.round(textData.x + textData.width - textData.textMargin);\n        textData.anchor = \"end\";\n        textData.dominantBaseline = \"middle\";\n        textData.alignmentBaseline = \"middle\";\n        break;\n    }\n  }\n  for (let [i, line] of lines.entries()) {\n    if (textData.textMargin !== void 0 && textData.textMargin === 0 && _textFontSize !== void 0) {\n      dy = i * _textFontSize;\n    }\n    const textElem = elem.append(\"text\");\n    textElem.attr(\"x\", textData.x);\n    textElem.attr(\"y\", yfunc());\n    if (textData.anchor !== void 0) {\n      textElem.attr(\"text-anchor\", textData.anchor).attr(\"dominant-baseline\", textData.dominantBaseline).attr(\"alignment-baseline\", textData.alignmentBaseline);\n    }\n    if (textData.fontFamily !== void 0) {\n      textElem.style(\"font-family\", textData.fontFamily);\n    }\n    if (_textFontSizePx !== void 0) {\n      textElem.style(\"font-size\", _textFontSizePx);\n    }\n    if (textData.fontWeight !== void 0) {\n      textElem.style(\"font-weight\", textData.fontWeight);\n    }\n    if (textData.fill !== void 0) {\n      textElem.attr(\"fill\", textData.fill);\n    }\n    if (textData.class !== void 0) {\n      textElem.attr(\"class\", textData.class);\n    }\n    if (textData.dy !== void 0) {\n      textElem.attr(\"dy\", textData.dy);\n    } else if (dy !== 0) {\n      textElem.attr(\"dy\", dy);\n    }\n    const text = line || ZERO_WIDTH_SPACE;\n    if (textData.tspan) {\n      const span = textElem.append(\"tspan\");\n      span.attr(\"x\", textData.x);\n      if (textData.fill !== void 0) {\n        span.attr(\"fill\", textData.fill);\n      }\n      span.text(text);\n    } else {\n      textElem.text(text);\n    }\n    if (textData.valign !== void 0 && textData.textMargin !== void 0 && textData.textMargin > 0) {\n      textHeight += (textElem._groups || textElem)[0][0].getBBox().height;\n      prevTextHeight = textHeight;\n    }\n    textElems.push(textElem);\n  }\n  return textElems;\n}, \"drawText\");\nvar drawLabel = /* @__PURE__ */ __name(function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  __name(genPoints, \"genPoints\");\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, txtObject.width, txtObject.height, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.height / 2;\n  drawText(elem, txtObject);\n  return polygon;\n}, \"drawLabel\");\nvar actorCnt = -1;\nvar fixLifeLineHeights = /* @__PURE__ */ __name((diagram2, actors, actorKeys, conf2) => {\n  if (!diagram2.select) {\n    return;\n  }\n  actorKeys.forEach((actorKey) => {\n    const actor = actors.get(actorKey);\n    const actorDOM = diagram2.select(\"#actor\" + actor.actorCnt);\n    if (!conf2.mirrorActors && actor.stopy) {\n      actorDOM.attr(\"y2\", actor.stopy + actor.height / 2);\n    } else if (conf2.mirrorActors) {\n      actorDOM.attr(\"y2\", actor.stopy);\n    }\n  });\n}, \"fixLifeLineHeights\");\nvar drawActorTypeParticipant = /* @__PURE__ */ __name(function(elem, actor, conf2, isFooter) {\n  const actorY = isFooter ? actor.stopy : actor.starty;\n  const center = actor.x + actor.width / 2;\n  const centerY = actorY + actor.height;\n  const boxplusLineGroup = elem.append(\"g\").lower();\n  var g = boxplusLineGroup;\n  if (!isFooter) {\n    actorCnt++;\n    if (Object.keys(actor.links || {}).length && !conf2.forceMenus) {\n      g.attr(\"onclick\", popupMenuToggle(`actor${actorCnt}_popup`)).attr(\"cursor\", \"pointer\");\n    }\n    g.append(\"line\").attr(\"id\", \"actor\" + actorCnt).attr(\"x1\", center).attr(\"y1\", centerY).attr(\"x2\", center).attr(\"y2\", 2e3).attr(\"class\", \"actor-line 200\").attr(\"stroke-width\", \"0.5px\").attr(\"stroke\", \"#999\").attr(\"name\", actor.name);\n    g = boxplusLineGroup.append(\"g\");\n    actor.actorCnt = actorCnt;\n    if (actor.links != null) {\n      g.attr(\"id\", \"root-\" + actorCnt);\n    }\n  }\n  const rect = getNoteRect();\n  var cssclass = \"actor\";\n  if (actor.properties?.class) {\n    cssclass = actor.properties.class;\n  } else {\n    rect.fill = \"#eaeaea\";\n  }\n  if (isFooter) {\n    cssclass += ` ${BOTTOM_ACTOR_CLASS}`;\n  } else {\n    cssclass += ` ${TOP_ACTOR_CLASS}`;\n  }\n  rect.x = actor.x;\n  rect.y = actorY;\n  rect.width = actor.width;\n  rect.height = actor.height;\n  rect.class = cssclass;\n  rect.rx = 3;\n  rect.ry = 3;\n  rect.name = actor.name;\n  const rectElem = drawRect2(g, rect);\n  actor.rectData = rect;\n  if (actor.properties?.icon) {\n    const iconSrc = actor.properties.icon.trim();\n    if (iconSrc.charAt(0) === \"@\") {\n      drawEmbeddedImage(g, rect.x + rect.width - 20, rect.y + 10, iconSrc.substr(1));\n    } else {\n      drawImage(g, rect.x + rect.width - 20, rect.y + 10, iconSrc);\n    }\n  }\n  _drawTextCandidateFunc(conf2, hasKatex(actor.description))(\n    actor.description,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: `actor ${ACTOR_BOX_CLASS}` },\n    conf2\n  );\n  let height = actor.height;\n  if (rectElem.node) {\n    const bounds2 = rectElem.node().getBBox();\n    actor.height = bounds2.height;\n    height = bounds2.height;\n  }\n  return height;\n}, \"drawActorTypeParticipant\");\nvar drawActorTypeActor = /* @__PURE__ */ __name(function(elem, actor, conf2, isFooter) {\n  const actorY = isFooter ? actor.stopy : actor.starty;\n  const center = actor.x + actor.width / 2;\n  const centerY = actorY + 80;\n  const line = elem.append(\"g\").lower();\n  if (!isFooter) {\n    actorCnt++;\n    line.append(\"line\").attr(\"id\", \"actor\" + actorCnt).attr(\"x1\", center).attr(\"y1\", centerY).attr(\"x2\", center).attr(\"y2\", 2e3).attr(\"class\", \"actor-line 200\").attr(\"stroke-width\", \"0.5px\").attr(\"stroke\", \"#999\").attr(\"name\", actor.name);\n    actor.actorCnt = actorCnt;\n  }\n  const actElem = elem.append(\"g\");\n  let cssClass = ACTOR_MAN_FIGURE_CLASS;\n  if (isFooter) {\n    cssClass += ` ${BOTTOM_ACTOR_CLASS}`;\n  } else {\n    cssClass += ` ${TOP_ACTOR_CLASS}`;\n  }\n  actElem.attr(\"class\", cssClass);\n  actElem.attr(\"name\", actor.name);\n  const rect = getNoteRect();\n  rect.x = actor.x;\n  rect.y = actorY;\n  rect.fill = \"#eaeaea\";\n  rect.width = actor.width;\n  rect.height = actor.height;\n  rect.class = \"actor\";\n  rect.rx = 3;\n  rect.ry = 3;\n  actElem.append(\"line\").attr(\"id\", \"actor-man-torso\" + actorCnt).attr(\"x1\", center).attr(\"y1\", actorY + 25).attr(\"x2\", center).attr(\"y2\", actorY + 45);\n  actElem.append(\"line\").attr(\"id\", \"actor-man-arms\" + actorCnt).attr(\"x1\", center - ACTOR_TYPE_WIDTH / 2).attr(\"y1\", actorY + 33).attr(\"x2\", center + ACTOR_TYPE_WIDTH / 2).attr(\"y2\", actorY + 33);\n  actElem.append(\"line\").attr(\"x1\", center - ACTOR_TYPE_WIDTH / 2).attr(\"y1\", actorY + 60).attr(\"x2\", center).attr(\"y2\", actorY + 45);\n  actElem.append(\"line\").attr(\"x1\", center).attr(\"y1\", actorY + 45).attr(\"x2\", center + ACTOR_TYPE_WIDTH / 2 - 2).attr(\"y2\", actorY + 60);\n  const circle = actElem.append(\"circle\");\n  circle.attr(\"cx\", actor.x + actor.width / 2);\n  circle.attr(\"cy\", actorY + 10);\n  circle.attr(\"r\", 15);\n  circle.attr(\"width\", actor.width);\n  circle.attr(\"height\", actor.height);\n  const bounds2 = actElem.node().getBBox();\n  actor.height = bounds2.height;\n  _drawTextCandidateFunc(conf2, hasKatex(actor.description))(\n    actor.description,\n    actElem,\n    rect.x,\n    rect.y + 35,\n    rect.width,\n    rect.height,\n    { class: `actor ${ACTOR_MAN_FIGURE_CLASS}` },\n    conf2\n  );\n  return actor.height;\n}, \"drawActorTypeActor\");\nvar drawActor = /* @__PURE__ */ __name(async function(elem, actor, conf2, isFooter) {\n  switch (actor.type) {\n    case \"actor\":\n      return await drawActorTypeActor(elem, actor, conf2, isFooter);\n    case \"participant\":\n      return await drawActorTypeParticipant(elem, actor, conf2, isFooter);\n  }\n}, \"drawActor\");\nvar drawBox = /* @__PURE__ */ __name(function(elem, box, conf2) {\n  const boxplusTextGroup = elem.append(\"g\");\n  const g = boxplusTextGroup;\n  drawBackgroundRect2(g, box);\n  if (box.name) {\n    _drawTextCandidateFunc(conf2)(\n      box.name,\n      g,\n      box.x,\n      box.y + (box.textMaxHeight || 0) / 2,\n      box.width,\n      0,\n      { class: \"text\" },\n      conf2\n    );\n  }\n  g.lower();\n}, \"drawBox\");\nvar anchorElement = /* @__PURE__ */ __name(function(elem) {\n  return elem.append(\"g\");\n}, \"anchorElement\");\nvar drawActivation = /* @__PURE__ */ __name(function(elem, bounds2, verticalPos, conf2, actorActivations2) {\n  const rect = getNoteRect();\n  const g = bounds2.anchored;\n  rect.x = bounds2.startx;\n  rect.y = bounds2.starty;\n  rect.class = \"activation\" + actorActivations2 % 3;\n  rect.width = bounds2.stopx - bounds2.startx;\n  rect.height = verticalPos - bounds2.starty;\n  drawRect2(g, rect);\n}, \"drawActivation\");\nvar drawLoop = /* @__PURE__ */ __name(async function(elem, loopModel, labelText, conf2) {\n  const {\n    boxMargin,\n    boxTextMargin,\n    labelBoxHeight,\n    labelBoxWidth,\n    messageFontFamily: fontFamily,\n    messageFontSize: fontSize,\n    messageFontWeight: fontWeight\n  } = conf2;\n  const g = elem.append(\"g\");\n  const drawLoopLine = /* @__PURE__ */ __name(function(startx, starty, stopx, stopy) {\n    return g.append(\"line\").attr(\"x1\", startx).attr(\"y1\", starty).attr(\"x2\", stopx).attr(\"y2\", stopy).attr(\"class\", \"loopLine\");\n  }, \"drawLoopLine\");\n  drawLoopLine(loopModel.startx, loopModel.starty, loopModel.stopx, loopModel.starty);\n  drawLoopLine(loopModel.stopx, loopModel.starty, loopModel.stopx, loopModel.stopy);\n  drawLoopLine(loopModel.startx, loopModel.stopy, loopModel.stopx, loopModel.stopy);\n  drawLoopLine(loopModel.startx, loopModel.starty, loopModel.startx, loopModel.stopy);\n  if (loopModel.sections !== void 0) {\n    loopModel.sections.forEach(function(item) {\n      drawLoopLine(loopModel.startx, item.y, loopModel.stopx, item.y).style(\n        \"stroke-dasharray\",\n        \"3, 3\"\n      );\n    });\n  }\n  let txt = getTextObj();\n  txt.text = labelText;\n  txt.x = loopModel.startx;\n  txt.y = loopModel.starty;\n  txt.fontFamily = fontFamily;\n  txt.fontSize = fontSize;\n  txt.fontWeight = fontWeight;\n  txt.anchor = \"middle\";\n  txt.valign = \"middle\";\n  txt.tspan = false;\n  txt.width = labelBoxWidth || 50;\n  txt.height = labelBoxHeight || 20;\n  txt.textMargin = boxTextMargin;\n  txt.class = \"labelText\";\n  drawLabel(g, txt);\n  txt = getTextObj2();\n  txt.text = loopModel.title;\n  txt.x = loopModel.startx + labelBoxWidth / 2 + (loopModel.stopx - loopModel.startx) / 2;\n  txt.y = loopModel.starty + boxMargin + boxTextMargin;\n  txt.anchor = \"middle\";\n  txt.valign = \"middle\";\n  txt.textMargin = boxTextMargin;\n  txt.class = \"loopText\";\n  txt.fontFamily = fontFamily;\n  txt.fontSize = fontSize;\n  txt.fontWeight = fontWeight;\n  txt.wrap = true;\n  let textElem = hasKatex(txt.text) ? await drawKatex(g, txt, loopModel) : drawText(g, txt);\n  if (loopModel.sectionTitles !== void 0) {\n    for (const [idx, item] of Object.entries(loopModel.sectionTitles)) {\n      if (item.message) {\n        txt.text = item.message;\n        txt.x = loopModel.startx + (loopModel.stopx - loopModel.startx) / 2;\n        txt.y = loopModel.sections[idx].y + boxMargin + boxTextMargin;\n        txt.class = \"loopText\";\n        txt.anchor = \"middle\";\n        txt.valign = \"middle\";\n        txt.tspan = false;\n        txt.fontFamily = fontFamily;\n        txt.fontSize = fontSize;\n        txt.fontWeight = fontWeight;\n        txt.wrap = loopModel.wrap;\n        if (hasKatex(txt.text)) {\n          loopModel.starty = loopModel.sections[idx].y;\n          await drawKatex(g, txt, loopModel);\n        } else {\n          drawText(g, txt);\n        }\n        let sectionHeight = Math.round(\n          textElem.map((te) => (te._groups || te)[0][0].getBBox().height).reduce((acc, curr) => acc + curr)\n        );\n        loopModel.sections[idx].height += sectionHeight - (boxMargin + boxTextMargin);\n      }\n    }\n  }\n  loopModel.height = Math.round(loopModel.stopy - loopModel.starty);\n  return g;\n}, \"drawLoop\");\nvar drawBackgroundRect2 = /* @__PURE__ */ __name(function(elem, bounds2) {\n  drawBackgroundRect(elem, bounds2);\n}, \"drawBackgroundRect\");\nvar insertDatabaseIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"database\").attr(\"fill-rule\", \"evenodd\").attr(\"clip-rule\", \"evenodd\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z\"\n  );\n}, \"insertDatabaseIcon\");\nvar insertComputerIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"computer\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z\"\n  );\n}, \"insertComputerIcon\");\nvar insertClockIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"clock\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z\"\n  );\n}, \"insertClockIcon\");\nvar insertArrowHead = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 7.9).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto-start-reverse\").append(\"path\").attr(\"d\", \"M -1 0 L 10 5 L 0 10 z\");\n}, \"insertArrowHead\");\nvar insertArrowFilledHead = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"filled-head\").attr(\"refX\", 15.5).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertArrowFilledHead\");\nvar insertSequenceNumber = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"sequencenumber\").attr(\"refX\", 15).attr(\"refY\", 15).attr(\"markerWidth\", 60).attr(\"markerHeight\", 40).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", 15).attr(\"cy\", 15).attr(\"r\", 6);\n}, \"insertSequenceNumber\");\nvar insertArrowCrossHead = /* @__PURE__ */ __name(function(elem) {\n  const defs = elem.append(\"defs\");\n  const marker = defs.append(\"marker\").attr(\"id\", \"crosshead\").attr(\"markerWidth\", 15).attr(\"markerHeight\", 8).attr(\"orient\", \"auto\").attr(\"refX\", 4).attr(\"refY\", 4.5);\n  marker.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1pt\").attr(\"d\", \"M 1,2 L 6,7 M 6,2 L 1,7\");\n}, \"insertArrowCrossHead\");\nvar getTextObj2 = /* @__PURE__ */ __name(function() {\n  return {\n    x: 0,\n    y: 0,\n    fill: void 0,\n    anchor: void 0,\n    style: \"#666\",\n    width: void 0,\n    height: void 0,\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true,\n    valign: void 0\n  };\n}, \"getTextObj\");\nvar getNoteRect2 = /* @__PURE__ */ __name(function() {\n  return {\n    x: 0,\n    y: 0,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    width: 100,\n    anchor: \"start\",\n    height: 100,\n    rx: 0,\n    ry: 0\n  };\n}, \"getNoteRect\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { actorFontSize, actorFontFamily, actorFontWeight } = conf2;\n    const [_actorFontSize, _actorFontSizePx] = parseFontSize(actorFontSize);\n    const lines = content.split(common_default.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * _actorFontSize - _actorFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).style(\"text-anchor\", \"middle\").style(\"font-size\", _actorFontSizePx).style(\"font-weight\", actorFontWeight).style(\"font-family\", actorFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  __name(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byFo, \"byFo\");\n  async function byKatex(content, g, x, y, width, height, textAttrs, conf2) {\n    const dim = await calculateMathMLDimensions(content, getConfig());\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x + width / 2 - dim.width / 2).attr(\"y\", y + height / 2 - dim.height / 2).attr(\"width\", dim.width).attr(\"height\", dim.height);\n    const text = f.append(\"xhtml:div\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").html(await renderKatex(content, getConfig()));\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byKatex, \"byKatex\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  __name(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2, hasKatex2 = false) {\n    if (hasKatex2) {\n      return byKatex;\n    }\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar _drawMenuItemTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x).attr(\"y\", y).style(\"text-anchor\", \"start\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { actorFontSize, actorFontFamily, actorFontWeight } = conf2;\n    const lines = content.split(common_default.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * actorFontSize - actorFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x).attr(\"y\", y).style(\"text-anchor\", \"start\").style(\"font-size\", actorFontSize).style(\"font-weight\", actorFontWeight).style(\"font-family\", actorFontFamily);\n      text.append(\"tspan\").attr(\"x\", x).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  __name(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  __name(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawText,\n  drawLabel,\n  drawActor,\n  drawBox,\n  drawPopup,\n  anchorElement,\n  drawActivation,\n  drawLoop,\n  drawBackgroundRect: drawBackgroundRect2,\n  insertArrowHead,\n  insertArrowFilledHead,\n  insertSequenceNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon,\n  getTextObj: getTextObj2,\n  getNoteRect: getNoteRect2,\n  fixLifeLineHeights,\n  sanitizeUrl\n};\n\n// src/diagrams/sequence/sequenceRenderer.ts\nvar conf = {};\nvar bounds = {\n  data: {\n    startx: void 0,\n    stopx: void 0,\n    starty: void 0,\n    stopy: void 0\n  },\n  verticalPos: 0,\n  sequenceItems: [],\n  activations: [],\n  models: {\n    getHeight: /* @__PURE__ */ __name(function() {\n      return Math.max.apply(\n        null,\n        this.actors.length === 0 ? [0] : this.actors.map((actor) => actor.height || 0)\n      ) + (this.loops.length === 0 ? 0 : this.loops.map((it) => it.height || 0).reduce((acc, h) => acc + h)) + (this.messages.length === 0 ? 0 : this.messages.map((it) => it.height || 0).reduce((acc, h) => acc + h)) + (this.notes.length === 0 ? 0 : this.notes.map((it) => it.height || 0).reduce((acc, h) => acc + h));\n    }, \"getHeight\"),\n    clear: /* @__PURE__ */ __name(function() {\n      this.actors = [];\n      this.boxes = [];\n      this.loops = [];\n      this.messages = [];\n      this.notes = [];\n    }, \"clear\"),\n    addBox: /* @__PURE__ */ __name(function(boxModel) {\n      this.boxes.push(boxModel);\n    }, \"addBox\"),\n    addActor: /* @__PURE__ */ __name(function(actorModel) {\n      this.actors.push(actorModel);\n    }, \"addActor\"),\n    addLoop: /* @__PURE__ */ __name(function(loopModel) {\n      this.loops.push(loopModel);\n    }, \"addLoop\"),\n    addMessage: /* @__PURE__ */ __name(function(msgModel) {\n      this.messages.push(msgModel);\n    }, \"addMessage\"),\n    addNote: /* @__PURE__ */ __name(function(noteModel) {\n      this.notes.push(noteModel);\n    }, \"addNote\"),\n    lastActor: /* @__PURE__ */ __name(function() {\n      return this.actors[this.actors.length - 1];\n    }, \"lastActor\"),\n    lastLoop: /* @__PURE__ */ __name(function() {\n      return this.loops[this.loops.length - 1];\n    }, \"lastLoop\"),\n    lastMessage: /* @__PURE__ */ __name(function() {\n      return this.messages[this.messages.length - 1];\n    }, \"lastMessage\"),\n    lastNote: /* @__PURE__ */ __name(function() {\n      return this.notes[this.notes.length - 1];\n    }, \"lastNote\"),\n    actors: [],\n    boxes: [],\n    loops: [],\n    messages: [],\n    notes: []\n  },\n  init: /* @__PURE__ */ __name(function() {\n    this.sequenceItems = [];\n    this.activations = [];\n    this.models.clear();\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0\n    };\n    this.verticalPos = 0;\n    setConf(getConfig2());\n  }, \"init\"),\n  updateVal: /* @__PURE__ */ __name(function(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }, \"updateVal\"),\n  updateBounds: /* @__PURE__ */ __name(function(startx, starty, stopx, stopy) {\n    const _self = this;\n    let cnt = 0;\n    function updateFn(type) {\n      return /* @__PURE__ */ __name(function updateItemBounds(item) {\n        cnt++;\n        const n = _self.sequenceItems.length - cnt + 1;\n        _self.updateVal(item, \"starty\", starty - n * conf.boxMargin, Math.min);\n        _self.updateVal(item, \"stopy\", stopy + n * conf.boxMargin, Math.max);\n        _self.updateVal(bounds.data, \"startx\", startx - n * conf.boxMargin, Math.min);\n        _self.updateVal(bounds.data, \"stopx\", stopx + n * conf.boxMargin, Math.max);\n        if (!(type === \"activation\")) {\n          _self.updateVal(item, \"startx\", startx - n * conf.boxMargin, Math.min);\n          _self.updateVal(item, \"stopx\", stopx + n * conf.boxMargin, Math.max);\n          _self.updateVal(bounds.data, \"starty\", starty - n * conf.boxMargin, Math.min);\n          _self.updateVal(bounds.data, \"stopy\", stopy + n * conf.boxMargin, Math.max);\n        }\n      }, \"updateItemBounds\");\n    }\n    __name(updateFn, \"updateFn\");\n    this.sequenceItems.forEach(updateFn());\n    this.activations.forEach(updateFn(\"activation\"));\n  }, \"updateBounds\"),\n  insert: /* @__PURE__ */ __name(function(startx, starty, stopx, stopy) {\n    const _startx = common_default.getMin(startx, stopx);\n    const _stopx = common_default.getMax(startx, stopx);\n    const _starty = common_default.getMin(starty, stopy);\n    const _stopy = common_default.getMax(starty, stopy);\n    this.updateVal(bounds.data, \"startx\", _startx, Math.min);\n    this.updateVal(bounds.data, \"starty\", _starty, Math.min);\n    this.updateVal(bounds.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(bounds.data, \"stopy\", _stopy, Math.max);\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  }, \"insert\"),\n  newActivation: /* @__PURE__ */ __name(function(message, diagram2, actors) {\n    const actorRect = actors.get(message.from);\n    const stackedSize = actorActivations(message.from).length || 0;\n    const x = actorRect.x + actorRect.width / 2 + (stackedSize - 1) * conf.activationWidth / 2;\n    this.activations.push({\n      startx: x,\n      starty: this.verticalPos + 2,\n      stopx: x + conf.activationWidth,\n      stopy: void 0,\n      actor: message.from,\n      anchored: svgDraw_default.anchorElement(diagram2)\n    });\n  }, \"newActivation\"),\n  endActivation: /* @__PURE__ */ __name(function(message) {\n    const lastActorActivationIdx = this.activations.map(function(activation) {\n      return activation.actor;\n    }).lastIndexOf(message.from);\n    return this.activations.splice(lastActorActivationIdx, 1)[0];\n  }, \"endActivation\"),\n  createLoop: /* @__PURE__ */ __name(function(title = { message: void 0, wrap: false, width: void 0 }, fill) {\n    return {\n      startx: void 0,\n      starty: this.verticalPos,\n      stopx: void 0,\n      stopy: void 0,\n      title: title.message,\n      wrap: title.wrap,\n      width: title.width,\n      height: 0,\n      fill\n    };\n  }, \"createLoop\"),\n  newLoop: /* @__PURE__ */ __name(function(title = { message: void 0, wrap: false, width: void 0 }, fill) {\n    this.sequenceItems.push(this.createLoop(title, fill));\n  }, \"newLoop\"),\n  endLoop: /* @__PURE__ */ __name(function() {\n    return this.sequenceItems.pop();\n  }, \"endLoop\"),\n  isLoopOverlap: /* @__PURE__ */ __name(function() {\n    return this.sequenceItems.length ? this.sequenceItems[this.sequenceItems.length - 1].overlap : false;\n  }, \"isLoopOverlap\"),\n  addSectionToLoop: /* @__PURE__ */ __name(function(message) {\n    const loop = this.sequenceItems.pop();\n    loop.sections = loop.sections || [];\n    loop.sectionTitles = loop.sectionTitles || [];\n    loop.sections.push({ y: bounds.getVerticalPos(), height: 0 });\n    loop.sectionTitles.push(message);\n    this.sequenceItems.push(loop);\n  }, \"addSectionToLoop\"),\n  saveVerticalPos: /* @__PURE__ */ __name(function() {\n    if (this.isLoopOverlap()) {\n      this.savedVerticalPos = this.verticalPos;\n    }\n  }, \"saveVerticalPos\"),\n  resetVerticalPos: /* @__PURE__ */ __name(function() {\n    if (this.isLoopOverlap()) {\n      this.verticalPos = this.savedVerticalPos;\n    }\n  }, \"resetVerticalPos\"),\n  bumpVerticalPos: /* @__PURE__ */ __name(function(bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = common_default.getMax(this.data.stopy, this.verticalPos);\n  }, \"bumpVerticalPos\"),\n  getVerticalPos: /* @__PURE__ */ __name(function() {\n    return this.verticalPos;\n  }, \"getVerticalPos\"),\n  getBounds: /* @__PURE__ */ __name(function() {\n    return { bounds: this.data, models: this.models };\n  }, \"getBounds\")\n};\nvar drawNote = /* @__PURE__ */ __name(async function(elem, noteModel) {\n  bounds.bumpVerticalPos(conf.boxMargin);\n  noteModel.height = conf.boxMargin;\n  noteModel.starty = bounds.getVerticalPos();\n  const rect = getNoteRect();\n  rect.x = noteModel.startx;\n  rect.y = noteModel.starty;\n  rect.width = noteModel.width || conf.width;\n  rect.class = \"note\";\n  const g = elem.append(\"g\");\n  const rectElem = svgDraw_default.drawRect(g, rect);\n  const textObj = getTextObj();\n  textObj.x = noteModel.startx;\n  textObj.y = noteModel.starty;\n  textObj.width = rect.width;\n  textObj.dy = \"1em\";\n  textObj.text = noteModel.message;\n  textObj.class = \"noteText\";\n  textObj.fontFamily = conf.noteFontFamily;\n  textObj.fontSize = conf.noteFontSize;\n  textObj.fontWeight = conf.noteFontWeight;\n  textObj.anchor = conf.noteAlign;\n  textObj.textMargin = conf.noteMargin;\n  textObj.valign = \"center\";\n  const textElem = hasKatex(textObj.text) ? await drawKatex(g, textObj) : drawText(g, textObj);\n  const textHeight = Math.round(\n    textElem.map((te) => (te._groups || te)[0][0].getBBox().height).reduce((acc, curr) => acc + curr)\n  );\n  rectElem.attr(\"height\", textHeight + 2 * conf.noteMargin);\n  noteModel.height += textHeight + 2 * conf.noteMargin;\n  bounds.bumpVerticalPos(textHeight + 2 * conf.noteMargin);\n  noteModel.stopy = noteModel.starty + textHeight + 2 * conf.noteMargin;\n  noteModel.stopx = noteModel.startx + rect.width;\n  bounds.insert(noteModel.startx, noteModel.starty, noteModel.stopx, noteModel.stopy);\n  bounds.models.addNote(noteModel);\n}, \"drawNote\");\nvar messageFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight\n  };\n}, \"messageFont\");\nvar noteFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.noteFontFamily,\n    fontSize: cnf.noteFontSize,\n    fontWeight: cnf.noteFontWeight\n  };\n}, \"noteFont\");\nvar actorFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.actorFontFamily,\n    fontSize: cnf.actorFontSize,\n    fontWeight: cnf.actorFontWeight\n  };\n}, \"actorFont\");\nasync function boundMessage(_diagram, msgModel) {\n  bounds.bumpVerticalPos(10);\n  const { startx, stopx, message } = msgModel;\n  const lines = common_default.splitBreaks(message).length;\n  const isKatexMsg = hasKatex(message);\n  const textDims = isKatexMsg ? await calculateMathMLDimensions(message, getConfig2()) : utils_default.calculateTextDimensions(message, messageFont(conf));\n  if (!isKatexMsg) {\n    const lineHeight = textDims.height / lines;\n    msgModel.height += lineHeight;\n    bounds.bumpVerticalPos(lineHeight);\n  }\n  let lineStartY;\n  let totalOffset = textDims.height - 10;\n  const textWidth = textDims.width;\n  if (startx === stopx) {\n    lineStartY = bounds.getVerticalPos() + totalOffset;\n    if (!conf.rightAngles) {\n      totalOffset += conf.boxMargin;\n      lineStartY = bounds.getVerticalPos() + totalOffset;\n    }\n    totalOffset += 30;\n    const dx = common_default.getMax(textWidth / 2, conf.width / 2);\n    bounds.insert(\n      startx - dx,\n      bounds.getVerticalPos() - 10 + totalOffset,\n      stopx + dx,\n      bounds.getVerticalPos() + 30 + totalOffset\n    );\n  } else {\n    totalOffset += conf.boxMargin;\n    lineStartY = bounds.getVerticalPos() + totalOffset;\n    bounds.insert(startx, lineStartY - 10, stopx, lineStartY);\n  }\n  bounds.bumpVerticalPos(totalOffset);\n  msgModel.height += totalOffset;\n  msgModel.stopy = msgModel.starty + msgModel.height;\n  bounds.insert(msgModel.fromBounds, msgModel.starty, msgModel.toBounds, msgModel.stopy);\n  return lineStartY;\n}\n__name(boundMessage, \"boundMessage\");\nvar drawMessage = /* @__PURE__ */ __name(async function(diagram2, msgModel, lineStartY, diagObj) {\n  const { startx, stopx, starty, message, type, sequenceIndex, sequenceVisible } = msgModel;\n  const textDims = utils_default.calculateTextDimensions(message, messageFont(conf));\n  const textObj = getTextObj();\n  textObj.x = startx;\n  textObj.y = starty + 10;\n  textObj.width = stopx - startx;\n  textObj.class = \"messageText\";\n  textObj.dy = \"1em\";\n  textObj.text = message;\n  textObj.fontFamily = conf.messageFontFamily;\n  textObj.fontSize = conf.messageFontSize;\n  textObj.fontWeight = conf.messageFontWeight;\n  textObj.anchor = conf.messageAlign;\n  textObj.valign = \"center\";\n  textObj.textMargin = conf.wrapPadding;\n  textObj.tspan = false;\n  if (hasKatex(textObj.text)) {\n    await drawKatex(diagram2, textObj, { startx, stopx, starty: lineStartY });\n  } else {\n    drawText(diagram2, textObj);\n  }\n  const textWidth = textDims.width;\n  let line;\n  if (startx === stopx) {\n    if (conf.rightAngles) {\n      line = diagram2.append(\"path\").attr(\n        \"d\",\n        `M  ${startx},${lineStartY} H ${startx + common_default.getMax(conf.width / 2, textWidth / 2)} V ${lineStartY + 25} H ${startx}`\n      );\n    } else {\n      line = diagram2.append(\"path\").attr(\n        \"d\",\n        \"M \" + startx + \",\" + lineStartY + \" C \" + (startx + 60) + \",\" + (lineStartY - 10) + \" \" + (startx + 60) + \",\" + (lineStartY + 30) + \" \" + startx + \",\" + (lineStartY + 20)\n      );\n    }\n  } else {\n    line = diagram2.append(\"line\");\n    line.attr(\"x1\", startx);\n    line.attr(\"y1\", lineStartY);\n    line.attr(\"x2\", stopx);\n    line.attr(\"y2\", lineStartY);\n  }\n  if (type === diagObj.db.LINETYPE.DOTTED || type === diagObj.db.LINETYPE.DOTTED_CROSS || type === diagObj.db.LINETYPE.DOTTED_POINT || type === diagObj.db.LINETYPE.DOTTED_OPEN || type === diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED) {\n    line.style(\"stroke-dasharray\", \"3, 3\");\n    line.attr(\"class\", \"messageLine1\");\n  } else {\n    line.attr(\"class\", \"messageLine0\");\n  }\n  let url = \"\";\n  if (conf.arrowMarkerAbsolute) {\n    url = window.location.protocol + \"//\" + window.location.host + window.location.pathname + window.location.search;\n    url = url.replace(/\\(/g, \"\\\\(\");\n    url = url.replace(/\\)/g, \"\\\\)\");\n  }\n  line.attr(\"stroke-width\", 2);\n  line.attr(\"stroke\", \"none\");\n  line.style(\"fill\", \"none\");\n  if (type === diagObj.db.LINETYPE.SOLID || type === diagObj.db.LINETYPE.DOTTED) {\n    line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n  }\n  if (type === diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID || type === diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED) {\n    line.attr(\"marker-start\", \"url(\" + url + \"#arrowhead)\");\n    line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n  }\n  if (type === diagObj.db.LINETYPE.SOLID_POINT || type === diagObj.db.LINETYPE.DOTTED_POINT) {\n    line.attr(\"marker-end\", \"url(\" + url + \"#filled-head)\");\n  }\n  if (type === diagObj.db.LINETYPE.SOLID_CROSS || type === diagObj.db.LINETYPE.DOTTED_CROSS) {\n    line.attr(\"marker-end\", \"url(\" + url + \"#crosshead)\");\n  }\n  if (sequenceVisible || conf.showSequenceNumbers) {\n    line.attr(\"marker-start\", \"url(\" + url + \"#sequencenumber)\");\n    diagram2.append(\"text\").attr(\"x\", startx).attr(\"y\", lineStartY + 4).attr(\"font-family\", \"sans-serif\").attr(\"font-size\", \"12px\").attr(\"text-anchor\", \"middle\").attr(\"class\", \"sequenceNumber\").text(sequenceIndex);\n  }\n}, \"drawMessage\");\nvar addActorRenderingData = /* @__PURE__ */ __name(function(diagram2, actors, createdActors, actorKeys, verticalPos, messages, isFooter) {\n  let prevWidth = 0;\n  let prevMargin = 0;\n  let prevBox = void 0;\n  let maxHeight = 0;\n  for (const actorKey of actorKeys) {\n    const actor = actors.get(actorKey);\n    const box = actor.box;\n    if (prevBox && prevBox != box) {\n      if (!isFooter) {\n        bounds.models.addBox(prevBox);\n      }\n      prevMargin += conf.boxMargin + prevBox.margin;\n    }\n    if (box && box != prevBox) {\n      if (!isFooter) {\n        box.x = prevWidth + prevMargin;\n        box.y = verticalPos;\n      }\n      prevMargin += box.margin;\n    }\n    actor.width = actor.width || conf.width;\n    actor.height = common_default.getMax(actor.height || conf.height, conf.height);\n    actor.margin = actor.margin || conf.actorMargin;\n    maxHeight = common_default.getMax(maxHeight, actor.height);\n    if (createdActors.get(actor.name)) {\n      prevMargin += actor.width / 2;\n    }\n    actor.x = prevWidth + prevMargin;\n    actor.starty = bounds.getVerticalPos();\n    bounds.insert(actor.x, verticalPos, actor.x + actor.width, actor.height);\n    prevWidth += actor.width + prevMargin;\n    if (actor.box) {\n      actor.box.width = prevWidth + box.margin - actor.box.x;\n    }\n    prevMargin = actor.margin;\n    prevBox = actor.box;\n    bounds.models.addActor(actor);\n  }\n  if (prevBox && !isFooter) {\n    bounds.models.addBox(prevBox);\n  }\n  bounds.bumpVerticalPos(maxHeight);\n}, \"addActorRenderingData\");\nvar drawActors = /* @__PURE__ */ __name(async function(diagram2, actors, actorKeys, isFooter) {\n  if (!isFooter) {\n    for (const actorKey of actorKeys) {\n      const actor = actors.get(actorKey);\n      await svgDraw_default.drawActor(diagram2, actor, conf, false);\n    }\n  } else {\n    let maxHeight = 0;\n    bounds.bumpVerticalPos(conf.boxMargin * 2);\n    for (const actorKey of actorKeys) {\n      const actor = actors.get(actorKey);\n      if (!actor.stopy) {\n        actor.stopy = bounds.getVerticalPos();\n      }\n      const height = await svgDraw_default.drawActor(diagram2, actor, conf, true);\n      maxHeight = common_default.getMax(maxHeight, height);\n    }\n    bounds.bumpVerticalPos(maxHeight + conf.boxMargin);\n  }\n}, \"drawActors\");\nvar drawActorsPopup = /* @__PURE__ */ __name(function(diagram2, actors, actorKeys, doc) {\n  let maxHeight = 0;\n  let maxWidth = 0;\n  for (const actorKey of actorKeys) {\n    const actor = actors.get(actorKey);\n    const minMenuWidth = getRequiredPopupWidth(actor);\n    const menuDimensions = svgDraw_default.drawPopup(\n      diagram2,\n      actor,\n      minMenuWidth,\n      conf,\n      conf.forceMenus,\n      doc\n    );\n    if (menuDimensions.height > maxHeight) {\n      maxHeight = menuDimensions.height;\n    }\n    if (menuDimensions.width + actor.x > maxWidth) {\n      maxWidth = menuDimensions.width + actor.x;\n    }\n  }\n  return { maxHeight, maxWidth };\n}, \"drawActorsPopup\");\nvar setConf = /* @__PURE__ */ __name(function(cnf) {\n  assignWithDepth_default(conf, cnf);\n  if (cnf.fontFamily) {\n    conf.actorFontFamily = conf.noteFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.actorFontSize = conf.noteFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.actorFontWeight = conf.noteFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n}, \"setConf\");\nvar actorActivations = /* @__PURE__ */ __name(function(actor) {\n  return bounds.activations.filter(function(activation) {\n    return activation.actor === actor;\n  });\n}, \"actorActivations\");\nvar activationBounds = /* @__PURE__ */ __name(function(actor, actors) {\n  const actorObj = actors.get(actor);\n  const activations = actorActivations(actor);\n  const left = activations.reduce(\n    function(acc, activation) {\n      return common_default.getMin(acc, activation.startx);\n    },\n    actorObj.x + actorObj.width / 2 - 1\n  );\n  const right = activations.reduce(\n    function(acc, activation) {\n      return common_default.getMax(acc, activation.stopx);\n    },\n    actorObj.x + actorObj.width / 2 + 1\n  );\n  return [left, right];\n}, \"activationBounds\");\nfunction adjustLoopHeightForWrap(loopWidths, msg, preMargin, postMargin, addLoopFn) {\n  bounds.bumpVerticalPos(preMargin);\n  let heightAdjust = postMargin;\n  if (msg.id && msg.message && loopWidths[msg.id]) {\n    const loopWidth = loopWidths[msg.id].width;\n    const textConf = messageFont(conf);\n    msg.message = utils_default.wrapLabel(`[${msg.message}]`, loopWidth - 2 * conf.wrapPadding, textConf);\n    msg.width = loopWidth;\n    msg.wrap = true;\n    const textDims = utils_default.calculateTextDimensions(msg.message, textConf);\n    const totalOffset = common_default.getMax(textDims.height, conf.labelBoxHeight);\n    heightAdjust = postMargin + totalOffset;\n    log.debug(`${totalOffset} - ${msg.message}`);\n  }\n  addLoopFn(msg);\n  bounds.bumpVerticalPos(heightAdjust);\n}\n__name(adjustLoopHeightForWrap, \"adjustLoopHeightForWrap\");\nfunction adjustCreatedDestroyedData(msg, msgModel, lineStartY, index, actors, createdActors, destroyedActors) {\n  function receiverAdjustment(actor, adjustment) {\n    if (actor.x < actors.get(msg.from).x) {\n      bounds.insert(\n        msgModel.stopx - adjustment,\n        msgModel.starty,\n        msgModel.startx,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.stopx = msgModel.stopx + adjustment;\n    } else {\n      bounds.insert(\n        msgModel.startx,\n        msgModel.starty,\n        msgModel.stopx + adjustment,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.stopx = msgModel.stopx - adjustment;\n    }\n  }\n  __name(receiverAdjustment, \"receiverAdjustment\");\n  function senderAdjustment(actor, adjustment) {\n    if (actor.x < actors.get(msg.to).x) {\n      bounds.insert(\n        msgModel.startx - adjustment,\n        msgModel.starty,\n        msgModel.stopx,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.startx = msgModel.startx + adjustment;\n    } else {\n      bounds.insert(\n        msgModel.stopx,\n        msgModel.starty,\n        msgModel.startx + adjustment,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.startx = msgModel.startx - adjustment;\n    }\n  }\n  __name(senderAdjustment, \"senderAdjustment\");\n  if (createdActors.get(msg.to) == index) {\n    const actor = actors.get(msg.to);\n    const adjustment = actor.type == \"actor\" ? ACTOR_TYPE_WIDTH / 2 + 3 : actor.width / 2 + 3;\n    receiverAdjustment(actor, adjustment);\n    actor.starty = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  } else if (destroyedActors.get(msg.from) == index) {\n    const actor = actors.get(msg.from);\n    if (conf.mirrorActors) {\n      const adjustment = actor.type == \"actor\" ? ACTOR_TYPE_WIDTH / 2 : actor.width / 2;\n      senderAdjustment(actor, adjustment);\n    }\n    actor.stopy = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  } else if (destroyedActors.get(msg.to) == index) {\n    const actor = actors.get(msg.to);\n    if (conf.mirrorActors) {\n      const adjustment = actor.type == \"actor\" ? ACTOR_TYPE_WIDTH / 2 + 3 : actor.width / 2 + 3;\n      receiverAdjustment(actor, adjustment);\n    }\n    actor.stopy = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  }\n}\n__name(adjustCreatedDestroyedData, \"adjustCreatedDestroyedData\");\nvar draw = /* @__PURE__ */ __name(async function(_text, id, _version, diagObj) {\n  const { securityLevel, sequence } = getConfig2();\n  conf = sequence;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  bounds.init();\n  log.debug(diagObj.db);\n  const diagram2 = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : select(`[id=\"${id}\"]`);\n  const actors = diagObj.db.getActors();\n  const createdActors = diagObj.db.getCreatedActors();\n  const destroyedActors = diagObj.db.getDestroyedActors();\n  const boxes = diagObj.db.getBoxes();\n  let actorKeys = diagObj.db.getActorKeys();\n  const messages = diagObj.db.getMessages();\n  const title = diagObj.db.getDiagramTitle();\n  const hasBoxes = diagObj.db.hasAtLeastOneBox();\n  const hasBoxTitles = diagObj.db.hasAtLeastOneBoxWithTitle();\n  const maxMessageWidthPerActor = await getMaxMessageWidthPerActor(actors, messages, diagObj);\n  conf.height = await calculateActorMargins(actors, maxMessageWidthPerActor, boxes);\n  svgDraw_default.insertComputerIcon(diagram2);\n  svgDraw_default.insertDatabaseIcon(diagram2);\n  svgDraw_default.insertClockIcon(diagram2);\n  if (hasBoxes) {\n    bounds.bumpVerticalPos(conf.boxMargin);\n    if (hasBoxTitles) {\n      bounds.bumpVerticalPos(boxes[0].textMaxHeight);\n    }\n  }\n  if (conf.hideUnusedParticipants === true) {\n    const newActors = /* @__PURE__ */ new Set();\n    messages.forEach((message) => {\n      newActors.add(message.from);\n      newActors.add(message.to);\n    });\n    actorKeys = actorKeys.filter((actorKey) => newActors.has(actorKey));\n  }\n  addActorRenderingData(diagram2, actors, createdActors, actorKeys, 0, messages, false);\n  const loopWidths = await calculateLoopBounds(messages, actors, maxMessageWidthPerActor, diagObj);\n  svgDraw_default.insertArrowHead(diagram2);\n  svgDraw_default.insertArrowCrossHead(diagram2);\n  svgDraw_default.insertArrowFilledHead(diagram2);\n  svgDraw_default.insertSequenceNumber(diagram2);\n  function activeEnd(msg, verticalPos) {\n    const activationData = bounds.endActivation(msg);\n    if (activationData.starty + 18 > verticalPos) {\n      activationData.starty = verticalPos - 6;\n      verticalPos += 12;\n    }\n    svgDraw_default.drawActivation(\n      diagram2,\n      activationData,\n      verticalPos,\n      conf,\n      actorActivations(msg.from).length\n    );\n    bounds.insert(activationData.startx, verticalPos - 10, activationData.stopx, verticalPos);\n  }\n  __name(activeEnd, \"activeEnd\");\n  let sequenceIndex = 1;\n  let sequenceIndexStep = 1;\n  const messagesToDraw = [];\n  const backgrounds = [];\n  let index = 0;\n  for (const msg of messages) {\n    let loopModel, noteModel, msgModel;\n    switch (msg.type) {\n      case diagObj.db.LINETYPE.NOTE:\n        bounds.resetVerticalPos();\n        noteModel = msg.noteModel;\n        await drawNote(diagram2, noteModel);\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_START:\n        bounds.newActivation(msg, diagram2, actors);\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_END:\n        activeEnd(msg, bounds.getVerticalPos());\n        break;\n      case diagObj.db.LINETYPE.LOOP_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.LOOP_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"loop\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.RECT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin,\n          (message) => bounds.newLoop(void 0, message.message)\n        );\n        break;\n      case diagObj.db.LINETYPE.RECT_END:\n        loopModel = bounds.endLoop();\n        backgrounds.push(loopModel);\n        bounds.models.addLoop(loopModel);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        break;\n      case diagObj.db.LINETYPE.OPT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.OPT_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"opt\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.ALT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.ALT_ELSE:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.ALT_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"alt\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.PAR_START:\n      case diagObj.db.LINETYPE.PAR_OVER_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        bounds.saveVerticalPos();\n        break;\n      case diagObj.db.LINETYPE.PAR_AND:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.PAR_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"par\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.AUTONUMBER:\n        sequenceIndex = msg.message.start || sequenceIndex;\n        sequenceIndexStep = msg.message.step || sequenceIndexStep;\n        if (msg.message.visible) {\n          diagObj.db.enableSequenceNumbers();\n        } else {\n          diagObj.db.disableSequenceNumbers();\n        }\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_OPTION:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"critical\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.BREAK_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.BREAK_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"break\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      default:\n        try {\n          msgModel = msg.msgModel;\n          msgModel.starty = bounds.getVerticalPos();\n          msgModel.sequenceIndex = sequenceIndex;\n          msgModel.sequenceVisible = diagObj.db.showSequenceNumbers();\n          const lineStartY = await boundMessage(diagram2, msgModel);\n          adjustCreatedDestroyedData(\n            msg,\n            msgModel,\n            lineStartY,\n            index,\n            actors,\n            createdActors,\n            destroyedActors\n          );\n          messagesToDraw.push({ messageModel: msgModel, lineStartY });\n          bounds.models.addMessage(msgModel);\n        } catch (e) {\n          log.error(\"error while drawing message\", e);\n        }\n    }\n    if ([\n      diagObj.db.LINETYPE.SOLID_OPEN,\n      diagObj.db.LINETYPE.DOTTED_OPEN,\n      diagObj.db.LINETYPE.SOLID,\n      diagObj.db.LINETYPE.DOTTED,\n      diagObj.db.LINETYPE.SOLID_CROSS,\n      diagObj.db.LINETYPE.DOTTED_CROSS,\n      diagObj.db.LINETYPE.SOLID_POINT,\n      diagObj.db.LINETYPE.DOTTED_POINT,\n      diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID,\n      diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED\n    ].includes(msg.type)) {\n      sequenceIndex = sequenceIndex + sequenceIndexStep;\n    }\n    index++;\n  }\n  log.debug(\"createdActors\", createdActors);\n  log.debug(\"destroyedActors\", destroyedActors);\n  await drawActors(diagram2, actors, actorKeys, false);\n  for (const e of messagesToDraw) {\n    await drawMessage(diagram2, e.messageModel, e.lineStartY, diagObj);\n  }\n  if (conf.mirrorActors) {\n    await drawActors(diagram2, actors, actorKeys, true);\n  }\n  backgrounds.forEach((e) => svgDraw_default.drawBackgroundRect(diagram2, e));\n  fixLifeLineHeights(diagram2, actors, actorKeys, conf);\n  for (const box2 of bounds.models.boxes) {\n    box2.height = bounds.getVerticalPos() - box2.y;\n    bounds.insert(box2.x, box2.y, box2.x + box2.width, box2.height);\n    box2.startx = box2.x;\n    box2.starty = box2.y;\n    box2.stopx = box2.startx + box2.width;\n    box2.stopy = box2.starty + box2.height;\n    box2.stroke = \"rgb(0,0,0, 0.5)\";\n    svgDraw_default.drawBox(diagram2, box2, conf);\n  }\n  if (hasBoxes) {\n    bounds.bumpVerticalPos(conf.boxMargin);\n  }\n  const requiredBoxSize = drawActorsPopup(diagram2, actors, actorKeys, doc);\n  const { bounds: box } = bounds.getBounds();\n  if (box.startx === void 0) {\n    box.startx = 0;\n  }\n  if (box.starty === void 0) {\n    box.starty = 0;\n  }\n  if (box.stopx === void 0) {\n    box.stopx = 0;\n  }\n  if (box.stopy === void 0) {\n    box.stopy = 0;\n  }\n  let boxHeight = box.stopy - box.starty;\n  if (boxHeight < requiredBoxSize.maxHeight) {\n    boxHeight = requiredBoxSize.maxHeight;\n  }\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  if (conf.mirrorActors) {\n    height = height - conf.boxMargin + conf.bottomMarginAdj;\n  }\n  let boxWidth = box.stopx - box.startx;\n  if (boxWidth < requiredBoxSize.maxWidth) {\n    boxWidth = requiredBoxSize.maxWidth;\n  }\n  const width = boxWidth + 2 * conf.diagramMarginX;\n  if (title) {\n    diagram2.append(\"text\").text(title).attr(\"x\", (box.stopx - box.startx) / 2 - 2 * conf.diagramMarginX).attr(\"y\", -25);\n  }\n  configureSvgSize(diagram2, height, width, conf.useMaxWidth);\n  const extraVertForTitle = title ? 40 : 0;\n  diagram2.attr(\n    \"viewBox\",\n    box.startx - conf.diagramMarginX + \" -\" + (conf.diagramMarginY + extraVertForTitle) + \" \" + width + \" \" + (height + extraVertForTitle)\n  );\n  log.debug(`models:`, bounds.models);\n}, \"draw\");\nasync function getMaxMessageWidthPerActor(actors, messages, diagObj) {\n  const maxMessageWidthPerActor = {};\n  for (const msg of messages) {\n    if (actors.get(msg.to) && actors.get(msg.from)) {\n      const actor = actors.get(msg.to);\n      if (msg.placement === diagObj.db.PLACEMENT.LEFTOF && !actor.prevActor) {\n        continue;\n      }\n      if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF && !actor.nextActor) {\n        continue;\n      }\n      const isNote = msg.placement !== void 0;\n      const isMessage = !isNote;\n      const textFont = isNote ? noteFont(conf) : messageFont(conf);\n      const wrappedMessage = msg.wrap ? utils_default.wrapLabel(msg.message, conf.width - 2 * conf.wrapPadding, textFont) : msg.message;\n      const messageDimensions = hasKatex(wrappedMessage) ? await calculateMathMLDimensions(msg.message, getConfig2()) : utils_default.calculateTextDimensions(wrappedMessage, textFont);\n      const messageWidth = messageDimensions.width + 2 * conf.wrapPadding;\n      if (isMessage && msg.from === actor.nextActor) {\n        maxMessageWidthPerActor[msg.to] = common_default.getMax(\n          maxMessageWidthPerActor[msg.to] || 0,\n          messageWidth\n        );\n      } else if (isMessage && msg.from === actor.prevActor) {\n        maxMessageWidthPerActor[msg.from] = common_default.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth\n        );\n      } else if (isMessage && msg.from === msg.to) {\n        maxMessageWidthPerActor[msg.from] = common_default.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth / 2\n        );\n        maxMessageWidthPerActor[msg.to] = common_default.getMax(\n          maxMessageWidthPerActor[msg.to] || 0,\n          messageWidth / 2\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF) {\n        maxMessageWidthPerActor[msg.from] = common_default.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.LEFTOF) {\n        maxMessageWidthPerActor[actor.prevActor] = common_default.getMax(\n          maxMessageWidthPerActor[actor.prevActor] || 0,\n          messageWidth\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.OVER) {\n        if (actor.prevActor) {\n          maxMessageWidthPerActor[actor.prevActor] = common_default.getMax(\n            maxMessageWidthPerActor[actor.prevActor] || 0,\n            messageWidth / 2\n          );\n        }\n        if (actor.nextActor) {\n          maxMessageWidthPerActor[msg.from] = common_default.getMax(\n            maxMessageWidthPerActor[msg.from] || 0,\n            messageWidth / 2\n          );\n        }\n      }\n    }\n  }\n  log.debug(\"maxMessageWidthPerActor:\", maxMessageWidthPerActor);\n  return maxMessageWidthPerActor;\n}\n__name(getMaxMessageWidthPerActor, \"getMaxMessageWidthPerActor\");\nvar getRequiredPopupWidth = /* @__PURE__ */ __name(function(actor) {\n  let requiredPopupWidth = 0;\n  const textFont = actorFont(conf);\n  for (const key in actor.links) {\n    const labelDimensions = utils_default.calculateTextDimensions(key, textFont);\n    const labelWidth = labelDimensions.width + 2 * conf.wrapPadding + 2 * conf.boxMargin;\n    if (requiredPopupWidth < labelWidth) {\n      requiredPopupWidth = labelWidth;\n    }\n  }\n  return requiredPopupWidth;\n}, \"getRequiredPopupWidth\");\nasync function calculateActorMargins(actors, actorToMessageWidth, boxes) {\n  let maxHeight = 0;\n  for (const prop of actors.keys()) {\n    const actor = actors.get(prop);\n    if (actor.wrap) {\n      actor.description = utils_default.wrapLabel(\n        actor.description,\n        conf.width - 2 * conf.wrapPadding,\n        actorFont(conf)\n      );\n    }\n    const actDims = hasKatex(actor.description) ? await calculateMathMLDimensions(actor.description, getConfig2()) : utils_default.calculateTextDimensions(actor.description, actorFont(conf));\n    actor.width = actor.wrap ? conf.width : common_default.getMax(conf.width, actDims.width + 2 * conf.wrapPadding);\n    actor.height = actor.wrap ? common_default.getMax(actDims.height, conf.height) : conf.height;\n    maxHeight = common_default.getMax(maxHeight, actor.height);\n  }\n  for (const actorKey in actorToMessageWidth) {\n    const actor = actors.get(actorKey);\n    if (!actor) {\n      continue;\n    }\n    const nextActor = actors.get(actor.nextActor);\n    if (!nextActor) {\n      const messageWidth2 = actorToMessageWidth[actorKey];\n      const actorWidth2 = messageWidth2 + conf.actorMargin - actor.width / 2;\n      actor.margin = common_default.getMax(actorWidth2, conf.actorMargin);\n      continue;\n    }\n    const messageWidth = actorToMessageWidth[actorKey];\n    const actorWidth = messageWidth + conf.actorMargin - actor.width / 2 - nextActor.width / 2;\n    actor.margin = common_default.getMax(actorWidth, conf.actorMargin);\n  }\n  let maxBoxHeight = 0;\n  boxes.forEach((box) => {\n    const textFont = messageFont(conf);\n    let totalWidth = box.actorKeys.reduce((total, aKey) => {\n      return total += actors.get(aKey).width + (actors.get(aKey).margin || 0);\n    }, 0);\n    totalWidth -= 2 * conf.boxTextMargin;\n    if (box.wrap) {\n      box.name = utils_default.wrapLabel(box.name, totalWidth - 2 * conf.wrapPadding, textFont);\n    }\n    const boxMsgDimensions = utils_default.calculateTextDimensions(box.name, textFont);\n    maxBoxHeight = common_default.getMax(boxMsgDimensions.height, maxBoxHeight);\n    const minWidth = common_default.getMax(totalWidth, boxMsgDimensions.width + 2 * conf.wrapPadding);\n    box.margin = conf.boxTextMargin;\n    if (totalWidth < minWidth) {\n      const missing = (minWidth - totalWidth) / 2;\n      box.margin += missing;\n    }\n  });\n  boxes.forEach((box) => box.textMaxHeight = maxBoxHeight);\n  return common_default.getMax(maxHeight, conf.height);\n}\n__name(calculateActorMargins, \"calculateActorMargins\");\nvar buildNoteModel = /* @__PURE__ */ __name(async function(msg, actors, diagObj) {\n  const fromActor = actors.get(msg.from);\n  const toActor = actors.get(msg.to);\n  const startx = fromActor.x;\n  const stopx = toActor.x;\n  const shouldWrap = msg.wrap && msg.message;\n  let textDimensions = hasKatex(msg.message) ? await calculateMathMLDimensions(msg.message, getConfig2()) : utils_default.calculateTextDimensions(\n    shouldWrap ? utils_default.wrapLabel(msg.message, conf.width, noteFont(conf)) : msg.message,\n    noteFont(conf)\n  );\n  const noteModel = {\n    width: shouldWrap ? conf.width : common_default.getMax(conf.width, textDimensions.width + 2 * conf.noteMargin),\n    height: 0,\n    startx: fromActor.x,\n    stopx: 0,\n    starty: 0,\n    stopy: 0,\n    message: msg.message\n  };\n  if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF) {\n    noteModel.width = shouldWrap ? common_default.getMax(conf.width, textDimensions.width) : common_default.getMax(\n      fromActor.width / 2 + toActor.width / 2,\n      textDimensions.width + 2 * conf.noteMargin\n    );\n    noteModel.startx = startx + (fromActor.width + conf.actorMargin) / 2;\n  } else if (msg.placement === diagObj.db.PLACEMENT.LEFTOF) {\n    noteModel.width = shouldWrap ? common_default.getMax(conf.width, textDimensions.width + 2 * conf.noteMargin) : common_default.getMax(\n      fromActor.width / 2 + toActor.width / 2,\n      textDimensions.width + 2 * conf.noteMargin\n    );\n    noteModel.startx = startx - noteModel.width + (fromActor.width - conf.actorMargin) / 2;\n  } else if (msg.to === msg.from) {\n    textDimensions = utils_default.calculateTextDimensions(\n      shouldWrap ? utils_default.wrapLabel(msg.message, common_default.getMax(conf.width, fromActor.width), noteFont(conf)) : msg.message,\n      noteFont(conf)\n    );\n    noteModel.width = shouldWrap ? common_default.getMax(conf.width, fromActor.width) : common_default.getMax(fromActor.width, conf.width, textDimensions.width + 2 * conf.noteMargin);\n    noteModel.startx = startx + (fromActor.width - noteModel.width) / 2;\n  } else {\n    noteModel.width = Math.abs(startx + fromActor.width / 2 - (stopx + toActor.width / 2)) + conf.actorMargin;\n    noteModel.startx = startx < stopx ? startx + fromActor.width / 2 - conf.actorMargin / 2 : stopx + toActor.width / 2 - conf.actorMargin / 2;\n  }\n  if (shouldWrap) {\n    noteModel.message = utils_default.wrapLabel(\n      msg.message,\n      noteModel.width - 2 * conf.wrapPadding,\n      noteFont(conf)\n    );\n  }\n  log.debug(\n    `NM:[${noteModel.startx},${noteModel.stopx},${noteModel.starty},${noteModel.stopy}:${noteModel.width},${noteModel.height}=${msg.message}]`\n  );\n  return noteModel;\n}, \"buildNoteModel\");\nvar buildMessageModel = /* @__PURE__ */ __name(function(msg, actors, diagObj) {\n  if (![\n    diagObj.db.LINETYPE.SOLID_OPEN,\n    diagObj.db.LINETYPE.DOTTED_OPEN,\n    diagObj.db.LINETYPE.SOLID,\n    diagObj.db.LINETYPE.DOTTED,\n    diagObj.db.LINETYPE.SOLID_CROSS,\n    diagObj.db.LINETYPE.DOTTED_CROSS,\n    diagObj.db.LINETYPE.SOLID_POINT,\n    diagObj.db.LINETYPE.DOTTED_POINT,\n    diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID,\n    diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED\n  ].includes(msg.type)) {\n    return {};\n  }\n  const [fromLeft, fromRight] = activationBounds(msg.from, actors);\n  const [toLeft, toRight] = activationBounds(msg.to, actors);\n  const isArrowToRight = fromLeft <= toLeft;\n  let startx = isArrowToRight ? fromRight : fromLeft;\n  let stopx = isArrowToRight ? toLeft : toRight;\n  const isArrowToActivation = Math.abs(toLeft - toRight) > 2;\n  const adjustValue = /* @__PURE__ */ __name((value) => {\n    return isArrowToRight ? -value : value;\n  }, \"adjustValue\");\n  if (msg.from === msg.to) {\n    stopx = startx;\n  } else {\n    if (msg.activate && !isArrowToActivation) {\n      stopx += adjustValue(conf.activationWidth / 2 - 1);\n    }\n    if (![diagObj.db.LINETYPE.SOLID_OPEN, diagObj.db.LINETYPE.DOTTED_OPEN].includes(msg.type)) {\n      stopx += adjustValue(3);\n    }\n    if ([diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID, diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(\n      msg.type\n    )) {\n      startx -= adjustValue(3);\n    }\n  }\n  const allBounds = [fromLeft, fromRight, toLeft, toRight];\n  const boundedWidth = Math.abs(startx - stopx);\n  if (msg.wrap && msg.message) {\n    msg.message = utils_default.wrapLabel(\n      msg.message,\n      common_default.getMax(boundedWidth + 2 * conf.wrapPadding, conf.width),\n      messageFont(conf)\n    );\n  }\n  const msgDims = utils_default.calculateTextDimensions(msg.message, messageFont(conf));\n  return {\n    width: common_default.getMax(\n      msg.wrap ? 0 : msgDims.width + 2 * conf.wrapPadding,\n      boundedWidth + 2 * conf.wrapPadding,\n      conf.width\n    ),\n    height: 0,\n    startx,\n    stopx,\n    starty: 0,\n    stopy: 0,\n    message: msg.message,\n    type: msg.type,\n    wrap: msg.wrap,\n    fromBounds: Math.min.apply(null, allBounds),\n    toBounds: Math.max.apply(null, allBounds)\n  };\n}, \"buildMessageModel\");\nvar calculateLoopBounds = /* @__PURE__ */ __name(async function(messages, actors, _maxWidthPerActor, diagObj) {\n  const loops = {};\n  const stack = [];\n  let current, noteModel, msgModel;\n  for (const msg of messages) {\n    switch (msg.type) {\n      case diagObj.db.LINETYPE.LOOP_START:\n      case diagObj.db.LINETYPE.ALT_START:\n      case diagObj.db.LINETYPE.OPT_START:\n      case diagObj.db.LINETYPE.PAR_START:\n      case diagObj.db.LINETYPE.PAR_OVER_START:\n      case diagObj.db.LINETYPE.CRITICAL_START:\n      case diagObj.db.LINETYPE.BREAK_START:\n        stack.push({\n          id: msg.id,\n          msg: msg.message,\n          from: Number.MAX_SAFE_INTEGER,\n          to: Number.MIN_SAFE_INTEGER,\n          width: 0\n        });\n        break;\n      case diagObj.db.LINETYPE.ALT_ELSE:\n      case diagObj.db.LINETYPE.PAR_AND:\n      case diagObj.db.LINETYPE.CRITICAL_OPTION:\n        if (msg.message) {\n          current = stack.pop();\n          loops[current.id] = current;\n          loops[msg.id] = current;\n          stack.push(current);\n        }\n        break;\n      case diagObj.db.LINETYPE.LOOP_END:\n      case diagObj.db.LINETYPE.ALT_END:\n      case diagObj.db.LINETYPE.OPT_END:\n      case diagObj.db.LINETYPE.PAR_END:\n      case diagObj.db.LINETYPE.CRITICAL_END:\n      case diagObj.db.LINETYPE.BREAK_END:\n        current = stack.pop();\n        loops[current.id] = current;\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_START:\n        {\n          const actorRect = actors.get(msg.from ? msg.from : msg.to.actor);\n          const stackedSize = actorActivations(msg.from ? msg.from : msg.to.actor).length;\n          const x = actorRect.x + actorRect.width / 2 + (stackedSize - 1) * conf.activationWidth / 2;\n          const toAdd = {\n            startx: x,\n            stopx: x + conf.activationWidth,\n            actor: msg.from,\n            enabled: true\n          };\n          bounds.activations.push(toAdd);\n        }\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_END:\n        {\n          const lastActorActivationIdx = bounds.activations.map((a) => a.actor).lastIndexOf(msg.from);\n          bounds.activations.splice(lastActorActivationIdx, 1).splice(0, 1);\n        }\n        break;\n    }\n    const isNote = msg.placement !== void 0;\n    if (isNote) {\n      noteModel = await buildNoteModel(msg, actors, diagObj);\n      msg.noteModel = noteModel;\n      stack.forEach((stk) => {\n        current = stk;\n        current.from = common_default.getMin(current.from, noteModel.startx);\n        current.to = common_default.getMax(current.to, noteModel.startx + noteModel.width);\n        current.width = common_default.getMax(current.width, Math.abs(current.from - current.to)) - conf.labelBoxWidth;\n      });\n    } else {\n      msgModel = buildMessageModel(msg, actors, diagObj);\n      msg.msgModel = msgModel;\n      if (msgModel.startx && msgModel.stopx && stack.length > 0) {\n        stack.forEach((stk) => {\n          current = stk;\n          if (msgModel.startx === msgModel.stopx) {\n            const from = actors.get(msg.from);\n            const to = actors.get(msg.to);\n            current.from = common_default.getMin(\n              from.x - msgModel.width / 2,\n              from.x - from.width / 2,\n              current.from\n            );\n            current.to = common_default.getMax(\n              to.x + msgModel.width / 2,\n              to.x + from.width / 2,\n              current.to\n            );\n            current.width = common_default.getMax(current.width, Math.abs(current.to - current.from)) - conf.labelBoxWidth;\n          } else {\n            current.from = common_default.getMin(msgModel.startx, current.from);\n            current.to = common_default.getMax(msgModel.stopx, current.to);\n            current.width = common_default.getMax(current.width, msgModel.width) - conf.labelBoxWidth;\n          }\n        });\n      }\n    }\n  }\n  bounds.activations = [];\n  log.debug(\"Loop type widths:\", loops);\n  return loops;\n}, \"calculateLoopBounds\");\nvar sequenceRenderer_default = {\n  bounds,\n  drawActors,\n  drawActorsPopup,\n  setConf,\n  draw\n};\n\n// src/diagrams/sequence/sequenceDiagram.ts\nvar diagram = {\n  parser: sequenceDiagram_default,\n  get db() {\n    return new SequenceDB();\n  },\n  renderer: sequenceRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.sequence) {\n      cnf.sequence = {};\n    }\n    if (cnf.wrap) {\n      cnf.sequence.wrap = cnf.wrap;\n      setConfig({ sequence: { wrap: cnf.wrap } });\n    }\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "names": ["drawRect", "__name", "element", "rectData", "rectElement", "append", "attr", "x", "y", "fill", "stroke", "width", "height", "name", "rx", "ry", "attrs", "attrKey", "class", "drawBackgroundRect", "bounds", "startx", "starty", "stopx", "stopy", "lower", "drawText", "textData", "nText", "text", "replace", "lineBreakRegex", "textElem", "style", "anchor", "tspan", "textMargin", "drawImage", "elem", "link", "imageElement", "sanitizedLink", "sanitizeUrl", "drawEmbeddedImage", "getNoteRect", "getTextObj", "ImperativeState", "constructor", "init", "this", "records", "reset", "parser", "o", "k", "v", "o2", "l", "length", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "$Vr", "$Vs", "$Vt", "$Vu", "$Vv", "$Vw", "$Vx", "$Vy", "$Vz", "$VA", "$VB", "$VC", "$VD", "$VE", "parser2", "trace", "yy", "symbols_", "terminals_", "productions_", "performAction", "yytext", "yyleng", "y<PERSON><PERSON>o", "yystate", "$$", "_$", "$0", "apply", "$", "push", "type", "unshift", "boxData", "parseBoxData", "boxText", "sequenceIndex", "Number", "sequenceIndexStep", "sequenceVisible", "signalType", "LINETYPE", "AUTONUMBER", "ACTIVE_START", "actor", "ACTIVE_END", "setDiagramTitle", "substring", "trim", "setAccTitle", "setAccDescription", "loopText", "parseMessage", "LOOP_START", "LOOP_END", "color", "RECT_START", "RECT_END", "optText", "OPT_START", "OPT_END", "altText", "ALT_START", "ALT_END", "parText", "PAR_START", "PAR_END", "PAR_OVER_START", "criticalText", "CRITICAL_START", "CRITICAL_END", "breakText", "BREAK_START", "BREAK_END", "concat", "optionText", "CRITICAL_OPTION", "PAR_AND", "ALT_ELSE", "draw", "description", "placement", "slice", "PLACEMENT", "OVER", "LEFTOF", "RIGHTOF", "from", "to", "msg", "activate", "SOLID_OPEN", "DOTTED_OPEN", "SOLID", "BIDIRECTIONAL_SOLID", "DOTTED", "BIDIRECTIONAL_DOTTED", "SOLID_CROSS", "DOTTED_CROSS", "SOLID_POINT", "DOTTED_POINT", "table", "defaultActions", "parseError", "str", "hash", "recoverable", "error", "Error", "parse", "input", "self", "stack", "tstack", "vstack", "lstack", "recovering", "args", "call", "arguments", "lexer2", "Object", "create", "lexer", "sharedState", "prototype", "hasOwnProperty", "setInput", "yylloc", "yyloc", "ranges", "options", "lex", "token", "pop", "Array", "getPrototypeOf", "n", "symbol", "preErrorSymbol", "state", "action", "r", "p", "len", "newState", "expected", "yyval", "errStr", "showPosition", "join", "match", "line", "loc", "first_line", "last_line", "first_column", "last_column", "range", "EOF", "_input", "_more", "_backtrack", "done", "matched", "conditionStack", "offset", "ch", "unput", "lines", "split", "substr", "oldLines", "more", "reject", "backtrack_lexer", "less", "pastInput", "past", "upcomingInput", "next", "pre", "c", "test_match", "indexed_rule", "backup", "matches", "tempMatch", "index", "rules", "_currentRules", "i", "flex", "begin", "condition", "popState", "conditions", "topState", "Math", "abs", "pushState", "stateStackSize", "yy_", "$avoiding_name_collisions", "YY_START", "<PERSON><PERSON><PERSON>", "sequenceDiagram_default", "NOTE", "ARROWTYPE", "FILLED", "OPEN", "SequenceDB", "prevActor", "actors", "Map", "createdActors", "destroyedActors", "boxes", "messages", "notes", "sequenceNumbersEnabled", "wrapEnabled", "currentBox", "lastCreated", "lastDestroyed", "getAccTitle", "getAccDescription", "getDiagramTitle", "bind", "clear", "setWrap", "getConfig2", "wrap", "addBox", "data", "autoWrap", "<PERSON><PERSON><PERSON><PERSON>", "addActor", "id", "assignedBox", "old", "get", "box", "set", "links", "properties", "actor<PERSON>nt", "prevActorInRecords", "nextActor", "activationCount", "part", "count", "addMessage", "idFrom", "idTo", "message", "answer", "toString", "addSignal", "messageType", "undefined", "hasAtLeastOneBox", "hasAtLeastOneBoxWithTitle", "some", "b", "getMessages", "getBoxes", "getActors", "getCreatedActors", "getDestroyedActors", "getActor", "get<PERSON>ctor<PERSON><PERSON>s", "keys", "enableSequenceNumbers", "disableSequenceNumbers", "showSequenceNumbers", "wrapSetting", "extractWrap", "exec", "cleanedText", "sequence", "trimmedStr", "log", "debug", "JSON", "stringify", "title", "window", "CSS", "supports", "Option", "sanitizeText", "addNote", "note", "addLinks", "actorId", "sanitizedText", "insertLinks", "e", "addALink", "sep", "indexOf", "label", "key", "addProperties", "insertProperties", "boxEnd", "addDetails", "document", "getElementById", "text2", "innerHTML", "details", "getActorProperty", "param", "isArray", "for<PERSON>ach", "item", "start", "step", "visible", "has", "getConfig", "styles_default", "<PERSON><PERSON><PERSON><PERSON>", "actorBkg", "actorTextColor", "actor<PERSON><PERSON><PERSON><PERSON><PERSON>", "signalColor", "sequenceNumberColor", "signalTextColor", "labelBoxBorderColor", "labelBoxBkgColor", "labelTextColor", "loopTextColor", "noteBorderColor", "noteBkgColor", "noteTextColor", "activationBkgColor", "activationBorderColor", "TOP_ACTOR_CLASS", "BOTTOM_ACTOR_CLASS", "ACTOR_MAN_FIGURE_CLASS", "drawRect2", "drawPopup", "minMenuWidth", "textAttrs", "forceMenus", "actorCnt2", "displayValue", "g", "<PERSON><PERSON><PERSON>", "menuWidth", "rectElem", "linkY", "linkElem", "_drawMenuItemTextCandidateFunc", "popupMenuToggle", "popId", "drawKatex", "async", "msgModel", "renderKatex", "dim", "html", "node", "getBoundingClientRect", "round", "<PERSON><PERSON><PERSON><PERSON>", "setAttribute", "rectDim", "getBBox", "temp", "prevTextHeight", "textHeight", "common_default", "_textFontSize", "_textFontSizePx", "parseFontSize", "fontSize", "textElems", "dy", "yfunc", "valign", "dominantBaseline", "alignmentBaseline", "entries", "fontFamily", "fontWeight", "ZERO_WIDTH_SPACE", "span", "_groups", "drawLabel", "txtObject", "genPoints", "cut", "polygon", "fixLifeLineHeights", "diagram2", "conf2", "select", "<PERSON><PERSON><PERSON>", "actor<PERSON><PERSON>", "mirrorActors", "drawActorTypeParticipant", "<PERSON><PERSON>ooter", "actorY", "center", "centerY", "boxplusLineGroup", "rect", "cssclass", "icon", "iconSrc", "char<PERSON>t", "_drawTextCandidateFunc", "hasKatex", "bounds2", "drawActorTypeActor", "actElem", "cssClass", "ACTOR_TYPE_WIDTH", "circle", "drawActor", "drawBox", "drawBackgroundRect2", "textMaxHeight", "anchorElement", "drawActivation", "verticalPos", "actorActivations2", "anchored", "drawLoop", "loopModel", "labelText", "boxMargin", "boxTextMargin", "labelBoxHeight", "labelBoxWidth", "messageFontFamily", "messageFontSize", "messageFontWeight", "drawLoopLine", "sections", "txt", "getTextObj2", "sectionTitles", "idx", "sectionHeight", "map", "te", "reduce", "acc", "curr", "insertDatabaseIcon", "insertComputerIcon", "insertClockIcon", "insertArrowHead", "insertArrowFilledHead", "insertSequenceNumber", "insertArrowCrossHead", "getNoteRect2", "byText", "content", "_setTextAttrs", "byTspan", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_actorFontSize", "_actorFontSizePx", "byFo", "s", "byKatex", "calculateMathMLDimensions", "toText", "fromTextAttrsDict", "textPlacement", "svgDraw_default", "conf", "sequenceItems", "activations", "models", "getHeight", "max", "loops", "it", "h", "boxModel", "<PERSON><PERSON><PERSON><PERSON>", "addLoop", "noteModel", "lastActor", "lastLoop", "lastMessage", "lastNote", "setConf", "updateVal", "obj", "val", "fun", "updateBounds", "_self", "cnt", "updateFn", "min", "insert", "_startx", "getMin", "_stopx", "getMax", "_starty", "_stopy", "newActivation", "<PERSON><PERSON><PERSON><PERSON>", "stackedSize", "actorActivations", "activationWidth", "endActivation", "lastActorActivationIdx", "activation", "lastIndexOf", "splice", "createLoop", "newLoop", "endLoop", "isLoopOverlap", "overlap", "addSectionToLoop", "loop", "getVerticalPos", "saveVerticalPos", "savedVerticalPos", "resetVerticalPos", "bumpVerticalPos", "bump", "getBounds", "drawNote", "textObj", "noteFontFamily", "noteFontSize", "noteFontWeight", "noteAlign", "note<PERSON><PERSON><PERSON>", "messageFont", "cnf", "noteFont", "<PERSON><PERSON><PERSON>", "boundMessage", "_diagram", "splitBreaks", "isKatexMsg", "textDims", "utils_default", "calculateTextDimensions", "lineHeight", "lineStartY", "totalOffset", "textWidth", "rightAngles", "dx", "fromBounds", "toBounds", "drawMessage", "diagObj", "messageAlign", "wrapPadding", "db", "url", "arrowMarkerAbsolute", "location", "protocol", "host", "pathname", "search", "addActorRenderingData", "prevBox", "prevWidth", "<PERSON>v<PERSON><PERSON><PERSON>", "maxHeight", "margin", "<PERSON><PERSON><PERSON><PERSON>", "drawActors", "drawActorsPopup", "doc", "max<PERSON><PERSON><PERSON>", "getRequiredPopupWidth", "menuDimensions", "assignWithDepth_default", "filter", "activationBounds", "<PERSON><PERSON><PERSON><PERSON>", "adjustLoopHeightForWrap", "loopWidths", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "addLoopFn", "heightAdjust", "loopWidth", "textConf", "wrapLabel", "adjustCreatedDestroyedData", "receiverAdjustment", "adjustment", "senderAdjustment", "_text", "_version", "securityLevel", "sandboxElement", "root", "nodes", "contentDocument", "body", "hasBoxes", "hasBoxTitles", "maxMessageWidthPerActor", "getMaxMessageWidthPerActor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hideUnusedParticipants", "newActors", "Set", "add", "calculateLoopBounds", "activeEnd", "activationData", "messagesToDraw", "backgrounds", "messageModel", "includes", "box2", "requiredBoxSize", "boxHeight", "diagramMarginY", "bottomMarginAdj", "boxWidth", "diagramMarginX", "configureSvgSize", "useMaxWidth", "extraVertForTitle", "isNote", "isMessage", "textFont", "wrappedMessage", "messageWidth", "required<PERSON><PERSON><PERSON><PERSON>id<PERSON>", "labelWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prop", "actDims", "actorWidth2", "<PERSON><PERSON><PERSON><PERSON>", "maxBoxHeight", "totalWidth", "total", "a<PERSON><PERSON>", "boxMsgDimensions", "min<PERSON><PERSON><PERSON>", "missing", "buildNoteModel", "fromActor", "to<PERSON><PERSON>", "shouldWrap", "textDimensions", "buildMessageModel", "fromLeft", "fromRight", "toLeft", "toRight", "isArrowToRight", "isArrowToActivation", "adjustValue", "value", "allBounds", "boundedWidth", "msgDims", "_maxWidthPerActor", "current", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "toAdd", "enabled", "a", "stk", "sequenceRenderer_default", "diagram", "renderer", "styles", "setConfig"], "sourceRoot": ""}