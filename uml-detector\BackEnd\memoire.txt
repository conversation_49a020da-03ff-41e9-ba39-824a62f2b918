NOM_CLASSE: Commande
ATTRIBUTS:
- numBonCommande: int
- dateCommande: Date
- dateReglement: Date
- moyenPaiement: String
- totalCommande: float = 0.00
- paiementValide: boolean
- etatCde: string
- statutCde: String
- idClient: int
date
modeReglement
delaLivraison
fraisDePort
montant
MÉTHODES:


NOM_CLASSE: Client
ATTRIBUTS:
-idClient: int
-nomClient: String
-prenomClient: String
-login: String
-password: String
-rue: String
-codePostal: string
-ville: string
-pays: String
-tel: String
-eMail: String
+ Name : string
+ Address : string
+ id : integer
identifier : StringValue
address : StringValue
nom
prenom
email
uid : Integer
email : varchar
password : varchar
nom : varchar
prenom : varchar
telephone : Integer
adresse : varchar
MÉTHODES:
void ajouter()
void supprimer()
void modifier()
ajouterdemande()
modifierdemande()


NOM_CLASSE: Produit
ATTRIBUTS:
-idProd: int
-refProd: string
-nomProd: String
-prixUnitaireHT: float = 0.00
-qteStock: int = 0
-genre: string
-couleur: string
-taille: String
-poids: float
-infosArt: String
+TVA: const = 0.2
-prixTTC: float = 0.00
-idCatProd: int
-idMarque: int
-idActivite: int
MÉTHODES:


NOM_CLASSE: Marque
ATTRIBUTS:
-idMarque: int
-nomMarque: string
MÉTHODES:


NOM_CLASSE: Categorie_produit
ATTRIBUTS:
-idCatProd: int
-nomCategorie: String
MÉTHODES:


NOM_CLASSE: ActiviteSportive
ATTRIBUTS:
-idActivite: int
-nomActivite: String
MÉTHODES:


NOM_CLASSE: Constituer
ATTRIBUTS:
-qte: int = 0
MÉTHODES:


NOM_CLASSE: Stay
ATTRIBUTS:
- Start : date
- End : date
MÉTHODES:
- calculatePrice()


NOM_CLASSE: Hotel Booking
ATTRIBUTS:
Room n° : integer
Start : date
End : date
N° nights : integer
Rec : string
Smoking : boolean
MÉTHODES:


NOM_CLASSE: Plane Ticket
ATTRIBUTS:
Reference : string
MÉTHODES:


NOM_CLASSE: Room Type
ATTRIBUTS:
Single
Double
Family
MÉTHODES:


NOM_CLASSE: Date
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Seminar
ATTRIBUTS:
- name
- id
- fees
MÉTHODES:


NOM_CLASSE: Student
ATTRIBUTS:
name
address
phone
id
mark
+Album Number : String
MÉTHODES:
+Student()
+~Student()


NOM_CLASSE: Enrollment
ATTRIBUTS:
marks
MÉTHODES:


NOM_CLASSE: Professor
ATTRIBUTS:
name
address
phone
email
salary
MÉTHODES:


NOM_CLASSE: Sweeping
ATTRIBUTS:
- id: int
- pages: int
id: int
pages: int
MÉTHODES:


NOM_CLASSE: Publication
ATTRIBUTS:
title: string
MÉTHODES:


NOM_CLASSE: Compilation
ATTRIBUTS:
- id: number
- pages: int
- id: int
MÉTHODES:


NOM_CLASSE: Hardcover
ATTRIBUTS:
- id: int
MÉTHODES:


NOM_CLASSE: Paperback
ATTRIBUTS:
id: int
MÉTHODES:


NOM_CLASSE: Account
ATTRIBUTS:
accountNumber : StringValue
clientAddress : StringValue
Id: int
CustomerId: int
MÉTHODES:


NOM_CLASSE: BalanceMovement
ATTRIBUTS:
amount : DoubleValue
account : StringValue
movementDate : BusinessDate
MÉTHODES:


NOM_CLASSE: Instruction
ATTRIBUTS:
reference : LongValue
amount : DoubleValue
settlementDate : BusinessDate
status : Status[0..1]
clientRef : StringValue
clientAddress : StringValue[
MÉTHODES:


NOM_CLASSE: InstructionImpactSLA
ATTRIBUTS:
- context=account,impactType
- waitForSettlementDate : BooleanValue[0..1]
- impactType : ImpactType[0..1]
MÉTHODES:
- waitForSettlementDate : BooleanValue[0..1]
- impactType : ImpactType[0..1]


NOM_CLASSE: ImpactType
ATTRIBUTS:
- Debit : StringValue = Debit
- Credit : StringValue = Credit
MÉTHODES:


NOM_CLASSE: Panier
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: CartaBanca
ATTRIBUTS:
type
numero
dateValidite
MÉTHODES:


NOM_CLASSE: Student_Group
ATTRIBUTS:
+Group Name : String
+Yearbook: Long
MÉTHODES:
+Student_Group()
+~Student_Group()
+Edit() : Boolean
+StudentCount() : Long
+MaxCount() : Long


NOM_CLASSE: Course
ATTRIBUTS:
Course Name : String
MÉTHODES:
Course()
~Course()
Edit() : Boolean


NOM_CLASSE: Grade
ATTRIBUTS:
+Value : String
MÉTHODES:
+Grade()
+~Grade()
+Edit() : Boolean


NOM_CLASSE: Meeting
ATTRIBUTS:
Term : Date
Place : String
Duration : Long
Subject : String
MÉTHODES:
Meeting()
~Meeting()
Edit() : Boolean


NOM_CLASSE: Attendance
ATTRIBUTS:
-Presence : Boolean
-Notes : String
MÉTHODES:
-Attendance()
-~Attendance()
-Edit() : Boolean


NOM_CLASSE: Final_grade
ATTRIBUTS:
+Pass : Boolean
MÉTHODES:
+Final_grade()
~Final_grade()


NOM_CLASSE: Employee
ATTRIBUTS:
MÉTHODES:
+ Employee()
+ ~Employee()


NOM_CLASSE: Partial_grade
ATTRIBUTS:
+Notes : String
MÉTHODES:
+Partial_grade()
~Partial_grade()


NOM_CLASSE: Object
ATTRIBUTS:
name : String
class : String
MÉTHODES:


NOM_CLASSE: Message
ATTRIBUTS:
operation: String
sequence Number: String
MÉTHODES:


NOM_CLASSE: LifeLine
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Interaction
ATTRIBUTS:
processId : String
MÉTHODES:


NOM_CLASSE: Car
ATTRIBUTS:
Model: String
Color: String
brand: String
type: String
MÉTHODES:


NOM_CLASSE: Wheel
ATTRIBUTS:
Model: String
MÉTHODES:


NOM_CLASSE: Sedan
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Seat
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Door
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Coupe
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: OrderItem
ATTRIBUTS:
- productId: int
- product: String
- price: int
- quantity: int
MÉTHODES:


NOM_CLASSE: Customer
ATTRIBUTS:
customerId : String
Id: int
Name: string
Address: string
PhoneNo: int
AcctNo: int
MÉTHODES:
+GeneralInquiry()
+DepositMoney()
+WithdrawMoney()
+OpenAccount()
+CloseAccount()
+ApplyForLoan()
+RequestCard()


NOM_CLASSE: Order
ATTRIBUTS:
orderNo : int
total : double
MÉTHODES:


NOM_CLASSE: Implementon
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Concrete Implementor
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Abstraction
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Refine
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Location
ATTRIBUTS:
x: double
y: double
label: String
MÉTHODES:


NOM_CLASSE: Balance
ATTRIBUTS:
amount : DoubleValue
balanceDate : BusinessDate
+ amount : DoubleValue
+ balanceDate : BusinessDate
MÉTHODES:


NOM_CLASSE: SLAInterface
ATTRIBUTS:
weight : LongValue
MÉTHODES:


NOM_CLASSE: Historicized
ATTRIBUTS:
MÉTHODES:


NOM_CLASSE: Salma
ATTRIBUTS:
sex
lieu
MÉTHODES:


NOM_CLASSE: Ahmed
ATTRIBUTS:
Nom
Prénom
MÉTHODES:


NOM_CLASSE: fases
ATTRIBUTS:
Titre
Cim
MÉTHODES:


NOM_CLASSE: Fasah
ATTRIBUTS:
Age
Temps
MÉTHODES:


NOM_CLASSE: Khalil
ATTRIBUTS:
Nom
id
MÉTHODES:


NOM_CLASSE: Bank
ATTRIBUTS:
+BankId: int
+Name: string
+Location: string
MÉTHODES:


NOM_CLASSE: Teller
ATTRIBUTS:
Id: int
Name: string
MÉTHODES:
CollectMoney()
OpenAccount()
CloseAccount()
LoanRequest()
ProvideInfo()
IssueCard()


NOM_CLASSE: Loan
ATTRIBUTS:
+Id: int
+Type: string
+AccountId: int
+CustomerId: int
MÉTHODES:


NOM_CLASSE: Savings
ATTRIBUTS:
+Id: int
+CustomerId: int
MÉTHODES:


NOM_CLASSE: Checking
ATTRIBUTS:
+Id: int
+CustomerId: int
MÉTHODES:


NOM_CLASSE: client
ATTRIBUTS:
nom
prenom
adresse
telephone
code postal
- nom
- prénom
- adresse
- téléphone
- code postal
MÉTHODES:
Passe commande()
- passe commande ()
- paie commande (cmd)


NOM_CLASSE: Admin
ATTRIBUTS:
aid : intger
password : varchar
name : varchar
MÉTHODES:
ajouter_client(parameter : client) : boolean
ajouter_transporteur(parameter : transporteur) : boolean
Ajouter_camion(parameter : camion) : boolean
consulter_demande(parameter : demande) : boolean
affecter_demande(parameter)


NOM_CLASSE: demande
ATTRIBUTS:
cid : Integer
uid : Integer
name : varchar
email : varchar
phone : Integer
newAttr : Integer
capacite : Integer
MÉTHODES:
setdemande()
apdatedemande()


NOM_CLASSE: Avis
ATTRIBUTS:
av_id : Integer
commentaire : varchar
cid : Integer
uid : Integer
MÉTHODES:
setavis()
ajout()


NOM_CLASSE: Transporteur
ATTRIBUTS:
tid : Integer
email : varchar
password : varchar
nom : varchar
prenom: varchar
telephone : Integer
idcamion : Integer
etat_tr : Integer
MÉTHODES:
consulterdemande()


NOM_CLASSE: ville
ATTRIBUTS:
id : integer
nom : varchar
lag : Integer
alt : Integer
MÉTHODES:
setdb()
ajout()


NOM_CLASSE: adresse
ATTRIBUTS:
id : Integer
nom : varchar
lag : Integer
MÉTHODES:
setdb()
ajout()


NOM_CLASSE: Camion
ATTRIBUTS:
cid : Integer
marque : varchar
etat : Integer
annee : Integer
MÉTHODES:
setbd()
ajout()


NOM_CLASSE: G_map
ATTRIBUTS:
id : Integer
lat : Integer
Long : Integer
MÉTHODES:


