{"version": 3, "file": "static/js/728.0a9e55d6.chunk.js", "mappings": "0MAOIA,GAA2BC,EAAAA,EAAAA,KAAO,CAACC,EAASC,KAC9C,MAAMC,EAAcF,EAAQG,OAAO,QAgBnC,GAfAD,EAAYE,KAAK,IAAKH,EAASI,GAC/BH,EAAYE,KAAK,IAAKH,EAASK,GAC/BJ,EAAYE,KAAK,OAAQH,EAASM,MAClCL,EAAYE,KAAK,SAAUH,EAASO,QACpCN,EAAYE,KAAK,QAASH,EAASQ,OACnCP,EAAYE,KAAK,SAAUH,EAASS,QAChCT,EAASU,MACXT,EAAYE,KAAK,OAAQH,EAASU,MAEhCV,EAASW,IACXV,EAAYE,KAAK,KAAMH,EAASW,IAE9BX,EAASY,IACXX,EAAYE,KAAK,KAAMH,EAASY,SAEX,IAAnBZ,EAASa,MACX,IAAK,MAAMC,KAAWd,EAASa,MAC7BZ,EAAYE,KAAKW,EAASd,EAASa,MAAMC,IAM7C,OAHId,EAASe,OACXd,EAAYE,KAAK,QAASH,EAASe,OAE9Bd,CAAW,GACjB,YACCe,GAAqClB,EAAAA,EAAAA,KAAO,CAACC,EAASkB,KACxD,MAAMjB,EAAW,CACfI,EAAGa,EAAOC,OACVb,EAAGY,EAAOE,OACVX,MAAOS,EAAOG,MAAQH,EAAOC,OAC7BT,OAAQQ,EAAOI,MAAQJ,EAAOE,OAC9Bb,KAAMW,EAAOX,KACbC,OAAQU,EAAOV,OACfQ,MAAO,QAEWlB,EAASE,EAASC,GAC1BsB,OAAO,GAClB,sBACCC,GAA2BzB,EAAAA,EAAAA,KAAO,CAACC,EAASyB,KAC9C,MAAMC,EAAQD,EAASE,KAAKC,QAAQC,EAAAA,GAAgB,KAC9CC,EAAW9B,EAAQG,OAAO,QAChC2B,EAAS1B,KAAK,IAAKqB,EAASpB,GAC5ByB,EAAS1B,KAAK,IAAKqB,EAASnB,GAC5BwB,EAAS1B,KAAK,QAAS,UACvB0B,EAASC,MAAM,cAAeN,EAASO,QACnCP,EAAST,OACXc,EAAS1B,KAAK,QAASqB,EAAST,OAElC,MAAMiB,EAAQH,EAAS3B,OAAO,SAG9B,OAFA8B,EAAM7B,KAAK,IAAKqB,EAASpB,EAA0B,EAAtBoB,EAASS,YACtCD,EAAMN,KAAKD,GACJI,CAAQ,GACd,YACCK,GAA4BpC,EAAAA,EAAAA,KAAO,CAACqC,EAAM/B,EAAGC,EAAG+B,KAClD,MAAMC,EAAeF,EAAKjC,OAAO,SACjCmC,EAAalC,KAAK,IAAKC,GACvBiC,EAAalC,KAAK,IAAKE,GACvB,MAAMiC,GAAgBC,EAAAA,EAAAA,GAAYH,GAClCC,EAAalC,KAAK,aAAcmC,EAAc,GAC7C,aACCE,GAAoC1C,EAAAA,EAAAA,KAAO,CAACC,EAASK,EAAGC,EAAG+B,KAC7D,MAAMC,EAAetC,EAAQG,OAAO,OACpCmC,EAAalC,KAAK,IAAKC,GACvBiC,EAAalC,KAAK,IAAKE,GACvB,MAAMiC,GAAgBC,EAAAA,EAAAA,GAAYH,GAClCC,EAAalC,KAAK,aAAc,IAAImC,IAAgB,GACnD,qBACCG,GAA8B3C,EAAAA,EAAAA,KAAO,KAClB,CACnBM,EAAG,EACHC,EAAG,EACHG,MAAO,IACPC,OAAQ,IACRH,KAAM,UACNC,OAAQ,OACRwB,OAAQ,QACRpB,GAAI,EACJC,GAAI,KAGL,eACC8B,GAA6B5C,EAAAA,EAAAA,KAAO,KACnB,CACjBM,EAAG,EACHC,EAAG,EACHG,MAAO,IACPC,OAAQ,IACR,cAAe,QACfqB,MAAO,OACPG,WAAY,EACZtB,GAAI,EACJC,GAAI,EACJoB,OAAO,KAGR,a,+FChFCW,EAAS,WACX,IAAIC,GAAoB9C,EAAAA,EAAAA,KAAO,SAAS+C,EAAGC,EAAGC,EAAIC,GAChD,IAAKD,EAAKA,GAAM,CAAC,EAAGC,EAAIH,EAAEI,OAAQD,IAAKD,EAAGF,EAAEG,IAAMF,GAClD,OAAOC,CACT,GAAG,KAAMG,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,GAAI,IAAKC,EAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,GAAI,GAAI,IAAKC,GAAM,CAAC,GAAI,GAAI,GAAI,IAAKC,GAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,GAAM,CAAC,GAAI,IAAKC,GAAM,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IACzyCC,GAAU,CACZC,OAAuB5G,EAAAA,EAAAA,KAAO,WAC9B,GAAG,SACH6G,GAAI,CAAC,EACLC,SAAU,CAAE,MAAS,EAAG,MAAS,EAAG,WAAc,EAAG,UAAa,EAAG,aAAgB,EAAG,aAAgB,EAAG,aAAgB,EAAG,aAAgB,EAAG,YAAe,GAAI,WAAc,GAAI,QAAW,GAAI,WAAc,GAAI,IAAO,GAAI,aAAgB,GAAI,aAAgB,GAAI,WAAc,GAAI,cAAiB,GAAI,gBAAmB,GAAI,kBAAqB,GAAI,eAAkB,GAAI,MAAS,GAAI,eAAkB,GAAI,UAAa,GAAI,gBAAmB,GAAI,UAAa,GAAI,gBAAmB,GAAI,0BAA6B,GAAI,kBAAqB,GAAI,uBAA0B,GAAI,sBAAyB,GAAI,cAAiB,GAAI,OAAU,GAAI,oBAAuB,GAAI,WAAc,GAAI,gBAAmB,GAAI,SAAY,GAAI,mBAAsB,GAAI,KAAQ,GAAI,OAAU,GAAI,OAAU,GAAI,OAAU,GAAI,iBAAoB,GAAI,OAAU,GAAI,WAAc,GAAI,OAAU,GAAI,UAAa,GAAI,aAAgB,GAAI,WAAc,GAAI,cAAiB,GAAI,iBAAoB,GAAI,UAAa,GAAI,aAAgB,GAAI,gBAAmB,GAAI,cAAiB,GAAI,iBAAoB,GAAI,oBAAuB,GAAI,UAAa,GAAI,aAAgB,GAAI,gBAAmB,GAAI,cAAiB,GAAI,iBAAoB,GAAI,oBAAuB,GAAI,IAAO,GAAI,MAAS,GAAI,MAAS,GAAI,MAAS,GAAI,MAAS,GAAI,MAAS,GAAI,MAAS,GAAI,UAAa,GAAI,gBAAmB,GAAI,iBAAoB,GAAI,qBAAwB,GAAI,UAAa,GAAI,IAAO,GAAI,QAAW,GAAI,UAAa,GAAI,UAAa,GAAI,gBAAmB,GAAI,QAAW,EAAG,KAAQ,GACvgDC,WAAY,CAAE,EAAG,QAAS,EAAG,eAAgB,EAAG,eAAgB,EAAG,eAAgB,EAAG,eAAgB,GAAI,aAAc,GAAI,UAAW,GAAI,MAAO,GAAI,eAAgB,GAAI,eAAgB,GAAI,aAAc,GAAI,gBAAiB,GAAI,QAAS,GAAI,iBAAkB,GAAI,YAAa,GAAI,kBAAmB,GAAI,YAAa,GAAI,kBAAmB,GAAI,4BAA6B,GAAI,SAAU,GAAI,sBAAuB,GAAI,kBAAmB,GAAI,WAAY,GAAI,qBAAsB,GAAI,OAAQ,GAAI,SAAU,GAAI,SAAU,GAAI,SAAU,GAAI,SAAU,GAAI,aAAc,GAAI,SAAU,GAAI,YAAa,GAAI,eAAgB,GAAI,aAAc,GAAI,gBAAiB,GAAI,mBAAoB,GAAI,YAAa,GAAI,eAAgB,GAAI,kBAAmB,GAAI,gBAAiB,GAAI,mBAAoB,GAAI,sBAAuB,GAAI,YAAa,GAAI,eAAgB,GAAI,kBAAmB,GAAI,gBAAiB,GAAI,mBAAoB,GAAI,sBAAuB,GAAI,MAAO,GAAI,QAAS,GAAI,QAAS,GAAI,QAAS,GAAI,QAAS,GAAI,QAAS,GAAI,QAAS,GAAI,YAAa,GAAI,kBAAmB,GAAI,mBAAoB,GAAI,uBAAwB,GAAI,MAAO,GAAI,UAAW,GAAI,YAAa,GAAI,YAAa,GAAI,mBACpqCC,aAAc,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,IACnrBC,eAA+BjH,EAAAA,EAAAA,KAAO,SAAmBkH,EAAQC,EAAQC,EAAUP,EAAIQ,EAASC,EAAIC,GAClG,IAAIC,EAAKF,EAAGnE,OAAS,EACrB,OAAQkE,GACN,KAAK,EACHR,EAAGY,aAAa,MAChB,MACF,KAAK,EACHZ,EAAGY,aAAa,MAChB,MACF,KAAK,EACHZ,EAAGY,aAAa,MAChB,MACF,KAAK,EACHZ,EAAGY,aAAa,MAChB,MACF,KAAK,EACL,KAAK,EACL,KAAK,GACL,KAAK,GACL,KAAK,GACHZ,EAAGa,UAAUJ,EAAGE,EAAK,IACrB,MACF,KAAK,GACHX,EAAGc,SAASL,EAAGE,GAAII,UAAU,IAC7BC,KAAKC,EAAIR,EAAGE,GAAII,UAAU,GAC1B,MACF,KAAK,GACHf,EAAGkB,kBAAkBT,EAAGE,GAAII,UAAU,KACtCC,KAAKC,EAAIR,EAAGE,GAAII,UAAU,IAC1B,MACF,KAAK,GACHC,KAAKC,EAAIR,EAAGE,GAAIQ,OAChBnB,EAAGc,SAASE,KAAKC,GACjB,MACF,KAAK,GACL,KAAK,GACHD,KAAKC,EAAIR,EAAGE,GAAIQ,OAChBnB,EAAGkB,kBAAkBF,KAAKC,GAC1B,MACF,KAAK,GACHR,EAAGE,GAAIS,OAAO,EAAG,EAAG,cACpBpB,EAAGqB,6BAA6BZ,EAAGE,IACnCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHF,EAAGE,GAAIS,OAAO,EAAG,EAAG,UACpBpB,EAAGqB,6BAA6BZ,EAAGE,IACnCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGqB,6BAA6BZ,EAAGE,IACnCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHF,EAAGE,GAAIS,OAAO,EAAG,EAAG,aACpBpB,EAAGsB,wBAAwBb,EAAGE,IAC9BK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGuB,kBAAkB,UAAWd,EAAGE,IACnCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGuB,kBAAkB,WAAYd,EAAGE,IACpCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGuB,kBAAkB,WAAYd,EAAGE,IACpCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGwB,wBACH,MACF,KAAK,GACHxB,EAAGyB,kBAAkB,YAAahB,EAAGE,IACrCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGyB,kBAAkB,qBAAsBhB,EAAGE,IAC9CK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGyB,kBAAkB,YAAahB,EAAGE,IACrCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGyB,kBAAkB,eAAgBhB,EAAGE,IACxCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGyB,kBAAkB,kBAAmBhB,EAAGE,IAC3CK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGyB,kBAAkB,qBAAsBhB,EAAGE,IAC9CK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGyB,kBAAkB,wBAAyBhB,EAAGE,IACjDK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAGyB,kBAAkB,2BAA4BhB,EAAGE,IACpDK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG0B,aAAa,eAAgBjB,EAAGE,IACnCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG0B,aAAa,kBAAmBjB,EAAGE,IACtCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG0B,aAAa,qBAAsBjB,EAAGE,IACzCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG0B,aAAa,wBAAyBjB,EAAGE,IAC5CK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG0B,aAAa,2BAA4BjB,EAAGE,IAC/CK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG0B,aAAa,8BAA+BjB,EAAGE,IAClDK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG2B,aAAa,eAAgBlB,EAAGE,IACnCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG2B,aAAa,kBAAmBlB,EAAGE,IACtCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG2B,aAAa,qBAAsBlB,EAAGE,IACzCK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG2B,aAAa,wBAAyBlB,EAAGE,IAC5CK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG2B,aAAa,2BAA4BlB,EAAGE,IAC/CK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG2B,aAAa,8BAA+BlB,EAAGE,IAClDK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG4B,OAAO,SAAUnB,EAAGE,IACvBK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG4B,OAAO,WAAYnB,EAAGE,IACzBK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG4B,OAAO,WAAYnB,EAAGE,IACzBK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG4B,OAAO,WAAYnB,EAAGE,IACzBK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG4B,OAAO,WAAYnB,EAAGE,IACzBK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG4B,OAAO,WAAYnB,EAAGE,IACzBK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG4B,OAAO,WAAYnB,EAAGE,IACzBK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHF,EAAGE,GAAIS,OAAO,EAAG,GACjBpB,EAAG4B,OAAO,SAAUnB,EAAGE,IACvBK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG6B,cAAc,qBAAsBpB,EAAGE,IAC1CK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG8B,eAAe,sBAAuBrB,EAAGE,IAC5CK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHX,EAAG+B,mBAAmB,0BAA2BtB,EAAGE,IACpDK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACHK,KAAKC,EAAI,CAACR,EAAGE,IACb,MACF,KAAK,GACHF,EAAGE,GAAIqB,QAAQvB,EAAGE,EAAK,IACvBK,KAAKC,EAAIR,EAAGE,GACZ,MACF,KAAK,GACL,KAAK,GACHK,KAAKC,EAAIR,EAAGE,GAAIQ,OAChB,MACF,KAAK,GACH,IAAIc,EAAK,CAAC,EACVA,EAAGxB,EAAGE,EAAK,GAAGQ,QAAUV,EAAGE,GAAIQ,OAC/BH,KAAKC,EAAIgB,EACT,MACF,KAAK,GACHjB,KAAKC,EAAI,GAGf,GAAG,aACHiB,MAAO,CAAC,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAAG,GAAI,EAAG,CAAC,EAAG,GAAI,EAAG,CAAC,EAAG,GAAI,EAAG,CAAC,EAAG,GAAI,GAAI,EAAG,GAAI,CAAC,EAAG,GAAI,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI3F,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI1C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI1C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI1C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI1C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,CAAC,EAAG,KAAOhD,EAAEiD,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAItC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQhD,EAAEiD,EAAK,CAAC,EAAG,KAAMjD,EAAEkD,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,CAAC,EAAG,MAAQlD,EAAEiD,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,CAAC,EAAG,MAAQjD,EAAEmD,EAAK,CAAC,EAAG,KAAMnD,EAAEmD,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAOnD,EAAEmD,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAOvD,EAAEwD,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIJ,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI5C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,CAAC,EAAG,KAAM,GAAI,CAAC,EAAG,MAAQ,CAAE,GAAI,IAAK,GAAI,GAAI,GAAII,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,IAAK,GAAI,GAAI,GAAIH,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,EAAG,CAAC,EAAG,IAAMvD,EAAEiD,EAAK,CAAC,EAAG,KAAMjD,EAAEkD,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,IAAK,GAAI5C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQV,EAAEiD,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,GAAI3C,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQhD,EAAEmD,EAAK,CAAC,EAAG,KAAMnD,EAAEmD,EAAK,CAAC,EAAG,KAAMnD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEyD,GAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,IAAK,GAAIL,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQvD,EAAE0D,GAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,MAAQ1D,EAAE0D,GAAK,CAAC,EAAG,KAAM1D,EAAE0D,GAAK,CAAC,EAAG,KAAM1D,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,IAAK,GAAI,CAAC,EAAG,MAAQ,CAAE,GAAI,CAAC,EAAG,MAAQ,CAAE,GAAI,CAAC,EAAG,MAAQxD,EAAE2D,GAAK,CAAC,EAAG,KAAM3D,EAAE2D,GAAK,CAAC,EAAG,KAAM3D,EAAE2D,GAAK,CAAC,EAAG,KAAM3D,EAAE2D,GAAK,CAAC,EAAG,KAAM3D,EAAE2D,GAAK,CAAC,EAAG,KAAM3D,EAAE2D,GAAK,CAAC,EAAG,KAAM3D,EAAE2D,GAAK,CAAC,EAAG,KAAM,CAAE,EAAG,CAAC,EAAG,IAAM,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO,CAAE,EAAG,CAAC,EAAG,KAAO3D,EAAEkD,EAAK,CAAC,EAAG,KAAMlD,EAAEiD,EAAK,CAAC,EAAG,KAAMjD,EAAEyD,GAAK,CAAC,EAAG,KAAMzD,EAAE0D,GAAK,CAAC,EAAG,KAAM1D,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAEwD,EAAK,CAAC,EAAG,KAAMxD,EAAE4D,GAAK,CAAC,EAAG,KAAM5D,EAAE4D,GAAK,CAAC,EAAG,IAAK,CAAE,GAAI,CAAC,EAAG,OAAS5D,EAAE4D,GAAK,CAAC,EAAG,MACr5OsC,eAAgB,CAAE,EAAG,CAAC,EAAG,GAAI,EAAG,CAAC,EAAG,GAAI,EAAG,CAAC,EAAG,GAAI,EAAG,CAAC,EAAG,GAAI,EAAG,CAAC,EAAG,GAAI,EAAG,CAAC,EAAG,GAAI,EAAG,CAAC,EAAG,GAAI,GAAI,CAAC,EAAG,GAAI,IAAK,CAAC,EAAG,GAAI,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,IAAK,IAAK,CAAC,EAAG,KAC7JC,YAA4BjJ,EAAAA,EAAAA,KAAO,SAAoBkJ,EAAKC,GAC1D,IAAIA,EAAKC,YAEF,CACL,IAAIC,EAAQ,IAAIC,MAAMJ,GAEtB,MADAG,EAAMF,KAAOA,EACPE,CACR,CALExB,KAAKjB,MAAMsC,EAMf,GAAG,cACHK,OAAuBvJ,EAAAA,EAAAA,KAAO,SAAewJ,GAC3C,IAAIC,EAAO5B,KAAM6B,EAAQ,CAAC,GAAIC,EAAS,GAAIC,EAAS,CAAC,MAAOC,EAAS,GAAId,EAAQlB,KAAKkB,MAAO7B,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAG2C,EAAa,EAC7IC,EAAOF,EAAOG,MAAMC,KAAKC,UAAW,GACpCC,EAASC,OAAOC,OAAOxC,KAAKyC,OAC5BC,EAAc,CAAE1D,GAAI,CAAC,GACzB,IAAK,IAAI9D,KAAK8E,KAAKhB,GACbuD,OAAOI,UAAUC,eAAeR,KAAKpC,KAAKhB,GAAI9D,KAChDwH,EAAY1D,GAAG9D,GAAK8E,KAAKhB,GAAG9D,IAGhCoH,EAAOO,SAASlB,EAAOe,EAAY1D,IACnC0D,EAAY1D,GAAGyD,MAAQH,EACvBI,EAAY1D,GAAGhE,OAASgF,KACI,oBAAjBsC,EAAOQ,SAChBR,EAAOQ,OAAS,CAAC,GAEnB,IAAIC,EAAQT,EAAOQ,OACnBd,EAAOgB,KAAKD,GACZ,IAAIE,EAASX,EAAOY,SAAWZ,EAAOY,QAAQD,OAY9C,SAASE,IACP,IAAIC,EASJ,MAPqB,kBADrBA,EAAQtB,EAAOuB,OAASf,EAAOa,OA/BqI,KAiC9JC,aAAiBE,QAEnBF,GADAtB,EAASsB,GACMC,OAEjBD,EAAQxB,EAAK3C,SAASmE,IAAUA,GAE3BA,CACT,CAtByC,oBAA9BV,EAAY1D,GAAGoC,WACxBpB,KAAKoB,WAAasB,EAAY1D,GAAGoC,WAEjCpB,KAAKoB,WAAamB,OAAOgB,eAAevD,MAAMoB,YAOhDjJ,EAAAA,EAAAA,KALA,SAAkBqL,GAChB3B,EAAMvG,OAASuG,EAAMvG,OAAS,EAAIkI,EAClCzB,EAAOzG,OAASyG,EAAOzG,OAASkI,EAChCxB,EAAO1G,OAAS0G,EAAO1G,OAASkI,CAClC,GACiB,aAajBrL,EAAAA,EAAAA,IAAOgL,EAAK,OAEZ,IADA,IAAIM,EAAQC,EAAgBC,EAAOC,EAAWC,EAAeC,EAAGC,EAAKC,EAAUC,EAA9BC,EAAQ,CAAC,IAC7C,CAUX,GATAP,EAAQ9B,EAAMA,EAAMvG,OAAS,GACzB0E,KAAKmB,eAAewC,GACtBC,EAAS5D,KAAKmB,eAAewC,IAEd,OAAXF,GAAoC,oBAAVA,IAC5BA,EAASN,KAEXS,EAAS1C,EAAMyC,IAAUzC,EAAMyC,GAAOF,IAElB,qBAAXG,IAA2BA,EAAOtI,SAAWsI,EAAO,GAAI,CACjE,IAAIO,EAAS,GAEb,IAAKL,KADLG,EAAW,GACD/C,EAAMyC,GACV3D,KAAKd,WAAW4E,IAAMA,EAzD6H,GA0DrJG,EAASjB,KAAK,IAAMhD,KAAKd,WAAW4E,GAAK,KAI3CK,EADE7B,EAAO8B,aACA,wBAA0B7E,EAAW,GAAK,MAAQ+C,EAAO8B,eAAiB,eAAiBH,EAASI,KAAK,MAAQ,WAAarE,KAAKd,WAAWuE,IAAWA,GAAU,IAEnK,wBAA0BlE,EAAW,GAAK,iBAhE6G,GAgE1FkE,EAAgB,eAAiB,KAAOzD,KAAKd,WAAWuE,IAAWA,GAAU,KAErJzD,KAAKoB,WAAW+C,EAAQ,CACtBpK,KAAMuI,EAAOgC,MACblB,MAAOpD,KAAKd,WAAWuE,IAAWA,EAClCc,KAAMjC,EAAO/C,SACbiF,IAAKzB,EACLkB,YAEJ,CACA,GAAIL,EAAO,aAAcN,OAASM,EAAOtI,OAAS,EAChD,MAAM,IAAImG,MAAM,oDAAsDkC,EAAQ,YAAcF,GAE9F,OAAQG,EAAO,IACb,KAAK,EACH/B,EAAMmB,KAAKS,GACX1B,EAAOiB,KAAKV,EAAOjD,QACnB2C,EAAOgB,KAAKV,EAAOQ,QACnBjB,EAAMmB,KAAKY,EAAO,IAClBH,EAAS,KACJC,GASHD,EAASC,EACTA,EAAiB,OATjBpE,EAASgD,EAAOhD,OAChBD,EAASiD,EAAOjD,OAChBE,EAAW+C,EAAO/C,SAClBwD,EAAQT,EAAOQ,OACXb,EAAa,GACfA,KAMJ,MACF,KAAK,EAwBH,GAvBA8B,EAAM/D,KAAKb,aAAayE,EAAO,IAAI,GACnCM,EAAMjE,EAAI8B,EAAOA,EAAOzG,OAASyI,GACjCG,EAAMxE,GAAK,CACT+E,WAAYzC,EAAOA,EAAO1G,QAAUyI,GAAO,IAAIU,WAC/CC,UAAW1C,EAAOA,EAAO1G,OAAS,GAAGoJ,UACrCC,aAAc3C,EAAOA,EAAO1G,QAAUyI,GAAO,IAAIY,aACjDC,YAAa5C,EAAOA,EAAO1G,OAAS,GAAGsJ,aAErC3B,IACFiB,EAAMxE,GAAGmF,MAAQ,CACf7C,EAAOA,EAAO1G,QAAUyI,GAAO,IAAIc,MAAM,GACzC7C,EAAOA,EAAO1G,OAAS,GAAGuJ,MAAM,KAYnB,qBATjBhB,EAAI7D,KAAKZ,cAAc0F,MAAMZ,EAAO,CAClC7E,EACAC,EACAC,EACAmD,EAAY1D,GACZ4E,EAAO,GACP7B,EACAC,GACA+C,OAAO7C,KAEP,OAAO2B,EAELE,IACFlC,EAAQA,EAAMM,MAAM,GAAI,EAAI4B,EAAM,GAClChC,EAASA,EAAOI,MAAM,GAAI,EAAI4B,GAC9B/B,EAASA,EAAOG,MAAM,GAAI,EAAI4B,IAEhClC,EAAMmB,KAAKhD,KAAKb,aAAayE,EAAO,IAAI,IACxC7B,EAAOiB,KAAKkB,EAAMjE,GAClB+B,EAAOgB,KAAKkB,EAAMxE,IAClBsE,EAAW9C,EAAMW,EAAMA,EAAMvG,OAAS,IAAIuG,EAAMA,EAAMvG,OAAS,IAC/DuG,EAAMmB,KAAKgB,GACX,MACF,KAAK,EACH,OAAO,EAEb,CACA,OAAO,CACT,GAAG,UAEDvB,GAAwB,WAmlB1B,MAllBa,CACXuC,IAAK,EACL5D,YAA4BjJ,EAAAA,EAAAA,KAAO,SAAoBkJ,EAAKC,GAC1D,IAAItB,KAAKhB,GAAGhE,OAGV,MAAM,IAAIyG,MAAMJ,GAFhBrB,KAAKhB,GAAGhE,OAAOoG,WAAWC,EAAKC,EAInC,GAAG,cAEHuB,UAA0B1K,EAAAA,EAAAA,KAAO,SAASwJ,EAAO3C,GAiB/C,OAhBAgB,KAAKhB,GAAKA,GAAMgB,KAAKhB,IAAM,CAAC,EAC5BgB,KAAKiF,OAAStD,EACd3B,KAAKkF,MAAQlF,KAAKmF,WAAanF,KAAKoF,MAAO,EAC3CpF,KAAKT,SAAWS,KAAKV,OAAS,EAC9BU,KAAKX,OAASW,KAAKqF,QAAUrF,KAAKsE,MAAQ,GAC1CtE,KAAKsF,eAAiB,CAAC,WACvBtF,KAAK8C,OAAS,CACZ2B,WAAY,EACZE,aAAc,EACdD,UAAW,EACXE,YAAa,GAEX5E,KAAKkD,QAAQD,SACfjD,KAAK8C,OAAO+B,MAAQ,CAAC,EAAG,IAE1B7E,KAAKuF,OAAS,EACPvF,IACT,GAAG,YAEH2B,OAAuBxJ,EAAAA,EAAAA,KAAO,WAC5B,IAAIqN,EAAKxF,KAAKiF,OAAO,GAiBrB,OAhBAjF,KAAKX,QAAUmG,EACfxF,KAAKV,SACLU,KAAKuF,SACLvF,KAAKsE,OAASkB,EACdxF,KAAKqF,SAAWG,EACJA,EAAGlB,MAAM,oBAEnBtE,KAAKT,WACLS,KAAK8C,OAAO4B,aAEZ1E,KAAK8C,OAAO8B,cAEV5E,KAAKkD,QAAQD,QACfjD,KAAK8C,OAAO+B,MAAM,KAEpB7E,KAAKiF,OAASjF,KAAKiF,OAAO9C,MAAM,GACzBqD,CACT,GAAG,SAEHC,OAAuBtN,EAAAA,EAAAA,KAAO,SAASqN,GACrC,IAAIzB,EAAMyB,EAAGlK,OACToK,EAAQF,EAAGG,MAAM,iBACrB3F,KAAKiF,OAASO,EAAKxF,KAAKiF,OACxBjF,KAAKX,OAASW,KAAKX,OAAOuG,OAAO,EAAG5F,KAAKX,OAAO/D,OAASyI,GACzD/D,KAAKuF,QAAUxB,EACf,IAAI8B,EAAW7F,KAAKsE,MAAMqB,MAAM,iBAChC3F,KAAKsE,MAAQtE,KAAKsE,MAAMsB,OAAO,EAAG5F,KAAKsE,MAAMhJ,OAAS,GACtD0E,KAAKqF,QAAUrF,KAAKqF,QAAQO,OAAO,EAAG5F,KAAKqF,QAAQ/J,OAAS,GACxDoK,EAAMpK,OAAS,IACjB0E,KAAKT,UAAYmG,EAAMpK,OAAS,GAElC,IAAIuI,EAAI7D,KAAK8C,OAAO+B,MAWpB,OAVA7E,KAAK8C,OAAS,CACZ2B,WAAYzE,KAAK8C,OAAO2B,WACxBC,UAAW1E,KAAKT,SAAW,EAC3BoF,aAAc3E,KAAK8C,OAAO6B,aAC1BC,YAAac,GAASA,EAAMpK,SAAWuK,EAASvK,OAAS0E,KAAK8C,OAAO6B,aAAe,GAAKkB,EAASA,EAASvK,OAASoK,EAAMpK,QAAQA,OAASoK,EAAM,GAAGpK,OAAS0E,KAAK8C,OAAO6B,aAAeZ,GAEtL/D,KAAKkD,QAAQD,SACfjD,KAAK8C,OAAO+B,MAAQ,CAAChB,EAAE,GAAIA,EAAE,GAAK7D,KAAKV,OAASyE,IAElD/D,KAAKV,OAASU,KAAKX,OAAO/D,OACnB0E,IACT,GAAG,SAEH8F,MAAsB3N,EAAAA,EAAAA,KAAO,WAE3B,OADA6H,KAAKkF,OAAQ,EACNlF,IACT,GAAG,QAEH+F,QAAwB5N,EAAAA,EAAAA,KAAO,WAC7B,OAAI6H,KAAKkD,QAAQ8C,iBACfhG,KAAKmF,YAAa,EAQbnF,MANEA,KAAKoB,WAAW,0BAA4BpB,KAAKT,SAAW,GAAK,mIAAqIS,KAAKoE,eAAgB,CAChOrK,KAAM,GACNqJ,MAAO,KACPmB,KAAMvE,KAAKT,UAIjB,GAAG,UAEH0G,MAAsB9N,EAAAA,EAAAA,KAAO,SAASqL,GACpCxD,KAAKyF,MAAMzF,KAAKsE,MAAMnC,MAAMqB,GAC9B,GAAG,QAEH0C,WAA2B/N,EAAAA,EAAAA,KAAO,WAChC,IAAIgO,EAAOnG,KAAKqF,QAAQO,OAAO,EAAG5F,KAAKqF,QAAQ/J,OAAS0E,KAAKsE,MAAMhJ,QACnE,OAAQ6K,EAAK7K,OAAS,GAAK,MAAQ,IAAM6K,EAAKP,QAAQ,IAAI5L,QAAQ,MAAO,GAC3E,GAAG,aAEHoM,eAA+BjO,EAAAA,EAAAA,KAAO,WACpC,IAAIkO,EAAOrG,KAAKsE,MAIhB,OAHI+B,EAAK/K,OAAS,KAChB+K,GAAQrG,KAAKiF,OAAOW,OAAO,EAAG,GAAKS,EAAK/K,UAElC+K,EAAKT,OAAO,EAAG,KAAOS,EAAK/K,OAAS,GAAK,MAAQ,KAAKtB,QAAQ,MAAO,GAC/E,GAAG,iBAEHoK,cAA8BjM,EAAAA,EAAAA,KAAO,WACnC,IAAImO,EAAMtG,KAAKkG,YACXK,EAAK,IAAIjD,MAAMgD,EAAIhL,OAAS,GAAG+I,KAAK,KACxC,OAAOiC,EAAMtG,KAAKoG,gBAAkB,KAAOG,EAAK,GAClD,GAAG,gBAEHC,YAA4BrO,EAAAA,EAAAA,KAAO,SAASmM,EAAOmC,GACjD,IAAIrD,EAAOsC,EAAOgB,EAmDlB,GAlDI1G,KAAKkD,QAAQ8C,kBACfU,EAAS,CACPnH,SAAUS,KAAKT,SACfuD,OAAQ,CACN2B,WAAYzE,KAAK8C,OAAO2B,WACxBC,UAAW1E,KAAK0E,UAChBC,aAAc3E,KAAK8C,OAAO6B,aAC1BC,YAAa5E,KAAK8C,OAAO8B,aAE3BvF,OAAQW,KAAKX,OACbiF,MAAOtE,KAAKsE,MACZqC,QAAS3G,KAAK2G,QACdtB,QAASrF,KAAKqF,QACd/F,OAAQU,KAAKV,OACbiG,OAAQvF,KAAKuF,OACbL,MAAOlF,KAAKkF,MACZD,OAAQjF,KAAKiF,OACbjG,GAAIgB,KAAKhB,GACTsG,eAAgBtF,KAAKsF,eAAenD,MAAM,GAC1CiD,KAAMpF,KAAKoF,MAETpF,KAAKkD,QAAQD,SACfyD,EAAO5D,OAAO+B,MAAQ7E,KAAK8C,OAAO+B,MAAM1C,MAAM,MAGlDuD,EAAQpB,EAAM,GAAGA,MAAM,sBAErBtE,KAAKT,UAAYmG,EAAMpK,QAEzB0E,KAAK8C,OAAS,CACZ2B,WAAYzE,KAAK8C,OAAO4B,UACxBA,UAAW1E,KAAKT,SAAW,EAC3BoF,aAAc3E,KAAK8C,OAAO8B,YAC1BA,YAAac,EAAQA,EAAMA,EAAMpK,OAAS,GAAGA,OAASoK,EAAMA,EAAMpK,OAAS,GAAGgJ,MAAM,UAAU,GAAGhJ,OAAS0E,KAAK8C,OAAO8B,YAAcN,EAAM,GAAGhJ,QAE/I0E,KAAKX,QAAUiF,EAAM,GACrBtE,KAAKsE,OAASA,EAAM,GACpBtE,KAAK2G,QAAUrC,EACftE,KAAKV,OAASU,KAAKX,OAAO/D,OACtB0E,KAAKkD,QAAQD,SACfjD,KAAK8C,OAAO+B,MAAQ,CAAC7E,KAAKuF,OAAQvF,KAAKuF,QAAUvF,KAAKV,SAExDU,KAAKkF,OAAQ,EACblF,KAAKmF,YAAa,EAClBnF,KAAKiF,OAASjF,KAAKiF,OAAO9C,MAAMmC,EAAM,GAAGhJ,QACzC0E,KAAKqF,SAAWf,EAAM,GACtBlB,EAAQpD,KAAKZ,cAAcgD,KAAKpC,KAAMA,KAAKhB,GAAIgB,KAAMyG,EAAczG,KAAKsF,eAAetF,KAAKsF,eAAehK,OAAS,IAChH0E,KAAKoF,MAAQpF,KAAKiF,SACpBjF,KAAKoF,MAAO,GAEVhC,EACF,OAAOA,EACF,GAAIpD,KAAKmF,WAAY,CAC1B,IAAK,IAAIjK,KAAKwL,EACZ1G,KAAK9E,GAAKwL,EAAOxL,GAEnB,OAAO,CACT,CACA,OAAO,CACT,GAAG,cAEHmL,MAAsBlO,EAAAA,EAAAA,KAAO,WAC3B,GAAI6H,KAAKoF,KACP,OAAOpF,KAAKgF,IAKd,IAAI5B,EAAOkB,EAAOsC,EAAWC,EAHxB7G,KAAKiF,SACRjF,KAAKoF,MAAO,GAGTpF,KAAKkF,QACRlF,KAAKX,OAAS,GACdW,KAAKsE,MAAQ,IAGf,IADA,IAAIwC,EAAQ9G,KAAK+G,gBACRC,EAAI,EAAGA,EAAIF,EAAMxL,OAAQ0L,IAEhC,IADAJ,EAAY5G,KAAKiF,OAAOX,MAAMtE,KAAK8G,MAAMA,EAAME,SAC5B1C,GAASsC,EAAU,GAAGtL,OAASgJ,EAAM,GAAGhJ,QAAS,CAGlE,GAFAgJ,EAAQsC,EACRC,EAAQG,EACJhH,KAAKkD,QAAQ8C,gBAAiB,CAEhC,IAAc,KADd5C,EAAQpD,KAAKwG,WAAWI,EAAWE,EAAME,KAEvC,OAAO5D,EACF,GAAIpD,KAAKmF,WAAY,CAC1Bb,GAAQ,EACR,QACF,CACE,OAAO,CAEX,CAAO,IAAKtE,KAAKkD,QAAQ+D,KACvB,KAEJ,CAEF,OAAI3C,GAEY,KADdlB,EAAQpD,KAAKwG,WAAWlC,EAAOwC,EAAMD,MAE5BzD,EAIS,KAAhBpD,KAAKiF,OACAjF,KAAKgF,IAELhF,KAAKoB,WAAW,0BAA4BpB,KAAKT,SAAW,GAAK,yBAA2BS,KAAKoE,eAAgB,CACtHrK,KAAM,GACNqJ,MAAO,KACPmB,KAAMvE,KAAKT,UAGjB,GAAG,QAEH4D,KAAqBhL,EAAAA,EAAAA,KAAO,WAC1B,IAAI0L,EAAI7D,KAAKqG,OACb,OAAIxC,GAGK7D,KAAKmD,KAEhB,GAAG,OAEH+D,OAAuB/O,EAAAA,EAAAA,KAAO,SAAegP,GAC3CnH,KAAKsF,eAAetC,KAAKmE,EAC3B,GAAG,SAEHC,UAA0BjP,EAAAA,EAAAA,KAAO,WAE/B,OADQ6H,KAAKsF,eAAehK,OAAS,EAC7B,EACC0E,KAAKsF,eAAejC,MAEpBrD,KAAKsF,eAAe,EAE/B,GAAG,YAEHyB,eAA+B5O,EAAAA,EAAAA,KAAO,WACpC,OAAI6H,KAAKsF,eAAehK,QAAU0E,KAAKsF,eAAetF,KAAKsF,eAAehK,OAAS,GAC1E0E,KAAKqH,WAAWrH,KAAKsF,eAAetF,KAAKsF,eAAehK,OAAS,IAAIwL,MAErE9G,KAAKqH,WAAoB,QAAEP,KAEtC,GAAG,iBAEHQ,UAA0BnP,EAAAA,EAAAA,KAAO,SAAkBqL,GAEjD,OADAA,EAAIxD,KAAKsF,eAAehK,OAAS,EAAIiM,KAAKC,IAAIhE,GAAK,KAC1C,EACAxD,KAAKsF,eAAe9B,GAEpB,SAEX,GAAG,YAEHiE,WAA2BtP,EAAAA,EAAAA,KAAO,SAAmBgP,GACnDnH,KAAKkH,MAAMC,EACb,GAAG,aAEHO,gBAAgCvP,EAAAA,EAAAA,KAAO,WACrC,OAAO6H,KAAKsF,eAAehK,MAC7B,GAAG,kBACH4H,QAAS,CAAC,EACV9D,eAA+BjH,EAAAA,EAAAA,KAAO,SAAmB6G,EAAI2I,EAAKC,EAA2BC,GAE3F,OAAQD,GACN,KAAK,EACH,OAAO,EAET,KAAK,EACH,OAAO,EAET,KAAK,EACH,OAAO,EAET,KAAK,EACH,OAAO,EAET,KAAK,EACH,OAAO,GAET,KAAK,EACH,OAAO,GAET,KAAK,EAEH,OADA5H,KAAKkH,MAAM,aACJ,GAET,KAAK,EAEH,OADAlH,KAAKoH,WACE,kBAET,KAAK,EAEH,OADApH,KAAKkH,MAAM,aACJ,GAET,KAAK,EAEH,OADAlH,KAAKoH,WACE,kBAET,KAAK,GACHpH,KAAKkH,MAAM,uBACX,MACF,KAAK,GAoOL,KAAK,GACHlH,KAAKoH,WACL,MAnOF,KAAK,GACH,MAAO,4BAET,KAAK,GAQL,KAAK,GA8ML,KAAK,GACH,MArNF,KAAK,GACHU,EACA,MACF,KAAK,GACH,OAAO,GAIT,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GAEH,OADA9H,KAAKkH,MAAM,cACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,UACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,oBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,iBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,cACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,gBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,aACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,UACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,YACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,uBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,mBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,uBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,oBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,iBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,mBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,gBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,aACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,sBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,uBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,oBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,iBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,mBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,gBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,aACJ,GAET,KAAK,GAIL,KAAK,GAEH,OADAlH,KAAKkH,MAAM,QACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,UACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,UACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,OACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,SACJ,GAET,KAAK,GAIL,KAAK,GAEH,OADAlH,KAAKkH,MAAM,SACJ,GAET,KAAK,GAIL,KAAK,GAEH,OADAlH,KAAKkH,MAAM,SACJ,GAET,KAAK,GAIL,KAAK,GAEH,OADAlH,KAAKkH,MAAM,SACJ,GAET,KAAK,GAIL,KAAK,GAEH,OADAlH,KAAKkH,MAAM,SACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,SACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,aACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,mBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,oBACJ,GAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,wBACJ,GAET,KAAK,GACH,MAAO,gBAET,KAAK,GAEH,OADAlH,KAAKkH,MAAM,aACJ,kBAET,KAAK,GACHlH,KAAKkH,MAAM,aACX,MACF,KAAK,GAmCL,KAAK,GACHlH,KAAKoH,WACLpH,KAAKoH,WACL,MAlCF,KAAK,GAKL,KAAK,GACH,OAAO,GAET,KAAK,GACHpH,KAAKkH,MAAM,UACX,MAIF,KAAK,GAqBL,KAAK,GACH,MAAO,MAnBT,KAAK,GACHlH,KAAKkH,MAAM,aACX,MACF,KAAK,GAEH,OADAlH,KAAKkH,MAAM,iBACJ,UAET,KAAK,GACHlH,KAAKoH,WACLpH,KAAKkH,MAAM,mBACX,MACF,KAAK,GACH,MAAO,YAST,KAAK,GACH,MAAO,SAET,KAAK,GACH,MAAO,SAET,KAAK,GACH,MAAO,QAET,KAAK,GACH,MAAO,MAET,KAAK,GACH,OAAO,GAGb,GAAG,aACHJ,MAAO,CAAC,8BAA+B,8BAA+B,8BAA+B,8BAA+B,uBAAwB,gCAAiC,uBAAwB,uBAAwB,uBAAwB,uBAAwB,wBAAyB,YAAa,cAAe,gCAAiC,wBAAyB,mBAAoB,WAAY,mBAAoB,qBAAsB,qBAAsB,mBAAoB,sBAAuB,oBAAqB,gBAAiB,yBAA0B,sBAAuB,oBAAqB,qBAAsB,kBAAmB,gBAAiB,kBAAmB,6BAA8B,yBAA0B,4BAA6B,yBAA0B,uBAAwB,wBAAyB,qBAAsB,mBAAoB,4BAA6B,4BAA6B,yBAA0B,uBAAwB,wBAAyB,qBAAsB,mBAAoB,yBAA0B,cAAe,gBAAiB,gBAAiB,aAAc,eAAgB,gBAAiB,eAAgB,kBAAmB,eAAgB,kBAAmB,eAAgB,mBAAoB,eAAgB,kBAAmB,kBAAmB,4BAA6B,wBAAyB,4BAA6B,SAAU,kBAAmB,WAAY,WAAY,UAAW,SAAU,kBAAmB,eAAgB,WAAY,aAAc,gBAAiB,aAAc,kBAAmB,aAAc,WAAY,aAAc,UAAW,UAAW,aAAc,eAAgB,UAC3sDO,WAAY,CAAE,oBAAuB,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,WAAa,GAAS,gBAAmB,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,cAAiB,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,OAAU,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,qBAAwB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,iBAAoB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,gBAAmB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,OAAU,CAAE,MAAS,GAAI,WAAa,GAAS,IAAO,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,OAAU,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,OAAU,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,KAAQ,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,MAAS,CAAE,MAAS,GAAI,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,oBAAuB,CAAE,MAAS,GAAI,WAAa,GAAS,iBAAoB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,cAAiB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,gBAAmB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,aAAgB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,mBAAsB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,oBAAuB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,iBAAoB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,cAAiB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,gBAAmB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,aAAgB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,MAAS,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,gBAAmB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,oBAAuB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,SAAY,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,iBAAoB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,cAAiB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,WAAc,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,aAAgB,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,OAAU,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,WAAc,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,OAAU,CAAE,MAAS,CAAC,GAAI,GAAI,GAAI,IAAK,WAAa,GAAS,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,IAGtyG,CAplB4B,GAslB5B,SAASU,KACP/H,KAAKhB,GAAK,CAAC,CACb,CAIA,OAPAF,GAAQ2D,MAAQA,IAIhBtK,EAAAA,EAAAA,IAAO4P,GAAQ,UACfA,GAAOpF,UAAY7D,GACnBA,GAAQiJ,OAASA,GACV,IAAIA,EACb,CA99Ba,GA+9Bb/M,EAAOA,OAASA,EAChB,IAsBIgN,EAtBAC,EAAoBjN,EAGpBkN,EAAe,GACfC,EAAqB,CAAC,IACtBC,EAAuB,SACvBC,EAAsB,GACtBC,EAAa,CACf,CACEC,MAAO,SACPC,MAAO,CAAEzO,KAAM,UACf0O,KAAM,CAAE1O,KAAM,UACd2O,KAAM,KACNjO,KAAM,KACNkO,eAAgB,KAGhBC,EAAO,GACPC,EAAQ,GACRC,GAAc,EACdC,EAAe,EACfC,EAAkB,EAElBC,GAA4B9Q,EAAAA,EAAAA,KAAO,WACrC,OAAO6P,CACT,GAAG,aACCnI,GAA4B1H,EAAAA,EAAAA,KAAO,SAAS+Q,GAC9C,IAAIC,GAAgBC,EAAAA,EAAAA,IAAaF,GAAaG,EAAAA,EAAAA,OAC9CrB,EAASmB,CACX,GAAG,aACCvI,GAAyBzI,EAAAA,EAAAA,KAAO,SAASsQ,EAAMa,EAAMC,EAAIf,EAAOgB,EAAOC,EAAOC,EAAQhB,EAAMjO,GAC9F,QAAa,IAATgO,GAA4B,OAATA,QAA0B,IAATa,GAA4B,OAATA,QAAwB,IAAPC,GAAwB,OAAPA,QAAyB,IAAVf,GAA8B,OAAVA,EAC9H,OAEF,IAAImB,EAAM,CAAC,EACX,MAAMC,EAAMhB,EAAKiB,MAAMC,GAASA,EAAKR,OAASA,GAAQQ,EAAKP,KAAOA,IAUlE,GATIK,EACFD,EAAMC,EAENhB,EAAK5F,KAAK2G,GAEZA,EAAIlB,KAAOA,EACXkB,EAAIL,KAAOA,EACXK,EAAIJ,GAAKA,EACTI,EAAInB,MAAQ,CAAEzO,KAAMyO,QACN,IAAVgB,GAA8B,OAAVA,EACtBG,EAAIH,MAAQ,CAAEzP,KAAM,SAEpB,GAAqB,kBAAVyP,EAAoB,CAC7B,IAAKO,EAAKC,GAASzH,OAAO0H,QAAQT,GAAO,GACzCG,EAAII,GAAO,CAAEhQ,KAAMiQ,EACrB,MACEL,EAAIH,MAAQ,CAAEzP,KAAMyP,GAGxB,QAAc,IAAVC,GAA8B,OAAVA,EACtBE,EAAIF,MAAQ,CAAE1P,KAAM,SAEpB,GAAqB,kBAAV0P,EAAoB,CAC7B,IAAKM,EAAKC,GAASzH,OAAO0H,QAAQR,GAAO,GACzCE,EAAII,GAAO,CAAEhQ,KAAMiQ,EACrB,MACEL,EAAIF,MAAQ,CAAE1P,KAAM0P,GAGxB,GAAsB,kBAAXC,EAAqB,CAC9B,IAAKK,EAAKC,GAASzH,OAAO0H,QAAQP,GAAQ,GAC1CC,EAAII,GAAOC,CACb,MACEL,EAAID,OAASA,EAEf,GAAoB,kBAAThB,EAAmB,CAC5B,IAAKqB,EAAKC,GAASzH,OAAO0H,QAAQvB,GAAM,GACxCiB,EAAII,GAAOC,CACb,MACEL,EAAIjB,KAAOA,EAEb,GAAoB,kBAATjO,EAAmB,CAC5B,IAAKsP,EAAKC,GAASzH,OAAO0H,QAAQxP,GAAM,GACxCkP,EAAII,GAAOC,CACb,MACEL,EAAIlP,KAAOA,EAEbkP,EAAIO,KAAOC,GACb,GAAG,UACC1J,GAAoCtI,EAAAA,EAAAA,KAAO,SAASiS,EAAa7B,EAAOC,EAAOiB,EAAOC,EAAQhB,EAAMjO,GACtG,GAAc,OAAV8N,GAA4B,OAAVC,EACpB,OAEF,IAAI6B,EAAiB,CAAC,EACtB,MAAMT,EAAM1B,EAAa2B,MAAMS,GAAoBA,EAAgB/B,QAAUA,IAY7E,GAXIqB,GAAOrB,IAAUqB,EAAIrB,MACvB8B,EAAiBT,GAEjBS,EAAe9B,MAAQA,EACvBL,EAAalF,KAAKqH,IAGlBA,EAAe7B,WADH,IAAVA,GAA8B,OAAVA,EACC,CAAEzO,KAAM,IAER,CAAEA,KAAMyO,QAEnB,IAAViB,GAA8B,OAAVA,EACtBY,EAAeZ,MAAQ,CAAE1P,KAAM,SAE/B,GAAqB,kBAAV0P,EAAoB,CAC7B,IAAKM,EAAKC,GAASzH,OAAO0H,QAAQR,GAAO,GACzCY,EAAeN,GAAO,CAAEhQ,KAAMiQ,EAChC,MACEK,EAAeZ,MAAQ,CAAE1P,KAAM0P,GAGnC,GAAsB,kBAAXC,EAAqB,CAC9B,IAAKK,EAAKC,GAASzH,OAAO0H,QAAQP,GAAQ,GAC1CW,EAAeN,GAAOC,CACxB,MACEK,EAAeX,OAASA,EAE1B,GAAoB,kBAAThB,EAAmB,CAC5B,IAAKqB,EAAKC,GAASzH,OAAO0H,QAAQvB,GAAM,GACxC2B,EAAeN,GAAOC,CACxB,MACEK,EAAe3B,KAAOA,EAExB,GAAoB,kBAATjO,EAAmB,CAC5B,IAAKsP,EAAKC,GAASzH,OAAO0H,QAAQxP,GAAM,GACxC4P,EAAeN,GAAOC,CACxB,MACEK,EAAe5P,KAAOA,EAExB4P,EAAeD,YAAc,CAAErQ,KAAMqQ,GACrCC,EAAe1B,eAAiBP,EAChCiC,EAAeH,KAAOC,GACxB,GAAG,qBACCzJ,GAA+BvI,EAAAA,EAAAA,KAAO,SAASiS,EAAa7B,EAAOC,EAAOgB,EAAOC,EAAOC,EAAQhB,EAAMjO,GACxG,GAAc,OAAV8N,GAA4B,OAAVC,EACpB,OAEF,IAAI+B,EAAY,CAAC,EACjB,MAAMX,EAAM1B,EAAa2B,MAAMW,GAAeA,EAAWjC,QAAUA,IAYnE,GAXIqB,GAAOrB,IAAUqB,EAAIrB,MACvBgC,EAAYX,GAEZW,EAAUhC,MAAQA,EAClBL,EAAalF,KAAKuH,IAGlBA,EAAU/B,WADE,IAAVA,GAA8B,OAAVA,EACJ,CAAEzO,KAAM,IAER,CAAEA,KAAMyO,QAEd,IAAVgB,GAA8B,OAAVA,EACtBe,EAAUf,MAAQ,CAAEzP,KAAM,SAE1B,GAAqB,kBAAVyP,EAAoB,CAC7B,IAAKO,EAAKC,GAASzH,OAAO0H,QAAQT,GAAO,GACzCe,EAAUR,GAAO,CAAEhQ,KAAMiQ,EAC3B,MACEO,EAAUf,MAAQ,CAAEzP,KAAMyP,GAG9B,QAAc,IAAVC,GAA8B,OAAVA,EACtBc,EAAUd,MAAQ,CAAE1P,KAAM,SAE1B,GAAqB,kBAAV0P,EAAoB,CAC7B,IAAKM,EAAKC,GAASzH,OAAO0H,QAAQR,GAAO,GACzCc,EAAUR,GAAO,CAAEhQ,KAAMiQ,EAC3B,MACEO,EAAUd,MAAQ,CAAE1P,KAAM0P,GAG9B,GAAsB,kBAAXC,EAAqB,CAC9B,IAAKK,EAAKC,GAASzH,OAAO0H,QAAQP,GAAQ,GAC1Ca,EAAUR,GAAOC,CACnB,MACEO,EAAUb,OAASA,EAErB,GAAoB,kBAAThB,EAAmB,CAC5B,IAAKqB,EAAKC,GAASzH,OAAO0H,QAAQvB,GAAM,GACxC6B,EAAUR,GAAOC,CACnB,MACEO,EAAU7B,KAAOA,EAEnB,GAAoB,kBAATjO,EAAmB,CAC5B,IAAKsP,EAAKC,GAASzH,OAAO0H,QAAQxP,GAAM,GACxC8P,EAAUR,GAAOC,CACnB,MACEO,EAAU9P,KAAOA,EAEnB8P,EAAUL,KAAOC,IACjBI,EAAUH,YAAc,CAAErQ,KAAMqQ,GAChCG,EAAU5B,eAAiBP,CAC7B,GAAG,gBACCzH,GAA+BxI,EAAAA,EAAAA,KAAO,SAASiS,EAAa7B,EAAOC,EAAOgB,EAAOC,EAAOC,EAAQhB,EAAMjO,GACxG,GAAc,OAAV8N,GAA4B,OAAVC,EACpB,OAEF,IAAIiC,EAAY,CAAC,EACjB,MAAMb,EAAM1B,EAAa2B,MAAMa,GAAeA,EAAWnC,QAAUA,IAYnE,GAXIqB,GAAOrB,IAAUqB,EAAIrB,MACvBkC,EAAYb,GAEZa,EAAUlC,MAAQA,EAClBL,EAAalF,KAAKyH,IAGlBA,EAAUjC,WADE,IAAVA,GAA8B,OAAVA,EACJ,CAAEzO,KAAM,IAER,CAAEA,KAAMyO,QAEd,IAAVgB,GAA8B,OAAVA,EACtBiB,EAAUjB,MAAQ,CAAEzP,KAAM,SAE1B,GAAqB,kBAAVyP,EAAoB,CAC7B,IAAKO,EAAKC,GAASzH,OAAO0H,QAAQT,GAAO,GACzCiB,EAAUV,GAAO,CAAEhQ,KAAMiQ,EAC3B,MACES,EAAUjB,MAAQ,CAAEzP,KAAMyP,GAG9B,QAAc,IAAVC,GAA8B,OAAVA,EACtBgB,EAAUhB,MAAQ,CAAE1P,KAAM,SAE1B,GAAqB,kBAAV0P,EAAoB,CAC7B,IAAKM,EAAKC,GAASzH,OAAO0H,QAAQR,GAAO,GACzCgB,EAAUV,GAAO,CAAEhQ,KAAMiQ,EAC3B,MACES,EAAUhB,MAAQ,CAAE1P,KAAM0P,GAG9B,GAAsB,kBAAXC,EAAqB,CAC9B,IAAKK,EAAKC,GAASzH,OAAO0H,QAAQP,GAAQ,GAC1Ce,EAAUV,GAAOC,CACnB,MACES,EAAUf,OAASA,EAErB,GAAoB,kBAAThB,EAAmB,CAC5B,IAAKqB,EAAKC,GAASzH,OAAO0H,QAAQvB,GAAM,GACxC+B,EAAUV,GAAOC,CACnB,MACES,EAAU/B,KAAOA,EAEnB,GAAoB,kBAATjO,EAAmB,CAC5B,IAAKsP,EAAKC,GAASzH,OAAO0H,QAAQxP,GAAM,GACxCgQ,EAAUV,GAAOC,CACnB,MACES,EAAUhQ,KAAOA,EAEnBgQ,EAAUP,KAAOC,IACjBM,EAAUL,YAAc,CAAErQ,KAAMqQ,GAChCK,EAAU9B,eAAiBP,CAC7B,GAAG,gBACC/H,GAA4ClI,EAAAA,EAAAA,KAAO,SAASoQ,EAAOC,EAAOC,EAAMC,EAAMjO,GACxF,GAAc,OAAV8N,GAA4B,OAAVC,EACpB,OAEF,IAAImC,EAAW,CAAC,EAChB,MAAMf,EAAMtB,EAAWuB,MAAMe,GAAcA,EAAUrC,QAAUA,IAY/D,GAXIqB,GAAOrB,IAAUqB,EAAIrB,MACvBoC,EAAWf,GAEXe,EAASpC,MAAQA,EACjBD,EAAWtF,KAAK2H,IAGhBA,EAASnC,WADG,IAAVA,GAA8B,OAAVA,EACL,CAAEzO,KAAM,IAER,CAAEA,KAAMyO,QAEd,IAATC,GAA4B,OAATA,EACrBkC,EAASlC,KAAO,CAAE1O,KAAM,eAExB,GAAoB,kBAAT0O,EAAmB,CAC5B,IAAKsB,EAAKC,GAASzH,OAAO0H,QAAQxB,GAAM,GACxCkC,EAASZ,GAAO,CAAEhQ,KAAMiQ,EAC1B,MACEW,EAASlC,KAAO,CAAE1O,KAAM0O,GAG5B,GAAoB,kBAATC,EAAmB,CAC5B,IAAKqB,EAAKC,GAASzH,OAAO0H,QAAQvB,GAAM,GACxCiC,EAASZ,GAAOC,CAClB,MACEW,EAASjC,KAAOA,EAElB,GAAoB,kBAATjO,EAAmB,CAC5B,IAAKsP,EAAKC,GAASzH,OAAO0H,QAAQxP,GAAM,GACxCkQ,EAASZ,GAAOC,CAClB,MACEW,EAASlQ,KAAOA,EAElBkQ,EAAShC,eAAiBP,EAC1BuC,EAAST,KAAOC,IAChB9B,EAAsBD,EACtBA,EAAuBG,EACvBJ,EAAmBnF,KAAKqF,EAC1B,GAAG,6BACC/H,GAAuCnI,EAAAA,EAAAA,KAAO,SAASoQ,EAAOC,EAAOC,EAAMC,EAAMjO,GACnF,GAAc,OAAV8N,GAA4B,OAAVC,EACpB,OAEF,IAAImC,EAAW,CAAC,EAChB,MAAMf,EAAMtB,EAAWuB,MAAMe,GAAcA,EAAUrC,QAAUA,IAY/D,GAXIqB,GAAOrB,IAAUqB,EAAIrB,MACvBoC,EAAWf,GAEXe,EAASpC,MAAQA,EACjBD,EAAWtF,KAAK2H,IAGhBA,EAASnC,WADG,IAAVA,GAA8B,OAAVA,EACL,CAAEzO,KAAM,IAER,CAAEA,KAAMyO,QAEd,IAATC,GAA4B,OAATA,EACrBkC,EAASlC,KAAO,CAAE1O,KAAM,kBAExB,GAAoB,kBAAT0O,EAAmB,CAC5B,IAAKsB,EAAKC,GAASzH,OAAO0H,QAAQxB,GAAM,GACxCkC,EAASZ,GAAO,CAAEhQ,KAAMiQ,EAC1B,MACEW,EAASlC,KAAO,CAAE1O,KAAM0O,GAG5B,GAAoB,kBAATC,EAAmB,CAC5B,IAAKqB,EAAKC,GAASzH,OAAO0H,QAAQvB,GAAM,GACxCiC,EAASZ,GAAOC,CAClB,MACEW,EAASjC,KAAOA,EAElB,GAAoB,kBAATjO,EAAmB,CAC5B,IAAKsP,EAAKC,GAASzH,OAAO0H,QAAQxP,GAAM,GACxCkQ,EAASZ,GAAOC,CAClB,MACEW,EAASlQ,KAAOA,EAElBkQ,EAAShC,eAAiBP,EAC1BuC,EAAST,KAAOC,IAChB9B,EAAsBD,EACtBA,EAAuBG,EACvBJ,EAAmBnF,KAAKqF,EAC1B,GAAG,wBACC9H,GAAoCpI,EAAAA,EAAAA,KAAO,SAAS0S,EAAUtC,EAAOC,EAAOC,EAAMgB,EAAOC,EAAQhB,EAAMjO,GACzG,GAAc,OAAV8N,GAA4B,OAAVC,EACpB,OAEF,IAAImC,EAAW,CAAC,EAChB,MAAMf,EAAMtB,EAAWuB,MAAMe,GAAcA,EAAUrC,QAAUA,IAY/D,GAXIqB,GAAOrB,IAAUqB,EAAIrB,MACvBoC,EAAWf,GAEXe,EAASpC,MAAQA,EACjBD,EAAWtF,KAAK2H,IAGhBA,EAASnC,WADG,IAAVA,GAA8B,OAAVA,EACL,CAAEzO,KAAM,IAER,CAAEA,KAAMyO,QAEd,IAATC,GAA4B,OAATA,EACrBkC,EAASlC,KAAO,CAAE1O,KAAM,aAExB,GAAoB,kBAAT0O,EAAmB,CAC5B,IAAKsB,EAAKC,GAASzH,OAAO0H,QAAQxB,GAAM,GACxCkC,EAASZ,GAAO,CAAEhQ,KAAMiQ,EAC1B,MACEW,EAASlC,KAAO,CAAE1O,KAAM0O,GAG5B,QAAc,IAAVgB,GAA8B,OAAVA,EACtBkB,EAASlB,MAAQ,CAAE1P,KAAM,SAEzB,GAAqB,kBAAV0P,EAAoB,CAC7B,IAAKM,EAAKC,GAASzH,OAAO0H,QAAQR,GAAO,GACzCkB,EAASZ,GAAO,CAAEhQ,KAAMiQ,EAC1B,MACEW,EAASlB,MAAQ,CAAE1P,KAAM0P,GAG7B,GAAoB,kBAATf,EAAmB,CAC5B,IAAKqB,EAAKC,GAASzH,OAAO0H,QAAQvB,GAAM,GACxCiC,EAASZ,GAAOC,CAClB,MACEW,EAASjC,KAAOA,EAElB,GAAoB,kBAATjO,EAAmB,CAC5B,IAAKsP,EAAKC,GAASzH,OAAO0H,QAAQxP,GAAM,GACxCkQ,EAASZ,GAAOC,CAClB,MACEW,EAASlQ,KAAOA,EAElBkQ,EAASE,SAAWA,EACpBF,EAAShC,eAAiBP,EAC1BuC,EAAST,KAAOC,IAChB9B,EAAsBD,EACtBA,EAAuBG,EACvBJ,EAAmBnF,KAAKqF,EAC1B,GAAG,qBACC7H,GAAwCrI,EAAAA,EAAAA,KAAO,WACjDiQ,EAAuBC,EACvBF,EAAmB9E,MACnBgF,EAAsBF,EAAmB9E,MACzC8E,EAAmBnF,KAAKqF,EAC1B,GAAG,yBACCxH,GAAgC1I,EAAAA,EAAAA,KAAO,SAASiS,EAAaU,EAAaC,EAASC,EAAWC,EAAaC,EAAWC,EAAOzB,EAAQF,EAAO4B,EAAYC,GAC1J,IAAIzB,EAAM1B,EAAa2B,MAAMzR,GAAYA,EAAQmQ,QAAUuC,IAC3D,QAAY,IAARlB,IACFA,EAAMtB,EAAWuB,MAAMzR,GAAYA,EAAQmQ,QAAUuC,SACzC,IAARlB,GAFN,CAMA,QAAgB,IAAZmB,GAAkC,OAAZA,EACxB,GAAuB,kBAAZA,EAAsB,CAC/B,IAAKhB,EAAKC,GAASzH,OAAO0H,QAAQc,GAAS,GAC3CnB,EAAIG,GAAOC,CACb,MACEJ,EAAImB,QAAUA,EAGlB,QAAkB,IAAdC,GAAsC,OAAdA,EAC1B,GAAyB,kBAAdA,EAAwB,CACjC,IAAKjB,EAAKC,GAASzH,OAAO0H,QAAQe,GAAW,GAC7CpB,EAAIG,GAAOC,CACb,MACEJ,EAAIoB,UAAYA,EAGpB,QAAoB,IAAhBC,GAA0C,OAAhBA,EAC5B,GAA2B,kBAAhBA,EAA0B,CACnC,IAAKlB,EAAKC,GAASzH,OAAO0H,QAAQgB,GAAa,GAC/CrB,EAAIG,GAAOC,CACb,MACEJ,EAAIqB,YAAcA,EAGtB,QAAkB,IAAdC,GAAsC,OAAdA,EAC1B,GAAyB,kBAAdA,EAAwB,CACjC,IAAKnB,EAAKC,GAASzH,OAAO0H,QAAQiB,GAAW,GAC7CtB,EAAIG,GAAOC,CACb,MACEJ,EAAIsB,UAAYA,EAGpB,QAAc,IAAVC,GAA8B,OAAVA,EACtB,GAAqB,kBAAVA,EAAoB,CAC7B,IAAKpB,EAAKC,GAASzH,OAAO0H,QAAQkB,GAAO,GACzCvB,EAAIG,GAAOC,CACb,MACEJ,EAAIuB,MAAQA,EAGhB,QAAe,IAAXzB,GAAgC,OAAXA,EACvB,GAAsB,kBAAXA,EAAqB,CAC9B,IAAKK,EAAKC,GAASzH,OAAO0H,QAAQP,GAAQ,GAC1CE,EAAIG,GAAOC,CACb,MACEJ,EAAIF,OAASA,EAGjB,QAAc,IAAVF,GAA8B,OAAVA,EACtB,GAAqB,kBAAVA,EAAoB,CAC7B,IAAKO,EAAKC,GAASzH,OAAO0H,QAAQT,GAAO,GACzCI,EAAIG,GAAOC,CACb,MACEJ,EAAIJ,MAAQA,EAGhB,QAAmB,IAAf4B,GAAwC,OAAfA,EAC3B,GAA0B,kBAAfA,EAAyB,CAClC,IAAKrB,EAAKC,GAASzH,OAAO0H,QAAQmB,GAAY,GAC9CxB,EAAIG,GAAOC,CACb,MACEJ,EAAIwB,WAAaA,EAGrB,QAAqB,IAAjBC,GAA4C,OAAjBA,EAC7B,GAA4B,kBAAjBA,EAA2B,CACpC,IAAKtB,EAAKC,GAASzH,OAAO0H,QAAQoB,GAAc,GAChDzB,EAAIG,GAAOC,CACb,MACEJ,EAAIyB,aAAeA,CAtEvB,CAyEF,GAAG,iBACCvK,GAAiC3I,EAAAA,EAAAA,KAAO,SAASiS,EAAad,EAAMC,EAAI+B,EAAWC,EAAWC,EAASC,GACzG,MAAM7B,EAAMhB,EAAKiB,MAAMF,GAAQA,EAAIL,OAASA,GAAQK,EAAIJ,KAAOA,IAC/D,QAAY,IAARK,EAAJ,CAGA,QAAkB,IAAd0B,GAAsC,OAAdA,EAC1B,GAAyB,kBAAdA,EAAwB,CACjC,IAAKvB,EAAKC,GAASzH,OAAO0H,QAAQqB,GAAW,GAC7C1B,EAAIG,GAAOC,CACb,MACEJ,EAAI0B,UAAYA,EAGpB,QAAkB,IAAdC,GAAsC,OAAdA,EAC1B,GAAyB,kBAAdA,EAAwB,CACjC,IAAKxB,EAAKC,GAASzH,OAAO0H,QAAQsB,GAAW,GAC7C3B,EAAIG,GAAOC,CACb,MACEJ,EAAI2B,UAAYA,EAGpB,QAAgB,IAAZC,GAAkC,OAAZA,EACxB,GAAuB,kBAAZA,EAAsB,CAC/B,IAAKzB,EAAKC,GAASzH,OAAO0H,QAAQuB,GAAS,GAC3C5B,EAAIG,GAAO2B,SAAS1B,EACtB,MACEJ,EAAI4B,QAAUE,SAASF,GAG3B,QAAgB,IAAZC,GAAkC,OAAZA,EACxB,GAAuB,kBAAZA,EAAsB,CAC/B,IAAK1B,EAAKC,GAASzH,OAAO0H,QAAQwB,GAAS,GAC3C7B,EAAIG,GAAO2B,SAAS1B,EACtB,MACEJ,EAAI6B,QAAUC,SAASD,EA9B3B,CAiCF,GAAG,kBACC1K,GAAqC5I,EAAAA,EAAAA,KAAO,SAASiS,EAAauB,EAAmBC,GACvF,IAAIC,EAAoB9C,EACpB+C,EAAuB9C,EAC3B,GAAiC,kBAAtB2C,EAAgC,CACzC,MAAM3B,EAAQzH,OAAOwJ,OAAOJ,GAAmB,GAC/CE,EAAoBH,SAAS1B,EAC/B,MACE6B,EAAoBH,SAASC,GAE/B,GAAoC,kBAAzBC,EAAmC,CAC5C,MAAM5B,EAAQzH,OAAOwJ,OAAOH,GAAsB,GAClDE,EAAuBJ,SAAS1B,EAClC,MACE8B,EAAuBJ,SAASE,GAE9BC,GAAqB,IACvB9C,EAAe8C,GAEbC,GAAwB,IAC1B9C,EAAkB8C,EAEtB,GAAG,sBACCE,GAAkC7T,EAAAA,EAAAA,KAAO,WAC3C,OAAO4Q,CACT,GAAG,mBACCkD,GAAqC9T,EAAAA,EAAAA,KAAO,WAC9C,OAAO6Q,CACT,GAAG,sBACCkD,GAA0C/T,EAAAA,EAAAA,KAAO,WACnD,OAAOiQ,CACT,GAAG,2BACC+D,GAAyChU,EAAAA,EAAAA,KAAO,WAClD,OAAOkQ,CACT,GAAG,0BACC+D,GAAkCjU,EAAAA,EAAAA,KAAO,SAASwQ,GACpD,YAAuB,IAAnBA,GAAgD,OAAnBA,EACxBT,EAEAA,EAAamE,QAAQhC,GACnBA,EAAe1B,iBAAmBA,GAG/C,GAAG,mBACC2D,GAA6BnU,EAAAA,EAAAA,KAAO,SAASoQ,GAC/C,OAAOL,EAAa2B,MAAMQ,GAAmBA,EAAe9B,QAAUA,GACxE,GAAG,cACCgE,GAAiCpU,EAAAA,EAAAA,KAAO,SAASwQ,GACnD,OAAOpG,OAAOiK,KAAKJ,EAAgBzD,GACrC,GAAG,kBACC8D,GAAgCtU,EAAAA,EAAAA,KAAO,SAASwQ,GAClD,YAAuB,IAAnBA,GAAgD,OAAnBA,EACxBL,EAEAA,EAAW+D,QAAQ1B,GAAaA,EAAShC,iBAAmBA,GAEvE,GAAG,iBACC+D,EAAeD,EACfE,GAA0BxU,EAAAA,EAAAA,KAAO,WACnC,OAAOyQ,CACT,GAAG,WACCgE,GAA2BzU,EAAAA,EAAAA,KAAO,WACpC,OAAO0Q,CACT,GAAG,YACCgE,GAA0B1U,EAAAA,EAAAA,KAAO,SAAS2U,GAC5ChE,EAAcgE,CAChB,GAAG,WACC3C,GAA2BhS,EAAAA,EAAAA,KAAO,WACpC,OAAO2Q,CACT,GAAG,YACCiE,GAAwB5U,EAAAA,EAAAA,KAAO,WACjC+P,EAAe,GACfI,EAAa,CACX,CACEC,MAAO,SACPC,MAAO,CAAEzO,KAAM,UACf0O,KAAM,CAAE1O,KAAM,UACd2O,KAAM,KACNjO,KAAM,KACNkO,eAAgB,KAGpBN,EAAsB,GACtBD,EAAuB,SACvBD,EAAqB,CAAC,IACtBS,EAAO,GACPT,EAAqB,CAAC,IACtBU,EAAQ,GACRC,GAAc,EACdC,EAAe,EACfC,EAAkB,CACpB,GAAG,SAmCClJ,GAA2B3H,EAAAA,EAAAA,KAAO,SAAS6U,GAC7C,IAAI7D,GAAgBC,EAAAA,EAAAA,IAAa4D,GAAK3D,EAAAA,EAAAA,OACtCR,EAAQM,CACV,GAAG,YACC8D,EAAe,CACjBxM,oBACAJ,4BACAK,eACAJ,uBACAK,eACAJ,oBACAC,wBACAI,SACAC,gBACAC,iBACAC,qBACAoJ,WACA0C,UACAT,kBACAE,aACAC,iBACAE,gBACAC,eACAR,0BACAC,yBACAQ,UACAC,WACA3D,YACA+C,kBACAC,qBACAiB,YAAW,KACXC,YAAW,KACXC,kBAAiB,KACjBlN,kBAAiB,KACjBmJ,WAA2BlR,EAAAA,EAAAA,KAAO,KAAMkR,EAAAA,EAAAA,MAAYgE,IAAI,aACxDN,QACAO,SAtEa,CACbC,MAAO,EACPC,OAAQ,EACRC,KAAM,EACNC,YAAa,EACbC,aAAc,EACdC,WAAY,EACZC,YAAa,EACbC,WAAY,GACZC,SAAU,GACVC,UAAW,GACXC,SAAU,GACVC,QAAS,GACTC,UAAW,GACXC,QAAS,GACTC,aAAc,GACdC,WAAY,GACZC,UAAW,GACXC,QAAS,GACTC,QAAS,GACTC,WAAY,GACZC,SAAU,GACVC,YAAa,GACbC,aAAc,IAgDdC,UA9Cc,CACdC,OAAQ,EACRC,KAAM,GA6CNC,UA3Cc,CACdC,OAAQ,EACRC,QAAS,EACTC,KAAM,GAyCNtP,WACAD,aASEwP,GAA4BlX,EAAAA,EAAAA,KAAO,SAASqC,EAAMnC,GACpD,OAAOH,EAAAA,EAAAA,IAASsC,EAAMnC,EACxB,GAAG,YACCkC,GAA4BpC,EAAAA,EAAAA,KAAO,SAASqC,EAAM3B,EAAOC,EAAQL,EAAGC,EAAG+B,GACzE,MAAM6U,EAAY9U,EAAKjC,OAAO,SAC9B+W,EAAU9W,KAAK,QAASK,GACxByW,EAAU9W,KAAK,SAAUM,GACzBwW,EAAU9W,KAAK,IAAKC,GACpB6W,EAAU9W,KAAK,IAAKE,GACpB,IAAIiC,EAAgBF,EAAK8U,WAAW,yBAA2B9U,GAAOG,EAAAA,EAAAA,GAAYH,GAClF6U,EAAU9W,KAAK,aAAcmC,EAC/B,GAAG,aACC6U,GAA2BrX,EAAAA,EAAAA,KAAO,CAACqC,EAAMiV,EAAOC,KAClD,MAAMC,EAAWnV,EAAKjC,OAAO,KAC7B,IAAIyO,EAAI,EACR,IAAK,IAAI2C,KAAO8F,EAAO,CACrB,IAAInE,EAAY3B,EAAI2B,UAAY3B,EAAI2B,UAAY,UAC5CsE,EAAcjG,EAAI4B,UAAY5B,EAAI4B,UAAY,UAC9CC,EAAU7B,EAAI6B,QAAUE,SAAS/B,EAAI6B,SAAW,EAChDC,EAAU9B,EAAI8B,QAAUC,SAAS/B,EAAI8B,SAAW,EAChDoE,EAAM,GACV,GAAU,IAAN7I,EAAS,CACX,IAAIzC,EAAOoL,EAASpX,OAAO,QAC3BgM,EAAK/L,KAAK,KAAMmR,EAAImG,WAAWrX,GAC/B8L,EAAK/L,KAAK,KAAMmR,EAAImG,WAAWpX,GAC/B6L,EAAK/L,KAAK,KAAMmR,EAAIoG,SAAStX,GAC7B8L,EAAK/L,KAAK,KAAMmR,EAAIoG,SAASrX,GAC7B6L,EAAK/L,KAAK,eAAgB,KAC1B+L,EAAK/L,KAAK,SAAUoX,GACpBrL,EAAKpK,MAAM,OAAQ,QACF,UAAbwP,EAAIlB,MACNlE,EAAK/L,KAAK,aAAc,OAASqX,EAAM,eAExB,UAAblG,EAAIlB,MAAiC,UAAbkB,EAAIlB,MAC9BlE,EAAK/L,KAAK,eAAgB,OAASqX,EAAM,cAE3C7I,GAAK,CACP,KAAO,CACL,IAAIzC,EAAOoL,EAASpX,OAAO,QAC3BgM,EAAK/L,KAAK,OAAQ,QAAQA,KAAK,eAAgB,KAAKA,KAAK,SAAUoX,GAAapX,KAC9E,IACA,iDAAiDwX,WAAW,SAAUrG,EAAImG,WAAWrX,GAAGuX,WAAW,SAAUrG,EAAImG,WAAWpX,GAAGsX,WAC7H,WACArG,EAAImG,WAAWrX,GAAKkR,EAAIoG,SAAStX,EAAIkR,EAAImG,WAAWrX,GAAK,GAAKkR,EAAIoG,SAAStX,EAAIkR,EAAImG,WAAWrX,GAAK,GACnGuX,WAAW,WAAYrG,EAAImG,WAAWpX,GAAKiR,EAAIoG,SAASrX,EAAIiR,EAAImG,WAAWpX,GAAK,GAAGsX,WAAW,QAASrG,EAAIoG,SAAStX,GAAGuX,WAAW,QAASrG,EAAIoG,SAASrX,IAE3I,UAAbiR,EAAIlB,MACNlE,EAAK/L,KAAK,aAAc,OAASqX,EAAM,eAExB,UAAblG,EAAIlB,MAAiC,UAAbkB,EAAIlB,MAC9BlE,EAAK/L,KAAK,eAAgB,OAASqX,EAAM,aAE7C,CACA,IAAII,EAAcP,EAAMQ,cACxBC,GAAuBT,EAAvBS,CACExG,EAAInB,MAAMzO,KACV4V,EACApI,KAAK6I,IAAIzG,EAAImG,WAAWrX,EAAGkR,EAAIoG,SAAStX,GAAK8O,KAAKC,IAAImC,EAAIoG,SAAStX,EAAIkR,EAAImG,WAAWrX,GAAK,EAAI+S,EAC/FjE,KAAK6I,IAAIzG,EAAImG,WAAWpX,EAAGiR,EAAIoG,SAASrX,GAAK6O,KAAKC,IAAImC,EAAIoG,SAASrX,EAAIiR,EAAImG,WAAWpX,GAAK,EAAI+S,EAC/F9B,EAAInB,MAAM3P,MACV8Q,EAAInB,MAAM1P,OACV,CAAEH,KAAM2S,GACR2E,GAEEtG,EAAIH,OAA4B,KAAnBG,EAAIH,MAAMzP,OACzBkW,EAAcP,EAAMQ,cACpBC,GAAuBT,EAAvBS,CACE,IAAMxG,EAAIH,MAAMzP,KAAO,IACvB4V,EACApI,KAAK6I,IAAIzG,EAAImG,WAAWrX,EAAGkR,EAAIoG,SAAStX,GAAK8O,KAAKC,IAAImC,EAAIoG,SAAStX,EAAIkR,EAAImG,WAAWrX,GAAK,EAAI+S,EAC/FjE,KAAK6I,IAAIzG,EAAImG,WAAWpX,EAAGiR,EAAIoG,SAASrX,GAAK6O,KAAKC,IAAImC,EAAIoG,SAASrX,EAAIiR,EAAImG,WAAWpX,GAAK,EAAIgX,EAAMW,gBAAkB,EAAI5E,EAC3HlE,KAAK+I,IAAI3G,EAAInB,MAAM3P,MAAO8Q,EAAIH,MAAM3Q,OACpC8Q,EAAIH,MAAM1Q,OACV,CAAEH,KAAM2S,EAAW,aAAc,UACjC2E,GAGN,IACC,YACCM,IAA+BpY,EAAAA,EAAAA,KAAO,SAASqC,EAAMmQ,EAAU+E,GACjE,MAAMc,EAAehW,EAAKjC,OAAO,KACjC,IAAIkY,EAAY9F,EAASI,QAAUJ,EAASI,QAAU,OAClD6E,EAAcjF,EAASM,YAAcN,EAASM,YAAc,UAC5DD,EAAYL,EAASK,UAAYL,EAASK,UAAY,QACtD0F,EAAa,CAAE,eAAgB,EAAG,mBAAoB,WACtD/F,EAASE,WACX6F,EAAa,CAAE,eAAgB,IAEjC,IAAIrY,EAAW,CACbI,EAAGkS,EAASlS,EACZC,EAAGiS,EAASjS,EACZC,KAAM8X,EACN7X,OAAQgX,EACR/W,MAAO8R,EAAS9R,MAChBC,OAAQ6R,EAAS7R,OACjBE,GAAI,IACJC,GAAI,IACJC,MAAOwX,GAETrB,EAAUmB,EAAcnY,GACxB,IAAIsY,EAAejB,EAAMkB,eACzBD,EAAaE,WAAa,OAC1BF,EAAaG,SAAWH,EAAaG,SAAW,EAChDH,EAAa3F,UAAYA,EACzBmF,GAAuBT,EAAvBS,CACExF,EAASnC,MAAMzO,KACfyW,EACA7F,EAASlS,EACTkS,EAASjS,EAAIiS,EAASnC,MAAMuI,EAC5BpG,EAAS9R,MACT8R,EAAS7R,OACT,CAAEH,KAAM,WACRgY,GAEEhG,EAASlC,MAA+B,KAAvBkC,EAASlC,KAAK1O,OACjC4W,EAAejB,EAAMkB,eACrBD,EAAa3F,UAAYA,EACzBmF,GAAuBT,EAAvBS,CACExF,EAASlC,KAAK1O,KACdyW,EACA7F,EAASlS,EACTkS,EAASjS,EAAIiS,EAASlC,KAAKsI,EAC3BpG,EAAS9R,MACT8R,EAAS7R,OACT,CAAEH,KAAM,WACRgY,IAGAhG,EAASlB,OAAiC,KAAxBkB,EAASlB,MAAM1P,OACnC4W,EAAejB,EAAMkB,eACrBD,EAAaG,SAAWH,EAAaG,SAAW,EAChDH,EAAa3F,UAAYA,EACzBmF,GAAuBT,EAAvBS,CACExF,EAASlB,MAAM1P,KACfyW,EACA7F,EAASlS,EACTkS,EAASjS,EAAIiS,EAASlB,MAAMsH,EAC5BpG,EAAS9R,MACT8R,EAAS7R,OACT,CAAEH,KAAM,WACRgY,GAGN,GAAG,gBACCK,IAA8B7Y,EAAAA,EAAAA,KAAO,SAASqC,EAAMyW,EAASvB,GAC/D,IAAIe,EAAYQ,EAAQlG,QAAUkG,EAAQlG,QAAU2E,EAAMuB,EAAQ7G,YAAYrQ,KAAO,aACjF6V,EAAcqB,EAAQhG,YAAcgG,EAAQhG,YAAcyE,EAAMuB,EAAQ7G,YAAYrQ,KAAO,iBAC3FiR,EAAYiG,EAAQjG,UAAYiG,EAAQjG,UAAY,UACpDkG,EAAY,qyBAChB,OAAQD,EAAQ7G,YAAYrQ,MAC1B,IAAK,SACHmX,EAAY,qyBACZ,MACF,IAAK,kBACHA,EAAY,ivBAGhB,MAAMC,EAAc3W,EAAKjC,OAAO,KAChC4Y,EAAY3Y,KAAK,QAAS,cAC1B,MAAM4Y,GAAOtW,EAAAA,EAAAA,MACb,OAAQmW,EAAQ7G,YAAYrQ,MAC1B,IAAK,SACL,IAAK,kBACL,IAAK,SACL,IAAK,kBACL,IAAK,YACL,IAAK,qBACL,IAAK,YACL,IAAK,qBACHqX,EAAK3Y,EAAIwY,EAAQxY,EACjB2Y,EAAK1Y,EAAIuY,EAAQvY,EACjB0Y,EAAKzY,KAAO8X,EACZW,EAAKvY,MAAQoY,EAAQpY,MACrBuY,EAAKtY,OAASmY,EAAQnY,OACtBsY,EAAKxY,OAASgX,EACdwB,EAAKpY,GAAK,IACVoY,EAAKnY,GAAK,IACVmY,EAAKlY,MAAQ,CAAE,eAAgB,IAC/BmW,EAAU8B,EAAaC,GACvB,MACF,IAAK,YACL,IAAK,qBACL,IAAK,eACL,IAAK,wBACL,IAAK,eACL,IAAK,wBACHD,EAAY5Y,OAAO,QAAQC,KAAK,OAAQiY,GAAWjY,KAAK,eAAgB,OAAOA,KAAK,SAAUoX,GAAapX,KACzG,IACA,4HAA4HwX,WAAW,SAAUiB,EAAQxY,GAAGuX,WAAW,SAAUiB,EAAQvY,GAAGsX,WAAW,OAAQiB,EAAQpY,MAAQ,GAAGmX,WAAW,SAAUiB,EAAQnY,SAEjQqY,EAAY5Y,OAAO,QAAQC,KAAK,OAAQ,QAAQA,KAAK,eAAgB,OAAOA,KAAK,SAAUoX,GAAapX,KACtG,IACA,0DAA0DwX,WAAW,SAAUiB,EAAQxY,GAAGuX,WAAW,SAAUiB,EAAQvY,GAAGsX,WAAW,OAAQiB,EAAQpY,MAAQ,IAE/J,MACF,IAAK,eACL,IAAK,wBACL,IAAK,kBACL,IAAK,2BACL,IAAK,kBACL,IAAK,2BACHsY,EAAY5Y,OAAO,QAAQC,KAAK,OAAQiY,GAAWjY,KAAK,eAAgB,OAAOA,KAAK,SAAUoX,GAAapX,KACzG,IACA,kHAAkHwX,WAAW,SAAUiB,EAAQxY,GAAGuX,WAAW,SAAUiB,EAAQvY,GAAGsX,WAAW,QAASiB,EAAQpY,OAAOmX,WAAW,OAAQiB,EAAQnY,OAAS,IAE3PqY,EAAY5Y,OAAO,QAAQC,KAAK,OAAQ,QAAQA,KAAK,eAAgB,OAAOA,KAAK,SAAUoX,GAAapX,KACtG,IACA,2DAA2DwX,WAAW,SAAUiB,EAAQxY,EAAIwY,EAAQpY,OAAOmX,WAAW,SAAUiB,EAAQvY,GAAGsX,WAAW,OAAQiB,EAAQnY,OAAS,IAIrL,IAAIuY,EAAkBC,GAAe5B,EAAOuB,EAAQ7G,YAAYrQ,MAEhE,OADAoX,EAAY5Y,OAAO,QAAQC,KAAK,OAAQwS,GAAWxS,KAAK,cAAe6Y,EAAgBE,YAAY/Y,KAAK,YAAa6Y,EAAgBP,SAAW,GAAGtY,KAAK,aAAc,UAAUA,KAAK,eAAgB,WAAWA,KAAK,aAAcyY,EAAQ7G,YAAYvR,OAAOL,KAAK,IAAKyY,EAAQxY,EAAIwY,EAAQpY,MAAQ,EAAIoY,EAAQ7G,YAAYvR,MAAQ,GAAGL,KAAK,IAAKyY,EAAQvY,EAAIuY,EAAQ7G,YAAY2G,GAAGhX,KAAK,KAAOkX,EAAQ7G,YAAYrQ,KAAO,MACnZkX,EAAQ7G,YAAYrQ,MAC1B,IAAK,SACL,IAAK,kBACHQ,EACE4W,EACA,GACA,GACAF,EAAQxY,EAAIwY,EAAQpY,MAAQ,EAAI,GAChCoY,EAAQvY,EAAIuY,EAAQO,MAAMT,EAC1BG,GAIN,IAAIO,EAAe/B,EAAMuB,EAAQ7G,YAAYrQ,KAAO,UAqDpD,OApDA0X,EAAaZ,WAAa,OAC1BY,EAAaX,SAAWW,EAAaX,SAAW,EAChDW,EAAazG,UAAYA,EACzBmF,GAAuBT,EAAvBS,CACEc,EAAQzI,MAAMzO,KACdoX,EACAF,EAAQxY,EACRwY,EAAQvY,EAAIuY,EAAQzI,MAAMuI,EAC1BE,EAAQpY,MACRoY,EAAQnY,OACR,CAAEH,KAAMqS,GACRyG,GAEFA,EAAe/B,EAAMuB,EAAQ7G,YAAYrQ,KAAO,UAChD0X,EAAazG,UAAYA,EACrBiG,EAAQzH,OAAiC,KAAxByH,EAAQzH,OAAOzP,KAClCoW,GAAuBT,EAAvBS,CACEc,EAAQzH,MAAMzP,KACdoX,EACAF,EAAQxY,EACRwY,EAAQvY,EAAIuY,EAAQzH,MAAMuH,EAC1BE,EAAQpY,MACRoY,EAAQnY,OACR,CAAEH,KAAMqS,EAAW,aAAc,UACjCyG,GAEOR,EAAQxI,MAA8B,KAAtBwI,EAAQxI,KAAK1O,MACtCoW,GAAuBT,EAAvBS,CACEc,EAAQxI,KAAK1O,KACboX,EACAF,EAAQxY,EACRwY,EAAQvY,EAAIuY,EAAQxI,KAAKsI,EACzBE,EAAQpY,MACRoY,EAAQnY,OACR,CAAEH,KAAMqS,EAAW,aAAc,UACjCyG,GAGAR,EAAQxH,OAAgC,KAAvBwH,EAAQxH,MAAM1P,OACjC0X,EAAe/B,EAAMgC,aACrBD,EAAazG,UAAYA,EACzBmF,GAAuBT,EAAvBS,CACEc,EAAQxH,MAAM1P,KACdoX,EACAF,EAAQxY,EACRwY,EAAQvY,EAAIuY,EAAQxH,MAAMsH,EAC1BE,EAAQpY,MACRoY,EAAQnY,OACR,CAAEH,KAAMqS,GACRyG,IAGGR,EAAQnY,MACjB,GAAG,eACC6Y,IAAqCxZ,EAAAA,EAAAA,KAAO,SAASqC,GACvDA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,YAAYA,KAAK,YAAa,WAAWA,KAAK,YAAa,WAAWD,OAAO,QAAQC,KAAK,YAAa,aAAaA,KAClK,IACA,k1ZAEJ,GAAG,sBACCoZ,IAAqCzZ,EAAAA,EAAAA,KAAO,SAASqC,GACvDA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,YAAYA,KAAK,QAAS,MAAMA,KAAK,SAAU,MAAMD,OAAO,QAAQC,KAAK,YAAa,aAAaA,KACjJ,IACA,2JAEJ,GAAG,sBACCqZ,IAAkC1Z,EAAAA,EAAAA,KAAO,SAASqC,GACpDA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,SAASA,KAAK,QAAS,MAAMA,KAAK,SAAU,MAAMD,OAAO,QAAQC,KAAK,YAAa,aAAaA,KAC9I,IACA,4UAEJ,GAAG,mBACCsZ,IAAkC3Z,EAAAA,EAAAA,KAAO,SAASqC,GACpDA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,aAAaA,KAAK,OAAQ,GAAGA,KAAK,OAAQ,GAAGA,KAAK,cAAe,kBAAkBA,KAAK,cAAe,IAAIA,KAAK,eAAgB,IAAIA,KAAK,SAAU,QAAQD,OAAO,QAAQC,KAAK,IAAK,wBACtO,GAAG,mBACCuZ,IAAiC5Z,EAAAA,EAAAA,KAAO,SAASqC,GACnDA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,YAAYA,KAAK,OAAQ,GAAGA,KAAK,OAAQ,GAAGA,KAAK,cAAe,kBAAkBA,KAAK,cAAe,IAAIA,KAAK,eAAgB,IAAIA,KAAK,SAAU,QAAQD,OAAO,QAAQC,KAAK,IAAK,yBACrO,GAAG,kBACCwZ,IAAwC7Z,EAAAA,EAAAA,KAAO,SAASqC,GAC1DA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,eAAeA,KAAK,OAAQ,IAAIA,KAAK,OAAQ,GAAGA,KAAK,cAAe,IAAIA,KAAK,eAAgB,IAAIA,KAAK,SAAU,QAAQD,OAAO,QAAQC,KAAK,IAAK,4BACnM,GAAG,yBACCyZ,IAAsC9Z,EAAAA,EAAAA,KAAO,SAASqC,GACxDA,EAAKjC,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,kBAAkBA,KAAK,OAAQ,IAAIA,KAAK,OAAQ,IAAIA,KAAK,cAAe,IAAIA,KAAK,eAAgB,IAAIA,KAAK,SAAU,QAAQD,OAAO,UAAUC,KAAK,KAAM,IAAIA,KAAK,KAAM,IAAIA,KAAK,IAAK,EACvO,GAAG,uBACC0Z,IAAuC/Z,EAAAA,EAAAA,KAAO,SAASqC,GACzD,MACM2X,EADO3X,EAAKjC,OAAO,QACLA,OAAO,UAAUC,KAAK,KAAM,aAAaA,KAAK,cAAe,IAAIA,KAAK,eAAgB,GAAGA,KAAK,SAAU,QAAQA,KAAK,OAAQ,IAAIA,KAAK,OAAQ,GAClK2Z,EAAO5Z,OAAO,QAAQC,KAAK,OAAQ,SAASA,KAAK,SAAU,WAAW2B,MAAM,mBAAoB,QAAQ3B,KAAK,eAAgB,OAAOA,KAAK,IAAK,qBAC9I2Z,EAAO5Z,OAAO,QAAQC,KAAK,OAAQ,QAAQA,KAAK,SAAU,WAAW2B,MAAM,mBAAoB,QAAQ3B,KAAK,eAAgB,OAAOA,KAAK,IAAK,0BAC/I,GAAG,wBACC8Y,IAAiCnZ,EAAAA,EAAAA,KAAO,CAACia,EAAKhI,KACzC,CACLmH,WAAYa,EAAIhI,EAAc,cAC9B0G,SAAUsB,EAAIhI,EAAc,YAC5ByG,WAAYuB,EAAIhI,EAAc,iBAE/B,kBACC+F,GAAyC,WAC3C,SAASkC,EAAOC,EAASC,EAAG9Z,EAAGC,EAAGG,EAAOC,EAAQ0Z,GAE/CC,EADaF,EAAEha,OAAO,QAAQC,KAAK,IAAKC,EAAII,EAAQ,GAAGL,KAAK,IAAKE,EAAII,EAAS,EAAI,GAAGqB,MAAM,cAAe,UAAUJ,KAAKuY,GACrGE,EACtB,CAEA,SAASE,EAAQJ,EAASC,EAAG9Z,EAAGC,EAAGG,EAAOC,EAAQ0Z,EAAW9C,GAC3D,MAAM,SAAEoB,EAAQ,WAAES,EAAU,WAAEV,GAAenB,EACvChK,EAAQ4M,EAAQ3M,MAAMgN,EAAAA,GAAe1Y,gBAC3C,IAAK,IAAI+M,EAAI,EAAGA,EAAItB,EAAMpK,OAAQ0L,IAAK,CACrC,MAAM4L,EAAK5L,EAAI8J,EAAWA,GAAYpL,EAAMpK,OAAS,GAAK,EACpDvB,EAAOwY,EAAEha,OAAO,QAAQC,KAAK,IAAKC,EAAII,EAAQ,GAAGL,KAAK,IAAKE,GAAGyB,MAAM,cAAe,UAAU3B,KAAK,oBAAqB,UAAU2B,MAAM,YAAa2W,GAAU3W,MAAM,cAAe0W,GAAY1W,MAAM,cAAeoX,GAC1NxX,EAAKxB,OAAO,SAASC,KAAK,KAAMoa,GAAI7Y,KAAK2L,EAAMsB,IAAIxO,KAAK,qBAAsB,gBAC9Eia,EAAc1Y,EAAMyY,EACtB,CACF,CAEA,SAASK,EAAKP,EAASC,EAAG9Z,EAAGC,EAAGG,EAAOC,EAAQ0Z,EAAW9C,GACxD,MAAMoD,EAAIP,EAAEha,OAAO,UAEbwB,EADI+Y,EAAEva,OAAO,iBAAiBC,KAAK,IAAKC,GAAGD,KAAK,IAAKE,GAAGF,KAAK,QAASK,GAAOL,KAAK,SAAUM,GACnFP,OAAO,aAAa4B,MAAM,UAAW,SAASA,MAAM,SAAU,QAAQA,MAAM,QAAS,QACpGJ,EAAKxB,OAAO,OAAO4B,MAAM,UAAW,cAAcA,MAAM,aAAc,UAAUA,MAAM,iBAAkB,UAAUJ,KAAKuY,GACvHI,EAAQJ,EAASQ,EAAGra,EAAGC,EAAGG,EAAOC,EAAQ0Z,EAAW9C,GACpD+C,EAAc1Y,EAAMyY,EACtB,CAEA,SAASC,EAAcM,EAAQC,GAC7B,IAAK,MAAMjJ,KAAOiJ,EACZA,EAAkBpQ,eAAemH,IACnCgJ,EAAOva,KAAKuR,EAAKiJ,EAAkBjJ,GAGzC,CAEA,OA7BA5R,EAAAA,EAAAA,IAAOka,EAAQ,WAWfla,EAAAA,EAAAA,IAAOua,EAAS,YAShBva,EAAAA,EAAAA,IAAO0a,EAAM,SAQb1a,EAAAA,EAAAA,IAAOsa,EAAe,iBACf,SAAS/C,GACd,MAA+B,OAAxBA,EAAMuD,cAAyBJ,EAA+B,QAAxBnD,EAAMuD,cAA0BZ,EAASK,CACxF,CACF,CArC6C,GAsCzCQ,GAAkB,CACpBhb,SAAUmX,EACVkB,gBACAS,eACAxB,WACAjV,YACAuX,mBACAC,kBACAC,yBACAC,uBACAC,wBACAP,sBACAC,sBACAC,oBAIEsB,GAAqB,EACrBC,GAAqB,EACrBC,GAAgB,EAChBC,GAAmB,EACvBtY,EAAOgE,GAAKiO,EACZ,IAAIsG,GAAO,CAAC,EACRC,GAAS,MAAM,eAEfrb,EAAAA,EAAAA,IAAO6H,KAAM,UAFE,GAIjByT,WAAAA,CAAYC,GACV1T,KAAKjH,KAAO,GACZiH,KAAK2T,KAAO,CAAC,EACb3T,KAAK2T,KAAKpa,YAAS,EACnByG,KAAK2T,KAAKla,WAAQ,EAClBuG,KAAK2T,KAAKna,YAAS,EACnBwG,KAAK2T,KAAKja,WAAQ,EAClBsG,KAAK2T,KAAKC,gBAAa,EACvB5T,KAAK6T,SAAW,CAAC,EACjB7T,KAAK6T,SAASta,YAAS,EACvByG,KAAK6T,SAASpa,WAAQ,EACtBuG,KAAK6T,SAASra,YAAS,EACvBwG,KAAK6T,SAASna,WAAQ,EACtBsG,KAAK6T,SAASC,IAAM,EACpBC,GAAQL,EAAQM,GAAG3K,YACrB,CACA4K,OAAAA,CAAQ1a,EAAQE,EAAOD,EAAQE,GAC7BsG,KAAK6T,SAASta,OAASyG,KAAK2T,KAAKpa,OAASA,EAC1CyG,KAAK6T,SAASpa,MAAQuG,KAAK2T,KAAKla,MAAQA,EACxCuG,KAAK6T,SAASra,OAASwG,KAAK2T,KAAKna,OAASA,EAC1CwG,KAAK6T,SAASna,MAAQsG,KAAK2T,KAAKja,MAAQA,CAC1C,CACAwa,SAAAA,CAAUC,EAAKpK,EAAKqK,EAAKC,QACN,IAAbF,EAAIpK,GACNoK,EAAIpK,GAAOqK,EAEXD,EAAIpK,GAAOsK,EAAID,EAAKD,EAAIpK,GAE5B,CACAuK,MAAAA,CAAOrD,GACLjR,KAAK6T,SAASC,IAAM9T,KAAK6T,SAASC,IAAM,EACxC,IAAIS,EAAUvU,KAAK6T,SAASta,SAAWyG,KAAK6T,SAASpa,MAAQuG,KAAK6T,SAASpa,MAAQwX,EAAQuD,OAASxU,KAAK6T,SAASpa,MAAyB,EAAjBwX,EAAQuD,OAC9HC,EAASF,EAAUtD,EAAQpY,MAC3B6b,EAAU1U,KAAK6T,SAASra,OAA0B,EAAjByX,EAAQuD,OACzCG,EAASD,EAAUzD,EAAQnY,QAC3Byb,GAAWvU,KAAK2T,KAAKC,YAAca,GAAUzU,KAAK2T,KAAKC,YAAc5T,KAAK6T,SAASC,IAAMT,MAC3FkB,EAAUvU,KAAK6T,SAASta,OAAS0X,EAAQuD,OAASjB,GAAKqB,iBACvDF,EAAU1U,KAAK6T,SAASna,MAAyB,EAAjBuX,EAAQuD,OACxCxU,KAAK6T,SAASpa,MAAQgb,EAASF,EAAUtD,EAAQpY,MACjDmH,KAAK6T,SAASra,OAASwG,KAAK6T,SAASna,MACrCsG,KAAK6T,SAASna,MAAQib,EAASD,EAAUzD,EAAQnY,OACjDkH,KAAK6T,SAASC,IAAM,GAEtB7C,EAAQxY,EAAI8b,EACZtD,EAAQvY,EAAIgc,EACZ1U,KAAKkU,UAAUlU,KAAK2T,KAAM,SAAUY,EAAShN,KAAK6I,KAClDpQ,KAAKkU,UAAUlU,KAAK2T,KAAM,SAAUe,EAASnN,KAAK6I,KAClDpQ,KAAKkU,UAAUlU,KAAK2T,KAAM,QAASc,EAAQlN,KAAK+I,KAChDtQ,KAAKkU,UAAUlU,KAAK2T,KAAM,QAASgB,EAAQpN,KAAK+I,KAChDtQ,KAAKkU,UAAUlU,KAAK6T,SAAU,SAAUU,EAAShN,KAAK6I,KACtDpQ,KAAKkU,UAAUlU,KAAK6T,SAAU,SAAUa,EAASnN,KAAK6I,KACtDpQ,KAAKkU,UAAUlU,KAAK6T,SAAU,QAASY,EAAQlN,KAAK+I,KACpDtQ,KAAKkU,UAAUlU,KAAK6T,SAAU,QAASc,EAAQpN,KAAK+I,IACtD,CACAuE,IAAAA,CAAKnB,GACH1T,KAAKjH,KAAO,GACZiH,KAAK2T,KAAO,CACVpa,YAAQ,EACRE,WAAO,EACPD,YAAQ,EACRE,WAAO,EACPka,gBAAY,GAEd5T,KAAK6T,SAAW,CACdta,YAAQ,EACRE,WAAO,EACPD,YAAQ,EACRE,WAAO,EACPoa,IAAK,GAEPC,GAAQL,EAAQM,GAAG3K,YACrB,CACAyL,cAAAA,CAAeN,GACbxU,KAAK2T,KAAKla,OAAS+a,EACnBxU,KAAK2T,KAAKja,OAAS8a,CACrB,GAEET,IAA0B5b,EAAAA,EAAAA,KAAO,SAASia,IAC5C2C,EAAAA,EAAAA,IAAwBxB,GAAMnB,GAC1BA,EAAIb,aACNgC,GAAKyB,iBAAmBzB,GAAK0B,iBAAmB1B,GAAK2B,kBAAoB9C,EAAIb,YAE3Ea,EAAItB,WACNyC,GAAK4B,eAAiB5B,GAAK6B,eAAiB7B,GAAKlD,gBAAkB+B,EAAItB,UAErEsB,EAAIvB,aACN0C,GAAK8B,iBAAmB9B,GAAK+B,iBAAmB/B,GAAKgC,kBAAoBnD,EAAIvB,WAEjF,GAAG,WACC2E,IAA8Brd,EAAAA,EAAAA,KAAO,CAACia,EAAKhI,KACtC,CACLmH,WAAYa,EAAIhI,EAAc,cAC9B0G,SAAUsB,EAAIhI,EAAc,YAC5ByG,WAAYuB,EAAIhI,EAAc,iBAE/B,eACCwG,IAA+BzY,EAAAA,EAAAA,KAAQia,IAClC,CACLb,WAAYa,EAAIqD,mBAChB3E,SAAUsB,EAAIsD,iBACd7E,WAAYuB,EAAIuD,sBAEjB,gBACCzF,IAA8B/X,EAAAA,EAAAA,KAAQia,IACjC,CACLb,WAAYa,EAAI8C,kBAChBpE,SAAUsB,EAAI/B,gBACdQ,WAAYuB,EAAImD,qBAEjB,eACH,SAASK,GAAkBC,EAAU5E,EAAS6E,EAAiBC,EAAUC,GACvE,IAAK/E,EAAQ4E,GAAUhd,MACrB,GAAIid,EACF7E,EAAQ4E,GAAU9b,MAAOkc,EAAAA,EAAAA,IAAUhF,EAAQ4E,GAAU9b,KAAMic,EAAgBD,GAC3E9E,EAAQ4E,GAAUK,UAAYjF,EAAQ4E,GAAU9b,KAAK4L,MAAMgN,EAAAA,GAAe1Y,gBAAgBqB,OAC1F2V,EAAQ4E,GAAUhd,MAAQmd,EAC1B/E,EAAQ4E,GAAU/c,QAASqd,EAAAA,EAAAA,IAAoBlF,EAAQ4E,GAAU9b,KAAMgc,OAClE,CACL,IAAIrQ,EAAQuL,EAAQ4E,GAAU9b,KAAK4L,MAAMgN,EAAAA,GAAe1Y,gBACxDgX,EAAQ4E,GAAUK,UAAYxQ,EAAMpK,OACpC,IAAI8a,EAAa,EACjBnF,EAAQ4E,GAAU/c,OAAS,EAC3BmY,EAAQ4E,GAAUhd,MAAQ,EAC1B,IAAK,MAAM0L,KAAQmB,EACjBuL,EAAQ4E,GAAUhd,MAAQ0O,KAAK+I,KAC7B+F,EAAAA,EAAAA,IAAmB9R,EAAMwR,GACzB9E,EAAQ4E,GAAUhd,OAEpBud,GAAaD,EAAAA,EAAAA,IAAoB5R,EAAMwR,GACvC9E,EAAQ4E,GAAU/c,OAASmY,EAAQ4E,GAAU/c,OAASsd,CAE1D,CAEJ,EACAje,EAAAA,EAAAA,IAAOyd,GAAmB,qBAC1B,IAAIU,IAAgCne,EAAAA,EAAAA,KAAO,SAASoe,EAAU5L,EAAUrR,GACtEqR,EAASlS,EAAIa,EAAOqa,KAAKpa,OACzBoR,EAASjS,EAAIY,EAAOqa,KAAKna,OACzBmR,EAAS9R,MAAQS,EAAOqa,KAAKla,MAAQH,EAAOqa,KAAKpa,OACjDoR,EAAS7R,OAASQ,EAAOqa,KAAKja,MAAQJ,EAAOqa,KAAKna,OAClDmR,EAASnC,MAAM9P,EAAI6a,GAAKiD,cAAgB,GACxC,IAAIC,EAAmB9L,EAAST,MAAQqJ,GAAKrJ,KACzCwM,EAAoB9F,GAAa2C,IACrCmD,EAAkB5F,SAAW4F,EAAkB5F,SAAW,EAC1D4F,EAAkB7F,WAAa,OAE/B+E,GAAkB,QAASjL,EAAU8L,EAAkBC,GADlCL,EAAAA,EAAAA,IAAmB1L,EAASnC,MAAMzO,KAAM2c,IAE7DxD,GAAgB3C,aAAagG,EAAU5L,EAAU4I,GACnD,GAAG,gBACCoD,IAAmCxe,EAAAA,EAAAA,KAAO,SAASye,EAAeL,EAAUM,EAAeC,GAC7F,IAAI/F,EAAI,EACR,IAAK,MAAMgG,KAAcD,EAAa,CACpC/F,EAAI,EACJ,MAAME,EAAU4F,EAAcE,GAC9B,IAAIC,EAAkBxB,GAAYjC,GAAMtC,EAAQ7G,YAAYrQ,MAU5D,OATAid,EAAgBlG,SAAWkG,EAAgBlG,SAAW,EACtDG,EAAQ7G,YAAYvR,OAAQwd,EAAAA,EAAAA,IAC1B,OAASpF,EAAQ7G,YAAYrQ,KAAO,OACpCid,GAEF/F,EAAQ7G,YAAYtR,OAASke,EAAgBlG,SAAW,EACxDG,EAAQ7G,YAAY2G,EAAIwC,GAAK0D,eAC7BlG,EAAIE,EAAQ7G,YAAY2G,EAAIE,EAAQ7G,YAAYtR,OAAS,EACzDmY,EAAQO,MAAQ,CAAE3Y,MAAO,EAAGC,OAAQ,EAAGiY,EAAG,GAClCE,EAAQ7G,YAAYrQ,MAC1B,IAAK,SACL,IAAK,kBACHkX,EAAQO,MAAM3Y,MAAQ,GACtBoY,EAAQO,MAAM1Y,OAAS,GACvBmY,EAAQO,MAAMT,EAAIA,EAClBA,EAAIE,EAAQO,MAAMT,EAAIE,EAAQO,MAAM1Y,OAGpCmY,EAAQvH,SACVuH,EAAQO,MAAM3Y,MAAQ,GACtBoY,EAAQO,MAAM1Y,OAAS,GACvBmY,EAAQO,MAAMT,EAAIA,EAClBA,EAAIE,EAAQO,MAAMT,EAAIE,EAAQO,MAAM1Y,QAEtC,IAAIgd,EAAkB7E,EAAQ/G,MAAQqJ,GAAKrJ,KACvC8L,EAAiBzC,GAAK1a,MAA8B,EAAtB0a,GAAK0D,eACnCC,EAAmB1B,GAAYjC,GAAMtC,EAAQ7G,YAAYrQ,MAM7D,GALAmd,EAAiBpG,SAAWoG,EAAiBpG,SAAW,EACxDoG,EAAiBrG,WAAa,OAC9B+E,GAAkB,QAAS3E,EAAS6E,EAAiBoB,EAAkBlB,GACvE/E,EAAQzI,MAAMuI,EAAIA,EAAI,EACtBA,EAAIE,EAAQzI,MAAMuI,EAAIE,EAAQzI,MAAM1P,OAChCmY,EAAQxI,MAA8B,KAAtBwI,EAAQxI,KAAK1O,KAAa,CAC5CkX,EAAQxI,KAAK1O,KAAO,IAAMkX,EAAQxI,KAAK1O,KAAO,IAE9C6b,GAAkB,OAAQ3E,EAAS6E,EADZN,GAAYjC,GAAMtC,EAAQ7G,YAAYrQ,MACSic,GACtE/E,EAAQxI,KAAKsI,EAAIA,EAAI,EACrBA,EAAIE,EAAQxI,KAAKsI,EAAIE,EAAQxI,KAAK3P,MACpC,MAAO,GAAImY,EAAQzH,OAAgC,KAAvByH,EAAQzH,MAAMzP,KAAa,CACrDkX,EAAQzH,MAAMzP,KAAO,IAAMkX,EAAQzH,MAAMzP,KAAO,IAEhD6b,GAAkB,QAAS3E,EAAS6E,EADbN,GAAYjC,GAAMtC,EAAQzH,MAAMzP,MACgBic,GACvE/E,EAAQzH,MAAMuH,EAAIA,EAAI,EACtBA,EAAIE,EAAQzH,MAAMuH,EAAIE,EAAQzH,MAAM1Q,MACtC,CACA,IAAIqe,EAAapG,EACbqG,EAAYnG,EAAQzI,MAAM3P,MAC9B,GAAIoY,EAAQxH,OAAgC,KAAvBwH,EAAQxH,MAAM1P,KAAa,CAE9C6b,GAAkB,QAAS3E,EAAS6E,EADbN,GAAYjC,GAAMtC,EAAQ7G,YAAYrQ,MACUic,GACvE/E,EAAQxH,MAAMsH,EAAIA,EAAI,GACtBA,EAAIE,EAAQxH,MAAMsH,EAAIE,EAAQxH,MAAM3Q,OACpCse,EAAY7P,KAAK+I,IAAIW,EAAQzI,MAAM3P,MAAOoY,EAAQxH,MAAM5Q,OACxDse,EAAapG,EAA8B,EAA1BE,EAAQxH,MAAMyM,SACjC,CACAkB,GAAwB7D,GAAK0D,eAC7BhG,EAAQpY,MAAQ0O,KAAK+I,IAAIW,EAAQpY,OAAS0a,GAAK1a,MAAOue,EAAW7D,GAAK1a,OACtEoY,EAAQnY,OAASyO,KAAK+I,IAAIW,EAAQnY,QAAUya,GAAKza,OAAQqe,EAAY5D,GAAKza,QAC1EmY,EAAQuD,OAASvD,EAAQuD,QAAUjB,GAAKiD,cACxCI,EAActC,OAAOrD,GACrBiC,GAAgBlC,YAAYuF,EAAUtF,EAASsC,GACjD,CACAqD,EAAc9B,eAAevB,GAAKiD,cACpC,GAAG,oBACCa,GAAQ,MAAM,eAEdlf,EAAAA,EAAAA,IAAO6H,KAAM,SAFC,GAIhByT,WAAAA,CAAYhb,EAAGC,GACbsH,KAAKvH,EAAIA,EACTuH,KAAKtH,EAAIA,CACX,GAEE4e,IAAoCnf,EAAAA,EAAAA,KAAO,SAASof,EAAUxH,GAChE,IAAIyH,EAAKD,EAAS9e,EACdgf,EAAKF,EAAS7e,EACdgf,EAAK3H,EAAStX,EACdkf,EAAK5H,EAASrX,EACdkf,EAAcJ,EAAKD,EAAS1e,MAAQ,EACpCgf,EAAcJ,EAAKF,EAASze,OAAS,EACrCgf,EAAKvQ,KAAKC,IAAIgQ,EAAKE,GACnB9E,EAAKrL,KAAKC,IAAIiQ,EAAKE,GACnBI,EAASnF,EAAKkF,EACdE,EAAUT,EAASze,OAASye,EAAS1e,MACrCof,EAAc,KAyClB,OAxCIR,GAAME,GAAMH,EAAKE,EACnBO,EAAc,IAAIZ,GAAMG,EAAKD,EAAS1e,MAAOgf,GACpCJ,GAAME,GAAMH,EAAKE,EAC1BO,EAAc,IAAIZ,GAAMG,EAAIK,GACnBL,GAAME,GAAMD,EAAKE,EAC1BM,EAAc,IAAIZ,GAAMO,EAAaH,EAAKF,EAASze,QAC1C0e,GAAME,GAAMD,EAAKE,IAC1BM,EAAc,IAAIZ,GAAMO,EAAaH,IAEnCD,EAAKE,GAAMD,EAAKE,EAEhBM,EADED,GAAWD,EACC,IAAIV,GAAMG,EAAIK,EAAcE,EAASR,EAAS1e,MAAQ,GAEtD,IAAIwe,GAChBO,EAAcE,EAAKlF,EAAK2E,EAASze,OAAS,EAC1C2e,EAAKF,EAASze,QAGT0e,EAAKE,GAAMD,EAAKE,EAEvBM,EADED,GAAWD,EACC,IAAIV,GAAMG,EAAKD,EAAS1e,MAAOgf,EAAcE,EAASR,EAAS1e,MAAQ,GAEvE,IAAIwe,GAChBO,EAAcE,EAAKlF,EAAK2E,EAASze,OAAS,EAC1C2e,EAAKF,EAASze,QAGT0e,EAAKE,GAAMD,EAAKE,EAEvBM,EADED,GAAWD,EACC,IAAIV,GAAMG,EAAKD,EAAS1e,MAAOgf,EAAcE,EAASR,EAAS1e,MAAQ,GAEvE,IAAIwe,GAAMO,EAAcL,EAASze,OAAS,EAAIgf,EAAKlF,EAAI6E,GAE9DD,EAAKE,GAAMD,EAAKE,IAEvBM,EADED,GAAWD,EACC,IAAIV,GAAMG,EAAIK,EAAcN,EAAS1e,MAAQ,EAAIkf,GAEjD,IAAIV,GAAMO,EAAcL,EAASze,OAAS,EAAIgf,EAAKlF,EAAI6E,IAGlEQ,CACT,GAAG,qBACCC,IAAqC/f,EAAAA,EAAAA,KAAO,SAASof,EAAUY,GACjE,IAAIC,EAAoB,CAAE3f,EAAG,EAAGC,EAAG,GACnC0f,EAAkB3f,EAAI0f,EAAQ1f,EAAI0f,EAAQtf,MAAQ,EAClDuf,EAAkB1f,EAAIyf,EAAQzf,EAAIyf,EAAQrf,OAAS,EACnD,IAAIgX,EAAawH,GAAkBC,EAAUa,GAI7C,OAHAA,EAAkB3f,EAAI8e,EAAS9e,EAAI8e,EAAS1e,MAAQ,EACpDuf,EAAkB1f,EAAI6e,EAAS7e,EAAI6e,EAASze,OAAS,EAE9C,CAAEgX,aAAYC,SADNuH,GAAkBa,EAASC,GAE5C,GAAG,sBACCC,IAA4BlgB,EAAAA,EAAAA,KAAO,SAASoe,EAAU9G,EAAO6I,EAAe5E,GAC9E,IAAI1M,EAAI,EACR,IAAK,IAAI2C,KAAO8F,EAAO,CACrBzI,GAAQ,EACR,IAAIuR,EAAc5O,EAAIO,MAAQqJ,GAAKrJ,KAC/BsO,EAAUtI,GAAYqD,IAEN,cADFG,EAAQM,GAAG/K,cAE3BU,EAAInB,MAAMzO,KAAOiN,EAAI,KAAO2C,EAAInB,MAAMzO,MAExC,IAAIic,GAAiBK,EAAAA,EAAAA,IAAmB1M,EAAInB,MAAMzO,KAAMye,GACxD5C,GAAkB,QAASjM,EAAK4O,EAAaC,EAASxC,GAClDrM,EAAIH,OAA4B,KAAnBG,EAAIH,MAAMzP,OACzBic,GAAiBK,EAAAA,EAAAA,IAAmB1M,EAAIH,MAAMzP,KAAMye,GACpD5C,GAAkB,QAASjM,EAAK4O,EAAaC,EAASxC,IAEpDrM,EAAIF,OAA4B,KAAnBE,EAAIF,MAAM1P,OACzBic,GAAiBK,EAAAA,EAAAA,IAAmB1M,EAAIF,MAAM1P,KAAMye,GACpD5C,GAAkB,QAASjM,EAAK4O,EAAaC,EAASxC,IAExD,IAAIuB,EAAWe,EAAc3O,EAAIL,MAC7B6O,EAAUG,EAAc3O,EAAIJ,IAC5BkP,EAASP,GAAmBX,EAAUY,GAC1CxO,EAAImG,WAAa2I,EAAO3I,WACxBnG,EAAIoG,SAAW0I,EAAO1I,QACxB,CACAmD,GAAgB1D,SAAS+G,EAAU9G,EAAO8D,GAC5C,GAAG,YACH,SAASmF,GAAmBnC,EAAUoC,EAAqBC,EAAcC,EAAmBnF,GAC1F,IAAIkD,EAAgB,IAAIpD,GAAOE,GAC/BkD,EAAcjD,KAAKC,WAAagF,EAAajF,KAAKC,WAAarM,KAAK6I,IAAIkD,GAAkBuF,EAAkBvd,QAC5G,IAAK,IAAK0L,EAAG8R,KAAoBD,EAAkB5O,UAAW,CAC5D,IAAI8G,EAAI,EACR+H,EAAgBtH,MAAQ,CAAE3Y,MAAO,EAAGC,OAAQ,EAAGiY,EAAG,GAC9C+H,EAAgBpP,SAClBoP,EAAgBtH,MAAM3Y,MAAQ,GAC9BigB,EAAgBtH,MAAM1Y,OAAS,GAC/BggB,EAAgBtH,MAAMT,EAAIA,EAC1BA,EAAI+H,EAAgBtH,MAAMT,EAAI+H,EAAgBtH,MAAM1Y,QAEtD,IAAIigB,EAA0BD,EAAgB5O,MAAQqJ,GAAKrJ,KACvD8O,EAA2BpI,GAAa2C,IAY5C,GAXAyF,EAAyBlI,SAAWkI,EAAyBlI,SAAW,EACxEkI,EAAyBnI,WAAa,OACtC+E,GACE,QACAkD,EACAC,EACAC,EACApC,EAAcjD,KAAKC,YAErBkF,EAAgBtQ,MAAMuI,EAAIA,EAAI,EAC9BA,EAAI+H,EAAgBtQ,MAAMuI,EAAI+H,EAAgBtQ,MAAM1P,OAChDggB,EAAgBrQ,MAAsC,KAA9BqQ,EAAgBrQ,KAAK1O,KAAa,CAC5D+e,EAAgBrQ,KAAK1O,KAAO,IAAM+e,EAAgBrQ,KAAK1O,KAAO,IAE9D6b,GACE,OACAkD,EACAC,EAJ4BnI,GAAa2C,IAMzCqD,EAAcjD,KAAKC,YAErBkF,EAAgBrQ,KAAKsI,EAAIA,EAAI,EAC7BA,EAAI+H,EAAgBrQ,KAAKsI,EAAI+H,EAAgBrQ,KAAK3P,MACpD,CACA,GAAIggB,EAAgBrP,OAAwC,KAA/BqP,EAAgBrP,MAAM1P,KAAa,CAC9D,IAAIkf,EAA2BrI,GAAa2C,IAC5C0F,EAAyBnI,SAAWmI,EAAyBnI,SAAW,EACxE8E,GACE,QACAkD,EACAC,EACAE,EACArC,EAAcjD,KAAKC,YAErBkF,EAAgBrP,MAAMsH,EAAIA,EAAI,GAC9BA,EAAI+H,EAAgBrP,MAAMsH,EAAI+H,EAAgBrP,MAAM3Q,MACtD,CACA,GAAS,GAALkO,GAAUA,EAAIsM,KAAqB,EAAG,CACxC,IAAI4F,EAAKN,EAAajF,KAAKpa,OAASga,GAAK4F,eACrCC,EAAKR,EAAajF,KAAKja,MAAQ6Z,GAAK8F,eAAiBtI,EACzD6F,EAAc3C,QAAQiF,EAAIA,EAAIE,EAAIA,EACpC,KAAO,CACL,IAAIF,EAAKtC,EAAcjD,KAAKla,QAAUmd,EAAcjD,KAAKpa,OAASqd,EAAcjD,KAAKla,MAAQ8Z,GAAK4F,eAAiBvC,EAAcjD,KAAKpa,OAClI6f,EAAKxC,EAAcjD,KAAKna,OAC5Bod,EAAc3C,QAAQiF,EAAIA,EAAIE,EAAIA,EACpC,CACAxC,EAAc7d,KAAO+f,EAAgBvQ,MACrC,IAAI+Q,EAA6B5F,EAAQM,GAAG5H,gBAAgB0M,EAAgBvQ,OACxEgR,EAA4B7F,EAAQM,GAAGzH,eAAeuM,EAAgBvQ,OACtEgR,EAA0Bje,OAAS,GACrCqb,GACEC,EACAL,EACA+C,EACAC,GAGJZ,EAAsBG,EAAgBvQ,MACtC,IAAIiR,EAAwB9F,EAAQM,GAAGtH,aAAaiM,GAChDa,EAAsBle,OAAS,GACjCod,GACEnC,EACAoC,EACA/B,EACA4C,EACA9F,GAG0B,WAA1BoF,EAAgBvQ,OAClB+N,GAAcC,EAAUuC,EAAiBlC,GAE3CgC,EAAajF,KAAKja,MAAQ6N,KAAK+I,IAC7BsG,EAAcjD,KAAKja,MAAQ6Z,GAAKiD,cAChCoC,EAAajF,KAAKja,OAEpBkf,EAAajF,KAAKla,MAAQ8N,KAAK+I,IAC7BsG,EAAcjD,KAAKla,MAAQ8Z,GAAKiD,cAChCoC,EAAajF,KAAKla,OAEpB0Z,GAAqB5L,KAAK+I,IAAI6C,GAAoByF,EAAajF,KAAKla,OACpE2Z,GAAqB7L,KAAK+I,IAAI8C,GAAoBwF,EAAajF,KAAKja,MACtE,CACF,EACAvB,EAAAA,EAAAA,IAAOugB,GAAoB,sBAC3B,IAqDIe,GAAqB,CACvBC,wBAAyB/C,GACzBpG,aAAc+F,GACdvC,WACA4F,MAzDyBxhB,EAAAA,EAAAA,KAAO,SAASyhB,EAAOC,EAAIC,EAAUpG,GAC9DH,IAAOlK,EAAAA,EAAAA,MAAYgE,GACnB,MAAM0M,GAAgB1Q,EAAAA,EAAAA,MAAY0Q,cAClC,IAAIC,EACkB,YAAlBD,IACFC,GAAiBC,EAAAA,EAAAA,KAAO,KAAOJ,IAEjC,MAAMK,EAAyB,YAAlBH,GAA8BE,EAAAA,EAAAA,KAAOD,EAAeG,QAAQ,GAAGC,gBAAgBC,OAAQJ,EAAAA,EAAAA,KAAO,QAC3G,IAAIjG,EAAKN,EAAQM,GACjBN,EAAQM,GAAGnH,QAAQ0G,GAAKrJ,MACxBmJ,GAAgBW,EAAGhI,kBACnBsH,GAAmBU,EAAG/H,qBACtBqO,EAAAA,GAAIC,MAAM,KAAKC,KAAKC,UAAUlH,GAAM,KAAM,MAC1C,MAAMgD,EAA6B,YAAlBwD,EAA8BG,EAAKD,OAAO,QAAQJ,QAAUI,EAAAA,EAAAA,KAAO,QAAQJ,OAC5F3G,GAAgBtB,mBAAmB2E,GACnCrD,GAAgBvB,mBAAmB4E,GACnCrD,GAAgBrB,gBAAgB0E,GAChC,IAAImE,EAAe,IAAIlH,GAAOE,GAC9BgH,EAAazG,QACXV,GAAK4F,eACL5F,GAAK4F,eACL5F,GAAK8F,eACL9F,GAAK8F,gBAEPqB,EAAa/G,KAAKC,WAAa+G,OAAOC,WACtCzH,GAAqBI,GAAK4F,eAC1B/F,GAAqBG,GAAK8F,eAC1B,MAAMwB,EAASnH,EAAQM,GAAGpH,WAE1B8L,GAAmBnC,EAAU,GAAImE,EADThH,EAAQM,GAAGtH,aAAa,IACkBgH,GAClER,GAAgBpB,gBAAgByE,GAChCrD,GAAgBnB,eAAewE,GAC/BrD,GAAgBhB,qBAAqBqE,GACrCrD,GAAgBlB,sBAAsBuE,GACtC8B,GAAU9B,EAAU7C,EAAQM,GAAGrH,UAAW+G,EAAQM,GAAG1H,WAAYoH,GACjEgH,EAAa/G,KAAKla,MAAQ0Z,GAC1BuH,EAAa/G,KAAKja,MAAQ0Z,GAC1B,MAAM0H,EAAMJ,EAAa/G,KACzB,IACI7a,EADYgiB,EAAIphB,MAAQohB,EAAIthB,OACP,EAAI+Z,GAAK8F,eAElC,MAAMxgB,EADSiiB,EAAIrhB,MAAQqhB,EAAIvhB,OACN,EAAIga,GAAK4F,eAC9B0B,GACFtE,EAAShe,OAAO,QAAQwB,KAAK8gB,GAAQriB,KAAK,KAAMsiB,EAAIrhB,MAAQqhB,EAAIvhB,QAAU,EAAI,EAAIga,GAAK4F,gBAAgB3gB,KAAK,IAAKsiB,EAAIthB,OAAS+Z,GAAK8F,iBAErI0B,EAAAA,EAAAA,IAAiBxE,EAAUzd,EAAQD,EAAO0a,GAAKyH,aAC/C,MAAMC,EAAoBJ,EAAS,GAAK,EACxCtE,EAAS/d,KACP,UACAsiB,EAAIvhB,OAASga,GAAK4F,eAAiB,MAAQ5F,GAAK8F,eAAiB4B,GAAqB,IAAMpiB,EAAQ,KAAOC,EAASmiB,IAEtHX,EAAAA,GAAIC,MAAM,UAAWO,EACvB,GAAG,SAiBCI,GAAU,CACZlgB,OAAQiN,EACR+L,GAAI/G,EACJkO,SAAU1B,GACV2B,QAZ8BjjB,EAAAA,EAAAA,KAAQ+K,GAAY,0BACtCA,EAAQmY,4BACVnY,EAAQoY,qBAEjB,aASDzG,MAAsB1c,EAAAA,EAAAA,KAAOojB,IAAkB,IAAjB,GAAElO,EAAE,KAAEnD,GAAMqR,EACxC9B,GAAmB1F,QAAQ1G,GAC3BJ,EAAaJ,QAAQ3C,EAAK,GACzB,Q", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-D6G4REZN.mjs", "../../node_modules/mermaid/dist/chunks/mermaid.core/c4Diagram-VJAJSXHY.mjs"], "sourcesContent": ["import {\n  __name,\n  lineBreakRegex\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/common/svgDrawCommon.ts\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nvar drawRect = /* @__PURE__ */ __name((element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr(\"rx\", rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr(\"ry\", rectData.ry);\n  }\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  if (rectData.class) {\n    rectElement.attr(\"class\", rectData.class);\n  }\n  return rectElement;\n}, \"drawRect\");\nvar drawBackgroundRect = /* @__PURE__ */ __name((element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n}, \"drawBackgroundRect\");\nvar drawText = /* @__PURE__ */ __name((element, textData) => {\n  const nText = textData.text.replace(lineBreakRegex, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawImage = /* @__PURE__ */ __name((elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = sanitizeUrl(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawEmbeddedImage = /* @__PURE__ */ __name((element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = sanitizeUrl(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n}, \"drawEmbeddedImage\");\nvar getNoteRect = /* @__PURE__ */ __name(() => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n}, \"getNoteRect\");\nvar getTextObj = /* @__PURE__ */ __name(() => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n}, \"getTextObj\");\n\nexport {\n  drawRect,\n  drawBackgroundRect,\n  drawText,\n  drawImage,\n  drawEmbeddedImage,\n  getNoteRect,\n  getTextObj\n};\n", "import {\n  drawRect,\n  getNoteRect\n} from \"./chunk-D6G4REZN.mjs\";\nimport {\n  calculateTextHeight,\n  calculateTextWidth,\n  wrapLabel\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  assignWithDepth_default,\n  common_default,\n  configureSvgSize,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  log,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/c4/parser/c4Diagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 24], $V1 = [1, 25], $V2 = [1, 26], $V3 = [1, 27], $V4 = [1, 28], $V5 = [1, 63], $V6 = [1, 64], $V7 = [1, 65], $V8 = [1, 66], $V9 = [1, 67], $Va = [1, 68], $Vb = [1, 69], $Vc = [1, 29], $Vd = [1, 30], $Ve = [1, 31], $Vf = [1, 32], $Vg = [1, 33], $Vh = [1, 34], $Vi = [1, 35], $Vj = [1, 36], $Vk = [1, 37], $Vl = [1, 38], $Vm = [1, 39], $Vn = [1, 40], $Vo = [1, 41], $Vp = [1, 42], $Vq = [1, 43], $Vr = [1, 44], $Vs = [1, 45], $Vt = [1, 46], $Vu = [1, 47], $Vv = [1, 48], $Vw = [1, 50], $Vx = [1, 51], $Vy = [1, 52], $Vz = [1, 53], $VA = [1, 54], $VB = [1, 55], $VC = [1, 56], $VD = [1, 57], $VE = [1, 58], $VF = [1, 59], $VG = [1, 60], $VH = [14, 42], $VI = [14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VJ = [12, 14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VK = [1, 82], $VL = [1, 83], $VM = [1, 84], $VN = [1, 85], $VO = [12, 14, 42], $VP = [12, 14, 33, 42], $VQ = [12, 14, 33, 42, 76, 77, 79, 80], $VR = [12, 33], $VS = [34, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"mermaidDoc\": 4, \"direction\": 5, \"direction_tb\": 6, \"direction_bt\": 7, \"direction_rl\": 8, \"direction_lr\": 9, \"graphConfig\": 10, \"C4_CONTEXT\": 11, \"NEWLINE\": 12, \"statements\": 13, \"EOF\": 14, \"C4_CONTAINER\": 15, \"C4_COMPONENT\": 16, \"C4_DYNAMIC\": 17, \"C4_DEPLOYMENT\": 18, \"otherStatements\": 19, \"diagramStatements\": 20, \"otherStatement\": 21, \"title\": 22, \"accDescription\": 23, \"acc_title\": 24, \"acc_title_value\": 25, \"acc_descr\": 26, \"acc_descr_value\": 27, \"acc_descr_multiline_value\": 28, \"boundaryStatement\": 29, \"boundaryStartStatement\": 30, \"boundaryStopStatement\": 31, \"boundaryStart\": 32, \"LBRACE\": 33, \"ENTERPRISE_BOUNDARY\": 34, \"attributes\": 35, \"SYSTEM_BOUNDARY\": 36, \"BOUNDARY\": 37, \"CONTAINER_BOUNDARY\": 38, \"NODE\": 39, \"NODE_L\": 40, \"NODE_R\": 41, \"RBRACE\": 42, \"diagramStatement\": 43, \"PERSON\": 44, \"PERSON_EXT\": 45, \"SYSTEM\": 46, \"SYSTEM_DB\": 47, \"SYSTEM_QUEUE\": 48, \"SYSTEM_EXT\": 49, \"SYSTEM_EXT_DB\": 50, \"SYSTEM_EXT_QUEUE\": 51, \"CONTAINER\": 52, \"CONTAINER_DB\": 53, \"CONTAINER_QUEUE\": 54, \"CONTAINER_EXT\": 55, \"CONTAINER_EXT_DB\": 56, \"CONTAINER_EXT_QUEUE\": 57, \"COMPONENT\": 58, \"COMPONENT_DB\": 59, \"COMPONENT_QUEUE\": 60, \"COMPONENT_EXT\": 61, \"COMPONENT_EXT_DB\": 62, \"COMPONENT_EXT_QUEUE\": 63, \"REL\": 64, \"BIREL\": 65, \"REL_U\": 66, \"REL_D\": 67, \"REL_L\": 68, \"REL_R\": 69, \"REL_B\": 70, \"REL_INDEX\": 71, \"UPDATE_EL_STYLE\": 72, \"UPDATE_REL_STYLE\": 73, \"UPDATE_LAYOUT_CONFIG\": 74, \"attribute\": 75, \"STR\": 76, \"STR_KEY\": 77, \"STR_VALUE\": 78, \"ATTRIBUTE\": 79, \"ATTRIBUTE_EMPTY\": 80, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 6: \"direction_tb\", 7: \"direction_bt\", 8: \"direction_rl\", 9: \"direction_lr\", 11: \"C4_CONTEXT\", 12: \"NEWLINE\", 14: \"EOF\", 15: \"C4_CONTAINER\", 16: \"C4_COMPONENT\", 17: \"C4_DYNAMIC\", 18: \"C4_DEPLOYMENT\", 22: \"title\", 23: \"accDescription\", 24: \"acc_title\", 25: \"acc_title_value\", 26: \"acc_descr\", 27: \"acc_descr_value\", 28: \"acc_descr_multiline_value\", 33: \"LBRACE\", 34: \"ENTERPRISE_BOUNDARY\", 36: \"SYSTEM_BOUNDARY\", 37: \"BOUNDARY\", 38: \"CONTAINER_BOUNDARY\", 39: \"NODE\", 40: \"NODE_L\", 41: \"NODE_R\", 42: \"RBRACE\", 44: \"PERSON\", 45: \"PERSON_EXT\", 46: \"SYSTEM\", 47: \"SYSTEM_DB\", 48: \"SYSTEM_QUEUE\", 49: \"SYSTEM_EXT\", 50: \"SYSTEM_EXT_DB\", 51: \"SYSTEM_EXT_QUEUE\", 52: \"CONTAINER\", 53: \"CONTAINER_DB\", 54: \"CONTAINER_QUEUE\", 55: \"CONTAINER_EXT\", 56: \"CONTAINER_EXT_DB\", 57: \"CONTAINER_EXT_QUEUE\", 58: \"COMPONENT\", 59: \"COMPONENT_DB\", 60: \"COMPONENT_QUEUE\", 61: \"COMPONENT_EXT\", 62: \"COMPONENT_EXT_DB\", 63: \"COMPONENT_EXT_QUEUE\", 64: \"REL\", 65: \"BIREL\", 66: \"REL_U\", 67: \"REL_D\", 68: \"REL_L\", 69: \"REL_R\", 70: \"REL_B\", 71: \"REL_INDEX\", 72: \"UPDATE_EL_STYLE\", 73: \"UPDATE_REL_STYLE\", 74: \"UPDATE_LAYOUT_CONFIG\", 76: \"STR\", 77: \"STR_KEY\", 78: \"STR_VALUE\", 79: \"ATTRIBUTE\", 80: \"ATTRIBUTE_EMPTY\" },\n    productions_: [0, [3, 1], [3, 1], [5, 1], [5, 1], [5, 1], [5, 1], [4, 1], [10, 4], [10, 4], [10, 4], [10, 4], [10, 4], [13, 1], [13, 1], [13, 2], [19, 1], [19, 2], [19, 3], [21, 1], [21, 1], [21, 2], [21, 2], [21, 1], [29, 3], [30, 3], [30, 3], [30, 4], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [31, 1], [20, 1], [20, 2], [20, 3], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 1], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [35, 1], [35, 2], [75, 1], [75, 2], [75, 1], [75, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setDirection(\"TB\");\n          break;\n        case 4:\n          yy.setDirection(\"BT\");\n          break;\n        case 5:\n          yy.setDirection(\"RL\");\n          break;\n        case 6:\n          yy.setDirection(\"LR\");\n          break;\n        case 8:\n        case 9:\n        case 10:\n        case 11:\n        case 12:\n          yy.setC4Type($$[$0 - 3]);\n          break;\n        case 19:\n          yy.setTitle($$[$0].substring(6));\n          this.$ = $$[$0].substring(6);\n          break;\n        case 20:\n          yy.setAccDescription($$[$0].substring(15));\n          this.$ = $$[$0].substring(15);\n          break;\n        case 21:\n          this.$ = $$[$0].trim();\n          yy.setTitle(this.$);\n          break;\n        case 22:\n        case 23:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 28:\n          $$[$0].splice(2, 0, \"ENTERPRISE\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 29:\n          $$[$0].splice(2, 0, \"SYSTEM\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 30:\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 31:\n          $$[$0].splice(2, 0, \"CONTAINER\");\n          yy.addContainerBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 32:\n          yy.addDeploymentNode(\"node\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 33:\n          yy.addDeploymentNode(\"nodeL\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 34:\n          yy.addDeploymentNode(\"nodeR\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 35:\n          yy.popBoundaryParseStack();\n          break;\n        case 39:\n          yy.addPersonOrSystem(\"person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 40:\n          yy.addPersonOrSystem(\"external_person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 41:\n          yy.addPersonOrSystem(\"system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 42:\n          yy.addPersonOrSystem(\"system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 43:\n          yy.addPersonOrSystem(\"system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 44:\n          yy.addPersonOrSystem(\"external_system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 45:\n          yy.addPersonOrSystem(\"external_system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 46:\n          yy.addPersonOrSystem(\"external_system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 47:\n          yy.addContainer(\"container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 48:\n          yy.addContainer(\"container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 49:\n          yy.addContainer(\"container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 50:\n          yy.addContainer(\"external_container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 51:\n          yy.addContainer(\"external_container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 52:\n          yy.addContainer(\"external_container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 53:\n          yy.addComponent(\"component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 54:\n          yy.addComponent(\"component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 55:\n          yy.addComponent(\"component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 56:\n          yy.addComponent(\"external_component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 57:\n          yy.addComponent(\"external_component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 58:\n          yy.addComponent(\"external_component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 60:\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 61:\n          yy.addRel(\"birel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 62:\n          yy.addRel(\"rel_u\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 63:\n          yy.addRel(\"rel_d\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 64:\n          yy.addRel(\"rel_l\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 65:\n          yy.addRel(\"rel_r\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 66:\n          yy.addRel(\"rel_b\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 67:\n          $$[$0].splice(0, 1);\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 68:\n          yy.updateElStyle(\"update_el_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 69:\n          yy.updateRelStyle(\"update_rel_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 70:\n          yy.updateLayoutConfig(\"update_layout_config\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 71:\n          this.$ = [$$[$0]];\n          break;\n        case 72:\n          $$[$0].unshift($$[$0 - 1]);\n          this.$ = $$[$0];\n          break;\n        case 73:\n        case 75:\n          this.$ = $$[$0].trim();\n          break;\n        case 74:\n          let kv = {};\n          kv[$$[$0 - 1].trim()] = $$[$0].trim();\n          this.$ = kv;\n          break;\n        case 76:\n          this.$ = \"\";\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 5: 3, 6: [1, 5], 7: [1, 6], 8: [1, 7], 9: [1, 8], 10: 4, 11: [1, 9], 15: [1, 10], 16: [1, 11], 17: [1, 12], 18: [1, 13] }, { 1: [3] }, { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 7] }, { 1: [2, 3] }, { 1: [2, 4] }, { 1: [2, 5] }, { 1: [2, 6] }, { 12: [1, 14] }, { 12: [1, 15] }, { 12: [1, 16] }, { 12: [1, 17] }, { 12: [1, 18] }, { 13: 19, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 70, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 71, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 72, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 73, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 14: [1, 74] }, o($VH, [2, 13], { 43: 23, 29: 49, 30: 61, 32: 62, 20: 75, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VH, [2, 14]), o($VI, [2, 16], { 12: [1, 76] }), o($VH, [2, 36], { 12: [1, 77] }), o($VJ, [2, 19]), o($VJ, [2, 20]), { 25: [1, 78] }, { 27: [1, 79] }, o($VJ, [2, 23]), { 35: 80, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 86, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 87, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 88, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 89, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 90, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 91, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 92, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 93, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 94, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 95, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 96, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 97, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 98, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 99, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 100, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 101, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 102, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 103, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 104, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, o($VO, [2, 59]), { 35: 105, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 106, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 107, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 108, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 109, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 110, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 111, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 112, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 113, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 114, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 115, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 20: 116, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 12: [1, 118], 33: [1, 117] }, { 35: 119, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 120, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 121, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 122, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 123, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 124, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 125, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 14: [1, 126] }, { 14: [1, 127] }, { 14: [1, 128] }, { 14: [1, 129] }, { 1: [2, 8] }, o($VH, [2, 15]), o($VI, [2, 17], { 21: 22, 19: 130, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4 }), o($VH, [2, 37], { 19: 20, 20: 21, 21: 22, 43: 23, 29: 49, 30: 61, 32: 62, 13: 131, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VJ, [2, 21]), o($VJ, [2, 22]), o($VO, [2, 39]), o($VP, [2, 71], { 75: 81, 35: 132, 76: $VK, 77: $VL, 79: $VM, 80: $VN }), o($VQ, [2, 73]), { 78: [1, 133] }, o($VQ, [2, 75]), o($VQ, [2, 76]), o($VO, [2, 40]), o($VO, [2, 41]), o($VO, [2, 42]), o($VO, [2, 43]), o($VO, [2, 44]), o($VO, [2, 45]), o($VO, [2, 46]), o($VO, [2, 47]), o($VO, [2, 48]), o($VO, [2, 49]), o($VO, [2, 50]), o($VO, [2, 51]), o($VO, [2, 52]), o($VO, [2, 53]), o($VO, [2, 54]), o($VO, [2, 55]), o($VO, [2, 56]), o($VO, [2, 57]), o($VO, [2, 58]), o($VO, [2, 60]), o($VO, [2, 61]), o($VO, [2, 62]), o($VO, [2, 63]), o($VO, [2, 64]), o($VO, [2, 65]), o($VO, [2, 66]), o($VO, [2, 67]), o($VO, [2, 68]), o($VO, [2, 69]), o($VO, [2, 70]), { 31: 134, 42: [1, 135] }, { 12: [1, 136] }, { 33: [1, 137] }, o($VR, [2, 28]), o($VR, [2, 29]), o($VR, [2, 30]), o($VR, [2, 31]), o($VR, [2, 32]), o($VR, [2, 33]), o($VR, [2, 34]), { 1: [2, 9] }, { 1: [2, 10] }, { 1: [2, 11] }, { 1: [2, 12] }, o($VI, [2, 18]), o($VH, [2, 38]), o($VP, [2, 72]), o($VQ, [2, 74]), o($VO, [2, 24]), o($VO, [2, 35]), o($VS, [2, 25]), o($VS, [2, 26], { 12: [1, 138] }), o($VS, [2, 27])],\n    defaultActions: { 2: [2, 1], 3: [2, 2], 4: [2, 7], 5: [2, 3], 6: [2, 4], 7: [2, 5], 8: [2, 6], 74: [2, 8], 126: [2, 9], 127: [2, 10], 128: [2, 11], 129: [2, 12] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c2 = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c2 + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: {},\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 6;\n            break;\n          case 1:\n            return 7;\n            break;\n          case 2:\n            return 8;\n            break;\n          case 3:\n            return 9;\n            break;\n          case 4:\n            return 22;\n            break;\n          case 5:\n            return 23;\n            break;\n          case 6:\n            this.begin(\"acc_title\");\n            return 24;\n            break;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 8:\n            this.begin(\"acc_descr\");\n            return 26;\n            break;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 13:\n            break;\n          case 14:\n            c;\n            break;\n          case 15:\n            return 12;\n            break;\n          case 16:\n            break;\n          case 17:\n            return 11;\n            break;\n          case 18:\n            return 15;\n            break;\n          case 19:\n            return 16;\n            break;\n          case 20:\n            return 17;\n            break;\n          case 21:\n            return 18;\n            break;\n          case 22:\n            this.begin(\"person_ext\");\n            return 45;\n            break;\n          case 23:\n            this.begin(\"person\");\n            return 44;\n            break;\n          case 24:\n            this.begin(\"system_ext_queue\");\n            return 51;\n            break;\n          case 25:\n            this.begin(\"system_ext_db\");\n            return 50;\n            break;\n          case 26:\n            this.begin(\"system_ext\");\n            return 49;\n            break;\n          case 27:\n            this.begin(\"system_queue\");\n            return 48;\n            break;\n          case 28:\n            this.begin(\"system_db\");\n            return 47;\n            break;\n          case 29:\n            this.begin(\"system\");\n            return 46;\n            break;\n          case 30:\n            this.begin(\"boundary\");\n            return 37;\n            break;\n          case 31:\n            this.begin(\"enterprise_boundary\");\n            return 34;\n            break;\n          case 32:\n            this.begin(\"system_boundary\");\n            return 36;\n            break;\n          case 33:\n            this.begin(\"container_ext_queue\");\n            return 57;\n            break;\n          case 34:\n            this.begin(\"container_ext_db\");\n            return 56;\n            break;\n          case 35:\n            this.begin(\"container_ext\");\n            return 55;\n            break;\n          case 36:\n            this.begin(\"container_queue\");\n            return 54;\n            break;\n          case 37:\n            this.begin(\"container_db\");\n            return 53;\n            break;\n          case 38:\n            this.begin(\"container\");\n            return 52;\n            break;\n          case 39:\n            this.begin(\"container_boundary\");\n            return 38;\n            break;\n          case 40:\n            this.begin(\"component_ext_queue\");\n            return 63;\n            break;\n          case 41:\n            this.begin(\"component_ext_db\");\n            return 62;\n            break;\n          case 42:\n            this.begin(\"component_ext\");\n            return 61;\n            break;\n          case 43:\n            this.begin(\"component_queue\");\n            return 60;\n            break;\n          case 44:\n            this.begin(\"component_db\");\n            return 59;\n            break;\n          case 45:\n            this.begin(\"component\");\n            return 58;\n            break;\n          case 46:\n            this.begin(\"node\");\n            return 39;\n            break;\n          case 47:\n            this.begin(\"node\");\n            return 39;\n            break;\n          case 48:\n            this.begin(\"node_l\");\n            return 40;\n            break;\n          case 49:\n            this.begin(\"node_r\");\n            return 41;\n            break;\n          case 50:\n            this.begin(\"rel\");\n            return 64;\n            break;\n          case 51:\n            this.begin(\"birel\");\n            return 65;\n            break;\n          case 52:\n            this.begin(\"rel_u\");\n            return 66;\n            break;\n          case 53:\n            this.begin(\"rel_u\");\n            return 66;\n            break;\n          case 54:\n            this.begin(\"rel_d\");\n            return 67;\n            break;\n          case 55:\n            this.begin(\"rel_d\");\n            return 67;\n            break;\n          case 56:\n            this.begin(\"rel_l\");\n            return 68;\n            break;\n          case 57:\n            this.begin(\"rel_l\");\n            return 68;\n            break;\n          case 58:\n            this.begin(\"rel_r\");\n            return 69;\n            break;\n          case 59:\n            this.begin(\"rel_r\");\n            return 69;\n            break;\n          case 60:\n            this.begin(\"rel_b\");\n            return 70;\n            break;\n          case 61:\n            this.begin(\"rel_index\");\n            return 71;\n            break;\n          case 62:\n            this.begin(\"update_el_style\");\n            return 72;\n            break;\n          case 63:\n            this.begin(\"update_rel_style\");\n            return 73;\n            break;\n          case 64:\n            this.begin(\"update_layout_config\");\n            return 74;\n            break;\n          case 65:\n            return \"EOF_IN_STRUCT\";\n            break;\n          case 66:\n            this.begin(\"attribute\");\n            return \"ATTRIBUTE_EMPTY\";\n            break;\n          case 67:\n            this.begin(\"attribute\");\n            break;\n          case 68:\n            this.popState();\n            this.popState();\n            break;\n          case 69:\n            return 80;\n            break;\n          case 70:\n            break;\n          case 71:\n            return 80;\n            break;\n          case 72:\n            this.begin(\"string\");\n            break;\n          case 73:\n            this.popState();\n            break;\n          case 74:\n            return \"STR\";\n            break;\n          case 75:\n            this.begin(\"string_kv\");\n            break;\n          case 76:\n            this.begin(\"string_kv_key\");\n            return \"STR_KEY\";\n            break;\n          case 77:\n            this.popState();\n            this.begin(\"string_kv_value\");\n            break;\n          case 78:\n            return \"STR_VALUE\";\n            break;\n          case 79:\n            this.popState();\n            this.popState();\n            break;\n          case 80:\n            return \"STR\";\n            break;\n          case 81:\n            return \"LBRACE\";\n            break;\n          case 82:\n            return \"RBRACE\";\n            break;\n          case 83:\n            return \"SPACE\";\n            break;\n          case 84:\n            return \"EOL\";\n            break;\n          case 85:\n            return 14;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:.*direction\\s+TB[^\\n]*)/, /^(?:.*direction\\s+BT[^\\n]*)/, /^(?:.*direction\\s+RL[^\\n]*)/, /^(?:.*direction\\s+LR[^\\n]*)/, /^(?:title\\s[^#\\n;]+)/, /^(?:accDescription\\s[^#\\n;]+)/, /^(?:accTitle\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*\\{\\s*)/, /^(?:[\\}])/, /^(?:[^\\}]*)/, /^(?:%%(?!\\{)*[^\\n]*(\\r?\\n?)+)/, /^(?:%%[^\\n]*(\\r?\\n)*)/, /^(?:\\s*(\\r?\\n)+)/, /^(?:\\s+)/, /^(?:C4Context\\b)/, /^(?:C4Container\\b)/, /^(?:C4Component\\b)/, /^(?:C4Dynamic\\b)/, /^(?:C4Deployment\\b)/, /^(?:Person_Ext\\b)/, /^(?:Person\\b)/, /^(?:SystemQueue_Ext\\b)/, /^(?:SystemDb_Ext\\b)/, /^(?:System_Ext\\b)/, /^(?:SystemQueue\\b)/, /^(?:SystemDb\\b)/, /^(?:System\\b)/, /^(?:Boundary\\b)/, /^(?:Enterprise_Boundary\\b)/, /^(?:System_Boundary\\b)/, /^(?:ContainerQueue_Ext\\b)/, /^(?:ContainerDb_Ext\\b)/, /^(?:Container_Ext\\b)/, /^(?:ContainerQueue\\b)/, /^(?:ContainerDb\\b)/, /^(?:Container\\b)/, /^(?:Container_Boundary\\b)/, /^(?:ComponentQueue_Ext\\b)/, /^(?:ComponentDb_Ext\\b)/, /^(?:Component_Ext\\b)/, /^(?:ComponentQueue\\b)/, /^(?:ComponentDb\\b)/, /^(?:Component\\b)/, /^(?:Deployment_Node\\b)/, /^(?:Node\\b)/, /^(?:Node_L\\b)/, /^(?:Node_R\\b)/, /^(?:Rel\\b)/, /^(?:BiRel\\b)/, /^(?:Rel_Up\\b)/, /^(?:Rel_U\\b)/, /^(?:Rel_Down\\b)/, /^(?:Rel_D\\b)/, /^(?:Rel_Left\\b)/, /^(?:Rel_L\\b)/, /^(?:Rel_Right\\b)/, /^(?:Rel_R\\b)/, /^(?:Rel_Back\\b)/, /^(?:RelIndex\\b)/, /^(?:UpdateElementStyle\\b)/, /^(?:UpdateRelStyle\\b)/, /^(?:UpdateLayoutConfig\\b)/, /^(?:$)/, /^(?:[(][ ]*[,])/, /^(?:[(])/, /^(?:[)])/, /^(?:,,)/, /^(?:,)/, /^(?:[ ]*[\"][\"])/, /^(?:[ ]*[\"])/, /^(?:[\"])/, /^(?:[^\"]*)/, /^(?:[ ]*[\\$])/, /^(?:[^=]*)/, /^(?:[=][ ]*[\"])/, /^(?:[^\"]+)/, /^(?:[\"])/, /^(?:[^,]+)/, /^(?:\\{)/, /^(?:\\})/, /^(?:[\\s]+)/, /^(?:[\\n\\r]+)/, /^(?:$)/],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"string_kv_value\": { \"rules\": [78, 79], \"inclusive\": false }, \"string_kv_key\": { \"rules\": [77], \"inclusive\": false }, \"string_kv\": { \"rules\": [76], \"inclusive\": false }, \"string\": { \"rules\": [73, 74], \"inclusive\": false }, \"attribute\": { \"rules\": [68, 69, 70, 71, 72, 75, 80], \"inclusive\": false }, \"update_layout_config\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_rel_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_el_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_b\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_d\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_u\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_bi\": { \"rules\": [], \"inclusive\": false }, \"rel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"index\": { \"rules\": [], \"inclusive\": false }, \"rel_index\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext_queue\": { \"rules\": [], \"inclusive\": false }, \"component_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"birel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"enterprise_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 81, 82, 83, 84, 85], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar c4Diagram_default = parser;\n\n// src/diagrams/c4/c4Db.js\nvar c4ShapeArray = [];\nvar boundaryParseStack = [\"\"];\nvar currentBoundaryParse = \"global\";\nvar parentBoundaryParse = \"\";\nvar boundaries = [\n  {\n    alias: \"global\",\n    label: { text: \"global\" },\n    type: { text: \"global\" },\n    tags: null,\n    link: null,\n    parentBoundary: \"\"\n  }\n];\nvar rels = [];\nvar title = \"\";\nvar wrapEnabled = false;\nvar c4ShapeInRow = 4;\nvar c4BoundaryInRow = 2;\nvar c4Type;\nvar getC4Type = /* @__PURE__ */ __name(function() {\n  return c4Type;\n}, \"getC4Type\");\nvar setC4Type = /* @__PURE__ */ __name(function(c4TypeParam) {\n  let sanitizedText = sanitizeText(c4TypeParam, getConfig());\n  c4Type = sanitizedText;\n}, \"setC4Type\");\nvar addRel = /* @__PURE__ */ __name(function(type, from, to, label, techn, descr, sprite, tags, link) {\n  if (type === void 0 || type === null || from === void 0 || from === null || to === void 0 || to === null || label === void 0 || label === null) {\n    return;\n  }\n  let rel = {};\n  const old = rels.find((rel2) => rel2.from === from && rel2.to === to);\n  if (old) {\n    rel = old;\n  } else {\n    rels.push(rel);\n  }\n  rel.type = type;\n  rel.from = from;\n  rel.to = to;\n  rel.label = { text: label };\n  if (techn === void 0 || techn === null) {\n    rel.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    rel.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    rel[key] = value;\n  } else {\n    rel.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    rel[key] = value;\n  } else {\n    rel.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    rel[key] = value;\n  } else {\n    rel.link = link;\n  }\n  rel.wrap = autoWrap();\n}, \"addRel\");\nvar addPersonOrSystem = /* @__PURE__ */ __name(function(typeC4Shape, alias, label, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let personOrSystem = {};\n  const old = c4ShapeArray.find((personOrSystem2) => personOrSystem2.alias === alias);\n  if (old && alias === old.alias) {\n    personOrSystem = old;\n  } else {\n    personOrSystem.alias = alias;\n    c4ShapeArray.push(personOrSystem);\n  }\n  if (label === void 0 || label === null) {\n    personOrSystem.label = { text: \"\" };\n  } else {\n    personOrSystem.label = { text: label };\n  }\n  if (descr === void 0 || descr === null) {\n    personOrSystem.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      personOrSystem[key] = { text: value };\n    } else {\n      personOrSystem.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.link = link;\n  }\n  personOrSystem.typeC4Shape = { text: typeC4Shape };\n  personOrSystem.parentBoundary = currentBoundaryParse;\n  personOrSystem.wrap = autoWrap();\n}, \"addPersonOrSystem\");\nvar addContainer = /* @__PURE__ */ __name(function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let container = {};\n  const old = c4ShapeArray.find((container2) => container2.alias === alias);\n  if (old && alias === old.alias) {\n    container = old;\n  } else {\n    container.alias = alias;\n    c4ShapeArray.push(container);\n  }\n  if (label === void 0 || label === null) {\n    container.label = { text: \"\" };\n  } else {\n    container.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    container.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      container[key] = { text: value };\n    } else {\n      container.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    container.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      container[key] = { text: value };\n    } else {\n      container.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    container[key] = value;\n  } else {\n    container.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    container[key] = value;\n  } else {\n    container.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    container[key] = value;\n  } else {\n    container.link = link;\n  }\n  container.wrap = autoWrap();\n  container.typeC4Shape = { text: typeC4Shape };\n  container.parentBoundary = currentBoundaryParse;\n}, \"addContainer\");\nvar addComponent = /* @__PURE__ */ __name(function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let component = {};\n  const old = c4ShapeArray.find((component2) => component2.alias === alias);\n  if (old && alias === old.alias) {\n    component = old;\n  } else {\n    component.alias = alias;\n    c4ShapeArray.push(component);\n  }\n  if (label === void 0 || label === null) {\n    component.label = { text: \"\" };\n  } else {\n    component.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    component.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      component[key] = { text: value };\n    } else {\n      component.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    component.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      component[key] = { text: value };\n    } else {\n      component.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    component[key] = value;\n  } else {\n    component.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    component[key] = value;\n  } else {\n    component.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    component[key] = value;\n  } else {\n    component.link = link;\n  }\n  component.wrap = autoWrap();\n  component.typeC4Shape = { text: typeC4Shape };\n  component.parentBoundary = currentBoundaryParse;\n}, \"addComponent\");\nvar addPersonOrSystemBoundary = /* @__PURE__ */ __name(function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"system\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addPersonOrSystemBoundary\");\nvar addContainerBoundary = /* @__PURE__ */ __name(function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"container\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addContainerBoundary\");\nvar addDeploymentNode = /* @__PURE__ */ __name(function(nodeType, alias, label, type, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"node\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    boundary.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.descr = { text: descr };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.nodeType = nodeType;\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addDeploymentNode\");\nvar popBoundaryParseStack = /* @__PURE__ */ __name(function() {\n  currentBoundaryParse = parentBoundaryParse;\n  boundaryParseStack.pop();\n  parentBoundaryParse = boundaryParseStack.pop();\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"popBoundaryParseStack\");\nvar updateElStyle = /* @__PURE__ */ __name(function(typeC4Shape, elementName, bgColor, fontColor, borderColor, shadowing, shape, sprite, techn, legendText, legendSprite) {\n  let old = c4ShapeArray.find((element) => element.alias === elementName);\n  if (old === void 0) {\n    old = boundaries.find((element) => element.alias === elementName);\n    if (old === void 0) {\n      return;\n    }\n  }\n  if (bgColor !== void 0 && bgColor !== null) {\n    if (typeof bgColor === \"object\") {\n      let [key, value] = Object.entries(bgColor)[0];\n      old[key] = value;\n    } else {\n      old.bgColor = bgColor;\n    }\n  }\n  if (fontColor !== void 0 && fontColor !== null) {\n    if (typeof fontColor === \"object\") {\n      let [key, value] = Object.entries(fontColor)[0];\n      old[key] = value;\n    } else {\n      old.fontColor = fontColor;\n    }\n  }\n  if (borderColor !== void 0 && borderColor !== null) {\n    if (typeof borderColor === \"object\") {\n      let [key, value] = Object.entries(borderColor)[0];\n      old[key] = value;\n    } else {\n      old.borderColor = borderColor;\n    }\n  }\n  if (shadowing !== void 0 && shadowing !== null) {\n    if (typeof shadowing === \"object\") {\n      let [key, value] = Object.entries(shadowing)[0];\n      old[key] = value;\n    } else {\n      old.shadowing = shadowing;\n    }\n  }\n  if (shape !== void 0 && shape !== null) {\n    if (typeof shape === \"object\") {\n      let [key, value] = Object.entries(shape)[0];\n      old[key] = value;\n    } else {\n      old.shape = shape;\n    }\n  }\n  if (sprite !== void 0 && sprite !== null) {\n    if (typeof sprite === \"object\") {\n      let [key, value] = Object.entries(sprite)[0];\n      old[key] = value;\n    } else {\n      old.sprite = sprite;\n    }\n  }\n  if (techn !== void 0 && techn !== null) {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      old[key] = value;\n    } else {\n      old.techn = techn;\n    }\n  }\n  if (legendText !== void 0 && legendText !== null) {\n    if (typeof legendText === \"object\") {\n      let [key, value] = Object.entries(legendText)[0];\n      old[key] = value;\n    } else {\n      old.legendText = legendText;\n    }\n  }\n  if (legendSprite !== void 0 && legendSprite !== null) {\n    if (typeof legendSprite === \"object\") {\n      let [key, value] = Object.entries(legendSprite)[0];\n      old[key] = value;\n    } else {\n      old.legendSprite = legendSprite;\n    }\n  }\n}, \"updateElStyle\");\nvar updateRelStyle = /* @__PURE__ */ __name(function(typeC4Shape, from, to, textColor, lineColor, offsetX, offsetY) {\n  const old = rels.find((rel) => rel.from === from && rel.to === to);\n  if (old === void 0) {\n    return;\n  }\n  if (textColor !== void 0 && textColor !== null) {\n    if (typeof textColor === \"object\") {\n      let [key, value] = Object.entries(textColor)[0];\n      old[key] = value;\n    } else {\n      old.textColor = textColor;\n    }\n  }\n  if (lineColor !== void 0 && lineColor !== null) {\n    if (typeof lineColor === \"object\") {\n      let [key, value] = Object.entries(lineColor)[0];\n      old[key] = value;\n    } else {\n      old.lineColor = lineColor;\n    }\n  }\n  if (offsetX !== void 0 && offsetX !== null) {\n    if (typeof offsetX === \"object\") {\n      let [key, value] = Object.entries(offsetX)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetX = parseInt(offsetX);\n    }\n  }\n  if (offsetY !== void 0 && offsetY !== null) {\n    if (typeof offsetY === \"object\") {\n      let [key, value] = Object.entries(offsetY)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetY = parseInt(offsetY);\n    }\n  }\n}, \"updateRelStyle\");\nvar updateLayoutConfig = /* @__PURE__ */ __name(function(typeC4Shape, c4ShapeInRowParam, c4BoundaryInRowParam) {\n  let c4ShapeInRowValue = c4ShapeInRow;\n  let c4BoundaryInRowValue = c4BoundaryInRow;\n  if (typeof c4ShapeInRowParam === \"object\") {\n    const value = Object.values(c4ShapeInRowParam)[0];\n    c4ShapeInRowValue = parseInt(value);\n  } else {\n    c4ShapeInRowValue = parseInt(c4ShapeInRowParam);\n  }\n  if (typeof c4BoundaryInRowParam === \"object\") {\n    const value = Object.values(c4BoundaryInRowParam)[0];\n    c4BoundaryInRowValue = parseInt(value);\n  } else {\n    c4BoundaryInRowValue = parseInt(c4BoundaryInRowParam);\n  }\n  if (c4ShapeInRowValue >= 1) {\n    c4ShapeInRow = c4ShapeInRowValue;\n  }\n  if (c4BoundaryInRowValue >= 1) {\n    c4BoundaryInRow = c4BoundaryInRowValue;\n  }\n}, \"updateLayoutConfig\");\nvar getC4ShapeInRow = /* @__PURE__ */ __name(function() {\n  return c4ShapeInRow;\n}, \"getC4ShapeInRow\");\nvar getC4BoundaryInRow = /* @__PURE__ */ __name(function() {\n  return c4BoundaryInRow;\n}, \"getC4BoundaryInRow\");\nvar getCurrentBoundaryParse = /* @__PURE__ */ __name(function() {\n  return currentBoundaryParse;\n}, \"getCurrentBoundaryParse\");\nvar getParentBoundaryParse = /* @__PURE__ */ __name(function() {\n  return parentBoundaryParse;\n}, \"getParentBoundaryParse\");\nvar getC4ShapeArray = /* @__PURE__ */ __name(function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return c4ShapeArray;\n  } else {\n    return c4ShapeArray.filter((personOrSystem) => {\n      return personOrSystem.parentBoundary === parentBoundary;\n    });\n  }\n}, \"getC4ShapeArray\");\nvar getC4Shape = /* @__PURE__ */ __name(function(alias) {\n  return c4ShapeArray.find((personOrSystem) => personOrSystem.alias === alias);\n}, \"getC4Shape\");\nvar getC4ShapeKeys = /* @__PURE__ */ __name(function(parentBoundary) {\n  return Object.keys(getC4ShapeArray(parentBoundary));\n}, \"getC4ShapeKeys\");\nvar getBoundaries = /* @__PURE__ */ __name(function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return boundaries;\n  } else {\n    return boundaries.filter((boundary) => boundary.parentBoundary === parentBoundary);\n  }\n}, \"getBoundaries\");\nvar getBoundarys = getBoundaries;\nvar getRels = /* @__PURE__ */ __name(function() {\n  return rels;\n}, \"getRels\");\nvar getTitle = /* @__PURE__ */ __name(function() {\n  return title;\n}, \"getTitle\");\nvar setWrap = /* @__PURE__ */ __name(function(wrapSetting) {\n  wrapEnabled = wrapSetting;\n}, \"setWrap\");\nvar autoWrap = /* @__PURE__ */ __name(function() {\n  return wrapEnabled;\n}, \"autoWrap\");\nvar clear = /* @__PURE__ */ __name(function() {\n  c4ShapeArray = [];\n  boundaries = [\n    {\n      alias: \"global\",\n      label: { text: \"global\" },\n      type: { text: \"global\" },\n      tags: null,\n      link: null,\n      parentBoundary: \"\"\n    }\n  ];\n  parentBoundaryParse = \"\";\n  currentBoundaryParse = \"global\";\n  boundaryParseStack = [\"\"];\n  rels = [];\n  boundaryParseStack = [\"\"];\n  title = \"\";\n  wrapEnabled = false;\n  c4ShapeInRow = 4;\n  c4BoundaryInRow = 2;\n}, \"clear\");\nvar LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25\n};\nvar ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1\n};\nvar PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2\n};\nvar setTitle = /* @__PURE__ */ __name(function(txt) {\n  let sanitizedText = sanitizeText(txt, getConfig());\n  title = sanitizedText;\n}, \"setTitle\");\nvar c4Db_default = {\n  addPersonOrSystem,\n  addPersonOrSystemBoundary,\n  addContainer,\n  addContainerBoundary,\n  addComponent,\n  addDeploymentNode,\n  popBoundaryParseStack,\n  addRel,\n  updateElStyle,\n  updateRelStyle,\n  updateLayoutConfig,\n  autoWrap,\n  setWrap,\n  getC4ShapeArray,\n  getC4Shape,\n  getC4ShapeKeys,\n  getBoundaries,\n  getBoundarys,\n  getCurrentBoundaryParse,\n  getParentBoundaryParse,\n  getRels,\n  getTitle,\n  getC4Type,\n  getC4ShapeInRow,\n  getC4BoundaryInRow,\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getConfig: /* @__PURE__ */ __name(() => getConfig().c4, \"getConfig\"),\n  clear,\n  LINETYPE,\n  ARROWTYPE,\n  PLACEMENT,\n  setTitle,\n  setC4Type\n  // apply,\n};\n\n// src/diagrams/c4/c4Renderer.js\nimport { select } from \"d3\";\n\n// src/diagrams/c4/svgDraw.js\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nvar drawRect2 = /* @__PURE__ */ __name(function(elem, rectData) {\n  return drawRect(elem, rectData);\n}, \"drawRect\");\nvar drawImage = /* @__PURE__ */ __name(function(elem, width, height, x, y, link) {\n  const imageElem = elem.append(\"image\");\n  imageElem.attr(\"width\", width);\n  imageElem.attr(\"height\", height);\n  imageElem.attr(\"x\", x);\n  imageElem.attr(\"y\", y);\n  let sanitizedLink = link.startsWith(\"data:image/png;base64\") ? link : sanitizeUrl(link);\n  imageElem.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawRels = /* @__PURE__ */ __name((elem, rels2, conf2) => {\n  const relsElem = elem.append(\"g\");\n  let i = 0;\n  for (let rel of rels2) {\n    let textColor = rel.textColor ? rel.textColor : \"#444444\";\n    let strokeColor = rel.lineColor ? rel.lineColor : \"#444444\";\n    let offsetX = rel.offsetX ? parseInt(rel.offsetX) : 0;\n    let offsetY = rel.offsetY ? parseInt(rel.offsetY) : 0;\n    let url = \"\";\n    if (i === 0) {\n      let line = relsElem.append(\"line\");\n      line.attr(\"x1\", rel.startPoint.x);\n      line.attr(\"y1\", rel.startPoint.y);\n      line.attr(\"x2\", rel.endPoint.x);\n      line.attr(\"y2\", rel.endPoint.y);\n      line.attr(\"stroke-width\", \"1\");\n      line.attr(\"stroke\", strokeColor);\n      line.style(\"fill\", \"none\");\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n      i = -1;\n    } else {\n      let line = relsElem.append(\"path\");\n      line.attr(\"fill\", \"none\").attr(\"stroke-width\", \"1\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,starty Qcontrolx,controly stopx,stopy \".replaceAll(\"startx\", rel.startPoint.x).replaceAll(\"starty\", rel.startPoint.y).replaceAll(\n          \"controlx\",\n          rel.startPoint.x + (rel.endPoint.x - rel.startPoint.x) / 2 - (rel.endPoint.x - rel.startPoint.x) / 4\n        ).replaceAll(\"controly\", rel.startPoint.y + (rel.endPoint.y - rel.startPoint.y) / 2).replaceAll(\"stopx\", rel.endPoint.x).replaceAll(\"stopy\", rel.endPoint.y)\n      );\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n    }\n    let messageConf = conf2.messageFont();\n    _drawTextCandidateFunc(conf2)(\n      rel.label.text,\n      relsElem,\n      Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n      Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + offsetY,\n      rel.label.width,\n      rel.label.height,\n      { fill: textColor },\n      messageConf\n    );\n    if (rel.techn && rel.techn.text !== \"\") {\n      messageConf = conf2.messageFont();\n      _drawTextCandidateFunc(conf2)(\n        \"[\" + rel.techn.text + \"]\",\n        relsElem,\n        Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n        Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + conf2.messageFontSize + 5 + offsetY,\n        Math.max(rel.label.width, rel.techn.width),\n        rel.techn.height,\n        { fill: textColor, \"font-style\": \"italic\" },\n        messageConf\n      );\n    }\n  }\n}, \"drawRels\");\nvar drawBoundary = /* @__PURE__ */ __name(function(elem, boundary, conf2) {\n  const boundaryElem = elem.append(\"g\");\n  let fillColor = boundary.bgColor ? boundary.bgColor : \"none\";\n  let strokeColor = boundary.borderColor ? boundary.borderColor : \"#444444\";\n  let fontColor = boundary.fontColor ? boundary.fontColor : \"black\";\n  let attrsValue = { \"stroke-width\": 1, \"stroke-dasharray\": \"7.0,7.0\" };\n  if (boundary.nodeType) {\n    attrsValue = { \"stroke-width\": 1 };\n  }\n  let rectData = {\n    x: boundary.x,\n    y: boundary.y,\n    fill: fillColor,\n    stroke: strokeColor,\n    width: boundary.width,\n    height: boundary.height,\n    rx: 2.5,\n    ry: 2.5,\n    attrs: attrsValue\n  };\n  drawRect2(boundaryElem, rectData);\n  let boundaryConf = conf2.boundaryFont();\n  boundaryConf.fontWeight = \"bold\";\n  boundaryConf.fontSize = boundaryConf.fontSize + 2;\n  boundaryConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    boundary.label.text,\n    boundaryElem,\n    boundary.x,\n    boundary.y + boundary.label.Y,\n    boundary.width,\n    boundary.height,\n    { fill: \"#444444\" },\n    boundaryConf\n  );\n  if (boundary.type && boundary.type.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.type.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.type.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n  if (boundary.descr && boundary.descr.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontSize = boundaryConf.fontSize - 2;\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.descr.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.descr.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n}, \"drawBoundary\");\nvar drawC4Shape = /* @__PURE__ */ __name(function(elem, c4Shape, conf2) {\n  let fillColor = c4Shape.bgColor ? c4Shape.bgColor : conf2[c4Shape.typeC4Shape.text + \"_bg_color\"];\n  let strokeColor = c4Shape.borderColor ? c4Shape.borderColor : conf2[c4Shape.typeC4Shape.text + \"_border_color\"];\n  let fontColor = c4Shape.fontColor ? c4Shape.fontColor : \"#FFFFFF\";\n  let personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n      break;\n    case \"external_person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=\";\n      break;\n  }\n  const c4ShapeElem = elem.append(\"g\");\n  c4ShapeElem.attr(\"class\", \"person-man\");\n  const rect = getNoteRect();\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n    case \"system\":\n    case \"external_system\":\n    case \"container\":\n    case \"external_container\":\n    case \"component\":\n    case \"external_component\":\n      rect.x = c4Shape.x;\n      rect.y = c4Shape.y;\n      rect.fill = fillColor;\n      rect.width = c4Shape.width;\n      rect.height = c4Shape.height;\n      rect.stroke = strokeColor;\n      rect.rx = 2.5;\n      rect.ry = 2.5;\n      rect.attrs = { \"stroke-width\": 0.5 };\n      drawRect2(c4ShapeElem, rect);\n      break;\n    case \"system_db\":\n    case \"external_system_db\":\n    case \"container_db\":\n    case \"external_container_db\":\n    case \"component_db\":\n    case \"external_component_db\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2).replaceAll(\"height\", c4Shape.height)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2)\n      );\n      break;\n    case \"system_queue\":\n    case \"external_system_queue\":\n    case \"container_queue\":\n    case \"external_container_queue\":\n    case \"component_queue\":\n    case \"external_component_queue\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"width\", c4Shape.width).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half\".replaceAll(\"startx\", c4Shape.x + c4Shape.width).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      break;\n  }\n  let c4ShapeFontConf = getC4ShapeFont(conf2, c4Shape.typeC4Shape.text);\n  c4ShapeElem.append(\"text\").attr(\"fill\", fontColor).attr(\"font-family\", c4ShapeFontConf.fontFamily).attr(\"font-size\", c4ShapeFontConf.fontSize - 2).attr(\"font-style\", \"italic\").attr(\"lengthAdjust\", \"spacing\").attr(\"textLength\", c4Shape.typeC4Shape.width).attr(\"x\", c4Shape.x + c4Shape.width / 2 - c4Shape.typeC4Shape.width / 2).attr(\"y\", c4Shape.y + c4Shape.typeC4Shape.Y).text(\"<<\" + c4Shape.typeC4Shape.text + \">>\");\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n      drawImage(\n        c4ShapeElem,\n        48,\n        48,\n        c4Shape.x + c4Shape.width / 2 - 24,\n        c4Shape.y + c4Shape.image.Y,\n        personImg\n      );\n      break;\n  }\n  let textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontWeight = \"bold\";\n  textFontConf.fontSize = textFontConf.fontSize + 2;\n  textFontConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    c4Shape.label.text,\n    c4ShapeElem,\n    c4Shape.x,\n    c4Shape.y + c4Shape.label.Y,\n    c4Shape.width,\n    c4Shape.height,\n    { fill: fontColor },\n    textFontConf\n  );\n  textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontColor = fontColor;\n  if (c4Shape.techn && c4Shape.techn?.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.techn.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.techn.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  } else if (c4Shape.type && c4Shape.type.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.type.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.type.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  }\n  if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n    textFontConf = conf2.personFont();\n    textFontConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.descr.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.descr.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor },\n      textFontConf\n    );\n  }\n  return c4Shape.height;\n}, \"drawC4Shape\");\nvar insertDatabaseIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"database\").attr(\"fill-rule\", \"evenodd\").attr(\"clip-rule\", \"evenodd\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z\"\n  );\n}, \"insertDatabaseIcon\");\nvar insertComputerIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"computer\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z\"\n  );\n}, \"insertComputerIcon\");\nvar insertClockIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"clock\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z\"\n  );\n}, \"insertClockIcon\");\nvar insertArrowHead = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 9).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0 0 L 10 5 L 0 10 z\");\n}, \"insertArrowHead\");\nvar insertArrowEnd = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowend\").attr(\"refX\", 1).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 10 0 L 0 5 L 10 10 z\");\n}, \"insertArrowEnd\");\nvar insertArrowFilledHead = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"filled-head\").attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertArrowFilledHead\");\nvar insertDynamicNumber = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"sequencenumber\").attr(\"refX\", 15).attr(\"refY\", 15).attr(\"markerWidth\", 60).attr(\"markerHeight\", 40).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", 15).attr(\"cy\", 15).attr(\"r\", 6);\n}, \"insertDynamicNumber\");\nvar insertArrowCrossHead = /* @__PURE__ */ __name(function(elem) {\n  const defs = elem.append(\"defs\");\n  const marker = defs.append(\"marker\").attr(\"id\", \"crosshead\").attr(\"markerWidth\", 15).attr(\"markerHeight\", 8).attr(\"orient\", \"auto\").attr(\"refX\", 16).attr(\"refY\", 4);\n  marker.append(\"path\").attr(\"fill\", \"black\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 9,2 V 6 L16,4 Z\");\n  marker.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 0,1 L 6,7 M 6,1 L 0,7\");\n}, \"insertArrowCrossHead\");\nvar getC4ShapeFont = /* @__PURE__ */ __name((cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n}, \"getC4ShapeFont\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { fontSize, fontFamily, fontWeight } = conf2;\n    const lines = content.split(common_default.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * fontSize - fontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).style(\"text-anchor\", \"middle\").attr(\"dominant-baseline\", \"middle\").style(\"font-size\", fontSize).style(\"font-weight\", fontWeight).style(\"font-family\", fontFamily);\n      text.append(\"tspan\").attr(\"dy\", dy).text(lines[i]).attr(\"alignment-baseline\", \"mathematical\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  __name(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  __name(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawBoundary,\n  drawC4Shape,\n  drawRels,\n  drawImage,\n  insertArrowHead,\n  insertArrowEnd,\n  insertArrowFilledHead,\n  insertDynamicNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon\n};\n\n// src/diagrams/c4/c4Renderer.js\nvar globalBoundaryMaxX = 0;\nvar globalBoundaryMaxY = 0;\nvar c4ShapeInRow2 = 4;\nvar c4BoundaryInRow2 = 2;\nparser.yy = c4Db_default;\nvar conf = {};\nvar Bounds = class {\n  static {\n    __name(this, \"Bounds\");\n  }\n  constructor(diagObj) {\n    this.name = \"\";\n    this.data = {};\n    this.data.startx = void 0;\n    this.data.stopx = void 0;\n    this.data.starty = void 0;\n    this.data.stopy = void 0;\n    this.data.widthLimit = void 0;\n    this.nextData = {};\n    this.nextData.startx = void 0;\n    this.nextData.stopx = void 0;\n    this.nextData.starty = void 0;\n    this.nextData.stopy = void 0;\n    this.nextData.cnt = 0;\n    setConf(diagObj.db.getConfig());\n  }\n  setData(startx, stopx, starty, stopy) {\n    this.nextData.startx = this.data.startx = startx;\n    this.nextData.stopx = this.data.stopx = stopx;\n    this.nextData.starty = this.data.starty = starty;\n    this.nextData.stopy = this.data.stopy = stopy;\n  }\n  updateVal(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }\n  insert(c4Shape) {\n    this.nextData.cnt = this.nextData.cnt + 1;\n    let _startx = this.nextData.startx === this.nextData.stopx ? this.nextData.stopx + c4Shape.margin : this.nextData.stopx + c4Shape.margin * 2;\n    let _stopx = _startx + c4Shape.width;\n    let _starty = this.nextData.starty + c4Shape.margin * 2;\n    let _stopy = _starty + c4Shape.height;\n    if (_startx >= this.data.widthLimit || _stopx >= this.data.widthLimit || this.nextData.cnt > c4ShapeInRow2) {\n      _startx = this.nextData.startx + c4Shape.margin + conf.nextLinePaddingX;\n      _starty = this.nextData.stopy + c4Shape.margin * 2;\n      this.nextData.stopx = _stopx = _startx + c4Shape.width;\n      this.nextData.starty = this.nextData.stopy;\n      this.nextData.stopy = _stopy = _starty + c4Shape.height;\n      this.nextData.cnt = 1;\n    }\n    c4Shape.x = _startx;\n    c4Shape.y = _starty;\n    this.updateVal(this.data, \"startx\", _startx, Math.min);\n    this.updateVal(this.data, \"starty\", _starty, Math.min);\n    this.updateVal(this.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.data, \"stopy\", _stopy, Math.max);\n    this.updateVal(this.nextData, \"startx\", _startx, Math.min);\n    this.updateVal(this.nextData, \"starty\", _starty, Math.min);\n    this.updateVal(this.nextData, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.nextData, \"stopy\", _stopy, Math.max);\n  }\n  init(diagObj) {\n    this.name = \"\";\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      widthLimit: void 0\n    };\n    this.nextData = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      cnt: 0\n    };\n    setConf(diagObj.db.getConfig());\n  }\n  bumpLastMargin(margin) {\n    this.data.stopx += margin;\n    this.data.stopy += margin;\n  }\n};\nvar setConf = /* @__PURE__ */ __name(function(cnf) {\n  assignWithDepth_default(conf, cnf);\n  if (cnf.fontFamily) {\n    conf.personFontFamily = conf.systemFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.personFontSize = conf.systemFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.personFontWeight = conf.systemFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n}, \"setConf\");\nvar c4ShapeFont = /* @__PURE__ */ __name((cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n}, \"c4ShapeFont\");\nvar boundaryFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.boundaryFontFamily,\n    fontSize: cnf.boundaryFontSize,\n    fontWeight: cnf.boundaryFontWeight\n  };\n}, \"boundaryFont\");\nvar messageFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight\n  };\n}, \"messageFont\");\nfunction calcC4ShapeTextWH(textType, c4Shape, c4ShapeTextWrap, textConf, textLimitWidth) {\n  if (!c4Shape[textType].width) {\n    if (c4ShapeTextWrap) {\n      c4Shape[textType].text = wrapLabel(c4Shape[textType].text, textLimitWidth, textConf);\n      c4Shape[textType].textLines = c4Shape[textType].text.split(common_default.lineBreakRegex).length;\n      c4Shape[textType].width = textLimitWidth;\n      c4Shape[textType].height = calculateTextHeight(c4Shape[textType].text, textConf);\n    } else {\n      let lines = c4Shape[textType].text.split(common_default.lineBreakRegex);\n      c4Shape[textType].textLines = lines.length;\n      let lineHeight = 0;\n      c4Shape[textType].height = 0;\n      c4Shape[textType].width = 0;\n      for (const line of lines) {\n        c4Shape[textType].width = Math.max(\n          calculateTextWidth(line, textConf),\n          c4Shape[textType].width\n        );\n        lineHeight = calculateTextHeight(line, textConf);\n        c4Shape[textType].height = c4Shape[textType].height + lineHeight;\n      }\n    }\n  }\n}\n__name(calcC4ShapeTextWH, \"calcC4ShapeTextWH\");\nvar drawBoundary2 = /* @__PURE__ */ __name(function(diagram2, boundary, bounds) {\n  boundary.x = bounds.data.startx;\n  boundary.y = bounds.data.starty;\n  boundary.width = bounds.data.stopx - bounds.data.startx;\n  boundary.height = bounds.data.stopy - bounds.data.starty;\n  boundary.label.y = conf.c4ShapeMargin - 35;\n  let boundaryTextWrap = boundary.wrap && conf.wrap;\n  let boundaryLabelConf = boundaryFont(conf);\n  boundaryLabelConf.fontSize = boundaryLabelConf.fontSize + 2;\n  boundaryLabelConf.fontWeight = \"bold\";\n  let textLimitWidth = calculateTextWidth(boundary.label.text, boundaryLabelConf);\n  calcC4ShapeTextWH(\"label\", boundary, boundaryTextWrap, boundaryLabelConf, textLimitWidth);\n  svgDraw_default.drawBoundary(diagram2, boundary, conf);\n}, \"drawBoundary\");\nvar drawC4ShapeArray = /* @__PURE__ */ __name(function(currentBounds, diagram2, c4ShapeArray2, c4ShapeKeys) {\n  let Y = 0;\n  for (const c4ShapeKey of c4ShapeKeys) {\n    Y = 0;\n    const c4Shape = c4ShapeArray2[c4ShapeKey];\n    let c4ShapeTypeConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeTypeConf.fontSize = c4ShapeTypeConf.fontSize - 2;\n    c4Shape.typeC4Shape.width = calculateTextWidth(\n      \"\\xAB\" + c4Shape.typeC4Shape.text + \"\\xBB\",\n      c4ShapeTypeConf\n    );\n    c4Shape.typeC4Shape.height = c4ShapeTypeConf.fontSize + 2;\n    c4Shape.typeC4Shape.Y = conf.c4ShapePadding;\n    Y = c4Shape.typeC4Shape.Y + c4Shape.typeC4Shape.height - 4;\n    c4Shape.image = { width: 0, height: 0, Y: 0 };\n    switch (c4Shape.typeC4Shape.text) {\n      case \"person\":\n      case \"external_person\":\n        c4Shape.image.width = 48;\n        c4Shape.image.height = 48;\n        c4Shape.image.Y = Y;\n        Y = c4Shape.image.Y + c4Shape.image.height;\n        break;\n    }\n    if (c4Shape.sprite) {\n      c4Shape.image.width = 48;\n      c4Shape.image.height = 48;\n      c4Shape.image.Y = Y;\n      Y = c4Shape.image.Y + c4Shape.image.height;\n    }\n    let c4ShapeTextWrap = c4Shape.wrap && conf.wrap;\n    let textLimitWidth = conf.width - conf.c4ShapePadding * 2;\n    let c4ShapeLabelConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeLabelConf.fontSize = c4ShapeLabelConf.fontSize + 2;\n    c4ShapeLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\"label\", c4Shape, c4ShapeTextWrap, c4ShapeLabelConf, textLimitWidth);\n    c4Shape.label.Y = Y + 8;\n    Y = c4Shape.label.Y + c4Shape.label.height;\n    if (c4Shape.type && c4Shape.type.text !== \"\") {\n      c4Shape.type.text = \"[\" + c4Shape.type.text + \"]\";\n      let c4ShapeTypeConf2 = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"type\", c4Shape, c4ShapeTextWrap, c4ShapeTypeConf2, textLimitWidth);\n      c4Shape.type.Y = Y + 5;\n      Y = c4Shape.type.Y + c4Shape.type.height;\n    } else if (c4Shape.techn && c4Shape.techn.text !== \"\") {\n      c4Shape.techn.text = \"[\" + c4Shape.techn.text + \"]\";\n      let c4ShapeTechnConf = c4ShapeFont(conf, c4Shape.techn.text);\n      calcC4ShapeTextWH(\"techn\", c4Shape, c4ShapeTextWrap, c4ShapeTechnConf, textLimitWidth);\n      c4Shape.techn.Y = Y + 5;\n      Y = c4Shape.techn.Y + c4Shape.techn.height;\n    }\n    let rectHeight = Y;\n    let rectWidth = c4Shape.label.width;\n    if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n      let c4ShapeDescrConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"descr\", c4Shape, c4ShapeTextWrap, c4ShapeDescrConf, textLimitWidth);\n      c4Shape.descr.Y = Y + 20;\n      Y = c4Shape.descr.Y + c4Shape.descr.height;\n      rectWidth = Math.max(c4Shape.label.width, c4Shape.descr.width);\n      rectHeight = Y - c4Shape.descr.textLines * 5;\n    }\n    rectWidth = rectWidth + conf.c4ShapePadding;\n    c4Shape.width = Math.max(c4Shape.width || conf.width, rectWidth, conf.width);\n    c4Shape.height = Math.max(c4Shape.height || conf.height, rectHeight, conf.height);\n    c4Shape.margin = c4Shape.margin || conf.c4ShapeMargin;\n    currentBounds.insert(c4Shape);\n    svgDraw_default.drawC4Shape(diagram2, c4Shape, conf);\n  }\n  currentBounds.bumpLastMargin(conf.c4ShapeMargin);\n}, \"drawC4ShapeArray\");\nvar Point = class {\n  static {\n    __name(this, \"Point\");\n  }\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n};\nvar getIntersectPoint = /* @__PURE__ */ __name(function(fromNode, endPoint) {\n  let x1 = fromNode.x;\n  let y1 = fromNode.y;\n  let x2 = endPoint.x;\n  let y2 = endPoint.y;\n  let fromCenterX = x1 + fromNode.width / 2;\n  let fromCenterY = y1 + fromNode.height / 2;\n  let dx = Math.abs(x1 - x2);\n  let dy = Math.abs(y1 - y2);\n  let tanDYX = dy / dx;\n  let fromDYX = fromNode.height / fromNode.width;\n  let returnPoint = null;\n  if (y1 == y2 && x1 < x2) {\n    returnPoint = new Point(x1 + fromNode.width, fromCenterY);\n  } else if (y1 == y2 && x1 > x2) {\n    returnPoint = new Point(x1, fromCenterY);\n  } else if (x1 == x2 && y1 < y2) {\n    returnPoint = new Point(fromCenterX, y1 + fromNode.height);\n  } else if (x1 == x2 && y1 > y2) {\n    returnPoint = new Point(fromCenterX, y1);\n  }\n  if (x1 > x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX - dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX + dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY - tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(fromCenterX + fromNode.height / 2 * dx / dy, y1);\n    }\n  } else if (x1 > x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY - fromNode.width / 2 * tanDYX);\n    } else {\n      returnPoint = new Point(fromCenterX - fromNode.height / 2 * dx / dy, y1);\n    }\n  }\n  return returnPoint;\n}, \"getIntersectPoint\");\nvar getIntersectPoints = /* @__PURE__ */ __name(function(fromNode, endNode) {\n  let endIntersectPoint = { x: 0, y: 0 };\n  endIntersectPoint.x = endNode.x + endNode.width / 2;\n  endIntersectPoint.y = endNode.y + endNode.height / 2;\n  let startPoint = getIntersectPoint(fromNode, endIntersectPoint);\n  endIntersectPoint.x = fromNode.x + fromNode.width / 2;\n  endIntersectPoint.y = fromNode.y + fromNode.height / 2;\n  let endPoint = getIntersectPoint(endNode, endIntersectPoint);\n  return { startPoint, endPoint };\n}, \"getIntersectPoints\");\nvar drawRels2 = /* @__PURE__ */ __name(function(diagram2, rels2, getC4ShapeObj, diagObj) {\n  let i = 0;\n  for (let rel of rels2) {\n    i = i + 1;\n    let relTextWrap = rel.wrap && conf.wrap;\n    let relConf = messageFont(conf);\n    let diagramType = diagObj.db.getC4Type();\n    if (diagramType === \"C4Dynamic\") {\n      rel.label.text = i + \": \" + rel.label.text;\n    }\n    let textLimitWidth = calculateTextWidth(rel.label.text, relConf);\n    calcC4ShapeTextWH(\"label\", rel, relTextWrap, relConf, textLimitWidth);\n    if (rel.techn && rel.techn.text !== \"\") {\n      textLimitWidth = calculateTextWidth(rel.techn.text, relConf);\n      calcC4ShapeTextWH(\"techn\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    if (rel.descr && rel.descr.text !== \"\") {\n      textLimitWidth = calculateTextWidth(rel.descr.text, relConf);\n      calcC4ShapeTextWH(\"descr\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    let fromNode = getC4ShapeObj(rel.from);\n    let endNode = getC4ShapeObj(rel.to);\n    let points = getIntersectPoints(fromNode, endNode);\n    rel.startPoint = points.startPoint;\n    rel.endPoint = points.endPoint;\n  }\n  svgDraw_default.drawRels(diagram2, rels2, conf);\n}, \"drawRels\");\nfunction drawInsideBoundary(diagram2, parentBoundaryAlias, parentBounds, currentBoundaries, diagObj) {\n  let currentBounds = new Bounds(diagObj);\n  currentBounds.data.widthLimit = parentBounds.data.widthLimit / Math.min(c4BoundaryInRow2, currentBoundaries.length);\n  for (let [i, currentBoundary] of currentBoundaries.entries()) {\n    let Y = 0;\n    currentBoundary.image = { width: 0, height: 0, Y: 0 };\n    if (currentBoundary.sprite) {\n      currentBoundary.image.width = 48;\n      currentBoundary.image.height = 48;\n      currentBoundary.image.Y = Y;\n      Y = currentBoundary.image.Y + currentBoundary.image.height;\n    }\n    let currentBoundaryTextWrap = currentBoundary.wrap && conf.wrap;\n    let currentBoundaryLabelConf = boundaryFont(conf);\n    currentBoundaryLabelConf.fontSize = currentBoundaryLabelConf.fontSize + 2;\n    currentBoundaryLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\n      \"label\",\n      currentBoundary,\n      currentBoundaryTextWrap,\n      currentBoundaryLabelConf,\n      currentBounds.data.widthLimit\n    );\n    currentBoundary.label.Y = Y + 8;\n    Y = currentBoundary.label.Y + currentBoundary.label.height;\n    if (currentBoundary.type && currentBoundary.type.text !== \"\") {\n      currentBoundary.type.text = \"[\" + currentBoundary.type.text + \"]\";\n      let currentBoundaryTypeConf = boundaryFont(conf);\n      calcC4ShapeTextWH(\n        \"type\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryTypeConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.type.Y = Y + 5;\n      Y = currentBoundary.type.Y + currentBoundary.type.height;\n    }\n    if (currentBoundary.descr && currentBoundary.descr.text !== \"\") {\n      let currentBoundaryDescrConf = boundaryFont(conf);\n      currentBoundaryDescrConf.fontSize = currentBoundaryDescrConf.fontSize - 2;\n      calcC4ShapeTextWH(\n        \"descr\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryDescrConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.descr.Y = Y + 20;\n      Y = currentBoundary.descr.Y + currentBoundary.descr.height;\n    }\n    if (i == 0 || i % c4BoundaryInRow2 === 0) {\n      let _x = parentBounds.data.startx + conf.diagramMarginX;\n      let _y = parentBounds.data.stopy + conf.diagramMarginY + Y;\n      currentBounds.setData(_x, _x, _y, _y);\n    } else {\n      let _x = currentBounds.data.stopx !== currentBounds.data.startx ? currentBounds.data.stopx + conf.diagramMarginX : currentBounds.data.startx;\n      let _y = currentBounds.data.starty;\n      currentBounds.setData(_x, _x, _y, _y);\n    }\n    currentBounds.name = currentBoundary.alias;\n    let currentPersonOrSystemArray = diagObj.db.getC4ShapeArray(currentBoundary.alias);\n    let currentPersonOrSystemKeys = diagObj.db.getC4ShapeKeys(currentBoundary.alias);\n    if (currentPersonOrSystemKeys.length > 0) {\n      drawC4ShapeArray(\n        currentBounds,\n        diagram2,\n        currentPersonOrSystemArray,\n        currentPersonOrSystemKeys\n      );\n    }\n    parentBoundaryAlias = currentBoundary.alias;\n    let nextCurrentBoundaries = diagObj.db.getBoundarys(parentBoundaryAlias);\n    if (nextCurrentBoundaries.length > 0) {\n      drawInsideBoundary(\n        diagram2,\n        parentBoundaryAlias,\n        currentBounds,\n        nextCurrentBoundaries,\n        diagObj\n      );\n    }\n    if (currentBoundary.alias !== \"global\") {\n      drawBoundary2(diagram2, currentBoundary, currentBounds);\n    }\n    parentBounds.data.stopy = Math.max(\n      currentBounds.data.stopy + conf.c4ShapeMargin,\n      parentBounds.data.stopy\n    );\n    parentBounds.data.stopx = Math.max(\n      currentBounds.data.stopx + conf.c4ShapeMargin,\n      parentBounds.data.stopx\n    );\n    globalBoundaryMaxX = Math.max(globalBoundaryMaxX, parentBounds.data.stopx);\n    globalBoundaryMaxY = Math.max(globalBoundaryMaxY, parentBounds.data.stopy);\n  }\n}\n__name(drawInsideBoundary, \"drawInsideBoundary\");\nvar draw = /* @__PURE__ */ __name(function(_text, id, _version, diagObj) {\n  conf = getConfig().c4;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  let db = diagObj.db;\n  diagObj.db.setWrap(conf.wrap);\n  c4ShapeInRow2 = db.getC4ShapeInRow();\n  c4BoundaryInRow2 = db.getC4BoundaryInRow();\n  log.debug(`C:${JSON.stringify(conf, null, 2)}`);\n  const diagram2 = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : select(`[id=\"${id}\"]`);\n  svgDraw_default.insertComputerIcon(diagram2);\n  svgDraw_default.insertDatabaseIcon(diagram2);\n  svgDraw_default.insertClockIcon(diagram2);\n  let screenBounds = new Bounds(diagObj);\n  screenBounds.setData(\n    conf.diagramMarginX,\n    conf.diagramMarginX,\n    conf.diagramMarginY,\n    conf.diagramMarginY\n  );\n  screenBounds.data.widthLimit = screen.availWidth;\n  globalBoundaryMaxX = conf.diagramMarginX;\n  globalBoundaryMaxY = conf.diagramMarginY;\n  const title2 = diagObj.db.getTitle();\n  let currentBoundaries = diagObj.db.getBoundarys(\"\");\n  drawInsideBoundary(diagram2, \"\", screenBounds, currentBoundaries, diagObj);\n  svgDraw_default.insertArrowHead(diagram2);\n  svgDraw_default.insertArrowEnd(diagram2);\n  svgDraw_default.insertArrowCrossHead(diagram2);\n  svgDraw_default.insertArrowFilledHead(diagram2);\n  drawRels2(diagram2, diagObj.db.getRels(), diagObj.db.getC4Shape, diagObj);\n  screenBounds.data.stopx = globalBoundaryMaxX;\n  screenBounds.data.stopy = globalBoundaryMaxY;\n  const box = screenBounds.data;\n  let boxHeight = box.stopy - box.starty;\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  let boxWidth = box.stopx - box.startx;\n  const width = boxWidth + 2 * conf.diagramMarginX;\n  if (title2) {\n    diagram2.append(\"text\").text(title2).attr(\"x\", (box.stopx - box.startx) / 2 - 4 * conf.diagramMarginX).attr(\"y\", box.starty + conf.diagramMarginY);\n  }\n  configureSvgSize(diagram2, height, width, conf.useMaxWidth);\n  const extraVertForTitle = title2 ? 60 : 0;\n  diagram2.attr(\n    \"viewBox\",\n    box.startx - conf.diagramMarginX + \" -\" + (conf.diagramMarginY + extraVertForTitle) + \" \" + width + \" \" + (height + extraVertForTitle)\n  );\n  log.debug(`models:`, box);\n}, \"draw\");\nvar c4Renderer_default = {\n  drawPersonOrSystemArray: drawC4ShapeArray,\n  drawBoundary: drawBoundary2,\n  setConf,\n  draw\n};\n\n// src/diagrams/c4/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `.person {\n    stroke: ${options.personBorder};\n    fill: ${options.personBkg};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/c4/c4Diagram.ts\nvar diagram = {\n  parser: c4Diagram_default,\n  db: c4Db_default,\n  renderer: c4Renderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name(({ c4, wrap }) => {\n    c4Renderer_default.setConf(c4);\n    c4Db_default.setWrap(wrap);\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "names": ["drawRect", "__name", "element", "rectData", "rectElement", "append", "attr", "x", "y", "fill", "stroke", "width", "height", "name", "rx", "ry", "attrs", "attrKey", "class", "drawBackgroundRect", "bounds", "startx", "starty", "stopx", "stopy", "lower", "drawText", "textData", "nText", "text", "replace", "lineBreakRegex", "textElem", "style", "anchor", "tspan", "textMargin", "drawImage", "elem", "link", "imageElement", "sanitizedLink", "sanitizeUrl", "drawEmbeddedImage", "getNoteRect", "getTextObj", "parser", "o", "k", "v", "o2", "l", "length", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "$Vr", "$Vs", "$Vt", "$Vu", "$Vv", "$Vw", "$Vx", "$Vy", "$Vz", "$VA", "$VB", "$VC", "$VD", "$VE", "$VF", "$VG", "$VH", "$VI", "$VJ", "$VK", "$VL", "$VM", "$VN", "$VO", "$VP", "$VQ", "$VR", "$VS", "parser2", "trace", "yy", "symbols_", "terminals_", "productions_", "performAction", "yytext", "yyleng", "y<PERSON><PERSON>o", "yystate", "$$", "_$", "$0", "setDirection", "setC4Type", "setTitle", "substring", "this", "$", "setAccDescription", "trim", "splice", "addPersonOrSystemBoundary", "addContainerBoundary", "addDeploymentNode", "popBoundaryParseStack", "addPersonOrSystem", "addContainer", "addComponent", "addRel", "updateElStyle", "updateRelStyle", "updateLayoutConfig", "unshift", "kv", "table", "defaultActions", "parseError", "str", "hash", "recoverable", "error", "Error", "parse", "input", "self", "stack", "tstack", "vstack", "lstack", "recovering", "args", "slice", "call", "arguments", "lexer2", "Object", "create", "lexer", "sharedState", "prototype", "hasOwnProperty", "setInput", "yylloc", "yyloc", "push", "ranges", "options", "lex", "token", "pop", "Array", "getPrototypeOf", "n", "symbol", "preErrorSymbol", "state", "action", "r", "p", "len", "newState", "expected", "yyval", "errStr", "showPosition", "join", "match", "line", "loc", "first_line", "last_line", "first_column", "last_column", "range", "apply", "concat", "EOF", "_input", "_more", "_backtrack", "done", "matched", "conditionStack", "offset", "ch", "unput", "lines", "split", "substr", "oldLines", "more", "reject", "backtrack_lexer", "less", "pastInput", "past", "upcomingInput", "next", "pre", "c2", "test_match", "indexed_rule", "backup", "matches", "tempMatch", "index", "rules", "_currentRules", "i", "flex", "begin", "condition", "popState", "conditions", "topState", "Math", "abs", "pushState", "stateStackSize", "yy_", "$avoiding_name_collisions", "YY_START", "c", "<PERSON><PERSON><PERSON>", "c4Type", "c4Diagram_default", "c4ShapeArray", "boundaryParseStack", "currentBoundaryParse", "parentBoundaryParse", "boundaries", "alias", "label", "type", "tags", "parentBoundary", "rels", "title", "wrapEnabled", "c4ShapeInRow", "c4BoundaryInRow", "getC4Type", "c4TypeParam", "sanitizedText", "sanitizeText", "getConfig", "from", "to", "techn", "descr", "sprite", "rel", "old", "find", "rel2", "key", "value", "entries", "wrap", "autoWrap", "typeC4Shape", "personOrSystem", "personOrSystem2", "container", "container2", "component", "component2", "boundary", "boundary2", "nodeType", "elementName", "bgColor", "fontColor", "borderColor", "shadowing", "shape", "legendText", "legendSprite", "textColor", "lineColor", "offsetX", "offsetY", "parseInt", "c4ShapeInRowParam", "c4BoundaryInRowParam", "c4ShapeInRowValue", "c4BoundaryInRowValue", "values", "getC4ShapeInRow", "getC4BoundaryInRow", "getCurrentBoundaryParse", "getParentBoundaryParse", "getC4ShapeArray", "filter", "getC4Shape", "getC4ShapeKeys", "keys", "getBoundaries", "getBoundarys", "getRels", "getTitle", "setWrap", "wrapSetting", "clear", "txt", "c4Db_default", "setAccTitle", "getAccTitle", "getAccDescription", "c4", "LINETYPE", "SOLID", "DOTTED", "NOTE", "SOLID_CROSS", "DOTTED_CROSS", "SOLID_OPEN", "DOTTED_OPEN", "LOOP_START", "LOOP_END", "ALT_START", "ALT_ELSE", "ALT_END", "OPT_START", "OPT_END", "ACTIVE_START", "ACTIVE_END", "PAR_START", "PAR_AND", "PAR_END", "RECT_START", "RECT_END", "SOLID_POINT", "DOTTED_POINT", "ARROWTYPE", "FILLED", "OPEN", "PLACEMENT", "LEFTOF", "RIGHTOF", "OVER", "drawRect2", "imageElem", "startsWith", "drawRels", "rels2", "conf2", "relsElem", "strokeColor", "url", "startPoint", "endPoint", "replaceAll", "messageConf", "messageFont", "_drawTextCandidateFunc", "min", "messageFontSize", "max", "drawBoundary", "boundaryElem", "fillColor", "attrsValue", "boundaryConf", "boundaryFont", "fontWeight", "fontSize", "Y", "drawC4Shape", "c4Shape", "personImg", "c4ShapeElem", "rect", "c4ShapeFontConf", "getC4ShapeFont", "fontFamily", "image", "textFontConf", "personFont", "insertDatabaseIcon", "insertComputerIcon", "insertClockIcon", "insertArrowHead", "insertArrowEnd", "insertArrowFilledHead", "insertDynamicNumber", "insertArrowCrossHead", "marker", "cnf", "byText", "content", "g", "textAttrs", "_setTextAttrs", "byTspan", "common_default", "dy", "byFo", "s", "toText", "fromTextAttrsDict", "textPlacement", "svgDraw_default", "globalBoundaryMaxX", "globalBoundaryMaxY", "c4ShapeInRow2", "c4BoundaryInRow2", "conf", "Bounds", "constructor", "diagObj", "data", "widthLimit", "nextData", "cnt", "setConf", "db", "setData", "updateVal", "obj", "val", "fun", "insert", "_startx", "margin", "_stopx", "_starty", "_stopy", "nextLinePaddingX", "init", "bumpLastMargin", "assignWithDepth_default", "personFontFamily", "systemFontFamily", "messageFontFamily", "personFontSize", "systemFontSize", "personFontWeight", "systemFontWeight", "messageFontWeight", "c4ShapeFont", "boundaryFontFamily", "boundaryFontSize", "boundaryFontWeight", "calcC4ShapeTextWH", "textType", "c4ShapeTextWrap", "textConf", "textLimitWidth", "wrapLabel", "textLines", "calculateTextHeight", "lineHeight", "calculateTextWidth", "drawBoundary2", "diagram2", "c4<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boundaryTextWrap", "boundaryLabelConf", "drawC4ShapeArray", "currentBounds", "c4ShapeArray2", "c4Shape<PERSON>eys", "c4Shape<PERSON>ey", "c4ShapeTypeConf", "c4ShapePadding", "c4ShapeLabelConf", "rectHeight", "rectWidth", "Point", "getIntersectPoint", "fromNode", "x1", "y1", "x2", "y2", "fromCenterX", "fromCenterY", "dx", "tanDYX", "fromDYX", "returnPoint", "getIntersectPoints", "endNode", "endIntersectPoint", "drawRels2", "getC4ShapeObj", "relTextWrap", "rel<PERSON>onf", "points", "drawInsideBoundary", "parentBoundaryAlias", "parentBounds", "currentBoundaries", "currentBoundary", "currentBoundaryTextWrap", "currentBoundaryLabelConf", "currentBoundaryDescrConf", "_x", "diagramMarginX", "_y", "diagramMarginY", "currentPersonOrSystemArray", "currentPersonOrSystemKeys", "nextCurrentBoundaries", "c4Renderer_default", "drawPersonOrSystemArray", "draw", "_text", "id", "_version", "securityLevel", "sandboxElement", "select", "root", "nodes", "contentDocument", "body", "log", "debug", "JSON", "stringify", "screenBounds", "screen", "availWidth", "title2", "box", "configureSvgSize", "useMaxWidth", "extraVertForTitle", "diagram", "renderer", "styles", "personBorder", "personBkg", "_ref"], "sourceRoot": ""}