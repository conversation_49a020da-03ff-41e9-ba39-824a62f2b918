{"version": 3, "file": "static/js/191.834610a0.chunk.js", "mappings": "sIA8RIA,E,yHA7PAC,EAAU,CAAC,EACXC,GAAsBC,EAAAA,EAAAA,KAAO,CAACC,EAAKC,KACrCJ,EAAQG,GAAOC,CAAG,GACjB,OACCC,GAAsBH,EAAAA,EAAAA,KAAQI,GAAMN,EAAQM,IAAI,OAChDC,GAAuBL,EAAAA,EAAAA,KAAO,IAAMM,OAAOD,KAAKP,IAAU,QAC1DS,GAAuBP,EAAAA,EAAAA,KAAO,IAAMK,IAAOG,QAAQ,QACnDC,EAAmB,CACrBN,MACAJ,MACAM,OACAE,QAIEG,GAAiCV,EAAAA,EAAAA,KAAQW,GAAMA,EAAEC,OAAO,UAAUC,KAAK,QAAS,eAAeA,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAMC,UAAUH,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAME,SAAUH,EAAAA,EAAAA,MAAYC,MAAMC,UAAUH,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAME,SAAUH,EAAAA,EAAAA,MAAYC,MAAMC,WAAW,kBAC5QE,GAA8BlB,EAAAA,EAAAA,KAAQW,GAAMA,EAAEC,OAAO,QAAQO,MAAM,SAAU,QAAQA,MAAM,mBAAoB,KAAKN,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAMK,YAAYP,KAAK,QAAS,WAAWA,KAAK,KAAqC,GAA/BC,EAAAA,EAAAA,MAAYC,MAAMK,YAAgBP,KAAK,KAAM,GAAGA,KAAK,KAAM,IAAI,eAChQQ,GAAkCrB,EAAAA,EAAAA,KAAO,CAACW,EAAGW,KAC/C,MAAMP,EAAQJ,EAAEC,OAAO,QAAQC,KAAK,IAAK,GAAIC,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAMK,WAAa,GAAIN,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,aAAaC,EAAAA,EAAAA,MAAYC,MAAMQ,UAAUV,KAAK,QAAS,eAAeW,KAAKF,EAASG,IACnOC,EAAWX,EAAMY,OAAOC,UAE9B,OADAjB,EAAEkB,OAAO,OAAQ,gBAAgBhB,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,QAASa,EAASI,MAAQ,GAAIhB,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,SAAUa,EAASK,OAAS,GAAIjB,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAMiB,QAC/PjB,CAAK,GACX,mBACCkB,GAAiCjC,EAAAA,EAAAA,KAAO,CAACW,EAAGW,KAC9C,MAAMY,GAA2BlC,EAAAA,EAAAA,KAAO,SAASmC,EAAQC,EAAKC,GAC5D,MAAMC,EAAQH,EAAOvB,OAAO,SAASC,KAAK,IAAK,GAAIC,EAAAA,EAAAA,MAAYC,MAAME,SAASO,KAAKY,GAC9EC,GACHC,EAAMzB,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAMK,WAEvC,GAAG,YAEGmB,EADQ5B,EAAEC,OAAO,QAAQC,KAAK,IAAK,GAAIC,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAMK,WAAa,KAAMN,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,aAAaC,EAAAA,EAAAA,MAAYC,MAAMQ,UAAUV,KAAK,QAAS,eAAeW,KAAKF,EAASkB,aAAa,IACjOb,OAAOC,UACxBa,EAAcF,EAASR,OACvBW,EAAc/B,EAAEC,OAAO,QAAQC,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KACxE,IACA4B,EAA0C,IAA5B3B,EAAAA,EAAAA,MAAYC,MAAME,SAAgBH,EAAAA,EAAAA,MAAYC,MAAM4B,eAAgB7B,EAAAA,EAAAA,MAAYC,MAAMK,YACpGP,KAAK,QAAS,qBAChB,IAAI+B,GAAU,EACVC,GAAW,EACfvB,EAASkB,aAAaM,SAAQ,SAASC,GAChCH,IACHV,EAASQ,EAAaK,EAAOF,GAC7BA,GAAW,GAEbD,GAAU,CACZ,IACA,MAAMI,EAAYrC,EAAEC,OAAO,QAAQC,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAME,QAAUwB,GAAc3B,EAAAA,EAAAA,MAAYC,MAAM4B,cAAgB,GAAG9B,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAME,QAAUwB,GAAc3B,EAAAA,EAAAA,MAAYC,MAAM4B,cAAgB,GAAG9B,KAAK,QAAS,iBACrQoC,EAAWP,EAAYf,OAAOC,UAC9BE,EAAQoB,KAAKC,IAAIF,EAASnB,MAAOS,EAAST,OAGhD,OAFAkB,EAAUnC,KAAK,KAAMiB,EAAQ,GAAIhB,EAAAA,EAAAA,MAAYC,MAAME,SACnDN,EAAEkB,OAAO,OAAQ,gBAAgBhB,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,QAASiB,EAAQ,GAAIhB,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,SAAUoC,EAASlB,OAASU,EAAc,GAAI3B,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAMiB,QACpQrB,CAAC,GACP,kBACCyC,GAAiCpD,EAAAA,EAAAA,KAAO,CAACW,EAAGW,EAAU+B,KACxD,MAAMC,GAAMxC,EAAAA,EAAAA,MAAYC,MAAME,QACxBsC,EAAS,GAAIzC,EAAAA,EAAAA,MAAYC,MAAME,QAC/BuC,EAAS7C,EAAEgB,OAAOC,UAClB6B,EAAWD,EAAO1B,MAClB4B,EAAOF,EAAOG,EACdC,EAAQjD,EAAEC,OAAO,QAAQC,KAAK,IAAK,GAAGA,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAM8C,YAAYhD,KAAK,aAAaC,EAAAA,EAAAA,MAAYC,MAAMQ,UAAUV,KAAK,QAAS,eAAeW,KAAKF,EAASG,IAEvKqC,EADWF,EAAMjC,OAAOC,UACFE,MAAQyB,EACpC,IAIIQ,EAJAjC,EAAQoB,KAAKC,IAAIW,EAAYL,GAC7B3B,IAAU2B,IACZ3B,GAAgByB,GAGlB,MAAMS,EAAWrD,EAAEgB,OAAOC,UACtBN,EAAS2C,IAEbF,EAASL,EAAOJ,EACZQ,EAAaL,IACfM,GAAUN,EAAW3B,GAAS,EAAIwB,GAEhCJ,KAAKgB,IAAIR,EAAOM,EAASL,GAAKL,GAAOQ,EAAaL,IACpDM,EAASL,GAAQI,EAAaL,GAAY,GAE5C,MAAMU,EAAQ,GAAIrD,EAAAA,EAAAA,MAAYC,MAAMK,WAiBpC,OAhBAT,EAAEkB,OAAO,OAAQ,gBAAgBhB,KAAK,IAAKkD,GAAQlD,KAAK,IAAKsD,GAAOtD,KAAK,QAASwC,EAAS,eAAiB,YAAYxC,KAAK,QAASiB,GAAOjB,KAC3I,SACAmD,EAASjC,QAASjB,EAAAA,EAAAA,MAAYC,MAAMK,YAAaN,EAAAA,EAAAA,MAAYC,MAAM8C,WAAa,GAChFhD,KAAK,KAAM,KACb+C,EAAM/C,KAAK,IAAKkD,EAAST,GACrBQ,GAAcL,GAChBG,EAAM/C,KAAK,IAAK6C,GAAQ5B,EAAQyB,GAAU,EAAIO,EAAa,EAAIR,GAEjE3C,EAAEkB,OAAO,OAAQ,gBAAgBhB,KAAK,IAAKkD,GAAQlD,KACjD,KACAC,EAAAA,EAAAA,MAAYC,MAAM8C,YAAa/C,EAAAA,EAAAA,MAAYC,MAAMK,YAAaN,EAAAA,EAAAA,MAAYC,MAAME,SAChFJ,KAAK,QAASiB,GAAOjB,KAAK,SAAyC,GAA/BC,EAAAA,EAAAA,MAAYC,MAAMK,YAAgBP,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAMiB,QACrGrB,EAAEkB,OAAO,OAAQ,gBAAgBhB,KAAK,IAAKkD,GAAQlD,KACjD,KACAC,EAAAA,EAAAA,MAAYC,MAAM8C,YAAa/C,EAAAA,EAAAA,MAAYC,MAAMK,YAAaN,EAAAA,EAAAA,MAAYC,MAAME,SAChFJ,KAAK,QAASiB,GAAOjB,KAAK,SAAUmD,EAASjC,OAAS,EAAI,GAAIjB,EAAAA,EAAAA,MAAYC,MAAMK,YAAYP,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAMiB,QACpHrB,CAAC,GACP,kBACCyD,GAA+BpE,EAAAA,EAAAA,KAAQW,IACzCA,EAAEC,OAAO,UAAUC,KAAK,QAAS,mBAAmBA,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAMC,UAAWF,EAAAA,EAAAA,MAAYC,MAAMsD,aAAaxD,KACxH,MACAC,EAAAA,EAAAA,MAAYC,MAAME,SAAUH,EAAAA,EAAAA,MAAYC,MAAMC,UAAWF,EAAAA,EAAAA,MAAYC,MAAMsD,aAC3ExD,KACA,MACAC,EAAAA,EAAAA,MAAYC,MAAME,SAAUH,EAAAA,EAAAA,MAAYC,MAAMC,UAAWF,EAAAA,EAAAA,MAAYC,MAAMsD,aAEtE1D,EAAEC,OAAO,UAAUC,KAAK,QAAS,mBAAmBA,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAMC,UAAUH,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAME,SAAUH,EAAAA,EAAAA,MAAYC,MAAMC,SAAW,GAAGH,KAAK,MAAMC,EAAAA,EAAAA,MAAYC,MAAME,SAAUH,EAAAA,EAAAA,MAAYC,MAAMC,SAAW,KAC3O,gBACCsD,GAAoCtE,EAAAA,EAAAA,KAAO,CAACW,EAAGW,KACjD,IAAIQ,GAAQhB,EAAAA,EAAAA,MAAYC,MAAMwD,UAC1BxC,GAASjB,EAAAA,EAAAA,MAAYC,MAAMyD,WAC/B,GAAIlD,EAASmD,SAAU,CACrB,IAAIC,EAAM5C,EACVA,EAAQC,EACRA,EAAS2C,CACX,CACA,OAAO/D,EAAEC,OAAO,QAAQO,MAAM,SAAU,SAASA,MAAM,OAAQ,SAASN,KAAK,QAASiB,GAAOjB,KAAK,SAAUkB,GAAQlB,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAME,QAAQ,GAC5L,qBACC0D,GAAgC3E,EAAAA,EAAAA,KAAO,CAAC4E,EAAOjB,EAAGkB,EAAGlE,KACvD,IAAIS,EAAa,EACjB,MAAM0D,EAAWnE,EAAEC,OAAO,QAC1BkE,EAAS3D,MAAM,cAAe,SAC9B2D,EAASjE,KAAK,QAAS,YACvB,IAAIW,EAAOoD,EAAMG,QAAQ,QAAS,SAClCvD,EAAOA,EAAKuD,QAAQ,MAAO,SAC3B,MAAMC,EAAQxD,EAAKyD,MAAMC,EAAAA,GAAeC,gBACxC,IAAIC,EAAU,MAAOtE,EAAAA,EAAAA,MAAYC,MAAMsE,WACvC,IAAK,MAAMC,KAASN,EAAO,CACzB,MAAM5C,EAAMkD,EAAMC,OAClB,GAAInD,EAAI5B,OAAS,EAAG,CAClB,MAAMgF,EAAOV,EAASlE,OAAO,SAE7B,GADA4E,EAAKhE,KAAKY,GACM,IAAZgD,EAAe,CAEjBA,GADmBI,EAAK7D,OAAOC,UACTG,MACxB,CACAX,GAAcgE,EACdI,EAAK3E,KAAK,IAAK8C,GAAI7C,EAAAA,EAAAA,MAAYC,MAAMsE,YACrCG,EAAK3E,KAAK,IAAKgE,EAAIzD,EAAa,MAAON,EAAAA,EAAAA,MAAYC,MAAMsE,WAC3D,CACF,CACA,MAAO,CAAEI,UAAWX,EAASnD,OAAOC,UAAUE,MAAOV,aAAY,GAChE,iBACCsE,GAA2B1F,EAAAA,EAAAA,KAAO,CAACwB,EAAMb,KAC3CA,EAAEE,KAAK,QAAS,cAChB,MAAM8E,EAAOhF,EAAEC,OAAO,QAAQC,KAAK,IAAK,GAAGA,KAAK,KAAKC,EAAAA,EAAAA,MAAYC,MAAME,SACjE2E,EAAWjF,EAAEC,OAAO,MACpB,UAAE6E,EAAS,WAAErE,GAAeuD,EAAcnD,EAAM,EAAG,EAAGoE,GAG5D,OAFAD,EAAK9E,KAAK,SAAUO,EAAa,GAAIN,EAAAA,EAAAA,MAAYC,MAAMsE,YACvDM,EAAK9E,KAAK,QAAS4E,EAA2C,GAA/B3E,EAAAA,EAAAA,MAAYC,MAAMsE,YAC1CM,CAAI,GACV,YACCE,GAA4B7F,EAAAA,EAAAA,KAAO,SAAS8F,EAAMxE,GACpD,MAAMG,EAAKH,EAASG,GACdsE,EAAY,CAChBtE,KACAuE,MAAO1E,EAASG,GAChBK,MAAO,EACPC,OAAQ,GAEJpB,EAAImF,EAAKlF,OAAO,KAAKC,KAAK,KAAMY,GAAIZ,KAAK,QAAS,cAClC,UAAlBS,EAAS2E,MACXvF,EAAeC,GAEK,QAAlBW,EAAS2E,MACX7B,EAAazD,GAEO,SAAlBW,EAAS2E,MAAqC,SAAlB3E,EAAS2E,MACvC3B,EAAkB3D,EAAGW,GAED,SAAlBA,EAAS2E,MACXP,EAASpE,EAASqE,KAAKnE,KAAMb,GAET,YAAlBW,EAAS2E,MACX/E,EAAYP,GAEQ,YAAlBW,EAAS2E,MAAuD,IAAjC3E,EAASkB,aAAahC,QACvDa,EAAgBV,EAAGW,GAEC,YAAlBA,EAAS2E,MAAsB3E,EAASkB,aAAahC,OAAS,GAChEyB,EAAetB,EAAGW,GAEpB,MAAM4E,EAAWvF,EAAEgB,OAAOC,UAI1B,OAHAmE,EAAUjE,MAAQoE,EAASpE,MAAQ,GAAIhB,EAAAA,EAAAA,MAAYC,MAAME,QACzD8E,EAAUhE,OAASmE,EAASnE,OAAS,GAAIjB,EAAAA,EAAAA,MAAYC,MAAME,QAC3DR,EAAiBV,IAAI0B,EAAIsE,GAClBA,CACT,GAAG,aACCI,EAAY,EACZC,GAA2BpG,EAAAA,EAAAA,KAAO,SAAS8F,EAAMO,EAAMC,GACzD,MAAMC,GAAkCvG,EAAAA,EAAAA,KAAO,SAASiG,GACtD,OAAQA,GACN,KAAKO,EAAAA,GAAQC,aAAaC,YACxB,MAAO,cACT,KAAKF,EAAAA,GAAQC,aAAaE,UACxB,MAAO,YACT,KAAKH,EAAAA,GAAQC,aAAaG,YACxB,MAAO,cACT,KAAKJ,EAAAA,GAAQC,aAAaI,WACxB,MAAO,aAEb,GAAG,mBACHR,EAAKS,OAAST,EAAKS,OAAOC,QAAQC,IAAOC,OAAOC,MAAMF,EAAEnC,KACxD,MAAMsC,EAAWd,EAAKS,OAChBM,GAAeC,EAAAA,EAAAA,OAAO1D,GAAE,SAAS2D,GACrC,OAAOA,EAAE3D,CACX,IAAGkB,GAAE,SAASyC,GACZ,OAAOA,EAAEzC,CACX,IAAG0C,MAAMC,EAAAA,KACHC,EAAU3B,EAAKlF,OAAO,QAAQC,KAAK,IAAKuG,EAAaD,IAAWtG,KAAK,KAAM,OAASsF,GAAWtF,KAAK,QAAS,cACnH,IAAI6G,EAAM,GAUV,IATI5G,EAAAA,EAAAA,MAAYC,MAAM4G,sBACpBD,EAAME,OAAOC,SAASC,SAAW,KAAOF,OAAOC,SAASE,KAAOH,OAAOC,SAASG,SAAWJ,OAAOC,SAASI,OAC1GP,EAAMA,EAAI3C,QAAQ,MAAO,OACzB2C,EAAMA,EAAI3C,QAAQ,MAAO,QAE3B0C,EAAQ5G,KACN,aACA,OAAS6G,EAAM,IAAMnB,EAAgBC,EAAAA,GAAQC,aAAaI,YAAc,aAEnD,IAAnBP,EAAS1C,MAAkB,CAC7B,MAAMoC,EAAQF,EAAKlF,OAAO,KAAKC,KAAK,QAAS,eACvC,EAAE8C,EAAC,EAAEkB,GAAMqD,EAAAA,GAAcC,kBAAkB9B,EAAKS,QAChDsB,EAAOlD,EAAAA,GAAemD,QAAQ/B,EAAS1C,OAC7C,IAAInB,EAAc,EAClB,MAAM6F,EAAY,GAClB,IAAIC,EAAW,EACXC,EAAO,EACX,IAAK,IAAIC,EAAI,EAAGA,GAAKL,EAAK5H,OAAQiI,IAAK,CACrC,MAAM7E,EAAQoC,EAAMpF,OAAO,QAAQC,KAAK,cAAe,UAAUW,KAAK4G,EAAKK,IAAI5H,KAAK,IAAK8C,GAAG9C,KAAK,IAAKgE,EAAIpC,GACpGiG,EAAY9E,EAAMjC,OAAOC,UAI/B,GAHA2G,EAAWrF,KAAKC,IAAIoF,EAAUG,EAAU5G,OACxC0G,EAAOtF,KAAKyF,IAAIH,EAAME,EAAU/E,GAChCiF,EAAAA,GAAIC,KAAKH,EAAU/E,EAAGA,EAAGkB,EAAIpC,GACT,IAAhBA,EAAmB,CACrB,MAAMF,EAAWqB,EAAMjC,OAAOC,UAC9Ba,EAAcF,EAASR,OACvB6G,EAAAA,GAAIC,KAAK,eAAgBpG,EAAaoC,EACxC,CACAyD,EAAUQ,KAAKlF,EACjB,CACA,IAAImF,EAAYtG,EAAc2F,EAAK5H,OACnC,GAAI4H,EAAK5H,OAAS,EAAG,CACnB,MAAMwI,GAAaZ,EAAK5H,OAAS,GAAKiC,EAAc,GACpD6F,EAAUxF,SAAQ,CAACc,EAAO6E,IAAM7E,EAAM/C,KAAK,IAAKgE,EAAI4D,EAAIhG,EAAcuG,KACtED,EAAYtG,EAAc2F,EAAK5H,MACjC,CACA,MAAMyI,EAASjD,EAAMrE,OAAOC,UAC5BoE,EAAMnE,OAAO,OAAQ,gBAAgBhB,KAAK,QAAS,OAAOA,KAAK,IAAK8C,EAAI4E,EAAW,GAAIzH,EAAAA,EAAAA,MAAYC,MAAME,QAAU,GAAGJ,KAAK,IAAKgE,EAAIkE,EAAY,GAAIjI,EAAAA,EAAAA,MAAYC,MAAME,QAAU,EAAI,KAAKJ,KAAK,QAAS0H,GAAWzH,EAAAA,EAAAA,MAAYC,MAAME,SAASJ,KAAK,SAAUkI,GAAYjI,EAAAA,EAAAA,MAAYC,MAAME,SAC1R2H,EAAAA,GAAIC,KAAKI,EACX,CACA9C,GACF,GAAG,YAIC+C,EAAoB,CAAC,EACrBC,GAA0BnJ,EAAAA,EAAAA,KAAO,WACrC,GAAG,WACCoJ,GAAgCpJ,EAAAA,EAAAA,KAAO,SAAS8F,GAClDA,EAAKlF,OAAO,QAAQA,OAAO,UAAUC,KAAK,KAAM,iBAAiBA,KAAK,OAAQ,IAAIA,KAAK,OAAQ,GAAGA,KAAK,cAAe,IAAIA,KAAK,eAAgB,IAAIA,KAAK,SAAU,QAAQD,OAAO,QAAQC,KAAK,IAAK,4BACrM,GAAG,iBACCwI,GAAuBrJ,EAAAA,EAAAA,KAAO,SAASwB,EAAMC,EAAI6H,EAAUC,GAC7D1J,GAAOiB,EAAAA,EAAAA,MAAYC,MACnB,MAAMyI,GAAgB1I,EAAAA,EAAAA,MAAY0I,cAClC,IAAIC,EACkB,YAAlBD,IACFC,GAAiBC,EAAAA,EAAAA,KAAO,KAAOjI,IAEjC,MAAMkI,EAAyB,YAAlBH,GAA8BE,EAAAA,EAAAA,KAAOD,EAAeG,QAAQ,GAAGC,gBAAgBC,OAAQJ,EAAAA,EAAAA,KAAO,QACrGzF,EAAwB,YAAlBuF,EAA8BC,EAAeG,QAAQ,GAAGC,gBAAkBE,SACtFnB,EAAAA,GAAIoB,MAAM,qBAAuBxI,GACjC,MAAMyI,EAAWN,EAAKD,OAAO,QAAQjI,OACrC2H,EAAca,GACd,MAAMC,EAAUX,EAAQY,GAAGC,aAC3BC,EAAUH,EAASD,OAAU,GAAQ,EAAON,EAAM1F,EAAKsF,GACvD,MAAMtI,EAAUpB,EAAKoB,QACfgI,EAASgB,EAAStI,OAAOC,UACzBE,EAAQmH,EAAOnH,MAAkB,EAAVb,EACvBc,EAASkH,EAAOlH,OAAmB,EAAVd,EACzBqJ,EAAmB,KAARxI,GACjByI,EAAAA,EAAAA,IAAiBN,EAAUlI,EAAQuI,EAAUzK,EAAK2K,aAClDP,EAASpJ,KACP,UACA,GAAGoI,EAAOtF,EAAI9D,EAAKoB,YAAYgI,EAAOpE,EAAIhF,EAAKoB,WAAaa,EAAQ,IAAMC,EAE9E,GAAG,QACC0I,GAAgCzK,EAAAA,EAAAA,KAAQwB,GACnCA,EAAOA,EAAKhB,OAASX,EAAK6K,eAAiB,GACjD,iBACCL,GAA4BrK,EAAAA,EAAAA,KAAO,CAACiE,EAAKgG,EAAUxF,EAAUpB,EAAQsG,EAAMgB,EAAapB,KAC1F,MAAMqB,EAAQ,IAAIC,EAAAA,EAAe,CAC/BC,UAAU,EACVC,YAAY,IAEd,IAAItC,EACAuC,GAAc,EAClB,IAAKvC,EAAI,EAAGA,EAAIxE,EAAIzD,OAAQiI,IAC1B,GAAoB,aAAhBxE,EAAIwE,GAAGwC,KAAqB,CAC9BD,GAAc,EACd,KACF,CAEEvG,EACFmG,EAAMM,SAAS,CACbC,QAAS,KACTJ,YAAY,EACZD,UAAU,EAEVM,OAAQ,aACRC,QAASL,EAAc,EAAInL,EAAKyL,iBAChCC,QAASP,EAAc,EAAI,GAC3BQ,cAAc,IAKhBZ,EAAMM,SAAS,CACbC,QAAS,KACTJ,YAAY,EACZD,UAAU,EAIVO,QAASL,EAAc,EAAInL,EAAKyL,iBAChCC,QAASP,EAAc,EAAI,GAC3BI,OAAQ,aAERI,cAAc,IAGlBZ,EAAMa,qBAAoB,WACxB,MAAO,CAAC,CACV,IACA,MAAMC,EAASnC,EAAQY,GAAGwB,YACpBC,EAAYrC,EAAQY,GAAG0B,eACvBC,EAAQxL,OAAOD,KAAKqL,GAE1B,IAAK,MAAMzL,KAAO6L,EAAO,CACvB,MAAMxK,EAAWoK,EAAOzL,GAIxB,IAAI0B,EACJ,GAJI8C,IACFnD,EAASmD,SAAWA,GAGlBnD,EAAS2C,IAAK,CAChB,IAAI8H,EAAM9B,EAASrJ,OAAO,KAAKC,KAAK,KAAMS,EAASG,IAAIZ,KAAK,QAAS,cACrEc,EAAO0I,EAAU/I,EAAS2C,IAAK8H,EAAKzK,EAASG,IAAK4B,EAAQsG,EAAMgB,EAAapB,GAClE,CACTwC,EAAM3I,EAAe2I,EAAKzK,EAAU+B,GACpC,IAAI2I,EAAYD,EAAIpK,OAAOC,UAC3BD,EAAKG,MAAQkK,EAAUlK,MACvBH,EAAKI,OAASiK,EAAUjK,OAASlC,EAAKoB,QAAU,EAChDiI,EAAkB5H,EAASG,IAAM,CAAEoD,EAAGhF,EAAKoM,kBAC7C,CAKF,MACEtK,EAAOkE,EAAUoE,EAAU3I,EAAUsJ,GAEvC,GAAItJ,EAASqE,KAAM,CACjB,MAAMuG,EAAU,CACd1J,aAAc,GACdf,GAAIH,EAASG,GAAK,QAClBkE,KAAMrE,EAASqE,KACfM,KAAM,QAEFN,EAAOE,EAAUoE,EAAUiC,EAAStB,GACX,YAA3BtJ,EAASqE,KAAKwG,UAChBvB,EAAMwB,QAAQzK,EAAKF,GAAK,QAASkE,GACjCiF,EAAMwB,QAAQzK,EAAKF,GAAIE,KAEvBiJ,EAAMwB,QAAQzK,EAAKF,GAAIE,GACvBiJ,EAAMwB,QAAQzK,EAAKF,GAAK,QAASkE,IAEnCiF,EAAMyB,UAAU1K,EAAKF,GAAIE,EAAKF,GAAK,UACnCmJ,EAAMyB,UAAU1K,EAAKF,GAAK,QAASE,EAAKF,GAAK,SAC/C,MACEmJ,EAAMwB,QAAQzK,EAAKF,GAAIE,EAE3B,CACAiH,EAAAA,GAAIoB,MAAM,SAAUY,EAAM0B,YAAa1B,GACvC,IAAI2B,EAAM,EACVX,EAAU9I,SAAQ,SAASwD,GACzBiG,IACA3D,EAAAA,GAAIoB,MAAM,eAAgB1D,GAC1BsE,EAAM4B,QACJlG,EAASmG,IACTnG,EAASoG,IACT,CACEpG,WACAxE,MAAO2I,EAAcnE,EAAS1C,OAC9B7B,OAAQlC,EAAK8M,YAAczH,EAAAA,GAAemD,QAAQ/B,EAAS1C,OAAOpD,OAClEoM,SAAU,KAEZ,KAAOL,EAEX,KACAM,EAAAA,EAAAA,IAAYjC,GACZhC,EAAAA,GAAIoB,MAAM,qBAAsBY,EAAMhB,SACtC,MAAMkD,EAAU7C,EAAStI,OACzBiJ,EAAMhB,QAAQ9G,SAAQ,SAASiK,GAC7B,QAAU,IAANA,QAAkC,IAAlBnC,EAAMjJ,KAAKoL,GAAe,CAC5CnE,EAAAA,GAAIoE,KAAK,QAAUD,EAAI,KAAOE,KAAKC,UAAUtC,EAAMjJ,KAAKoL,KACxDpD,EAAKD,OAAO,IAAMoD,EAAQrL,GAAK,KAAOsL,GAAGlM,KACvC,YACA,cAAgB+J,EAAMjJ,KAAKoL,GAAGpJ,EAAIiH,EAAMjJ,KAAKoL,GAAGjL,MAAQ,GAAK,KAAO8I,EAAMjJ,KAAKoL,GAAGlI,GAAKqE,EAAkB6D,GAAK7D,EAAkB6D,GAAGlI,EAAI,GAAK+F,EAAMjJ,KAAKoL,GAAGhL,OAAS,GAAK,MAE1K4H,EAAKD,OAAO,IAAMoD,EAAQrL,GAAK,KAAOsL,GAAGlM,KAAK,eAAgB+J,EAAMjJ,KAAKoL,GAAGpJ,EAAIiH,EAAMjJ,KAAKoL,GAAGjL,MAAQ,GACrF6I,EAAYwC,iBAAiB,IAAML,EAAQrL,GAAK,KAAOsL,EAAI,aACnEjK,SAASsK,IAChB,MAAMC,EAASD,EAAQE,cACvB,IAAIC,EAAS,EACTC,EAAS,EACTH,IACEA,EAAOC,gBACTC,EAASF,EAAOC,cAAc1L,UAAUE,OAE1C0L,EAASC,SAASJ,EAAOK,aAAa,gBAAiB,IACnDzG,OAAOC,MAAMsG,KACfA,EAAS,IAGbJ,EAAQO,aAAa,KAAM,EAAIH,EAAS,GACxCJ,EAAQO,aAAa,KAAMJ,EAASC,EAAS,EAAE,GAEnD,MACE5E,EAAAA,GAAIoB,MAAM,WAAa+C,EAAI,KAAOE,KAAKC,UAAUtC,EAAMjJ,KAAKoL,IAEhE,IACA,IAAI7G,EAAW4G,EAAQlL,UACvBgJ,EAAMgD,QAAQ9K,SAAQ,SAAS+K,QACnB,IAANA,QAAkC,IAAlBjD,EAAMkD,KAAKD,KAC7BjF,EAAAA,GAAIoB,MAAM,QAAU6D,EAAEd,EAAI,OAASc,EAAEE,EAAI,KAAOd,KAAKC,UAAUtC,EAAMkD,KAAKD,KAC1EzH,EAAS6D,EAAUW,EAAMkD,KAAKD,GAAIjD,EAAMkD,KAAKD,GAAGvH,UAEpD,IACAJ,EAAW4G,EAAQlL,UACnB,MAAMmE,EAAY,CAChBtE,GAAIgD,GAAsB,OAC1BuB,MAAOvB,GAAsB,OAC7B3C,MAAO,EACPC,OAAQ,GAKV,OAHAgE,EAAUjE,MAAQoE,EAASpE,MAAQ,EAAIjC,EAAKoB,QAC5C8E,EAAUhE,OAASmE,EAASnE,OAAS,EAAIlC,EAAKoB,QAC9C2H,EAAAA,GAAIoB,MAAM,eAAgBjE,EAAW6E,GAC9B7E,CAAS,GACf,aACCiI,EAAwB,CAC1B7E,UACAE,QAIE4E,EAAU,CACZC,OAAQC,EAAAA,GACR,MAAIhE,GACF,OAAO,IAAI3D,EAAAA,GAAQ,EACrB,EACA4H,SAAUJ,EACVK,OAAQC,EAAAA,GACRC,MAAsBvO,EAAAA,EAAAA,KAAQwO,IACvBA,EAAIzN,QACPyN,EAAIzN,MAAQ,CAAC,GAEfyN,EAAIzN,MAAM4G,oBAAsB6G,EAAI7G,mBAAmB,GACtD,Q", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-DGXRK772.mjs"], "sourcesContent": ["import {\n  StateDB,\n  stateDiagram_default,\n  styles_default\n} from \"./chunk-AEK57VVT.mjs\";\nimport \"./chunk-RZ5BOZE2.mjs\";\nimport \"./chunk-TYCBKAJE.mjs\";\nimport \"./chunk-IIMUDSI4.mjs\";\nimport \"./chunk-VV3M67IP.mjs\";\nimport \"./chunk-HRU6DDCH.mjs\";\nimport \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport {\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  common_default,\n  configureSvgSize,\n  getConfig2 as getConfig,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/state/stateRenderer.js\nimport { select } from \"d3\";\nimport { layout as dagreLayout } from \"dagre-d3-es/src/dagre/index.js\";\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/diagrams/state/shapes.js\nimport { line, curveBasis } from \"d3\";\n\n// src/diagrams/state/id-cache.js\nvar idCache = {};\nvar set = /* @__PURE__ */ __name((key, val) => {\n  idCache[key] = val;\n}, \"set\");\nvar get = /* @__PURE__ */ __name((k) => idCache[k], \"get\");\nvar keys = /* @__PURE__ */ __name(() => Object.keys(idCache), \"keys\");\nvar size = /* @__PURE__ */ __name(() => keys().length, \"size\");\nvar id_cache_default = {\n  get,\n  set,\n  keys,\n  size\n};\n\n// src/diagrams/state/shapes.js\nvar drawStartState = /* @__PURE__ */ __name((g) => g.append(\"circle\").attr(\"class\", \"start-state\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit), \"drawStartState\");\nvar drawDivider = /* @__PURE__ */ __name((g) => g.append(\"line\").style(\"stroke\", \"grey\").style(\"stroke-dasharray\", \"3\").attr(\"x1\", getConfig().state.textHeight).attr(\"class\", \"divider\").attr(\"x2\", getConfig().state.textHeight * 2).attr(\"y1\", 0).attr(\"y2\", 0), \"drawDivider\");\nvar drawSimpleState = /* @__PURE__ */ __name((g, stateDef) => {\n  const state = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 2 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const classBox = state.node().getBBox();\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", classBox.width + 2 * getConfig().state.padding).attr(\"height\", classBox.height + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return state;\n}, \"drawSimpleState\");\nvar drawDescrState = /* @__PURE__ */ __name((g, stateDef) => {\n  const addTspan = /* @__PURE__ */ __name(function(textEl, txt, isFirst2) {\n    const tSpan = textEl.append(\"tspan\").attr(\"x\", 2 * getConfig().state.padding).text(txt);\n    if (!isFirst2) {\n      tSpan.attr(\"dy\", getConfig().state.textHeight);\n    }\n  }, \"addTspan\");\n  const title = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 1.3 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.descriptions[0]);\n  const titleBox = title.node().getBBox();\n  const titleHeight = titleBox.height;\n  const description = g.append(\"text\").attr(\"x\", getConfig().state.padding).attr(\n    \"y\",\n    titleHeight + getConfig().state.padding * 0.4 + getConfig().state.dividerMargin + getConfig().state.textHeight\n  ).attr(\"class\", \"state-description\");\n  let isFirst = true;\n  let isSecond = true;\n  stateDef.descriptions.forEach(function(descr) {\n    if (!isFirst) {\n      addTspan(description, descr, isSecond);\n      isSecond = false;\n    }\n    isFirst = false;\n  });\n  const descrLine = g.append(\"line\").attr(\"x1\", getConfig().state.padding).attr(\"y1\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"y2\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"class\", \"descr-divider\");\n  const descrBox = description.node().getBBox();\n  const width = Math.max(descrBox.width, titleBox.width);\n  descrLine.attr(\"x2\", width + 3 * getConfig().state.padding);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", width + 2 * getConfig().state.padding).attr(\"height\", descrBox.height + titleHeight + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"drawDescrState\");\nvar addTitleAndBox = /* @__PURE__ */ __name((g, stateDef, altBkg) => {\n  const pad = getConfig().state.padding;\n  const dblPad = 2 * getConfig().state.padding;\n  const orgBox = g.node().getBBox();\n  const orgWidth = orgBox.width;\n  const orgX = orgBox.x;\n  const title = g.append(\"text\").attr(\"x\", 0).attr(\"y\", getConfig().state.titleShift).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const titleBox = title.node().getBBox();\n  const titleWidth = titleBox.width + dblPad;\n  let width = Math.max(titleWidth, orgWidth);\n  if (width === orgWidth) {\n    width = width + dblPad;\n  }\n  let startX;\n  const graphBox = g.node().getBBox();\n  if (stateDef.doc) {\n  }\n  startX = orgX - pad;\n  if (titleWidth > orgWidth) {\n    startX = (orgWidth - width) / 2 + pad;\n  }\n  if (Math.abs(orgX - graphBox.x) < pad && titleWidth > orgWidth) {\n    startX = orgX - (titleWidth - orgWidth) / 2;\n  }\n  const lineY = 1 - getConfig().state.textHeight;\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\"y\", lineY).attr(\"class\", altBkg ? \"alt-composit\" : \"composit\").attr(\"width\", width).attr(\n    \"height\",\n    graphBox.height + getConfig().state.textHeight + getConfig().state.titleShift + 1\n  ).attr(\"rx\", \"0\");\n  title.attr(\"x\", startX + pad);\n  if (titleWidth <= orgWidth) {\n    title.attr(\"x\", orgX + (width - dblPad) / 2 - titleWidth / 2 + pad);\n  }\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", getConfig().state.textHeight * 3).attr(\"rx\", getConfig().state.radius);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", graphBox.height + 3 + 2 * getConfig().state.textHeight).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"addTitleAndBox\");\nvar drawEndState = /* @__PURE__ */ __name((g) => {\n  g.append(\"circle\").attr(\"class\", \"end-state-outer\").attr(\"r\", getConfig().state.sizeUnit + getConfig().state.miniPadding).attr(\n    \"cx\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  ).attr(\n    \"cy\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  );\n  return g.append(\"circle\").attr(\"class\", \"end-state-inner\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit + 2).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit + 2);\n}, \"drawEndState\");\nvar drawForkJoinState = /* @__PURE__ */ __name((g, stateDef) => {\n  let width = getConfig().state.forkWidth;\n  let height = getConfig().state.forkHeight;\n  if (stateDef.parentId) {\n    let tmp = width;\n    width = height;\n    height = tmp;\n  }\n  return g.append(\"rect\").style(\"stroke\", \"black\").style(\"fill\", \"black\").attr(\"width\", width).attr(\"height\", height).attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding);\n}, \"drawForkJoinState\");\nvar _drawLongText = /* @__PURE__ */ __name((_text, x, y, g) => {\n  let textHeight = 0;\n  const textElem = g.append(\"text\");\n  textElem.style(\"text-anchor\", \"start\");\n  textElem.attr(\"class\", \"noteText\");\n  let text = _text.replace(/\\r\\n/g, \"<br/>\");\n  text = text.replace(/\\n/g, \"<br/>\");\n  const lines = text.split(common_default.lineBreakRegex);\n  let tHeight = 1.25 * getConfig().state.noteMargin;\n  for (const line2 of lines) {\n    const txt = line2.trim();\n    if (txt.length > 0) {\n      const span = textElem.append(\"tspan\");\n      span.text(txt);\n      if (tHeight === 0) {\n        const textBounds = span.node().getBBox();\n        tHeight += textBounds.height;\n      }\n      textHeight += tHeight;\n      span.attr(\"x\", x + getConfig().state.noteMargin);\n      span.attr(\"y\", y + textHeight + 1.25 * getConfig().state.noteMargin);\n    }\n  }\n  return { textWidth: textElem.node().getBBox().width, textHeight };\n}, \"_drawLongText\");\nvar drawNote = /* @__PURE__ */ __name((text, g) => {\n  g.attr(\"class\", \"state-note\");\n  const note = g.append(\"rect\").attr(\"x\", 0).attr(\"y\", getConfig().state.padding);\n  const rectElem = g.append(\"g\");\n  const { textWidth, textHeight } = _drawLongText(text, 0, 0, rectElem);\n  note.attr(\"height\", textHeight + 2 * getConfig().state.noteMargin);\n  note.attr(\"width\", textWidth + getConfig().state.noteMargin * 2);\n  return note;\n}, \"drawNote\");\nvar drawState = /* @__PURE__ */ __name(function(elem, stateDef) {\n  const id = stateDef.id;\n  const stateInfo = {\n    id,\n    label: stateDef.id,\n    width: 0,\n    height: 0\n  };\n  const g = elem.append(\"g\").attr(\"id\", id).attr(\"class\", \"stateGroup\");\n  if (stateDef.type === \"start\") {\n    drawStartState(g);\n  }\n  if (stateDef.type === \"end\") {\n    drawEndState(g);\n  }\n  if (stateDef.type === \"fork\" || stateDef.type === \"join\") {\n    drawForkJoinState(g, stateDef);\n  }\n  if (stateDef.type === \"note\") {\n    drawNote(stateDef.note.text, g);\n  }\n  if (stateDef.type === \"divider\") {\n    drawDivider(g);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length === 0) {\n    drawSimpleState(g, stateDef);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length > 0) {\n    drawDescrState(g, stateDef);\n  }\n  const stateBox = g.node().getBBox();\n  stateInfo.width = stateBox.width + 2 * getConfig().state.padding;\n  stateInfo.height = stateBox.height + 2 * getConfig().state.padding;\n  id_cache_default.set(id, stateInfo);\n  return stateInfo;\n}, \"drawState\");\nvar edgeCount = 0;\nvar drawEdge = /* @__PURE__ */ __name(function(elem, path, relation) {\n  const getRelationType = /* @__PURE__ */ __name(function(type) {\n    switch (type) {\n      case StateDB.relationType.AGGREGATION:\n        return \"aggregation\";\n      case StateDB.relationType.EXTENSION:\n        return \"extension\";\n      case StateDB.relationType.COMPOSITION:\n        return \"composition\";\n      case StateDB.relationType.DEPENDENCY:\n        return \"dependency\";\n    }\n  }, \"getRelationType\");\n  path.points = path.points.filter((p) => !Number.isNaN(p.y));\n  const lineData = path.points;\n  const lineFunction = line().x(function(d) {\n    return d.x;\n  }).y(function(d) {\n    return d.y;\n  }).curve(curveBasis);\n  const svgPath = elem.append(\"path\").attr(\"d\", lineFunction(lineData)).attr(\"id\", \"edge\" + edgeCount).attr(\"class\", \"transition\");\n  let url = \"\";\n  if (getConfig().state.arrowMarkerAbsolute) {\n    url = window.location.protocol + \"//\" + window.location.host + window.location.pathname + window.location.search;\n    url = url.replace(/\\(/g, \"\\\\(\");\n    url = url.replace(/\\)/g, \"\\\\)\");\n  }\n  svgPath.attr(\n    \"marker-end\",\n    \"url(\" + url + \"#\" + getRelationType(StateDB.relationType.DEPENDENCY) + \"End)\"\n  );\n  if (relation.title !== void 0) {\n    const label = elem.append(\"g\").attr(\"class\", \"stateLabel\");\n    const { x, y } = utils_default.calcLabelPosition(path.points);\n    const rows = common_default.getRows(relation.title);\n    let titleHeight = 0;\n    const titleRows = [];\n    let maxWidth = 0;\n    let minX = 0;\n    for (let i = 0; i <= rows.length; i++) {\n      const title = label.append(\"text\").attr(\"text-anchor\", \"middle\").text(rows[i]).attr(\"x\", x).attr(\"y\", y + titleHeight);\n      const boundsTmp = title.node().getBBox();\n      maxWidth = Math.max(maxWidth, boundsTmp.width);\n      minX = Math.min(minX, boundsTmp.x);\n      log.info(boundsTmp.x, x, y + titleHeight);\n      if (titleHeight === 0) {\n        const titleBox = title.node().getBBox();\n        titleHeight = titleBox.height;\n        log.info(\"Title height\", titleHeight, y);\n      }\n      titleRows.push(title);\n    }\n    let boxHeight = titleHeight * rows.length;\n    if (rows.length > 1) {\n      const heightAdj = (rows.length - 1) * titleHeight * 0.5;\n      titleRows.forEach((title, i) => title.attr(\"y\", y + i * titleHeight - heightAdj));\n      boxHeight = titleHeight * rows.length;\n    }\n    const bounds = label.node().getBBox();\n    label.insert(\"rect\", \":first-child\").attr(\"class\", \"box\").attr(\"x\", x - maxWidth / 2 - getConfig().state.padding / 2).attr(\"y\", y - boxHeight / 2 - getConfig().state.padding / 2 - 3.5).attr(\"width\", maxWidth + getConfig().state.padding).attr(\"height\", boxHeight + getConfig().state.padding);\n    log.info(bounds);\n  }\n  edgeCount++;\n}, \"drawEdge\");\n\n// src/diagrams/state/stateRenderer.js\nvar conf;\nvar transformationLog = {};\nvar setConf = /* @__PURE__ */ __name(function() {\n}, \"setConf\");\nvar insertMarkers = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"dependencyEnd\").attr(\"refX\", 19).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 19,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertMarkers\");\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  conf = getConfig().state;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  log.debug(\"Rendering diagram \" + text);\n  const diagram2 = root.select(`[id='${id}']`);\n  insertMarkers(diagram2);\n  const rootDoc = diagObj.db.getRootDoc();\n  renderDoc(rootDoc, diagram2, void 0, false, root, doc, diagObj);\n  const padding = conf.padding;\n  const bounds = diagram2.node().getBBox();\n  const width = bounds.width + padding * 2;\n  const height = bounds.height + padding * 2;\n  const svgWidth = width * 1.75;\n  configureSvgSize(diagram2, height, svgWidth, conf.useMaxWidth);\n  diagram2.attr(\n    \"viewBox\",\n    `${bounds.x - conf.padding}  ${bounds.y - conf.padding} ` + width + \" \" + height\n  );\n}, \"draw\");\nvar getLabelWidth = /* @__PURE__ */ __name((text) => {\n  return text ? text.length * conf.fontSizeFactor : 1;\n}, \"getLabelWidth\");\nvar renderDoc = /* @__PURE__ */ __name((doc, diagram2, parentId, altBkg, root, domDocument, diagObj) => {\n  const graph = new graphlib.Graph({\n    compound: true,\n    multigraph: true\n  });\n  let i;\n  let edgeFreeDoc = true;\n  for (i = 0; i < doc.length; i++) {\n    if (doc[i].stmt === \"relation\") {\n      edgeFreeDoc = false;\n      break;\n    }\n  }\n  if (parentId) {\n    graph.setGraph({\n      rankdir: \"LR\",\n      multigraph: true,\n      compound: true,\n      // acyclicer: 'greedy',\n      ranker: \"tight-tree\",\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      isMultiGraph: true\n      // ranksep: 5,\n      // nodesep: 1\n    });\n  } else {\n    graph.setGraph({\n      rankdir: \"TB\",\n      multigraph: true,\n      compound: true,\n      // isCompound: true,\n      // acyclicer: 'greedy',\n      // ranker: 'longest-path'\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      ranker: \"tight-tree\",\n      // ranker: 'network-simplex'\n      isMultiGraph: true\n    });\n  }\n  graph.setDefaultEdgeLabel(function() {\n    return {};\n  });\n  const states = diagObj.db.getStates();\n  const relations = diagObj.db.getRelations();\n  const keys2 = Object.keys(states);\n  let first = true;\n  for (const key of keys2) {\n    const stateDef = states[key];\n    if (parentId) {\n      stateDef.parentId = parentId;\n    }\n    let node;\n    if (stateDef.doc) {\n      let sub = diagram2.append(\"g\").attr(\"id\", stateDef.id).attr(\"class\", \"stateGroup\");\n      node = renderDoc(stateDef.doc, sub, stateDef.id, !altBkg, root, domDocument, diagObj);\n      if (first) {\n        sub = addTitleAndBox(sub, stateDef, altBkg);\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height + conf.padding / 2;\n        transformationLog[stateDef.id] = { y: conf.compositTitleSize };\n      } else {\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height;\n      }\n    } else {\n      node = drawState(diagram2, stateDef, graph);\n    }\n    if (stateDef.note) {\n      const noteDef = {\n        descriptions: [],\n        id: stateDef.id + \"-note\",\n        note: stateDef.note,\n        type: \"note\"\n      };\n      const note = drawState(diagram2, noteDef, graph);\n      if (stateDef.note.position === \"left of\") {\n        graph.setNode(node.id + \"-note\", note);\n        graph.setNode(node.id, node);\n      } else {\n        graph.setNode(node.id, node);\n        graph.setNode(node.id + \"-note\", note);\n      }\n      graph.setParent(node.id, node.id + \"-group\");\n      graph.setParent(node.id + \"-note\", node.id + \"-group\");\n    } else {\n      graph.setNode(node.id, node);\n    }\n  }\n  log.debug(\"Count=\", graph.nodeCount(), graph);\n  let cnt = 0;\n  relations.forEach(function(relation) {\n    cnt++;\n    log.debug(\"Setting edge\", relation);\n    graph.setEdge(\n      relation.id1,\n      relation.id2,\n      {\n        relation,\n        width: getLabelWidth(relation.title),\n        height: conf.labelHeight * common_default.getRows(relation.title).length,\n        labelpos: \"c\"\n      },\n      \"id\" + cnt\n    );\n  });\n  dagreLayout(graph);\n  log.debug(\"Graph after layout\", graph.nodes());\n  const svgElem = diagram2.node();\n  graph.nodes().forEach(function(v) {\n    if (v !== void 0 && graph.node(v) !== void 0) {\n      log.warn(\"Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\n        \"transform\",\n        \"translate(\" + (graph.node(v).x - graph.node(v).width / 2) + \",\" + (graph.node(v).y + (transformationLog[v] ? transformationLog[v].y : 0) - graph.node(v).height / 2) + \" )\"\n      );\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\"data-x-shift\", graph.node(v).x - graph.node(v).width / 2);\n      const dividers = domDocument.querySelectorAll(\"#\" + svgElem.id + \" #\" + v + \" .divider\");\n      dividers.forEach((divider) => {\n        const parent = divider.parentElement;\n        let pWidth = 0;\n        let pShift = 0;\n        if (parent) {\n          if (parent.parentElement) {\n            pWidth = parent.parentElement.getBBox().width;\n          }\n          pShift = parseInt(parent.getAttribute(\"data-x-shift\"), 10);\n          if (Number.isNaN(pShift)) {\n            pShift = 0;\n          }\n        }\n        divider.setAttribute(\"x1\", 0 - pShift + 8);\n        divider.setAttribute(\"x2\", pWidth - pShift - 8);\n      });\n    } else {\n      log.debug(\"No Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n    }\n  });\n  let stateBox = svgElem.getBBox();\n  graph.edges().forEach(function(e) {\n    if (e !== void 0 && graph.edge(e) !== void 0) {\n      log.debug(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(graph.edge(e)));\n      drawEdge(diagram2, graph.edge(e), graph.edge(e).relation);\n    }\n  });\n  stateBox = svgElem.getBBox();\n  const stateInfo = {\n    id: parentId ? parentId : \"root\",\n    label: parentId ? parentId : \"root\",\n    width: 0,\n    height: 0\n  };\n  stateInfo.width = stateBox.width + 2 * conf.padding;\n  stateInfo.height = stateBox.height + 2 * conf.padding;\n  log.debug(\"Doc rendered\", stateInfo, graph);\n  return stateInfo;\n}, \"renderDoc\");\nvar stateRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/state/stateDiagram.ts\nvar diagram = {\n  parser: stateDiagram_default,\n  get db() {\n    return new StateDB(1);\n  },\n  renderer: stateRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "names": ["conf", "idCache", "set", "__name", "key", "val", "get", "k", "keys", "Object", "size", "length", "id_cache_default", "drawStartState", "g", "append", "attr", "getConfig", "state", "sizeUnit", "padding", "drawDivider", "style", "textHeight", "drawSimpleState", "stateDef", "fontSize", "text", "id", "classBox", "node", "getBBox", "insert", "width", "height", "radius", "drawDescrState", "addTspan", "textEl", "txt", "isFirst2", "tSpan", "titleBox", "descriptions", "titleHeight", "description", "dividerMargin", "<PERSON><PERSON><PERSON><PERSON>", "isSecond", "for<PERSON>ach", "descr", "descrLine", "descrBox", "Math", "max", "addTitleAndBox", "altBkg", "pad", "dblPad", "orgBox", "orgWidth", "orgX", "x", "title", "titleShift", "titleWidth", "startX", "graphBox", "doc", "abs", "lineY", "drawEndState", "miniPadding", "drawForkJoinState", "<PERSON><PERSON><PERSON><PERSON>", "forkHeight", "parentId", "tmp", "_drawLongText", "_text", "y", "textElem", "replace", "lines", "split", "common_default", "lineBreakRegex", "tHeight", "note<PERSON><PERSON><PERSON>", "line2", "trim", "span", "textWidth", "drawNote", "note", "rectElem", "drawState", "elem", "stateInfo", "label", "type", "stateBox", "edgeCount", "drawEdge", "path", "relation", "getRelationType", "StateDB", "relationType", "AGGREGATION", "EXTENSION", "COMPOSITION", "DEPENDENCY", "points", "filter", "p", "Number", "isNaN", "lineData", "lineFunction", "line", "d", "curve", "curveBasis", "svgPath", "url", "arrowMarkerAbsolute", "window", "location", "protocol", "host", "pathname", "search", "utils_default", "calcLabelPosition", "rows", "getRows", "titleRows", "max<PERSON><PERSON><PERSON>", "minX", "i", "boundsTmp", "min", "log", "info", "push", "boxHeight", "heightAdj", "bounds", "transformationLog", "setConf", "insertMarkers", "draw", "_version", "diagObj", "securityLevel", "sandboxElement", "select", "root", "nodes", "contentDocument", "body", "document", "debug", "diagram2", "rootDoc", "db", "getRootDoc", "renderDoc", "svgWidth", "configureSvgSize", "useMaxWidth", "<PERSON><PERSON><PERSON><PERSON>", "fontSizeFactor", "domDocument", "graph", "graphlib", "compound", "multigraph", "edgeFreeDoc", "stmt", "setGraph", "rankdir", "ranker", "ranksep", "edgeLengthFactor", "nodeSep", "isMultiGraph", "setDefaultEdgeLabel", "states", "getStates", "relations", "getRelations", "keys2", "sub", "boxBounds", "compositTitleSize", "noteDef", "position", "setNode", "setParent", "nodeCount", "cnt", "setEdge", "id1", "id2", "labelHeight", "labelpos", "dagreLayout", "svgElem", "v", "warn", "JSON", "stringify", "querySelectorAll", "divider", "parent", "parentElement", "pWidth", "pShift", "parseInt", "getAttribute", "setAttribute", "edges", "e", "edge", "w", "stateRenderer_default", "diagram", "parser", "stateDiagram_default", "renderer", "styles", "styles_default", "init", "cnf"], "sourceRoot": ""}