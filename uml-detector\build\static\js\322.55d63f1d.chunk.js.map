{"version": 3, "file": "static/js/322.55d63f1d.chunk.js", "mappings": "uLAyBIA,EAAiB,CACnBC,YAAY,EACZC,MAAO,EACPC,IAAK,KACLC,IAAK,EACLC,UAAW,UAETC,EAAmB,CACrBC,KAAM,GACNC,OAAQ,GACRC,QAAST,GAEPU,EAAOC,gBAAgBL,GACvBM,EAAuBC,EAAAA,GAAsBC,MAC7CC,GAA6BC,EAAAA,EAAAA,KAAO,KACvBC,EAAAA,EAAAA,IAAc,IACxBL,MACAM,EAAAA,EAAAA,MAAYJ,SAGhB,aACCK,GAA0BH,EAAAA,EAAAA,KAAO,IAAMN,EAAKH,MAAM,WAClDa,GAA4BJ,EAAAA,EAAAA,KAAO,IAAMN,EAAKF,QAAQ,aACtDa,GAA6BL,EAAAA,EAAAA,KAAO,IAAMN,EAAKD,SAAS,cACxDa,GAA0BN,EAAAA,EAAAA,KAAQT,IACpCG,EAAKH,KAAOA,EAAKgB,KAAKC,IACb,CACLC,KAAMD,EAAKC,KACXC,MAAOF,EAAKE,OAASF,EAAKC,QAE5B,GACD,WACCE,GAA4BX,EAAAA,EAAAA,KAAQR,IACtCE,EAAKF,OAASA,EAAOe,KAAKK,IACjB,CACLH,KAAMG,EAAMH,KACZC,MAAOE,EAAMF,OAASE,EAAMH,KAC5BI,QAASC,EAAoBF,EAAMC,YAErC,GACD,aACCC,GAAsCd,EAAAA,EAAAA,KAAQa,IAChD,QAAuB,GAAnBA,EAAQ,GAAGL,KACb,OAAOK,EAAQN,KAAKQ,GAAUA,EAAMC,QAEtC,MAAMzB,EAAOY,IACb,GAAoB,IAAhBZ,EAAK0B,OACP,MAAM,IAAIC,MAAM,8DAElB,OAAO3B,EAAKgB,KAAKC,IACf,MAAMO,EAAQF,EAAQM,MAAMC,GAAWA,EAAOZ,MAAMa,WAAab,EAAKC,OACtE,QAAc,IAAVM,EACF,MAAM,IAAIG,MAAM,0BAA4BV,EAAKE,OAEnD,OAAOK,EAAMC,KAAK,GAClB,GACD,uBAqBCM,EAAK,CACPnB,UACAC,YACAC,aACAC,UACAK,YACAY,YA1B+BvB,EAAAA,EAAAA,KAAQP,IACvC,MAAM+B,EAAY/B,EAAQgC,QACxB,CAACC,EAAKC,KACJD,EAAIC,EAAOlB,MAAQkB,EACZD,IAET,CAAC,GAEHhC,EAAKD,QAAU,CACbR,WAAYuC,EAAUvC,YAAY+B,OAAShC,EAAeC,WAC1DC,MAAOsC,EAAUtC,OAAO8B,OAAShC,EAAeE,MAChDC,IAAKqC,EAAUrC,KAAK6B,OAAShC,EAAeG,IAC5CC,IAAKoC,EAAUpC,KAAK4B,OAAShC,EAAeI,IAC5CC,UAAWmC,EAAUnC,WAAW2B,OAAShC,EAAeK,UACzD,GACA,cAYDa,UAAWH,EACX6B,OAZ2B5B,EAAAA,EAAAA,KAAO,MAClC4B,EAAAA,EAAAA,MACAlC,EAAOC,gBAAgBL,EAAiB,GACvC,SAUDuC,YAAW,KACXC,YAAW,KACXC,gBAAe,KACfC,gBAAe,KACfC,kBAAiB,KACjBC,kBAAiBA,EAAAA,IAKfC,GAA2BnC,EAAAA,EAAAA,KAAQoC,KACrCC,EAAAA,EAAAA,GAAiBD,EAAKd,GACtB,MAAM,KAAE/B,EAAI,OAAEC,EAAM,QAAEC,GAAY2C,EAClCd,EAAGhB,QAAQf,GACX+B,EAAGX,UAAUnB,GACb8B,EAAGC,WAAW9B,EAAQ,GACrB,YACC6C,EAAS,CACXC,OAAuBvC,EAAAA,EAAAA,KAAOwC,UAC5B,MAAMJ,QAAYG,EAAAA,EAAAA,IAAM,QAASE,GACjCC,EAAAA,GAAIC,MAAMP,GACVD,EAASC,EAAI,GACZ,UAIDQ,GAAuB5C,EAAAA,EAAAA,KAAO,CAAC6C,EAAOC,EAAIC,EAAUC,KACtD,MAAMC,EAAMD,EAAS1B,GACf/B,EAAO0D,EAAI9C,UACXX,EAASyD,EAAI7C,YACbX,EAAUwD,EAAI5C,aACd6C,EAASD,EAAI/C,YACbiD,EAAQF,EAAIjB,kBACZoB,GAAMC,EAAAA,EAAAA,GAAiBP,GACvBQ,EAAIC,EAAUH,EAAKF,GACnBM,EAAW/D,EAAQN,KAAOsE,KAAKtE,OAAOK,EAAOe,KAAKK,GAAU6C,KAAKtE,OAAOyB,EAAMC,YAC9E6C,EAAWjE,EAAQL,IACnBuE,EAASF,KAAKrE,IAAI8D,EAAOU,MAAOV,EAAOW,QAAU,EACvDC,EAAcR,EAAG/D,EAAMoE,EAAQlE,EAAQP,MAAOO,EAAQJ,WACtD0E,EAAST,EAAG/D,EAAMoE,EAAQT,GAC1Bc,EAAWV,EAAG/D,EAAMC,EAAQkE,EAAUF,EAAU/D,EAAQJ,UAAW6D,GACnEe,EAAWX,EAAG9D,EAAQC,EAAQR,WAAYiE,GAC1CI,EAAEY,OAAO,QAAQC,KAAK,QAAS,cAAcC,KAAKjB,GAAOgB,KAAK,IAAK,GAAGA,KAAK,KAAMjB,EAAOW,OAAS,EAAIX,EAAOmB,UAAU,GACrH,QACCd,GAA4BvD,EAAAA,EAAAA,KAAO,CAACoD,EAAKF,KAC3C,MAAMoB,EAAapB,EAAOU,MAAQV,EAAOqB,WAAarB,EAAOsB,YACvDC,EAAcvB,EAAOW,OAASX,EAAOmB,UAAYnB,EAAOwB,aACxDC,EACDzB,EAAOqB,WAAarB,EAAOU,MAAQ,EADlCe,EAEDzB,EAAOmB,UAAYnB,EAAOW,OAAS,EAGxC,OADAT,EAAIe,KAAK,UAAW,OAAOG,KAAcG,KAAeN,KAAK,QAASG,GAAYH,KAAK,SAAUM,GAC1FrB,EAAIc,OAAO,KAAKC,KAAK,YAAa,aAAaQ,MAAaA,KAAY,GAC9E,aACCb,GAAgC9D,EAAAA,EAAAA,KAAO,CAACsD,EAAG/D,EAAMoE,EAAQzE,EAAOG,KAClE,GAAkB,WAAdA,EACF,IAAK,IAAIuF,EAAI,EAAGA,EAAI1F,EAAO0F,IAAK,CAC9B,MAAMC,EAAIlB,GAAUiB,EAAI,GAAK1F,EAC7BoE,EAAEY,OAAO,UAAUC,KAAK,IAAKU,GAAGV,KAAK,QAAS,iBAChD,MACK,GAAkB,YAAd9E,EAAyB,CAClC,MAAMyF,EAAUvF,EAAK0B,OACrB,IAAK,IAAI2D,EAAI,EAAGA,EAAI1F,EAAO0F,IAAK,CAC9B,MAAMC,EAAIlB,GAAUiB,EAAI,GAAK1F,EACvB6F,EAASxF,EAAKgB,KAAI,CAACyE,EAAGC,KAC1B,MAAMC,EAAQ,EAAID,EAAIxB,KAAK0B,GAAKL,EAAUrB,KAAK0B,GAAK,EAGpD,MAAO,GAFGN,EAAIpB,KAAK2B,IAAIF,MACbL,EAAIpB,KAAK4B,IAAIH,IACL,IACjBI,KAAK,KACRhC,EAAEY,OAAO,WAAWC,KAAK,SAAUY,GAAQZ,KAAK,QAAS,iBAC3D,CACF,IACC,iBACCJ,GAA2B/D,EAAAA,EAAAA,KAAO,CAACsD,EAAG/D,EAAMoE,EAAQT,KACtD,MAAM4B,EAAUvF,EAAK0B,OACrB,IAAK,IAAI2D,EAAI,EAAGA,EAAIE,EAASF,IAAK,CAChC,MAAMlE,EAAQnB,EAAKqF,GAAGlE,MAChBwE,EAAQ,EAAIN,EAAInB,KAAK0B,GAAKL,EAAUrB,KAAK0B,GAAK,EACpD7B,EAAEY,OAAO,QAAQC,KAAK,KAAM,GAAGA,KAAK,KAAM,GAAGA,KAAK,KAAMR,EAAST,EAAOqC,gBAAkB9B,KAAK2B,IAAIF,IAAQf,KAAK,KAAMR,EAAST,EAAOqC,gBAAkB9B,KAAK4B,IAAIH,IAAQf,KAAK,QAAS,iBACvLb,EAAEY,OAAO,QAAQE,KAAK1D,GAAOyD,KAAK,IAAKR,EAAST,EAAOsC,gBAAkB/B,KAAK2B,IAAIF,IAAQf,KAAK,IAAKR,EAAST,EAAOsC,gBAAkB/B,KAAK4B,IAAIH,IAAQf,KAAK,QAAS,iBACvK,IACC,YACH,SAASH,EAAWV,EAAG/D,EAAMC,EAAQkE,EAAUF,EAAUnE,EAAW6D,GAClE,MAAM4B,EAAUvF,EAAK0B,OACf0C,EAASF,KAAKrE,IAAI8D,EAAOU,MAAOV,EAAOW,QAAU,EACvDrE,EAAOiG,SAAQ,CAAC7E,EAAO8E,KACrB,GAAI9E,EAAMC,QAAQI,SAAW6D,EAC3B,OAEF,MAAMC,EAASnE,EAAMC,QAAQN,KAAI,CAACQ,EAAO6D,KACvC,MAAMM,EAAQ,EAAIzB,KAAK0B,GAAKP,EAAIE,EAAUrB,KAAK0B,GAAK,EAC9CN,EAAIc,EAAe5E,EAAO2C,EAAUF,EAAUG,GAGpD,MAAO,CAAEiC,EAFCf,EAAIpB,KAAK2B,IAAIF,GAEXW,EADFhB,EAAIpB,KAAK4B,IAAIH,GACR,IAEC,WAAd7F,EACFiE,EAAEY,OAAO,QAAQC,KAAK,IAAK2B,EAAiBf,EAAQ7B,EAAO6C,eAAe5B,KAAK,QAAS,cAAcuB,KAC/E,YAAdrG,GACTiE,EAAEY,OAAO,WAAWC,KAAK,SAAUY,EAAOxE,KAAKyF,GAAM,GAAGA,EAAEJ,KAAKI,EAAEH,MAAKP,KAAK,MAAMnB,KAAK,QAAS,cAAcuB,IAC/G,GAEJ,CAEA,SAASC,EAAe3E,EAAO0C,EAAUF,EAAUG,GAEjD,OAAOA,GADcF,KAAKrE,IAAIqE,KAAKtE,IAAI6B,EAAO0C,GAAWF,GACzBE,IAAaF,EAAWE,EAC1D,CAEA,SAASoC,EAAiBf,EAAQkB,GAChC,MAAMC,EAAYnB,EAAO9D,OACzB,IAAIkF,EAAI,IAAIpB,EAAO,GAAGa,KAAKb,EAAO,GAAGc,IACrC,IAAK,IAAIjB,EAAI,EAAGA,EAAIsB,EAAWtB,IAAK,CAClC,MAAMwB,EAAKrB,GAAQH,EAAI,EAAIsB,GAAaA,GAClCG,EAAKtB,EAAOH,GACZ0B,EAAKvB,GAAQH,EAAI,GAAKsB,GACtBK,EAAKxB,GAAQH,EAAI,GAAKsB,GACtBM,EAAM,CACVZ,EAAGS,EAAGT,GAAKU,EAAGV,EAAIQ,EAAGR,GAAKK,EAC1BJ,EAAGQ,EAAGR,GAAKS,EAAGT,EAAIO,EAAGP,GAAKI,GAEtBQ,EAAM,CACVb,EAAGU,EAAGV,GAAKW,EAAGX,EAAIS,EAAGT,GAAKK,EAC1BJ,EAAGS,EAAGT,GAAKU,EAAGV,EAAIQ,EAAGR,GAAKI,GAE5BE,GAAK,KAAKK,EAAIZ,KAAKY,EAAIX,KAAKY,EAAIb,KAAKa,EAAIZ,KAAKS,EAAGV,KAAKU,EAAGT,GAC3D,CACA,MAAO,GAAGM,KACZ,CAEA,SAASlC,EAAWX,EAAG9D,EAAQP,EAAYiE,GACzC,IAAKjE,EACH,OAEF,MAAMyH,EAAoD,GAAzCxD,EAAOU,MAAQ,EAAIV,EAAOsB,aAAmB,EACxDmC,EAAoD,IAAxCzD,EAAOW,OAAS,EAAIX,EAAOmB,WAAiB,EAE9D7E,EAAOiG,SAAQ,CAAC7E,EAAO8E,KACrB,MAAMkB,EAAYtD,EAAEY,OAAO,KAAKC,KAAK,YAAa,aAAauC,MAAYC,EAF1D,GAEoEjB,MACrFkB,EAAU1C,OAAO,QAAQC,KAAK,QAAS,IAAIA,KAAK,SAAU,IAAIA,KAAK,QAAS,kBAAkBuB,KAC9FkB,EAAU1C,OAAO,QAAQC,KAAK,IAAK,IAAIA,KAAK,IAAK,GAAGA,KAAK,QAAS,mBAAmBC,KAAKxD,EAAMF,MAAM,GAE1G,EAvCAV,EAAAA,EAAAA,IAAOgE,EAAY,eAKnBhE,EAAAA,EAAAA,IAAO2F,EAAgB,mBAqBvB3F,EAAAA,EAAAA,IAAO8F,EAAkB,qBAczB9F,EAAAA,EAAAA,IAAOiE,EAAY,cACnB,IAAI4C,EAAW,CAAEjE,QAGbkE,GAAiC9G,EAAAA,EAAAA,KAAO,CAAC+G,EAAgBC,KAC3D,IAAIC,EAAW,GACf,IAAK,IAAIrC,EAAI,EAAGA,EAAImC,EAAeG,kBAAmBtC,IAAK,CACzD,MAAMuC,EAAaJ,EAAe,SAASnC,KAC3CqC,GAAY,qBACArC,qBACJuC,mBACDA,2BACQH,EAAaI,gCACnBD,2BACMH,EAAaK,iDAEZzC,oBACTuC,2BACQH,EAAaI,gCACnBD,iBAGX,CACA,OAAOF,CAAQ,GACd,kBACCK,GAAyCtH,EAAAA,EAAAA,KAAQF,IACnD,MAAMyH,GAAwBC,EAAAA,EAAAA,MACxBC,GAAgBvH,EAAAA,EAAAA,MAChB6G,GAAiB9G,EAAAA,EAAAA,IAAcsH,EAAuBE,EAAcV,gBAE1E,MAAO,CAAEA,iBAAgBC,cADJ/G,EAAAA,EAAAA,IAAc8G,EAAejH,MAAOA,GAClB,GACtC,0BAoCC4H,EAAU,CACZpF,SACAhB,KACAuF,WACAc,QAvC2B3H,EAAAA,EAAAA,KAAO,WAAoB,IAAnB,MAAEF,GAAO8H,UAAA3G,OAAA,QAAA4G,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAChD,MAAM,eAAEb,EAAc,aAAEC,GAAiBM,EAAuBxH,GAChE,MAAO,qCAEMiH,EAAee,yBACnBf,EAAegB,gHAKdf,EAAagB,iCACPhB,EAAaiB,wHAKhBjB,EAAakB,oCACjBlB,EAAagB,mDAGdhB,EAAamB,sCACLnB,EAAaoB,kCACnBpB,EAAamB,sCACPnB,EAAaqB,6FAIhBrB,EAAasB,8DAGzBxB,EAAeC,EAAgBC,QAElC,GAAG,U,iBC1TH,SAAS3E,EAAiBD,EAAKd,GACzBc,EAAImG,UACNjH,EAAGY,oBAAoBE,EAAImG,UAEzBnG,EAAIoG,UACNlH,EAAGO,cAAcO,EAAIoG,UAEnBpG,EAAIe,OACN7B,EAAGS,kBAAkBK,EAAIe,MAE7B,C,kBACAnD,E,QAAAA,IAAOqC,EAAkB,mB", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/diagram-SSKATNLV.mjs", "../../node_modules/mermaid/dist/chunks/mermaid.core/chunk-4BMEZGHF.mjs"], "sourcesContent": ["import {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  clear,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  getThemeVariables,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/radar/db.ts\nvar defaultOptions = {\n  showLegend: true,\n  ticks: 5,\n  max: null,\n  min: 0,\n  graticule: \"circle\"\n};\nvar defaultRadarData = {\n  axes: [],\n  curves: [],\n  options: defaultOptions\n};\nvar data = structuredClone(defaultRadarData);\nvar DEFAULT_RADAR_CONFIG = defaultConfig_default.radar;\nvar getConfig2 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_RADAR_CONFIG,\n    ...getConfig().radar\n  });\n  return config;\n}, \"getConfig\");\nvar getAxes = /* @__PURE__ */ __name(() => data.axes, \"getAxes\");\nvar getCurves = /* @__PURE__ */ __name(() => data.curves, \"getCurves\");\nvar getOptions = /* @__PURE__ */ __name(() => data.options, \"getOptions\");\nvar setAxes = /* @__PURE__ */ __name((axes) => {\n  data.axes = axes.map((axis) => {\n    return {\n      name: axis.name,\n      label: axis.label ?? axis.name\n    };\n  });\n}, \"setAxes\");\nvar setCurves = /* @__PURE__ */ __name((curves) => {\n  data.curves = curves.map((curve) => {\n    return {\n      name: curve.name,\n      label: curve.label ?? curve.name,\n      entries: computeCurveEntries(curve.entries)\n    };\n  });\n}, \"setCurves\");\nvar computeCurveEntries = /* @__PURE__ */ __name((entries) => {\n  if (entries[0].axis == void 0) {\n    return entries.map((entry) => entry.value);\n  }\n  const axes = getAxes();\n  if (axes.length === 0) {\n    throw new Error(\"Axes must be populated before curves for reference entries\");\n  }\n  return axes.map((axis) => {\n    const entry = entries.find((entry2) => entry2.axis?.$refText === axis.name);\n    if (entry === void 0) {\n      throw new Error(\"Missing entry for axis \" + axis.label);\n    }\n    return entry.value;\n  });\n}, \"computeCurveEntries\");\nvar setOptions = /* @__PURE__ */ __name((options) => {\n  const optionMap = options.reduce(\n    (acc, option) => {\n      acc[option.name] = option;\n      return acc;\n    },\n    {}\n  );\n  data.options = {\n    showLegend: optionMap.showLegend?.value ?? defaultOptions.showLegend,\n    ticks: optionMap.ticks?.value ?? defaultOptions.ticks,\n    max: optionMap.max?.value ?? defaultOptions.max,\n    min: optionMap.min?.value ?? defaultOptions.min,\n    graticule: optionMap.graticule?.value ?? defaultOptions.graticule\n  };\n}, \"setOptions\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  clear();\n  data = structuredClone(defaultRadarData);\n}, \"clear\");\nvar db = {\n  getAxes,\n  getCurves,\n  getOptions,\n  setAxes,\n  setCurves,\n  setOptions,\n  getConfig: getConfig2,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription\n};\n\n// src/diagrams/radar/parser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar populate = /* @__PURE__ */ __name((ast) => {\n  populateCommonDb(ast, db);\n  const { axes, curves, options } = ast;\n  db.setAxes(axes);\n  db.setCurves(curves);\n  db.setOptions(options);\n}, \"populate\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"radar\", input);\n    log.debug(ast);\n    populate(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/radar/renderer.ts\nvar draw = /* @__PURE__ */ __name((_text, id, _version, diagram2) => {\n  const db2 = diagram2.db;\n  const axes = db2.getAxes();\n  const curves = db2.getCurves();\n  const options = db2.getOptions();\n  const config = db2.getConfig();\n  const title = db2.getDiagramTitle();\n  const svg = selectSvgElement(id);\n  const g = drawFrame(svg, config);\n  const maxValue = options.max ?? Math.max(...curves.map((curve) => Math.max(...curve.entries)));\n  const minValue = options.min;\n  const radius = Math.min(config.width, config.height) / 2;\n  drawGraticule(g, axes, radius, options.ticks, options.graticule);\n  drawAxes(g, axes, radius, config);\n  drawCurves(g, axes, curves, minValue, maxValue, options.graticule, config);\n  drawLegend(g, curves, options.showLegend, config);\n  g.append(\"text\").attr(\"class\", \"radarTitle\").text(title).attr(\"x\", 0).attr(\"y\", -config.height / 2 - config.marginTop);\n}, \"draw\");\nvar drawFrame = /* @__PURE__ */ __name((svg, config) => {\n  const totalWidth = config.width + config.marginLeft + config.marginRight;\n  const totalHeight = config.height + config.marginTop + config.marginBottom;\n  const center = {\n    x: config.marginLeft + config.width / 2,\n    y: config.marginTop + config.height / 2\n  };\n  svg.attr(\"viewbox\", `0 0 ${totalWidth} ${totalHeight}`).attr(\"width\", totalWidth).attr(\"height\", totalHeight);\n  return svg.append(\"g\").attr(\"transform\", `translate(${center.x}, ${center.y})`);\n}, \"drawFrame\");\nvar drawGraticule = /* @__PURE__ */ __name((g, axes, radius, ticks, graticule) => {\n  if (graticule === \"circle\") {\n    for (let i = 0; i < ticks; i++) {\n      const r = radius * (i + 1) / ticks;\n      g.append(\"circle\").attr(\"r\", r).attr(\"class\", \"radarGraticule\");\n    }\n  } else if (graticule === \"polygon\") {\n    const numAxes = axes.length;\n    for (let i = 0; i < ticks; i++) {\n      const r = radius * (i + 1) / ticks;\n      const points = axes.map((_, j) => {\n        const angle = 2 * j * Math.PI / numAxes - Math.PI / 2;\n        const x = r * Math.cos(angle);\n        const y = r * Math.sin(angle);\n        return `${x},${y}`;\n      }).join(\" \");\n      g.append(\"polygon\").attr(\"points\", points).attr(\"class\", \"radarGraticule\");\n    }\n  }\n}, \"drawGraticule\");\nvar drawAxes = /* @__PURE__ */ __name((g, axes, radius, config) => {\n  const numAxes = axes.length;\n  for (let i = 0; i < numAxes; i++) {\n    const label = axes[i].label;\n    const angle = 2 * i * Math.PI / numAxes - Math.PI / 2;\n    g.append(\"line\").attr(\"x1\", 0).attr(\"y1\", 0).attr(\"x2\", radius * config.axisScaleFactor * Math.cos(angle)).attr(\"y2\", radius * config.axisScaleFactor * Math.sin(angle)).attr(\"class\", \"radarAxisLine\");\n    g.append(\"text\").text(label).attr(\"x\", radius * config.axisLabelFactor * Math.cos(angle)).attr(\"y\", radius * config.axisLabelFactor * Math.sin(angle)).attr(\"class\", \"radarAxisLabel\");\n  }\n}, \"drawAxes\");\nfunction drawCurves(g, axes, curves, minValue, maxValue, graticule, config) {\n  const numAxes = axes.length;\n  const radius = Math.min(config.width, config.height) / 2;\n  curves.forEach((curve, index) => {\n    if (curve.entries.length !== numAxes) {\n      return;\n    }\n    const points = curve.entries.map((entry, i) => {\n      const angle = 2 * Math.PI * i / numAxes - Math.PI / 2;\n      const r = relativeRadius(entry, minValue, maxValue, radius);\n      const x = r * Math.cos(angle);\n      const y = r * Math.sin(angle);\n      return { x, y };\n    });\n    if (graticule === \"circle\") {\n      g.append(\"path\").attr(\"d\", closedRoundCurve(points, config.curveTension)).attr(\"class\", `radarCurve-${index}`);\n    } else if (graticule === \"polygon\") {\n      g.append(\"polygon\").attr(\"points\", points.map((p) => `${p.x},${p.y}`).join(\" \")).attr(\"class\", `radarCurve-${index}`);\n    }\n  });\n}\n__name(drawCurves, \"drawCurves\");\nfunction relativeRadius(value, minValue, maxValue, radius) {\n  const clippedValue = Math.min(Math.max(value, minValue), maxValue);\n  return radius * (clippedValue - minValue) / (maxValue - minValue);\n}\n__name(relativeRadius, \"relativeRadius\");\nfunction closedRoundCurve(points, tension) {\n  const numPoints = points.length;\n  let d = `M${points[0].x},${points[0].y}`;\n  for (let i = 0; i < numPoints; i++) {\n    const p0 = points[(i - 1 + numPoints) % numPoints];\n    const p1 = points[i];\n    const p2 = points[(i + 1) % numPoints];\n    const p3 = points[(i + 2) % numPoints];\n    const cp1 = {\n      x: p1.x + (p2.x - p0.x) * tension,\n      y: p1.y + (p2.y - p0.y) * tension\n    };\n    const cp2 = {\n      x: p2.x - (p3.x - p1.x) * tension,\n      y: p2.y - (p3.y - p1.y) * tension\n    };\n    d += ` C${cp1.x},${cp1.y} ${cp2.x},${cp2.y} ${p2.x},${p2.y}`;\n  }\n  return `${d} Z`;\n}\n__name(closedRoundCurve, \"closedRoundCurve\");\nfunction drawLegend(g, curves, showLegend, config) {\n  if (!showLegend) {\n    return;\n  }\n  const legendX = (config.width / 2 + config.marginRight) * 3 / 4;\n  const legendY = -(config.height / 2 + config.marginTop) * 3 / 4;\n  const lineHeight = 20;\n  curves.forEach((curve, index) => {\n    const itemGroup = g.append(\"g\").attr(\"transform\", `translate(${legendX}, ${legendY + index * lineHeight})`);\n    itemGroup.append(\"rect\").attr(\"width\", 12).attr(\"height\", 12).attr(\"class\", `radarLegendBox-${index}`);\n    itemGroup.append(\"text\").attr(\"x\", 16).attr(\"y\", 0).attr(\"class\", \"radarLegendText\").text(curve.label);\n  });\n}\n__name(drawLegend, \"drawLegend\");\nvar renderer = { draw };\n\n// src/diagrams/radar/styles.ts\nvar genIndexStyles = /* @__PURE__ */ __name((themeVariables, radarOptions) => {\n  let sections = \"\";\n  for (let i = 0; i < themeVariables.THEME_COLOR_LIMIT; i++) {\n    const indexColor = themeVariables[`cScale${i}`];\n    sections += `\n\t\t.radarCurve-${i} {\n\t\t\tcolor: ${indexColor};\n\t\t\tfill: ${indexColor};\n\t\t\tfill-opacity: ${radarOptions.curveOpacity};\n\t\t\tstroke: ${indexColor};\n\t\t\tstroke-width: ${radarOptions.curveStrokeWidth};\n\t\t}\n\t\t.radarLegendBox-${i} {\n\t\t\tfill: ${indexColor};\n\t\t\tfill-opacity: ${radarOptions.curveOpacity};\n\t\t\tstroke: ${indexColor};\n\t\t}\n\t\t`;\n  }\n  return sections;\n}, \"genIndexStyles\");\nvar buildRadarStyleOptions = /* @__PURE__ */ __name((radar) => {\n  const defaultThemeVariables = getThemeVariables();\n  const currentConfig = getConfig();\n  const themeVariables = cleanAndMerge(defaultThemeVariables, currentConfig.themeVariables);\n  const radarOptions = cleanAndMerge(themeVariables.radar, radar);\n  return { themeVariables, radarOptions };\n}, \"buildRadarStyleOptions\");\nvar styles = /* @__PURE__ */ __name(({ radar } = {}) => {\n  const { themeVariables, radarOptions } = buildRadarStyleOptions(radar);\n  return `\n\t.radarTitle {\n\t\tfont-size: ${themeVariables.fontSize};\n\t\tcolor: ${themeVariables.titleColor};\n\t\tdominant-baseline: hanging;\n\t\ttext-anchor: middle;\n\t}\n\t.radarAxisLine {\n\t\tstroke: ${radarOptions.axisColor};\n\t\tstroke-width: ${radarOptions.axisStrokeWidth};\n\t}\n\t.radarAxisLabel {\n\t\tdominant-baseline: middle;\n\t\ttext-anchor: middle;\n\t\tfont-size: ${radarOptions.axisLabelFontSize}px;\n\t\tcolor: ${radarOptions.axisColor};\n\t}\n\t.radarGraticule {\n\t\tfill: ${radarOptions.graticuleColor};\n\t\tfill-opacity: ${radarOptions.graticuleOpacity};\n\t\tstroke: ${radarOptions.graticuleColor};\n\t\tstroke-width: ${radarOptions.graticuleStrokeWidth};\n\t}\n\t.radarLegendText {\n\t\ttext-anchor: start;\n\t\tfont-size: ${radarOptions.legendFontSize}px;\n\t\tdominant-baseline: hanging;\n\t}\n\t${genIndexStyles(themeVariables, radarOptions)}\n\t`;\n}, \"styles\");\n\n// src/diagrams/radar/diagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles\n};\nexport {\n  diagram\n};\n", "import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/common/populateCommonDb.ts\nfunction populateCommonDb(ast, db) {\n  if (ast.accDescr) {\n    db.setAccDescription?.(ast.accDescr);\n  }\n  if (ast.accTitle) {\n    db.setAccTitle?.(ast.accTitle);\n  }\n  if (ast.title) {\n    db.setDiagramTitle?.(ast.title);\n  }\n}\n__name(populateCommonDb, \"populateCommonDb\");\n\nexport {\n  populateCommonDb\n};\n"], "names": ["defaultOptions", "showLegend", "ticks", "max", "min", "graticule", "defaultRadarData", "axes", "curves", "options", "data", "structuredClone", "DEFAULT_RADAR_CONFIG", "defaultConfig_default", "radar", "getConfig2", "__name", "cleanAndMerge", "getConfig", "getAxes", "getCurves", "getOptions", "setAxes", "map", "axis", "name", "label", "setCurves", "curve", "entries", "computeCurveEntries", "entry", "value", "length", "Error", "find", "entry2", "$refText", "db", "setOptions", "optionMap", "reduce", "acc", "option", "clear", "setAccTitle", "getAccTitle", "setDiagramTitle", "getDiagramTitle", "getAccDescription", "setAccDescription", "populate", "ast", "populateCommonDb", "parser", "parse", "async", "input", "log", "debug", "draw", "_text", "id", "_version", "diagram2", "db2", "config", "title", "svg", "selectSvgElement", "g", "draw<PERSON>rame", "maxValue", "Math", "minValue", "radius", "width", "height", "drawGraticule", "drawAxes", "drawCurves", "drawLegend", "append", "attr", "text", "marginTop", "totalWidth", "marginLeft", "marginRight", "totalHeight", "marginBottom", "center", "i", "r", "numAxes", "points", "_", "j", "angle", "PI", "cos", "sin", "join", "axisScaleFactor", "axisLabelFactor", "for<PERSON>ach", "index", "relativeRadius", "x", "y", "closedRoundCurve", "curveTension", "p", "tension", "numPoints", "d", "p0", "p1", "p2", "p3", "cp1", "cp2", "legendX", "legendY", "itemGroup", "renderer", "genIndexStyles", "themeVariables", "radarOptions", "sections", "THEME_COLOR_LIMIT", "indexColor", "curveOpacity", "curveStrokeWidth", "buildRadarStyleOptions", "defaultThemeVariables", "getThemeVariables", "currentConfig", "diagram", "styles", "arguments", "undefined", "fontSize", "titleColor", "axisColor", "axisStrokeWidth", "axisLabelFontSize", "graticuleColor", "graticuleOpacity", "graticuleStrokeWidth", "legendFontSize", "accDescr", "accTitle"], "sourceRoot": ""}