[{"C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\index.tsx": "1", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\App.tsx": "3", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\UMLDiagrameExtractor.tsx": "4", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\DetectionArrow.tsx": "5", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\AppStyles.tsx": "6", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\Login.tsx": "7", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\ImageUploader.tsx": "8", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\UMLDiagrameExtractorStyles.tsx": "9", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\AnalysisResults.tsx": "10", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\UserProfile.tsx": "11", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\convertToMermaid.ts": "12", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Documentation.tsx": "13", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\LoadingSpinner.tsx": "14", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\context\\HistoryContext.tsx": "15", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\History\\HistorySidebar.tsx": "16", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\context\\AuthContext.tsx": "17", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\config.ts": "18", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\EntityPopup.tsx": "19", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\LoginStyles.tsx": "20", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\EntityPopupStyles.ts": "21", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\History\\HistorySidebar.styles.ts": "22", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\convertToJava.ts": "23", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\HistoryAnalysisSection.tsx": "24", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\services\\HistoryAnalysisService.ts": "25", "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\ConflictResolutionModal.tsx": "26"}, {"size": 554, "mtime": 1743134091741, "results": "27", "hashOfConfig": "28"}, {"size": 425, "mtime": 1743134091738, "results": "29", "hashOfConfig": "28"}, {"size": 12965, "mtime": 1748458737439, "results": "30", "hashOfConfig": "28"}, {"size": 18981, "mtime": 1751130556630, "results": "31", "hashOfConfig": "28"}, {"size": 6311, "mtime": 1747995830066, "results": "32", "hashOfConfig": "28"}, {"size": 4894, "mtime": 1746607412296, "results": "33", "hashOfConfig": "28"}, {"size": 12097, "mtime": 1748509606093, "results": "34", "hashOfConfig": "28"}, {"size": 15040, "mtime": 1751137160004, "results": "35", "hashOfConfig": "28"}, {"size": 6735, "mtime": 1748353633295, "results": "36", "hashOfConfig": "28"}, {"size": 28685, "mtime": 1751218334010, "results": "37", "hashOfConfig": "28"}, {"size": 5997, "mtime": 1747998679613, "results": "38", "hashOfConfig": "28"}, {"size": 10444, "mtime": 1748363097655, "results": "39", "hashOfConfig": "28"}, {"size": 11661, "mtime": 1745590475474, "results": "40", "hashOfConfig": "28"}, {"size": 1538, "mtime": 1746536725382, "results": "41", "hashOfConfig": "28"}, {"size": 4852, "mtime": 1751217692569, "results": "42", "hashOfConfig": "28"}, {"size": 7061, "mtime": 1748518780466, "results": "43", "hashOfConfig": "28"}, {"size": 3550, "mtime": 1751217711360, "results": "44", "hashOfConfig": "28"}, {"size": 770, "mtime": 1748254620634, "results": "45", "hashOfConfig": "28"}, {"size": 12450, "mtime": 1751218316682, "results": "46", "hashOfConfig": "28"}, {"size": 6160, "mtime": 1748509603191, "results": "47", "hashOfConfig": "28"}, {"size": 9471, "mtime": 1751216633292, "results": "48", "hashOfConfig": "28"}, {"size": 3422, "mtime": 1748518747011, "results": "49", "hashOfConfig": "28"}, {"size": 9332, "mtime": 1751210954303, "results": "50", "hashOfConfig": "28"}, {"size": 25110, "mtime": 1751224382267, "results": "51", "hashOfConfig": "28"}, {"size": 10279, "mtime": 1751218226964, "results": "52", "hashOfConfig": "28"}, {"size": 13033, "mtime": 1751215875586, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "aiy5ot", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\UMLDiagrameExtractor.tsx", ["132", "133"], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\DetectionArrow.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\AppStyles.tsx", ["134"], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\Login.tsx", ["135"], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\ImageUploader.tsx", ["136"], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\UMLDiagrameExtractorStyles.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\AnalysisResults.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\UserProfile.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\convertToMermaid.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Documentation.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\context\\HistoryContext.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\History\\HistorySidebar.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\context\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\config.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\EntityPopup.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\Loginsignup\\LoginStyles.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\EntityPopupStyles.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\History\\HistorySidebar.styles.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\UmlDiagr\\convertToJava.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\HistoryAnalysisSection.tsx", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\services\\HistoryAnalysisService.ts", [], [], "C:\\Users\\<USER>\\FixTorchUMLDGM\\uml-detector\\src\\components\\imageUp\\ConflictResolutionModal.tsx", [], [], {"ruleId": "137", "severity": 1, "message": "138", "line": 64, "column": 6, "nodeType": "139", "endLine": 64, "endColumn": 16, "suggestions": "140"}, {"ruleId": "137", "severity": 1, "message": "141", "line": 103, "column": 6, "nodeType": "139", "endLine": 103, "endColumn": 27, "suggestions": "142"}, {"ruleId": "143", "severity": 1, "message": "144", "line": 1, "column": 8, "nodeType": "145", "messageId": "146", "endLine": 1, "endColumn": 13}, {"ruleId": "147", "severity": 1, "message": "148", "line": 263, "column": 19, "nodeType": "149", "endLine": 263, "endColumn": 57}, {"ruleId": "137", "severity": 1, "message": "150", "line": 276, "column": 6, "nodeType": "139", "endLine": 276, "endColumn": 45, "suggestions": "151"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'renderMermaidDiagram' and 'umlText'. Either include them or remove the dependency array.", "ArrayExpression", ["152"], "React Hook useEffect has a missing dependency: 'renderMermaidDiagram'. Either include it or remove the dependency array.", ["153"], "@typescript-eslint/no-unused-vars", "'React' is defined but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'addToHistoryOnce'. Either include it or remove the dependency array.", ["154"], {"desc": "155", "fix": "156"}, {"desc": "157", "fix": "158"}, {"desc": "159", "fix": "160"}, "Update the dependencies array to be: [darkMode, renderMermaidDiagram, umlText]", {"range": "161", "text": "162"}, "Update the dependencies array to be: [umlText, exportType, renderMermaidDiagram]", {"range": "163", "text": "164"}, "Update the dependencies array to be: [textUrl, imageUrl, onAnalysisComplete, addToHistoryOnce]", {"range": "165", "text": "166"}, [2249, 2259], "[darkMode, renderMermaidDiagram, umlText]", [3394, 3415], "[umlText, exportType, renderMermaidDiagram]", [9394, 9433], "[textUrl, imageUrl, onAnalysisComplete, addToHistoryOnce]"]