{"ast": null, "code": "// HistoryAnalysisService.ts - Service pour analyser l'historique et proposer des imports de classes\n\nexport class HistoryAnalysisService {\n  /**\n   * Recherche dans l'historique les diagrammes contenant une classe du même nom\n   */\n  static findMatchingDiagrams(targetClassName, historyItems, currentUserId, currentDiagramText) {\n    const matches = [];\n    historyItems.forEach(item => {\n      // Exclure le diagramme actuellement ouvert en comparant le contenu\n      if (currentDiagramText && item.extractedText === currentDiagramText) {\n        return;\n      }\n      const extractedClasses = this.extractClassesFromText(item.extractedText);\n      const matchingClasses = extractedClasses.filter(cls => cls.name.toLowerCase() === targetClassName.toLowerCase());\n      if (matchingClasses.length > 0) {\n        const similarity = this.calculateSimilarity(targetClassName, matchingClasses[0]);\n        const previewData = this.generatePreviewData(item, matchingClasses[0], extractedClasses);\n        matches.push({\n          historyItem: item,\n          matchingClasses,\n          similarity,\n          isShared: item.userId !== currentUserId,\n          hasAccess: this.checkAccess(item, currentUserId),\n          previewData\n        });\n      }\n    });\n    return matches;\n  }\n\n  /**\n   * Extrait les classes depuis le texte d'analyse\n   */\n  static extractClassesFromText(extractedText) {\n    const classes = [];\n    if (!extractedText) return classes;\n\n    // Diviser le texte par sections de classe\n    const classSections = extractedText.split(/class \\d+:/g).filter(section => section.trim());\n    classSections.forEach(section => {\n      var _cleanedSection$split;\n      const classNameMatch = section.match(/NOM_CLASSE:\\s*(.+)/);\n      if (!classNameMatch) return;\n      const className = classNameMatch[1].trim();\n\n      // Nettoyer la section en supprimant les parties de relations\n      let cleanedSection = section;\n\n      // Supprimer tout ce qui vient après les sections de résumé des relations\n      const relationsSummaryIndex = cleanedSection.indexOf('----- RÉSUMÉ DES RELATIONS -----');\n      if (relationsSummaryIndex !== -1) {\n        cleanedSection = cleanedSection.substring(0, relationsSummaryIndex);\n      }\n      const relationsDetectedIndex = cleanedSection.indexOf('----- RELATIONS DÉTECTÉES -----');\n      if (relationsDetectedIndex !== -1) {\n        cleanedSection = cleanedSection.substring(0, relationsDetectedIndex);\n      }\n\n      // Extraire les attributs\n      const attributesSection = ((_cleanedSection$split = cleanedSection.split('ATTRIBUTS:')[1]) === null || _cleanedSection$split === void 0 ? void 0 : _cleanedSection$split.split('MÉTHODES:')[0]) || '';\n      const attributes = attributesSection.split('\\n').map(attr => attr.trim()).filter(attr => attr && !attr.includes('NOM_CLASSE:') && !attr.startsWith('-----') && !attr.includes('RÉSUMÉ DES RELATIONS') && !attr.includes('RELATIONS DÉTECTÉES'));\n\n      // Extraire les méthodes\n      const methodsSection = cleanedSection.split('MÉTHODES:')[1] || '';\n      const methods = methodsSection.split('\\n').map(method => method.trim()).filter(method => method && !method.includes('class ') && !method.includes('RELATIONS') && !method.startsWith('-----') && !method.startsWith('•') &&\n      // Exclure les puces des relations\n      !method.includes('RÉSUMÉ DES RELATIONS') && !method.includes('RELATIONS DÉTECTÉES'));\n\n      // Exclure les classes vides (sans attributs ni méthodes)\n      if (attributes.length > 0 || methods.length > 0) {\n        classes.push({\n          name: className,\n          attributes,\n          methods\n        });\n      }\n    });\n    return classes;\n  }\n\n  /**\n   * Calcule la similarité entre deux classes\n   */\n  static calculateSimilarity(targetClassName, classData) {\n    // Similarité basée sur le nom (exact = 100%)\n    let similarity = targetClassName.toLowerCase() === classData.name.toLowerCase() ? 100 : 0;\n\n    // Bonus pour le nombre d'attributs et méthodes (plus il y en a, plus c'est intéressant)\n    const contentScore = Math.min((classData.attributes.length + classData.methods.length) * 5, 50);\n    similarity += contentScore;\n    return Math.min(similarity, 100);\n  }\n\n  /**\n   * Vérifie les droits d'accès à un diagramme\n   */\n  static checkAccess(item, currentUserId) {\n    // Pour l'instant, accès uniquement aux diagrammes de l'utilisateur\n    // TODO: Implémenter la logique de partage/collaboration\n    return item.userId === currentUserId;\n  }\n\n  /**\n   * Trie les résultats selon les critères spécifiés\n   */\n  static sortMatches(matches, sortOption) {\n    return [...matches].sort((a, b) => {\n      let comparison = 0;\n      switch (sortOption.key) {\n        case 'date':\n          comparison = a.historyItem.createdAt.getTime() - b.historyItem.createdAt.getTime();\n          break;\n        case 'similarity':\n          comparison = a.similarity - b.similarity;\n          break;\n        case 'name':\n          comparison = a.historyItem.title.localeCompare(b.historyItem.title);\n          break;\n      }\n      return sortOption.direction === 'desc' ? -comparison : comparison;\n    });\n  }\n\n  /**\n   * Fusionne les attributs et méthodes de plusieurs classes\n   */\n  static mergeClassData(currentClass, importedClasses, conflictResolution = 'merge') {\n    const mergedAttributes = [...currentClass.attributes];\n    const mergedMethods = [...currentClass.methods];\n    importedClasses.forEach(importedClass => {\n      // Fusionner les attributs\n      importedClass.attributes.forEach(attr => {\n        const exists = mergedAttributes.some(existing => this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr));\n        if (!exists) {\n          mergedAttributes.push(attr);\n        } else if (conflictResolution === 'replace') {\n          const index = mergedAttributes.findIndex(existing => this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr));\n          if (index !== -1) {\n            mergedAttributes[index] = attr;\n          }\n        }\n      });\n\n      // Fusionner les méthodes\n      importedClass.methods.forEach(method => {\n        const exists = mergedMethods.some(existing => this.normalizeMethodName(existing) === this.normalizeMethodName(method));\n        if (!exists) {\n          mergedMethods.push(method);\n        } else if (conflictResolution === 'replace') {\n          const index = mergedMethods.findIndex(existing => this.normalizeMethodName(existing) === this.normalizeMethodName(method));\n          if (index !== -1) {\n            mergedMethods[index] = method;\n          }\n        }\n      });\n    });\n    return {\n      name: currentClass.name,\n      attributes: mergedAttributes,\n      methods: mergedMethods\n    };\n  }\n\n  /**\n   * Normalise le nom d'un attribut pour la comparaison\n   */\n  static normalizeAttributeName(attribute) {\n    return attribute.toLowerCase().replace(/[^a-z0-9]/g, '');\n  }\n\n  /**\n   * Normalise le nom d'une méthode pour la comparaison\n   */\n  static normalizeMethodName(method) {\n    return method.toLowerCase().replace(/[^a-z0-9]/g, '');\n  }\n\n  /**\n   * Détecte les conflits potentiels lors de la fusion\n   */\n  static detectConflicts(currentClass, importedClasses) {\n    const conflictingAttributes = [];\n    const conflictingMethods = [];\n    importedClasses.forEach(importedClass => {\n      importedClass.attributes.forEach(attr => {\n        const exists = currentClass.attributes.some(existing => this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr));\n        if (exists && !conflictingAttributes.includes(attr)) {\n          conflictingAttributes.push(attr);\n        }\n      });\n      importedClass.methods.forEach(method => {\n        const exists = currentClass.methods.some(existing => this.normalizeMethodName(existing) === this.normalizeMethodName(method));\n        if (exists && !conflictingMethods.includes(method)) {\n          conflictingMethods.push(method);\n        }\n      });\n    });\n    return {\n      attributes: conflictingAttributes,\n      methods: conflictingMethods\n    };\n  }\n\n  /**\n   * Génère les données d'aperçu pour une correspondance\n   */\n  static generatePreviewData(historyItem, targetClass, allClasses) {\n    // Générer le contexte de la classe\n    const classContext = `${targetClass.name}\\n` + `Attributs: ${targetClass.attributes.length}\\n` + `Méthodes: ${targetClass.methods.length}`;\n\n    // Trouver les classes liées (mentionnées dans les relations ou ayant des attributs similaires)\n    const relatedClasses = allClasses.filter(cls => cls.name !== targetClass.name).map(cls => cls.name).slice(0, 3); // Limiter à 3 classes liées\n\n    return {\n      thumbnailUrl: historyItem.thumbnailUrl,\n      classContext,\n      relatedClasses\n    };\n  }\n\n  /**\n   * Vérifie si l'utilisateur a accès à un diagramme partagé\n   */\n  static hasSharedAccess(item, currentUserId) {\n    // TODO: Implémenter la logique de vérification des permissions partagées\n    // Pour l'instant, retourner false pour les diagrammes d'autres utilisateurs\n    return item.userId === currentUserId;\n  }\n\n  /**\n   * Marque les diagrammes partagés/collectifs\n   */\n  static getAccessLevel(item, currentUserId) {\n    if (item.userId === currentUserId) {\n      return 'owner';\n    }\n\n    // TODO: Implémenter la logique de partage\n    // Vérifier si le diagramme est partagé avec l'utilisateur\n    if (this.hasSharedAccess(item, currentUserId)) {\n      return 'shared';\n    }\n    return 'restricted';\n  }\n\n  /**\n   * Options de tri disponibles\n   */\n  static getSortOptions() {\n    return [{\n      key: 'similarity',\n      label: 'Similarité',\n      direction: 'desc'\n    }, {\n      key: 'date',\n      label: 'Date (récent)',\n      direction: 'desc'\n    }, {\n      key: 'date',\n      label: 'Date (ancien)',\n      direction: 'asc'\n    }, {\n      key: 'name',\n      label: 'Nom A-Z',\n      direction: 'asc'\n    }, {\n      key: 'name',\n      label: 'Nom Z-A',\n      direction: 'desc'\n    }];\n  }\n}", "map": {"version": 3, "names": ["HistoryAnalysisService", "findMatchingDiagrams", "targetClassName", "historyItems", "currentUserId", "currentDiagramText", "matches", "for<PERSON>ach", "item", "extractedText", "extractedClasses", "extractClassesFromText", "matchingClasses", "filter", "cls", "name", "toLowerCase", "length", "similarity", "calculateSimilarity", "previewData", "generatePreviewData", "push", "historyItem", "isShared", "userId", "hasAccess", "checkAccess", "classes", "classSections", "split", "section", "trim", "_cleanedSection$split", "classNameMatch", "match", "className", "cleanedSection", "relationsSummaryIndex", "indexOf", "substring", "relationsDetectedIndex", "attributesSection", "attributes", "map", "attr", "includes", "startsWith", "methodsSection", "methods", "method", "classData", "contentScore", "Math", "min", "sortMatches", "sortOption", "sort", "a", "b", "comparison", "key", "createdAt", "getTime", "title", "localeCompare", "direction", "mergeClassData", "currentClass", "importedClasses", "conflictResolution", "mergedAttributes", "mergedMethods", "importedClass", "exists", "some", "existing", "normalizeAttributeName", "index", "findIndex", "normalizeMethodName", "attribute", "replace", "detectConflicts", "conflictingAttributes", "conflictingMethods", "targetClass", "allClasses", "classContext", "relatedClasses", "slice", "thumbnailUrl", "hasSharedAccess", "getAccessLevel", "getSortOptions", "label"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/services/HistoryAnalysisService.ts"], "sourcesContent": ["// HistoryAnalysisService.ts - Service pour analyser l'historique et proposer des imports de classes\nimport { HistoryItem } from '../components/types/HistoryTypes';\n\nexport interface ClassData {\n  name: string;\n  attributes: string[];\n  methods: string[];\n}\n\nexport interface HistoryMatch {\n  historyItem: HistoryItem;\n  matchingClasses: ClassData[];\n  similarity: number;\n  isShared: boolean;\n  hasAccess: boolean;\n  previewData?: {\n    thumbnailUrl?: string;\n    classContext: string;\n    relatedClasses: string[];\n  };\n}\n\nexport interface SortOption {\n  key: 'date' | 'similarity' | 'name';\n  label: string;\n  direction: 'asc' | 'desc';\n}\n\nexport class HistoryAnalysisService {\n  \n  /**\n   * Recherche dans l'historique les diagrammes contenant une classe du même nom\n   */\n  static findMatchingDiagrams(\n    targetClassName: string,\n    historyItems: HistoryItem[],\n    currentUserId: string,\n    currentDiagramText?: string\n  ): HistoryMatch[] {\n    const matches: HistoryMatch[] = [];\n\n    historyItems.forEach(item => {\n      // Exclure le diagramme actuellement ouvert en comparant le contenu\n      if (currentDiagramText && item.extractedText === currentDiagramText) {\n        return;\n      }\n\n      const extractedClasses = this.extractClassesFromText(item.extractedText);\n      const matchingClasses = extractedClasses.filter(cls =>\n        cls.name.toLowerCase() === targetClassName.toLowerCase()\n      );\n\n      if (matchingClasses.length > 0) {\n        const similarity = this.calculateSimilarity(targetClassName, matchingClasses[0]);\n        const previewData = this.generatePreviewData(item, matchingClasses[0], extractedClasses);\n\n        matches.push({\n          historyItem: item,\n          matchingClasses,\n          similarity,\n          isShared: item.userId !== currentUserId,\n          hasAccess: this.checkAccess(item, currentUserId),\n          previewData\n        });\n      }\n    });\n\n    return matches;\n  }\n  \n  /**\n   * Extrait les classes depuis le texte d'analyse\n   */\n  static extractClassesFromText(extractedText: string): ClassData[] {\n    const classes: ClassData[] = [];\n\n    if (!extractedText) return classes;\n\n    // Diviser le texte par sections de classe\n    const classSections = extractedText.split(/class \\d+:/g).filter(section => section.trim());\n\n    classSections.forEach(section => {\n      const classNameMatch = section.match(/NOM_CLASSE:\\s*(.+)/);\n      if (!classNameMatch) return;\n\n      const className = classNameMatch[1].trim();\n\n      // Nettoyer la section en supprimant les parties de relations\n      let cleanedSection = section;\n\n      // Supprimer tout ce qui vient après les sections de résumé des relations\n      const relationsSummaryIndex = cleanedSection.indexOf('----- RÉSUMÉ DES RELATIONS -----');\n      if (relationsSummaryIndex !== -1) {\n        cleanedSection = cleanedSection.substring(0, relationsSummaryIndex);\n      }\n\n      const relationsDetectedIndex = cleanedSection.indexOf('----- RELATIONS DÉTECTÉES -----');\n      if (relationsDetectedIndex !== -1) {\n        cleanedSection = cleanedSection.substring(0, relationsDetectedIndex);\n      }\n\n      // Extraire les attributs\n      const attributesSection = cleanedSection.split('ATTRIBUTS:')[1]?.split('MÉTHODES:')[0] || '';\n      const attributes = attributesSection\n        .split('\\n')\n        .map(attr => attr.trim())\n        .filter(attr =>\n          attr &&\n          !attr.includes('NOM_CLASSE:') &&\n          !attr.startsWith('-----') &&\n          !attr.includes('RÉSUMÉ DES RELATIONS') &&\n          !attr.includes('RELATIONS DÉTECTÉES')\n        );\n\n      // Extraire les méthodes\n      const methodsSection = cleanedSection.split('MÉTHODES:')[1] || '';\n      const methods = methodsSection\n        .split('\\n')\n        .map(method => method.trim())\n        .filter(method =>\n          method &&\n          !method.includes('class ') &&\n          !method.includes('RELATIONS') &&\n          !method.startsWith('-----') &&\n          !method.startsWith('•') && // Exclure les puces des relations\n          !method.includes('RÉSUMÉ DES RELATIONS') &&\n          !method.includes('RELATIONS DÉTECTÉES')\n        );\n\n      // Exclure les classes vides (sans attributs ni méthodes)\n      if (attributes.length > 0 || methods.length > 0) {\n        classes.push({\n          name: className,\n          attributes,\n          methods\n        });\n      }\n    });\n\n    return classes;\n  }\n  \n  /**\n   * Calcule la similarité entre deux classes\n   */\n  static calculateSimilarity(targetClassName: string, classData: ClassData): number {\n    // Similarité basée sur le nom (exact = 100%)\n    let similarity = targetClassName.toLowerCase() === classData.name.toLowerCase() ? 100 : 0;\n    \n    // Bonus pour le nombre d'attributs et méthodes (plus il y en a, plus c'est intéressant)\n    const contentScore = Math.min((classData.attributes.length + classData.methods.length) * 5, 50);\n    similarity += contentScore;\n    \n    return Math.min(similarity, 100);\n  }\n  \n  /**\n   * Vérifie les droits d'accès à un diagramme\n   */\n  static checkAccess(item: HistoryItem, currentUserId: string): boolean {\n    // Pour l'instant, accès uniquement aux diagrammes de l'utilisateur\n    // TODO: Implémenter la logique de partage/collaboration\n    return item.userId === currentUserId;\n  }\n  \n  /**\n   * Trie les résultats selon les critères spécifiés\n   */\n  static sortMatches(matches: HistoryMatch[], sortOption: SortOption): HistoryMatch[] {\n    return [...matches].sort((a, b) => {\n      let comparison = 0;\n      \n      switch (sortOption.key) {\n        case 'date':\n          comparison = a.historyItem.createdAt.getTime() - b.historyItem.createdAt.getTime();\n          break;\n        case 'similarity':\n          comparison = a.similarity - b.similarity;\n          break;\n        case 'name':\n          comparison = a.historyItem.title.localeCompare(b.historyItem.title);\n          break;\n      }\n      \n      return sortOption.direction === 'desc' ? -comparison : comparison;\n    });\n  }\n  \n  /**\n   * Fusionne les attributs et méthodes de plusieurs classes\n   */\n  static mergeClassData(\n    currentClass: ClassData, \n    importedClasses: ClassData[], \n    conflictResolution: 'replace' | 'merge' | 'skip' = 'merge'\n  ): ClassData {\n    const mergedAttributes = [...currentClass.attributes];\n    const mergedMethods = [...currentClass.methods];\n    \n    importedClasses.forEach(importedClass => {\n      // Fusionner les attributs\n      importedClass.attributes.forEach(attr => {\n        const exists = mergedAttributes.some(existing => \n          this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)\n        );\n        \n        if (!exists) {\n          mergedAttributes.push(attr);\n        } else if (conflictResolution === 'replace') {\n          const index = mergedAttributes.findIndex(existing => \n            this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)\n          );\n          if (index !== -1) {\n            mergedAttributes[index] = attr;\n          }\n        }\n      });\n      \n      // Fusionner les méthodes\n      importedClass.methods.forEach(method => {\n        const exists = mergedMethods.some(existing => \n          this.normalizeMethodName(existing) === this.normalizeMethodName(method)\n        );\n        \n        if (!exists) {\n          mergedMethods.push(method);\n        } else if (conflictResolution === 'replace') {\n          const index = mergedMethods.findIndex(existing => \n            this.normalizeMethodName(existing) === this.normalizeMethodName(method)\n          );\n          if (index !== -1) {\n            mergedMethods[index] = method;\n          }\n        }\n      });\n    });\n    \n    return {\n      name: currentClass.name,\n      attributes: mergedAttributes,\n      methods: mergedMethods\n    };\n  }\n  \n  /**\n   * Normalise le nom d'un attribut pour la comparaison\n   */\n  static normalizeAttributeName(attribute: string): string {\n    return attribute.toLowerCase().replace(/[^a-z0-9]/g, '');\n  }\n  \n  /**\n   * Normalise le nom d'une méthode pour la comparaison\n   */\n  static normalizeMethodName(method: string): string {\n    return method.toLowerCase().replace(/[^a-z0-9]/g, '');\n  }\n  \n  /**\n   * Détecte les conflits potentiels lors de la fusion\n   */\n  static detectConflicts(\n    currentClass: ClassData, \n    importedClasses: ClassData[]\n  ): { attributes: string[], methods: string[] } {\n    const conflictingAttributes: string[] = [];\n    const conflictingMethods: string[] = [];\n    \n    importedClasses.forEach(importedClass => {\n      importedClass.attributes.forEach(attr => {\n        const exists = currentClass.attributes.some(existing => \n          this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)\n        );\n        if (exists && !conflictingAttributes.includes(attr)) {\n          conflictingAttributes.push(attr);\n        }\n      });\n      \n      importedClass.methods.forEach(method => {\n        const exists = currentClass.methods.some(existing => \n          this.normalizeMethodName(existing) === this.normalizeMethodName(method)\n        );\n        if (exists && !conflictingMethods.includes(method)) {\n          conflictingMethods.push(method);\n        }\n      });\n    });\n    \n    return { attributes: conflictingAttributes, methods: conflictingMethods };\n  }\n  \n  /**\n   * Génère les données d'aperçu pour une correspondance\n   */\n  static generatePreviewData(\n    historyItem: HistoryItem,\n    targetClass: ClassData,\n    allClasses: ClassData[]\n  ): { thumbnailUrl?: string; classContext: string; relatedClasses: string[] } {\n    // Générer le contexte de la classe\n    const classContext = `${targetClass.name}\\n` +\n      `Attributs: ${targetClass.attributes.length}\\n` +\n      `Méthodes: ${targetClass.methods.length}`;\n\n    // Trouver les classes liées (mentionnées dans les relations ou ayant des attributs similaires)\n    const relatedClasses = allClasses\n      .filter(cls => cls.name !== targetClass.name)\n      .map(cls => cls.name)\n      .slice(0, 3); // Limiter à 3 classes liées\n\n    return {\n      thumbnailUrl: historyItem.thumbnailUrl,\n      classContext,\n      relatedClasses\n    };\n  }\n\n  /**\n   * Vérifie si l'utilisateur a accès à un diagramme partagé\n   */\n  static hasSharedAccess(item: HistoryItem, currentUserId: string): boolean {\n    // TODO: Implémenter la logique de vérification des permissions partagées\n    // Pour l'instant, retourner false pour les diagrammes d'autres utilisateurs\n    return item.userId === currentUserId;\n  }\n\n  /**\n   * Marque les diagrammes partagés/collectifs\n   */\n  static getAccessLevel(item: HistoryItem, currentUserId: string): 'owner' | 'shared' | 'public' | 'restricted' {\n    if (item.userId === currentUserId) {\n      return 'owner';\n    }\n\n    // TODO: Implémenter la logique de partage\n    // Vérifier si le diagramme est partagé avec l'utilisateur\n    if (this.hasSharedAccess(item, currentUserId)) {\n      return 'shared';\n    }\n\n    return 'restricted';\n  }\n\n  /**\n   * Options de tri disponibles\n   */\n  static getSortOptions(): SortOption[] {\n    return [\n      { key: 'similarity', label: 'Similarité', direction: 'desc' },\n      { key: 'date', label: 'Date (récent)', direction: 'desc' },\n      { key: 'date', label: 'Date (ancien)', direction: 'asc' },\n      { key: 'name', label: 'Nom A-Z', direction: 'asc' },\n      { key: 'name', label: 'Nom Z-A', direction: 'desc' }\n    ];\n  }\n}\n"], "mappings": "AAAA;;AA4BA,OAAO,MAAMA,sBAAsB,CAAC;EAElC;AACF;AACA;EACE,OAAOC,oBAAoBA,CACzBC,eAAuB,EACvBC,YAA2B,EAC3BC,aAAqB,EACrBC,kBAA2B,EACX;IAChB,MAAMC,OAAuB,GAAG,EAAE;IAElCH,YAAY,CAACI,OAAO,CAACC,IAAI,IAAI;MAC3B;MACA,IAAIH,kBAAkB,IAAIG,IAAI,CAACC,aAAa,KAAKJ,kBAAkB,EAAE;QACnE;MACF;MAEA,MAAMK,gBAAgB,GAAG,IAAI,CAACC,sBAAsB,CAACH,IAAI,CAACC,aAAa,CAAC;MACxE,MAAMG,eAAe,GAAGF,gBAAgB,CAACG,MAAM,CAACC,GAAG,IACjDA,GAAG,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAKd,eAAe,CAACc,WAAW,CAAC,CACzD,CAAC;MAED,IAAIJ,eAAe,CAACK,MAAM,GAAG,CAAC,EAAE;QAC9B,MAAMC,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAACjB,eAAe,EAAEU,eAAe,CAAC,CAAC,CAAC,CAAC;QAChF,MAAMQ,WAAW,GAAG,IAAI,CAACC,mBAAmB,CAACb,IAAI,EAAEI,eAAe,CAAC,CAAC,CAAC,EAAEF,gBAAgB,CAAC;QAExFJ,OAAO,CAACgB,IAAI,CAAC;UACXC,WAAW,EAAEf,IAAI;UACjBI,eAAe;UACfM,UAAU;UACVM,QAAQ,EAAEhB,IAAI,CAACiB,MAAM,KAAKrB,aAAa;UACvCsB,SAAS,EAAE,IAAI,CAACC,WAAW,CAACnB,IAAI,EAAEJ,aAAa,CAAC;UAChDgB;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOd,OAAO;EAChB;;EAEA;AACF;AACA;EACE,OAAOK,sBAAsBA,CAACF,aAAqB,EAAe;IAChE,MAAMmB,OAAoB,GAAG,EAAE;IAE/B,IAAI,CAACnB,aAAa,EAAE,OAAOmB,OAAO;;IAElC;IACA,MAAMC,aAAa,GAAGpB,aAAa,CAACqB,KAAK,CAAC,aAAa,CAAC,CAACjB,MAAM,CAACkB,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;IAE1FH,aAAa,CAACtB,OAAO,CAACwB,OAAO,IAAI;MAAA,IAAAE,qBAAA;MAC/B,MAAMC,cAAc,GAAGH,OAAO,CAACI,KAAK,CAAC,oBAAoB,CAAC;MAC1D,IAAI,CAACD,cAAc,EAAE;MAErB,MAAME,SAAS,GAAGF,cAAc,CAAC,CAAC,CAAC,CAACF,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAIK,cAAc,GAAGN,OAAO;;MAE5B;MACA,MAAMO,qBAAqB,GAAGD,cAAc,CAACE,OAAO,CAAC,kCAAkC,CAAC;MACxF,IAAID,qBAAqB,KAAK,CAAC,CAAC,EAAE;QAChCD,cAAc,GAAGA,cAAc,CAACG,SAAS,CAAC,CAAC,EAAEF,qBAAqB,CAAC;MACrE;MAEA,MAAMG,sBAAsB,GAAGJ,cAAc,CAACE,OAAO,CAAC,iCAAiC,CAAC;MACxF,IAAIE,sBAAsB,KAAK,CAAC,CAAC,EAAE;QACjCJ,cAAc,GAAGA,cAAc,CAACG,SAAS,CAAC,CAAC,EAAEC,sBAAsB,CAAC;MACtE;;MAEA;MACA,MAAMC,iBAAiB,GAAG,EAAAT,qBAAA,GAAAI,cAAc,CAACP,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAAG,qBAAA,uBAArCA,qBAAA,CAAuCH,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAI,EAAE;MAC5F,MAAMa,UAAU,GAAGD,iBAAiB,CACjCZ,KAAK,CAAC,IAAI,CAAC,CACXc,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACb,IAAI,CAAC,CAAC,CAAC,CACxBnB,MAAM,CAACgC,IAAI,IACVA,IAAI,IACJ,CAACA,IAAI,CAACC,QAAQ,CAAC,aAAa,CAAC,IAC7B,CAACD,IAAI,CAACE,UAAU,CAAC,OAAO,CAAC,IACzB,CAACF,IAAI,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IACtC,CAACD,IAAI,CAACC,QAAQ,CAAC,qBAAqB,CACtC,CAAC;;MAEH;MACA,MAAME,cAAc,GAAGX,cAAc,CAACP,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;MACjE,MAAMmB,OAAO,GAAGD,cAAc,CAC3BlB,KAAK,CAAC,IAAI,CAAC,CACXc,GAAG,CAACM,MAAM,IAAIA,MAAM,CAAClB,IAAI,CAAC,CAAC,CAAC,CAC5BnB,MAAM,CAACqC,MAAM,IACZA,MAAM,IACN,CAACA,MAAM,CAACJ,QAAQ,CAAC,QAAQ,CAAC,IAC1B,CAACI,MAAM,CAACJ,QAAQ,CAAC,WAAW,CAAC,IAC7B,CAACI,MAAM,CAACH,UAAU,CAAC,OAAO,CAAC,IAC3B,CAACG,MAAM,CAACH,UAAU,CAAC,GAAG,CAAC;MAAI;MAC3B,CAACG,MAAM,CAACJ,QAAQ,CAAC,sBAAsB,CAAC,IACxC,CAACI,MAAM,CAACJ,QAAQ,CAAC,qBAAqB,CACxC,CAAC;;MAEH;MACA,IAAIH,UAAU,CAAC1B,MAAM,GAAG,CAAC,IAAIgC,OAAO,CAAChC,MAAM,GAAG,CAAC,EAAE;QAC/CW,OAAO,CAACN,IAAI,CAAC;UACXP,IAAI,EAAEqB,SAAS;UACfO,UAAU;UACVM;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOrB,OAAO;EAChB;;EAEA;AACF;AACA;EACE,OAAOT,mBAAmBA,CAACjB,eAAuB,EAAEiD,SAAoB,EAAU;IAChF;IACA,IAAIjC,UAAU,GAAGhB,eAAe,CAACc,WAAW,CAAC,CAAC,KAAKmC,SAAS,CAACpC,IAAI,CAACC,WAAW,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC;;IAEzF;IACA,MAAMoC,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACH,SAAS,CAACR,UAAU,CAAC1B,MAAM,GAAGkC,SAAS,CAACF,OAAO,CAAChC,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC;IAC/FC,UAAU,IAAIkC,YAAY;IAE1B,OAAOC,IAAI,CAACC,GAAG,CAACpC,UAAU,EAAE,GAAG,CAAC;EAClC;;EAEA;AACF;AACA;EACE,OAAOS,WAAWA,CAACnB,IAAiB,EAAEJ,aAAqB,EAAW;IACpE;IACA;IACA,OAAOI,IAAI,CAACiB,MAAM,KAAKrB,aAAa;EACtC;;EAEA;AACF;AACA;EACE,OAAOmD,WAAWA,CAACjD,OAAuB,EAAEkD,UAAsB,EAAkB;IAClF,OAAO,CAAC,GAAGlD,OAAO,CAAC,CAACmD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACjC,IAAIC,UAAU,GAAG,CAAC;MAElB,QAAQJ,UAAU,CAACK,GAAG;QACpB,KAAK,MAAM;UACTD,UAAU,GAAGF,CAAC,CAACnC,WAAW,CAACuC,SAAS,CAACC,OAAO,CAAC,CAAC,GAAGJ,CAAC,CAACpC,WAAW,CAACuC,SAAS,CAACC,OAAO,CAAC,CAAC;UAClF;QACF,KAAK,YAAY;UACfH,UAAU,GAAGF,CAAC,CAACxC,UAAU,GAAGyC,CAAC,CAACzC,UAAU;UACxC;QACF,KAAK,MAAM;UACT0C,UAAU,GAAGF,CAAC,CAACnC,WAAW,CAACyC,KAAK,CAACC,aAAa,CAACN,CAAC,CAACpC,WAAW,CAACyC,KAAK,CAAC;UACnE;MACJ;MAEA,OAAOR,UAAU,CAACU,SAAS,KAAK,MAAM,GAAG,CAACN,UAAU,GAAGA,UAAU;IACnE,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,OAAOO,cAAcA,CACnBC,YAAuB,EACvBC,eAA4B,EAC5BC,kBAAgD,GAAG,OAAO,EAC/C;IACX,MAAMC,gBAAgB,GAAG,CAAC,GAAGH,YAAY,CAACzB,UAAU,CAAC;IACrD,MAAM6B,aAAa,GAAG,CAAC,GAAGJ,YAAY,CAACnB,OAAO,CAAC;IAE/CoB,eAAe,CAAC9D,OAAO,CAACkE,aAAa,IAAI;MACvC;MACAA,aAAa,CAAC9B,UAAU,CAACpC,OAAO,CAACsC,IAAI,IAAI;QACvC,MAAM6B,MAAM,GAAGH,gBAAgB,CAACI,IAAI,CAACC,QAAQ,IAC3C,IAAI,CAACC,sBAAsB,CAACD,QAAQ,CAAC,KAAK,IAAI,CAACC,sBAAsB,CAAChC,IAAI,CAC5E,CAAC;QAED,IAAI,CAAC6B,MAAM,EAAE;UACXH,gBAAgB,CAACjD,IAAI,CAACuB,IAAI,CAAC;QAC7B,CAAC,MAAM,IAAIyB,kBAAkB,KAAK,SAAS,EAAE;UAC3C,MAAMQ,KAAK,GAAGP,gBAAgB,CAACQ,SAAS,CAACH,QAAQ,IAC/C,IAAI,CAACC,sBAAsB,CAACD,QAAQ,CAAC,KAAK,IAAI,CAACC,sBAAsB,CAAChC,IAAI,CAC5E,CAAC;UACD,IAAIiC,KAAK,KAAK,CAAC,CAAC,EAAE;YAChBP,gBAAgB,CAACO,KAAK,CAAC,GAAGjC,IAAI;UAChC;QACF;MACF,CAAC,CAAC;;MAEF;MACA4B,aAAa,CAACxB,OAAO,CAAC1C,OAAO,CAAC2C,MAAM,IAAI;QACtC,MAAMwB,MAAM,GAAGF,aAAa,CAACG,IAAI,CAACC,QAAQ,IACxC,IAAI,CAACI,mBAAmB,CAACJ,QAAQ,CAAC,KAAK,IAAI,CAACI,mBAAmB,CAAC9B,MAAM,CACxE,CAAC;QAED,IAAI,CAACwB,MAAM,EAAE;UACXF,aAAa,CAAClD,IAAI,CAAC4B,MAAM,CAAC;QAC5B,CAAC,MAAM,IAAIoB,kBAAkB,KAAK,SAAS,EAAE;UAC3C,MAAMQ,KAAK,GAAGN,aAAa,CAACO,SAAS,CAACH,QAAQ,IAC5C,IAAI,CAACI,mBAAmB,CAACJ,QAAQ,CAAC,KAAK,IAAI,CAACI,mBAAmB,CAAC9B,MAAM,CACxE,CAAC;UACD,IAAI4B,KAAK,KAAK,CAAC,CAAC,EAAE;YAChBN,aAAa,CAACM,KAAK,CAAC,GAAG5B,MAAM;UAC/B;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MACLnC,IAAI,EAAEqD,YAAY,CAACrD,IAAI;MACvB4B,UAAU,EAAE4B,gBAAgB;MAC5BtB,OAAO,EAAEuB;IACX,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOK,sBAAsBA,CAACI,SAAiB,EAAU;IACvD,OAAOA,SAAS,CAACjE,WAAW,CAAC,CAAC,CAACkE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;EAC1D;;EAEA;AACF;AACA;EACE,OAAOF,mBAAmBA,CAAC9B,MAAc,EAAU;IACjD,OAAOA,MAAM,CAAClC,WAAW,CAAC,CAAC,CAACkE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;EACvD;;EAEA;AACF;AACA;EACE,OAAOC,eAAeA,CACpBf,YAAuB,EACvBC,eAA4B,EACiB;IAC7C,MAAMe,qBAA+B,GAAG,EAAE;IAC1C,MAAMC,kBAA4B,GAAG,EAAE;IAEvChB,eAAe,CAAC9D,OAAO,CAACkE,aAAa,IAAI;MACvCA,aAAa,CAAC9B,UAAU,CAACpC,OAAO,CAACsC,IAAI,IAAI;QACvC,MAAM6B,MAAM,GAAGN,YAAY,CAACzB,UAAU,CAACgC,IAAI,CAACC,QAAQ,IAClD,IAAI,CAACC,sBAAsB,CAACD,QAAQ,CAAC,KAAK,IAAI,CAACC,sBAAsB,CAAChC,IAAI,CAC5E,CAAC;QACD,IAAI6B,MAAM,IAAI,CAACU,qBAAqB,CAACtC,QAAQ,CAACD,IAAI,CAAC,EAAE;UACnDuC,qBAAqB,CAAC9D,IAAI,CAACuB,IAAI,CAAC;QAClC;MACF,CAAC,CAAC;MAEF4B,aAAa,CAACxB,OAAO,CAAC1C,OAAO,CAAC2C,MAAM,IAAI;QACtC,MAAMwB,MAAM,GAAGN,YAAY,CAACnB,OAAO,CAAC0B,IAAI,CAACC,QAAQ,IAC/C,IAAI,CAACI,mBAAmB,CAACJ,QAAQ,CAAC,KAAK,IAAI,CAACI,mBAAmB,CAAC9B,MAAM,CACxE,CAAC;QACD,IAAIwB,MAAM,IAAI,CAACW,kBAAkB,CAACvC,QAAQ,CAACI,MAAM,CAAC,EAAE;UAClDmC,kBAAkB,CAAC/D,IAAI,CAAC4B,MAAM,CAAC;QACjC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MAAEP,UAAU,EAAEyC,qBAAqB;MAAEnC,OAAO,EAAEoC;IAAmB,CAAC;EAC3E;;EAEA;AACF;AACA;EACE,OAAOhE,mBAAmBA,CACxBE,WAAwB,EACxB+D,WAAsB,EACtBC,UAAuB,EACoD;IAC3E;IACA,MAAMC,YAAY,GAAG,GAAGF,WAAW,CAACvE,IAAI,IAAI,GAC1C,cAAcuE,WAAW,CAAC3C,UAAU,CAAC1B,MAAM,IAAI,GAC/C,aAAaqE,WAAW,CAACrC,OAAO,CAAChC,MAAM,EAAE;;IAE3C;IACA,MAAMwE,cAAc,GAAGF,UAAU,CAC9B1E,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAKuE,WAAW,CAACvE,IAAI,CAAC,CAC5C6B,GAAG,CAAC9B,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CACpB2E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhB,OAAO;MACLC,YAAY,EAAEpE,WAAW,CAACoE,YAAY;MACtCH,YAAY;MACZC;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOG,eAAeA,CAACpF,IAAiB,EAAEJ,aAAqB,EAAW;IACxE;IACA;IACA,OAAOI,IAAI,CAACiB,MAAM,KAAKrB,aAAa;EACtC;;EAEA;AACF;AACA;EACE,OAAOyF,cAAcA,CAACrF,IAAiB,EAAEJ,aAAqB,EAAgD;IAC5G,IAAII,IAAI,CAACiB,MAAM,KAAKrB,aAAa,EAAE;MACjC,OAAO,OAAO;IAChB;;IAEA;IACA;IACA,IAAI,IAAI,CAACwF,eAAe,CAACpF,IAAI,EAAEJ,aAAa,CAAC,EAAE;MAC7C,OAAO,QAAQ;IACjB;IAEA,OAAO,YAAY;EACrB;;EAEA;AACF;AACA;EACE,OAAO0F,cAAcA,CAAA,EAAiB;IACpC,OAAO,CACL;MAAEjC,GAAG,EAAE,YAAY;MAAEkC,KAAK,EAAE,YAAY;MAAE7B,SAAS,EAAE;IAAO,CAAC,EAC7D;MAAEL,GAAG,EAAE,MAAM;MAAEkC,KAAK,EAAE,eAAe;MAAE7B,SAAS,EAAE;IAAO,CAAC,EAC1D;MAAEL,GAAG,EAAE,MAAM;MAAEkC,KAAK,EAAE,eAAe;MAAE7B,SAAS,EAAE;IAAM,CAAC,EACzD;MAAEL,GAAG,EAAE,MAAM;MAAEkC,KAAK,EAAE,SAAS;MAAE7B,SAAS,EAAE;IAAM,CAAC,EACnD;MAAEL,GAAG,EAAE,MAAM;MAAEkC,KAAK,EAAE,SAAS;MAAE7B,SAAS,EAAE;IAAO,CAAC,CACrD;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}