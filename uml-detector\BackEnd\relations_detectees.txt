

----- R<PERSON>SUMÉ DES RELATIONS -----
• 1 relation(s) de type association
• 3 relation(s) de type one-way-association
• 1 relation(s) de type generalization
• 2 relation(s) de type endpoint

----- RELATIONS DÉTECTÉES -----
• Il y a une relation de association entre ville et adresse
• Admin est associé à Transporteur de façon unidirectionnelle (association unidirectionnelle)
• Client hérite de Avis (héritage (généralisation))
• demande est associé à G_map de façon unidirectionnelle (association unidirectionnelle)
• Il y a une relation de association entre adresse et G_map
• Il y a une relation de association entre Client et demande
• demande est associé à G_map de façon unidirectionnelle (association unidirectionnelle)