

----- R<PERSON>SUMÉ DES RELATIONS -----
• 2 relation(s) de type generalization
• 5 relation(s) de type endpoint

----- RELATIONS DÉTECTÉES -----
• Checking hérite de Account (héritage (généralisation))
• Savings hérite de Account (héritage (généralisation))
• Il y a une relation de association entre Customer et Teller
• Il y a une relation de association entre Customer et Loan
• Il y a une relation de association entre Customer et Account
• Il y a une relation de association entre Bank et Teller
• Il y a une relation de association entre Bank et Customer