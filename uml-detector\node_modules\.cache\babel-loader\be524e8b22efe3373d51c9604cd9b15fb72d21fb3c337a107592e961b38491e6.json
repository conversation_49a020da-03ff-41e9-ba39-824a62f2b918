{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\imageUp\\\\HistoryAnalysisSection.tsx\",\n  _s = $RefreshSig$();\n// HistoryAnalysisSection.tsx - Composant pour l'analyse historique et l'import de classes\nimport React, { useState, useEffect } from 'react';\nimport { useHistory } from '../../context/HistoryContext';\nimport { useAuth } from '../../context/AuthContext';\nimport { HistoryAnalysisService } from '../../services/HistoryAnalysisService';\nimport { ChevronDown, Eye, Clock, Zap, AlertTriangle, Shield, Users, Lock } from 'lucide-react';\nimport './HistoryAnalysisSection.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HistoryAnalysisSection = ({\n  darkMode,\n  targetClassName,\n  currentClassData,\n  onImport,\n  currentDiagramText\n}) => {\n  _s();\n  const {\n    historyItems\n  } = useHistory();\n  const {\n    currentUser\n  } = useAuth();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [matches, setMatches] = useState([]);\n  const [selectedMatches, setSelectedMatches] = useState(new Set());\n  const [selectedAttributes, setSelectedAttributes] = useState(new Map());\n  const [selectedMethods, setSelectedMethods] = useState(new Map());\n  const [sortOption, setSortOption] = useState(HistoryAnalysisService.getSortOptions()[0]);\n  const [showPreview, setShowPreview] = useState(null);\n  const [conflicts, setConflicts] = useState({\n    attributes: [],\n    methods: []\n  });\n  const [previewData, setPreviewData] = useState(null);\n\n  // Rechercher les correspondances lors du changement de classe cible\n  useEffect(() => {\n    if (!targetClassName || !currentUser) return;\n    const foundMatches = HistoryAnalysisService.findMatchingDiagrams(targetClassName, historyItems, currentUser.uid, currentDiagramText);\n    const sortedMatches = HistoryAnalysisService.sortMatches(foundMatches, sortOption);\n    setMatches(sortedMatches);\n  }, [targetClassName, historyItems, currentUser, sortOption, currentDiagramText]);\n\n  // Détecter les conflits lors du changement de sélection\n  useEffect(() => {\n    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));\n    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);\n    if (selectedClasses.length > 0) {\n      const detectedConflicts = HistoryAnalysisService.detectConflicts(currentClassData, selectedClasses);\n      setConflicts(detectedConflicts);\n    } else {\n      setConflicts({\n        attributes: [],\n        methods: []\n      });\n    }\n  }, [selectedMatches, matches, currentClassData]);\n  const handleToggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n  const handleMatchSelection = (matchId, selected) => {\n    const newSelection = new Set(selectedMatches);\n    if (selected) {\n      newSelection.add(matchId);\n    } else {\n      newSelection.delete(matchId);\n      // Nettoyer les sélections d'attributs/méthodes pour ce match\n      const newSelectedAttributes = new Map(selectedAttributes);\n      const newSelectedMethods = new Map(selectedMethods);\n      newSelectedAttributes.delete(matchId);\n      newSelectedMethods.delete(matchId);\n      setSelectedAttributes(newSelectedAttributes);\n      setSelectedMethods(newSelectedMethods);\n    }\n    setSelectedMatches(newSelection);\n  };\n  const handleAttributeSelection = (matchId, attribute, selected) => {\n    const newSelectedAttributes = new Map(selectedAttributes);\n    const currentAttributes = newSelectedAttributes.get(matchId) || new Set();\n    if (selected) {\n      currentAttributes.add(attribute);\n    } else {\n      currentAttributes.delete(attribute);\n    }\n    if (currentAttributes.size > 0) {\n      newSelectedAttributes.set(matchId, currentAttributes);\n    } else {\n      newSelectedAttributes.delete(matchId);\n    }\n    setSelectedAttributes(newSelectedAttributes);\n  };\n  const handleMethodSelection = (matchId, method, selected) => {\n    const newSelectedMethods = new Map(selectedMethods);\n    const currentMethods = newSelectedMethods.get(matchId) || new Set();\n    if (selected) {\n      currentMethods.add(method);\n    } else {\n      currentMethods.delete(method);\n    }\n    if (currentMethods.size > 0) {\n      newSelectedMethods.set(matchId, currentMethods);\n    } else {\n      newSelectedMethods.delete(matchId);\n    }\n    setSelectedMethods(newSelectedMethods);\n  };\n\n  // Fonction pour vérifier si tous les éléments d'un match sont sélectionnés\n  const isAllSelectedForMatch = match => {\n    const matchId = match.historyItem.id;\n    const matchingClass = match.matchingClasses[0];\n    if (!matchingClass) return false;\n    const selectedAttrs = selectedAttributes.get(matchId) || new Set();\n    const selectedMethodsSet = selectedMethods.get(matchId) || new Set();\n    const allAttrsSelected = matchingClass.attributes.every(attr => selectedAttrs.has(attr));\n    const allMethodsSelected = matchingClass.methods.every(method => selectedMethodsSet.has(method));\n    return allAttrsSelected && allMethodsSelected && (matchingClass.attributes.length > 0 || matchingClass.methods.length > 0);\n  };\n\n  // Fonction pour cocher/décocher tous les éléments d'un match\n  const handleCheckAll = (match, checked) => {\n    const matchId = match.historyItem.id;\n    const matchingClass = match.matchingClasses[0];\n    if (!matchingClass) return;\n    if (checked) {\n      // Cocher tous les attributs et méthodes\n      setSelectedAttributes(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set(matchingClass.attributes));\n        return newMap;\n      });\n      setSelectedMethods(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set(matchingClass.methods));\n        return newMap;\n      });\n    } else {\n      // Décocher tous les attributs et méthodes\n      setSelectedAttributes(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set());\n        return newMap;\n      });\n      setSelectedMethods(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set());\n        return newMap;\n      });\n    }\n  };\n  const handleImport = () => {\n    // Construire les données à importer basées sur les sélections granulaires\n    const attributesToImport = [];\n    const methodsToImport = [];\n\n    // Collecter tous les attributs et méthodes sélectionnés\n    selectedAttributes.forEach((attributes, matchId) => {\n      attributes.forEach(attr => {\n        if (!attributesToImport.includes(attr)) {\n          attributesToImport.push(attr);\n        }\n      });\n    });\n    selectedMethods.forEach((methods, matchId) => {\n      methods.forEach(method => {\n        if (!methodsToImport.includes(method)) {\n          methodsToImport.push(method);\n        }\n      });\n    });\n    if (attributesToImport.length > 0 || methodsToImport.length > 0) {\n      const importedData = {\n        name: currentClassData.name,\n        attributes: attributesToImport,\n        methods: methodsToImport\n      };\n      onImport(importedData);\n\n      // Reset toutes les sélections\n      setSelectedMatches(new Set());\n      setSelectedAttributes(new Map());\n      setSelectedMethods(new Map());\n    }\n  };\n  const handlePreview = matchId => {\n    if (showPreview === matchId) {\n      setShowPreview(null);\n      setPreviewData(null);\n    } else {\n      const match = matches.find(m => m.historyItem.id === matchId);\n      setShowPreview(matchId);\n      setPreviewData(match || null);\n    }\n  };\n  const getAccessIcon = match => {\n    const accessLevel = HistoryAnalysisService.getAccessLevel(match.historyItem, (currentUser === null || currentUser === void 0 ? void 0 : currentUser.uid) || '');\n    switch (accessLevel) {\n      case 'owner':\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          title: \"Votre diagramme\",\n          children: /*#__PURE__*/_jsxDEV(Shield, {\n            size: 12,\n            color: \"#10b981\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 16\n        }, this);\n      case 'shared':\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          title: \"Diagramme partag\\xE9\",\n          children: /*#__PURE__*/_jsxDEV(Users, {\n            size: 12,\n            color: \"#f59e0b\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 16\n        }, this);\n      case 'public':\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          title: \"Diagramme public\",\n          children: /*#__PURE__*/_jsxDEV(Eye, {\n            size: 12,\n            color: \"#3b82f6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          title: \"Acc\\xE8s restreint\",\n          children: /*#__PURE__*/_jsxDEV(Lock, {\n            size: 12,\n            color: \"#ef4444\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getSectionStyles = () => ({\n    container: {\n      marginBottom: '24px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '12px',\n      background: darkMode ? 'linear-gradient(135deg, rgba(10, 15, 28, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%)',\n      backdropFilter: 'blur(10px)',\n      WebkitBackdropFilter: 'blur(10px)'\n    },\n    header: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      padding: '16px 20px',\n      cursor: 'pointer',\n      borderBottom: isExpanded ? `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}` : 'none'\n    },\n    headerLeft: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px'\n    },\n    title: {\n      fontSize: '16px',\n      fontWeight: '600',\n      color: darkMode ? '#f8fafc' : '#0f172a',\n      margin: 0\n    },\n    badge: {\n      backgroundColor: matches.length > 0 ? '#3b82f6' : darkMode ? '#64748b' : '#94a3b8',\n      color: '#ffffff',\n      fontSize: '12px',\n      fontWeight: '600',\n      padding: '4px 8px',\n      borderRadius: '12px',\n      minWidth: '20px',\n      textAlign: 'center'\n    },\n    expandIcon: {\n      color: darkMode ? '#94a3b8' : '#64748b',\n      transition: 'transform 0.2s ease',\n      transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)'\n    },\n    content: {\n      padding: isExpanded ? '20px' : '0',\n      maxHeight: isExpanded ? '400px' : '0',\n      overflow: 'hidden',\n      transition: 'all 0.3s ease'\n    },\n    sortContainer: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      marginBottom: '16px'\n    },\n    sortLabel: {\n      fontSize: '14px',\n      color: darkMode ? '#cbd5e1' : '#64748b',\n      fontWeight: '500'\n    },\n    sortSelect: {\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '6px 12px',\n      fontSize: '14px',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      cursor: 'pointer'\n    },\n    matchesList: {\n      maxHeight: '250px',\n      overflowY: 'auto',\n      marginBottom: '16px'\n    },\n    matchItem: {\n      display: 'flex',\n      alignItems: 'flex-start',\n      flexDirection: 'column',\n      gap: '12px',\n      padding: '12px',\n      marginBottom: '8px',\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.4)' : 'rgba(248, 250, 252, 0.6)',\n      borderRadius: '8px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'}`,\n      transition: 'all 0.2s ease'\n    },\n    matchItemHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      width: '100%'\n    },\n    checkbox: {\n      width: '16px',\n      height: '16px',\n      accentColor: '#3b82f6',\n      cursor: 'pointer'\n    },\n    matchInfo: {\n      flex: 1\n    },\n    matchTitle: {\n      fontSize: '14px',\n      fontWeight: '500',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '4px'\n    },\n    matchMeta: {\n      fontSize: '12px',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    similarityBadge: {\n      backgroundColor: '#10b981',\n      color: '#ffffff',\n      fontSize: '11px',\n      fontWeight: '600',\n      padding: '2px 6px',\n      borderRadius: '8px'\n    },\n    actionButtons: {\n      display: 'flex',\n      gap: '8px'\n    },\n    actionButton: {\n      backgroundColor: 'transparent',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'}`,\n      borderRadius: '6px',\n      padding: '6px',\n      cursor: 'pointer',\n      color: darkMode ? '#60a5fa' : '#3b82f6',\n      transition: 'all 0.2s ease'\n    },\n    conflictsWarning: {\n      backgroundColor: darkMode ? 'rgba(245, 158, 11, 0.1)' : 'rgba(245, 158, 11, 0.05)',\n      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`,\n      borderRadius: '8px',\n      padding: '12px',\n      marginBottom: '16px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    conflictsText: {\n      fontSize: '13px',\n      color: darkMode ? '#fbbf24' : '#d97706',\n      fontWeight: '500'\n    },\n    importButton: {\n      backgroundColor: selectedMatches.size > 0 ? '#3b82f6' : darkMode ? '#374151' : '#9ca3af',\n      color: '#ffffff',\n      border: 'none',\n      borderRadius: '8px',\n      padding: '10px 16px',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: selectedMatches.size > 0 ? 'pointer' : 'not-allowed',\n      transition: 'all 0.2s ease',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    emptyState: {\n      textAlign: 'center',\n      color: darkMode ? '#64748b' : '#9ca3af',\n      fontSize: '14px',\n      fontStyle: 'italic',\n      padding: '20px'\n    },\n    previewContainer: {\n      backgroundColor: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(248, 250, 252, 0.9)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '16px',\n      marginTop: '12px',\n      animation: 'slideDown 0.3s ease'\n    },\n    previewHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      marginBottom: '12px',\n      fontSize: '14px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151'\n    },\n    previewContent: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '16px'\n    },\n    previewSection: {\n      fontSize: '13px',\n      color: darkMode ? '#cbd5e1' : '#64748b'\n    },\n    previewLabel: {\n      fontWeight: '600',\n      marginBottom: '4px',\n      color: darkMode ? '#f1f5f9' : '#374151'\n    },\n    relatedClassesList: {\n      display: 'flex',\n      flexWrap: 'wrap',\n      gap: '4px',\n      marginTop: '4px'\n    },\n    relatedClassTag: {\n      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',\n      color: darkMode ? '#93c5fd' : '#3b82f6',\n      fontSize: '11px',\n      padding: '2px 6px',\n      borderRadius: '4px',\n      fontWeight: '500'\n    },\n    // Styles pour la sélection granulaire\n    granularSelection: {\n      marginTop: '12px',\n      padding: '12px',\n      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.05)' : 'rgba(59, 130, 246, 0.02)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.08)'}`,\n      borderRadius: '8px'\n    },\n    granularHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      fontSize: '13px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '8px'\n    },\n    checkAllLabel: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '6px',\n      fontSize: '12px',\n      fontWeight: '500',\n      color: darkMode ? '#94a3b8' : '#6b7280',\n      cursor: 'pointer'\n    },\n    checkAllCheckbox: {\n      width: '14px',\n      height: '14px',\n      cursor: 'pointer'\n    },\n    granularTitle: {\n      fontSize: '13px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151'\n    },\n    granularContent: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '12px'\n    },\n    granularSection: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '6px'\n    },\n    granularSectionTitle: {\n      fontSize: '12px',\n      fontWeight: '600',\n      color: darkMode ? '#94a3b8' : '#6b7280',\n      textTransform: 'uppercase',\n      letterSpacing: '0.5px'\n    },\n    granularItems: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '4px',\n      paddingLeft: '8px'\n    },\n    granularItem: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      cursor: 'pointer',\n      padding: '4px 0'\n    },\n    granularCheckbox: {\n      width: '14px',\n      height: '14px',\n      cursor: 'pointer'\n    },\n    granularItemText: {\n      fontSize: '12px',\n      color: darkMode ? '#cbd5e1' : '#4b5563',\n      fontFamily: 'monospace'\n    }\n  });\n  const styles = getSectionStyles();\n  if (!currentUser) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.header,\n      onClick: handleToggleExpand,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.headerLeft,\n        children: [/*#__PURE__*/_jsxDEV(Zap, {\n          size: 18,\n          color: darkMode ? '#60a5fa' : '#3b82f6'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          style: styles.title,\n          children: \"Analyse Historique\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: styles.badge,\n          children: matches.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n        size: 20,\n        style: styles.expandIcon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 7\n    }, this), isExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.content,\n      children: matches.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.emptyState,\n        children: [\"Aucun diagramme historique trouv\\xE9 pour la classe \\\"\", targetClassName, \"\\\"\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.sortContainer,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: styles.sortLabel,\n            children: \"Trier par:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            style: styles.sortSelect,\n            value: `${sortOption.key}-${sortOption.direction}`,\n            onChange: e => {\n              const [key, direction] = e.target.value.split('-');\n              const option = HistoryAnalysisService.getSortOptions().find(opt => opt.key === key && opt.direction === direction);\n              if (option) setSortOption(option);\n            },\n            children: HistoryAnalysisService.getSortOptions().map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: `${option.key}-${option.direction}`,\n              children: option.label\n            }, `${option.key}-${option.direction}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 15\n        }, this), conflicts.attributes.length > 0 || conflicts.methods.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.conflictsWarning,\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: styles.conflictsText,\n            children: [\"Conflits d\\xE9tect\\xE9s: \", conflicts.attributes.length, \" attribut(s), \", conflicts.methods.length, \" m\\xE9thode(s)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 17\n        }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.matchesList,\n          children: matches.map(match => {\n            var _match$matchingClasse, _previewData$previewD, _previewData$previewD2, _previewData$previewD3, _match$matchingClasse2, _match$matchingClasse3;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: styles.matchItem,\n              className: \"history-match-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.matchItemHeader,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  style: styles.checkbox,\n                  checked: selectedMatches.has(match.historyItem.id),\n                  onChange: e => handleMatchSelection(match.historyItem.id, e.target.checked)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.matchInfo,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.matchTitle,\n                    children: match.historyItem.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.matchMeta,\n                    className: \"history-match-meta\",\n                    children: [/*#__PURE__*/_jsxDEV(Clock, {\n                      size: 12\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 614,\n                      columnNumber: 27\n                    }, this), match.historyItem.createdAt.toLocaleDateString('fr-FR'), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: styles.similarityBadge,\n                      className: \"history-similarity-badge\",\n                      children: [Math.round(match.similarity), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 27\n                    }, this), getAccessIcon(match), match.isShared && /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '11px',\n                        color: darkMode ? '#fbbf24' : '#d97706'\n                      },\n                      children: \"Partag\\xE9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 620,\n                      columnNumber: 46\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.actionButtons,\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    style: styles.actionButton,\n                    className: \"history-action-button\",\n                    onClick: () => handlePreview(match.historyItem.id),\n                    title: \"Voir aper\\xE7u\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      size: 14\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 630,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 21\n              }, this), showPreview === match.historyItem.id && previewData && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.previewContainer,\n                className: \"history-preview-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.previewHeader,\n                  children: [/*#__PURE__*/_jsxDEV(Eye, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 27\n                  }, this), \"Aper\\xE7u de la classe \\\"\", (_match$matchingClasse = match.matchingClasses[0]) === null || _match$matchingClasse === void 0 ? void 0 : _match$matchingClasse.name, \"\\\"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.previewContent,\n                  className: \"history-preview-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.previewSection,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.previewLabel,\n                      children: \"Contenu de la classe:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: (_previewData$previewD = previewData.previewData) === null || _previewData$previewD === void 0 ? void 0 : _previewData$previewD.classContext\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 645,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.previewSection,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.previewLabel,\n                      children: \"Classes li\\xE9es:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 648,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.relatedClassesList,\n                      className: \"history-related-classes-list\",\n                      children: [(_previewData$previewD2 = previewData.previewData) === null || _previewData$previewD2 === void 0 ? void 0 : _previewData$previewD2.relatedClasses.map((className, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: styles.relatedClassTag,\n                        children: className\n                      }, idx, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 651,\n                        columnNumber: 33\n                      }, this)), (!((_previewData$previewD3 = previewData.previewData) !== null && _previewData$previewD3 !== void 0 && _previewData$previewD3.relatedClasses) || previewData.previewData.relatedClasses.length === 0) && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontStyle: 'italic',\n                          color: darkMode ? '#64748b' : '#9ca3af'\n                        },\n                        children: \"Aucune classe li\\xE9e d\\xE9tect\\xE9e\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 656,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 23\n              }, this), selectedMatches.has(match.historyItem.id) && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: styles.granularSelection,\n                className: \"history-granular-selection\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.granularHeader,\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: styles.checkAllLabel,\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: isAllSelectedForMatch(match),\n                      onChange: e => handleCheckAll(match, e.target.checked),\n                      style: styles.checkAllCheckbox\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Cocher tous\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 677,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: styles.granularTitle,\n                    children: \"S\\xE9lectionner les \\xE9l\\xE9ments \\xE0 importer :\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: styles.granularContent,\n                  children: [((_match$matchingClasse2 = match.matchingClasses[0]) === null || _match$matchingClasse2 === void 0 ? void 0 : _match$matchingClasse2.attributes) && match.matchingClasses[0].attributes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.granularSection,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.granularSectionTitle,\n                      children: \"Attributs :\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 686,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.granularItems,\n                      children: match.matchingClasses[0].attributes.map((attribute, idx) => {\n                        var _selectedAttributes$g;\n                        return /*#__PURE__*/_jsxDEV(\"label\", {\n                          style: styles.granularItem,\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            type: \"checkbox\",\n                            style: styles.granularCheckbox,\n                            checked: ((_selectedAttributes$g = selectedAttributes.get(match.historyItem.id)) === null || _selectedAttributes$g === void 0 ? void 0 : _selectedAttributes$g.has(attribute)) || false,\n                            onChange: e => handleAttributeSelection(match.historyItem.id, attribute, e.target.checked)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 690,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: styles.granularItemText,\n                            children: attribute\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 696,\n                            columnNumber: 37\n                          }, this)]\n                        }, idx, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 689,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 687,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 29\n                  }, this), ((_match$matchingClasse3 = match.matchingClasses[0]) === null || _match$matchingClasse3 === void 0 ? void 0 : _match$matchingClasse3.methods) && match.matchingClasses[0].methods.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: styles.granularSection,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.granularSectionTitle,\n                      children: \"M\\xE9thodes :\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 706,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: styles.granularItems,\n                      children: match.matchingClasses[0].methods.map((method, idx) => {\n                        var _selectedMethods$get;\n                        return /*#__PURE__*/_jsxDEV(\"label\", {\n                          style: styles.granularItem,\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            type: \"checkbox\",\n                            style: styles.granularCheckbox,\n                            checked: ((_selectedMethods$get = selectedMethods.get(match.historyItem.id)) === null || _selectedMethods$get === void 0 ? void 0 : _selectedMethods$get.has(method)) || false,\n                            onChange: e => handleMethodSelection(match.historyItem.id, method, e.target.checked)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 710,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: styles.granularItemText,\n                            children: method\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 716,\n                            columnNumber: 37\n                          }, this)]\n                        }, idx, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 709,\n                          columnNumber: 35\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 707,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 705,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 23\n              }, this)]\n            }, match.historyItem.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          style: styles.importButton,\n          onClick: handleImport,\n          disabled: (() => {\n            const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);\n            const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);\n            return totalAttributes + totalMethods === 0;\n          })(),\n          children: [/*#__PURE__*/_jsxDEV(Zap, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 17\n          }, this), (() => {\n            const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);\n            const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);\n            const totalElements = totalAttributes + totalMethods;\n            if (totalElements === 0) {\n              return `Importer (${selectedMatches.size} diagramme${selectedMatches.size > 1 ? 's' : ''} sélectionné${selectedMatches.size > 1 ? 's' : ''})`;\n            }\n            return `Importer (${totalElements} élément${totalElements > 1 ? 's' : ''} sélectionné${totalElements > 1 ? 's' : ''})`;\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 729,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 553,\n    columnNumber: 5\n  }, this);\n};\n_s(HistoryAnalysisSection, \"6eI+8YThIxZ3WOVubXvcsG4KGLM=\", false, function () {\n  return [useHistory, useAuth];\n});\n_c = HistoryAnalysisSection;\nexport default HistoryAnalysisSection;\nvar _c;\n$RefreshReg$(_c, \"HistoryAnalysisSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useHistory", "useAuth", "HistoryAnalysisService", "ChevronDown", "Eye", "Clock", "Zap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shield", "Users", "Lock", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HistoryAnalysisSection", "darkMode", "targetClassName", "currentClassData", "onImport", "currentDiagramText", "_s", "historyItems", "currentUser", "isExpanded", "setIsExpanded", "matches", "setMatches", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMatches", "Set", "selectedAttributes", "setSelectedAttributes", "Map", "selectedMethods", "setSelectedMethods", "sortOption", "setSortOption", "getSortOptions", "showPreview", "setShowPreview", "conflicts", "setConflicts", "attributes", "methods", "previewData", "setPreviewData", "foundMatches", "findMatchingDiagrams", "uid", "sortedMatches", "sortMatches", "selectedMatchObjects", "filter", "match", "has", "historyItem", "id", "selectedClasses", "flatMap", "matchingClasses", "length", "detectedConflicts", "detectConflicts", "handleToggleExpand", "handleMatchSelection", "matchId", "selected", "newSelection", "add", "delete", "newSelectedAttributes", "newSelectedMethods", "handleAttributeSelection", "attribute", "currentAttributes", "get", "size", "set", "handleMethodSelection", "method", "currentMethods", "isAllSelectedForMatch", "matchingClass", "selected<PERSON>tt<PERSON>", "selectedMethodsSet", "allAttrsSelected", "every", "attr", "allMethodsSelected", "handleCheckAll", "checked", "prev", "newMap", "handleImport", "attributesToImport", "methodsToImport", "for<PERSON>ach", "includes", "push", "importedData", "name", "handlePreview", "find", "m", "getAccessIcon", "accessLevel", "getAccessLevel", "title", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getSectionStyles", "container", "marginBottom", "border", "borderRadius", "background", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "header", "display", "alignItems", "justifyContent", "padding", "cursor", "borderBottom", "headerLeft", "gap", "fontSize", "fontWeight", "margin", "badge", "backgroundColor", "min<PERSON><PERSON><PERSON>", "textAlign", "expandIcon", "transition", "transform", "content", "maxHeight", "overflow", "sortContainer", "sortLabel", "sortSelect", "matchesList", "overflowY", "matchItem", "flexDirection", "matchItemHeader", "width", "checkbox", "height", "accentColor", "matchInfo", "flex", "matchTitle", "matchMeta", "similarityBadge", "actionButtons", "actionButton", "conflictsWarning", "conflictsText", "importButton", "emptyState", "fontStyle", "previewContainer", "marginTop", "animation", "previewHeader", "previewContent", "gridTemplateColumns", "previewSection", "previewLabel", "relatedClassesList", "flexWrap", "relatedClassTag", "granularSelection", "granularHeader", "checkAllLabel", "checkAllCheckbox", "granularTitle", "granular<PERSON>ontent", "granularSection", "granularSectionTitle", "textTransform", "letterSpacing", "granularItems", "paddingLeft", "granularItem", "granularCheckbox", "granularItemText", "fontFamily", "styles", "style", "onClick", "value", "key", "direction", "onChange", "e", "target", "split", "option", "opt", "map", "label", "_match$matchingClasse", "_previewData$previewD", "_previewData$previewD2", "_previewData$previewD3", "_match$matchingClasse2", "_match$matchingClasse3", "className", "type", "createdAt", "toLocaleDateString", "Math", "round", "similarity", "isShared", "classContext", "relatedClasses", "idx", "_selectedAttributes$g", "_selectedMethods$get", "disabled", "totalAttributes", "Array", "from", "values", "reduce", "sum", "attrs", "totalMethods", "totalElements", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/imageUp/HistoryAnalysisSection.tsx"], "sourcesContent": ["// HistoryAnalysisSection.tsx - Composant pour l'analyse historique et l'import de classes\nimport React, { useState, useEffect } from 'react';\nimport { useHistory } from '../../context/HistoryContext';\nimport { useAuth } from '../../context/AuthContext';\nimport { HistoryAnalysisService, HistoryMatch, SortOption, ClassData } from '../../services/HistoryAnalysisService';\nimport { ChevronDown, Eye, Clock, Zap, AlertTriangle, Shield, Users, Lock } from 'lucide-react';\nimport './HistoryAnalysisSection.css';\n\ninterface HistoryAnalysisSectionProps {\n  darkMode: boolean;\n  targetClassName: string;\n  currentClassData: ClassData;\n  onImport: (importedData: ClassData) => void;\n  currentDiagramText?: string; // Texte du diagramme actuel pour l'exclure\n}\n\nconst HistoryAnalysisSection: React.FC<HistoryAnalysisSectionProps> = ({\n  darkMode,\n  targetClassName,\n  currentClassData,\n  onImport,\n  currentDiagramText\n}) => {\n  const { historyItems } = useHistory();\n  const { currentUser } = useAuth();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [matches, setMatches] = useState<HistoryMatch[]>([]);\n  const [selectedMatches, setSelectedMatches] = useState<Set<string>>(new Set());\n  const [selectedAttributes, setSelectedAttributes] = useState<Map<string, Set<string>>>(new Map());\n  const [selectedMethods, setSelectedMethods] = useState<Map<string, Set<string>>>(new Map());\n  const [sortOption, setSortOption] = useState<SortOption>(HistoryAnalysisService.getSortOptions()[0]);\n  const [showPreview, setShowPreview] = useState<string | null>(null);\n  const [conflicts, setConflicts] = useState<{ attributes: string[], methods: string[] }>({ attributes: [], methods: [] });\n  const [previewData, setPreviewData] = useState<HistoryMatch | null>(null);\n\n  // Rechercher les correspondances lors du changement de classe cible\n  useEffect(() => {\n    if (!targetClassName || !currentUser) return;\n    \n    const foundMatches = HistoryAnalysisService.findMatchingDiagrams(\n      targetClassName,\n      historyItems,\n      currentUser.uid,\n      currentDiagramText\n    );\n    \n    const sortedMatches = HistoryAnalysisService.sortMatches(foundMatches, sortOption);\n    setMatches(sortedMatches);\n  }, [targetClassName, historyItems, currentUser, sortOption, currentDiagramText]);\n\n  // Détecter les conflits lors du changement de sélection\n  useEffect(() => {\n    const selectedMatchObjects = matches.filter(match => selectedMatches.has(match.historyItem.id));\n    const selectedClasses = selectedMatchObjects.flatMap(match => match.matchingClasses);\n    \n    if (selectedClasses.length > 0) {\n      const detectedConflicts = HistoryAnalysisService.detectConflicts(currentClassData, selectedClasses);\n      setConflicts(detectedConflicts);\n    } else {\n      setConflicts({ attributes: [], methods: [] });\n    }\n  }, [selectedMatches, matches, currentClassData]);\n\n  const handleToggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n\n  const handleMatchSelection = (matchId: string, selected: boolean) => {\n    const newSelection = new Set(selectedMatches);\n    if (selected) {\n      newSelection.add(matchId);\n    } else {\n      newSelection.delete(matchId);\n      // Nettoyer les sélections d'attributs/méthodes pour ce match\n      const newSelectedAttributes = new Map(selectedAttributes);\n      const newSelectedMethods = new Map(selectedMethods);\n      newSelectedAttributes.delete(matchId);\n      newSelectedMethods.delete(matchId);\n      setSelectedAttributes(newSelectedAttributes);\n      setSelectedMethods(newSelectedMethods);\n    }\n    setSelectedMatches(newSelection);\n  };\n\n  const handleAttributeSelection = (matchId: string, attribute: string, selected: boolean) => {\n    const newSelectedAttributes = new Map(selectedAttributes);\n    const currentAttributes = newSelectedAttributes.get(matchId) || new Set();\n\n    if (selected) {\n      currentAttributes.add(attribute);\n    } else {\n      currentAttributes.delete(attribute);\n    }\n\n    if (currentAttributes.size > 0) {\n      newSelectedAttributes.set(matchId, currentAttributes);\n    } else {\n      newSelectedAttributes.delete(matchId);\n    }\n\n    setSelectedAttributes(newSelectedAttributes);\n  };\n\n  const handleMethodSelection = (matchId: string, method: string, selected: boolean) => {\n    const newSelectedMethods = new Map(selectedMethods);\n    const currentMethods = newSelectedMethods.get(matchId) || new Set();\n\n    if (selected) {\n      currentMethods.add(method);\n    } else {\n      currentMethods.delete(method);\n    }\n\n    if (currentMethods.size > 0) {\n      newSelectedMethods.set(matchId, currentMethods);\n    } else {\n      newSelectedMethods.delete(matchId);\n    }\n\n    setSelectedMethods(newSelectedMethods);\n  };\n\n  // Fonction pour vérifier si tous les éléments d'un match sont sélectionnés\n  const isAllSelectedForMatch = (match: HistoryMatch): boolean => {\n    const matchId = match.historyItem.id;\n    const matchingClass = match.matchingClasses[0];\n\n    if (!matchingClass) return false;\n\n    const selectedAttrs = selectedAttributes.get(matchId) || new Set<string>();\n    const selectedMethodsSet = selectedMethods.get(matchId) || new Set<string>();\n\n    const allAttrsSelected = matchingClass.attributes.every(attr => selectedAttrs.has(attr));\n    const allMethodsSelected = matchingClass.methods.every(method => selectedMethodsSet.has(method));\n\n    return allAttrsSelected && allMethodsSelected &&\n           (matchingClass.attributes.length > 0 || matchingClass.methods.length > 0);\n  };\n\n  // Fonction pour cocher/décocher tous les éléments d'un match\n  const handleCheckAll = (match: HistoryMatch, checked: boolean) => {\n    const matchId = match.historyItem.id;\n    const matchingClass = match.matchingClasses[0];\n\n    if (!matchingClass) return;\n\n    if (checked) {\n      // Cocher tous les attributs et méthodes\n      setSelectedAttributes(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set(matchingClass.attributes));\n        return newMap;\n      });\n\n      setSelectedMethods(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set(matchingClass.methods));\n        return newMap;\n      });\n    } else {\n      // Décocher tous les attributs et méthodes\n      setSelectedAttributes(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set<string>());\n        return newMap;\n      });\n\n      setSelectedMethods(prev => {\n        const newMap = new Map(prev);\n        newMap.set(matchId, new Set<string>());\n        return newMap;\n      });\n    }\n  };\n\n  const handleImport = () => {\n    // Construire les données à importer basées sur les sélections granulaires\n    const attributesToImport: string[] = [];\n    const methodsToImport: string[] = [];\n\n    // Collecter tous les attributs et méthodes sélectionnés\n    selectedAttributes.forEach((attributes, matchId) => {\n      attributes.forEach(attr => {\n        if (!attributesToImport.includes(attr)) {\n          attributesToImport.push(attr);\n        }\n      });\n    });\n\n    selectedMethods.forEach((methods, matchId) => {\n      methods.forEach(method => {\n        if (!methodsToImport.includes(method)) {\n          methodsToImport.push(method);\n        }\n      });\n    });\n\n    if (attributesToImport.length > 0 || methodsToImport.length > 0) {\n      const importedData: ClassData = {\n        name: currentClassData.name,\n        attributes: attributesToImport,\n        methods: methodsToImport\n      };\n\n      onImport(importedData);\n\n      // Reset toutes les sélections\n      setSelectedMatches(new Set());\n      setSelectedAttributes(new Map());\n      setSelectedMethods(new Map());\n    }\n  };\n\n  const handlePreview = (matchId: string) => {\n    if (showPreview === matchId) {\n      setShowPreview(null);\n      setPreviewData(null);\n    } else {\n      const match = matches.find(m => m.historyItem.id === matchId);\n      setShowPreview(matchId);\n      setPreviewData(match || null);\n    }\n  };\n\n  const getAccessIcon = (match: HistoryMatch) => {\n    const accessLevel = HistoryAnalysisService.getAccessLevel(match.historyItem, currentUser?.uid || '');\n\n    switch (accessLevel) {\n      case 'owner':\n        return <span title=\"Votre diagramme\"><Shield size={12} color=\"#10b981\" /></span>;\n      case 'shared':\n        return <span title=\"Diagramme partagé\"><Users size={12} color=\"#f59e0b\" /></span>;\n      case 'public':\n        return <span title=\"Diagramme public\"><Eye size={12} color=\"#3b82f6\" /></span>;\n      default:\n        return <span title=\"Accès restreint\"><Lock size={12} color=\"#ef4444\" /></span>;\n    }\n  };\n\n  const getSectionStyles = () => ({\n    container: {\n      marginBottom: '24px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '12px',\n      background: darkMode \n        ? 'linear-gradient(135deg, rgba(10, 15, 28, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)' \n        : 'linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%)',\n      backdropFilter: 'blur(10px)',\n      WebkitBackdropFilter: 'blur(10px)',\n    },\n    header: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      padding: '16px 20px',\n      cursor: 'pointer',\n      borderBottom: isExpanded ? `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}` : 'none',\n    },\n    headerLeft: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n    },\n    title: {\n      fontSize: '16px',\n      fontWeight: '600',\n      color: darkMode ? '#f8fafc' : '#0f172a',\n      margin: 0,\n    },\n    badge: {\n      backgroundColor: matches.length > 0 ? '#3b82f6' : darkMode ? '#64748b' : '#94a3b8',\n      color: '#ffffff',\n      fontSize: '12px',\n      fontWeight: '600',\n      padding: '4px 8px',\n      borderRadius: '12px',\n      minWidth: '20px',\n      textAlign: 'center' as const,\n    },\n    expandIcon: {\n      color: darkMode ? '#94a3b8' : '#64748b',\n      transition: 'transform 0.2s ease',\n      transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',\n    },\n    content: {\n      padding: isExpanded ? '20px' : '0',\n      maxHeight: isExpanded ? '400px' : '0',\n      overflow: 'hidden' as const,\n      transition: 'all 0.3s ease',\n    },\n    sortContainer: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      marginBottom: '16px',\n    },\n    sortLabel: {\n      fontSize: '14px',\n      color: darkMode ? '#cbd5e1' : '#64748b',\n      fontWeight: '500',\n    },\n    sortSelect: {\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.8)' : 'rgba(248, 250, 252, 0.8)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '6px 12px',\n      fontSize: '14px',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      cursor: 'pointer',\n    },\n    matchesList: {\n      maxHeight: '250px',\n      overflowY: 'auto' as const,\n      marginBottom: '16px',\n    },\n    matchItem: {\n      display: 'flex',\n      alignItems: 'flex-start',\n      flexDirection: 'column' as const,\n      gap: '12px',\n      padding: '12px',\n      marginBottom: '8px',\n      backgroundColor: darkMode ? 'rgba(30, 41, 59, 0.4)' : 'rgba(248, 250, 252, 0.6)',\n      borderRadius: '8px',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'}`,\n      transition: 'all 0.2s ease',\n    },\n    matchItemHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '12px',\n      width: '100%',\n    },\n    checkbox: {\n      width: '16px',\n      height: '16px',\n      accentColor: '#3b82f6',\n      cursor: 'pointer',\n    },\n    matchInfo: {\n      flex: 1,\n    },\n    matchTitle: {\n      fontSize: '14px',\n      fontWeight: '500',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '4px',\n    },\n    matchMeta: {\n      fontSize: '12px',\n      color: darkMode ? '#94a3b8' : '#64748b',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    similarityBadge: {\n      backgroundColor: '#10b981',\n      color: '#ffffff',\n      fontSize: '11px',\n      fontWeight: '600',\n      padding: '2px 6px',\n      borderRadius: '8px',\n    },\n    actionButtons: {\n      display: 'flex',\n      gap: '8px',\n    },\n    actionButton: {\n      backgroundColor: 'transparent',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'}`,\n      borderRadius: '6px',\n      padding: '6px',\n      cursor: 'pointer',\n      color: darkMode ? '#60a5fa' : '#3b82f6',\n      transition: 'all 0.2s ease',\n    },\n    conflictsWarning: {\n      backgroundColor: darkMode ? 'rgba(245, 158, 11, 0.1)' : 'rgba(245, 158, 11, 0.05)',\n      border: `1px solid ${darkMode ? 'rgba(245, 158, 11, 0.3)' : 'rgba(245, 158, 11, 0.2)'}`,\n      borderRadius: '8px',\n      padding: '12px',\n      marginBottom: '16px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    conflictsText: {\n      fontSize: '13px',\n      color: darkMode ? '#fbbf24' : '#d97706',\n      fontWeight: '500',\n    },\n    importButton: {\n      backgroundColor: selectedMatches.size > 0 ? '#3b82f6' : darkMode ? '#374151' : '#9ca3af',\n      color: '#ffffff',\n      border: 'none',\n      borderRadius: '8px',\n      padding: '10px 16px',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: selectedMatches.size > 0 ? 'pointer' : 'not-allowed',\n      transition: 'all 0.2s ease',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n    },\n    emptyState: {\n      textAlign: 'center' as const,\n      color: darkMode ? '#64748b' : '#9ca3af',\n      fontSize: '14px',\n      fontStyle: 'italic',\n      padding: '20px',\n    },\n    previewContainer: {\n      backgroundColor: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(248, 250, 252, 0.9)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n      borderRadius: '8px',\n      padding: '16px',\n      marginTop: '12px',\n      animation: 'slideDown 0.3s ease',\n    },\n    previewHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      marginBottom: '12px',\n      fontSize: '14px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151',\n    },\n    previewContent: {\n      display: 'grid',\n      gridTemplateColumns: '1fr 1fr',\n      gap: '16px',\n    },\n    previewSection: {\n      fontSize: '13px',\n      color: darkMode ? '#cbd5e1' : '#64748b',\n    },\n    previewLabel: {\n      fontWeight: '600',\n      marginBottom: '4px',\n      color: darkMode ? '#f1f5f9' : '#374151',\n    },\n    relatedClassesList: {\n      display: 'flex',\n      flexWrap: 'wrap' as const,\n      gap: '4px',\n      marginTop: '4px',\n    },\n    relatedClassTag: {\n      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',\n      color: darkMode ? '#93c5fd' : '#3b82f6',\n      fontSize: '11px',\n      padding: '2px 6px',\n      borderRadius: '4px',\n      fontWeight: '500',\n    },\n\n    // Styles pour la sélection granulaire\n    granularSelection: {\n      marginTop: '12px',\n      padding: '12px',\n      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.05)' : 'rgba(59, 130, 246, 0.02)',\n      border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.08)'}`,\n      borderRadius: '8px',\n    },\n\n    granularHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between',\n      fontSize: '13px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151',\n      marginBottom: '8px',\n    },\n\n    checkAllLabel: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '6px',\n      fontSize: '12px',\n      fontWeight: '500',\n      color: darkMode ? '#94a3b8' : '#6b7280',\n      cursor: 'pointer',\n    },\n\n    checkAllCheckbox: {\n      width: '14px',\n      height: '14px',\n      cursor: 'pointer',\n    },\n\n    granularTitle: {\n      fontSize: '13px',\n      fontWeight: '600',\n      color: darkMode ? '#e2e8f0' : '#374151',\n    },\n\n    granularContent: {\n      display: 'flex',\n      flexDirection: 'column' as const,\n      gap: '12px',\n    },\n\n    granularSection: {\n      display: 'flex',\n      flexDirection: 'column' as const,\n      gap: '6px',\n    },\n\n    granularSectionTitle: {\n      fontSize: '12px',\n      fontWeight: '600',\n      color: darkMode ? '#94a3b8' : '#6b7280',\n      textTransform: 'uppercase' as const,\n      letterSpacing: '0.5px',\n    },\n\n    granularItems: {\n      display: 'flex',\n      flexDirection: 'column' as const,\n      gap: '4px',\n      paddingLeft: '8px',\n    },\n\n    granularItem: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      cursor: 'pointer',\n      padding: '4px 0',\n    },\n\n    granularCheckbox: {\n      width: '14px',\n      height: '14px',\n      cursor: 'pointer',\n    },\n\n    granularItemText: {\n      fontSize: '12px',\n      color: darkMode ? '#cbd5e1' : '#4b5563',\n      fontFamily: 'monospace',\n    }\n  });\n\n  const styles = getSectionStyles();\n\n  if (!currentUser) return null;\n\n  return (\n    <div style={styles.container}>\n      <div style={styles.header} onClick={handleToggleExpand}>\n        <div style={styles.headerLeft}>\n          <Zap size={18} color={darkMode ? '#60a5fa' : '#3b82f6'} />\n          <h4 style={styles.title}>Analyse Historique</h4>\n          <span style={styles.badge}>{matches.length}</span>\n        </div>\n        <ChevronDown size={20} style={styles.expandIcon} />\n      </div>\n      \n      {isExpanded && (\n        <div style={styles.content}>\n          {matches.length === 0 ? (\n            <div style={styles.emptyState}>\n              Aucun diagramme historique trouvé pour la classe \"{targetClassName}\"\n            </div>\n          ) : (\n            <>\n              <div style={styles.sortContainer}>\n                <span style={styles.sortLabel}>Trier par:</span>\n                <select \n                  style={styles.sortSelect}\n                  value={`${sortOption.key}-${sortOption.direction}`}\n                  onChange={(e) => {\n                    const [key, direction] = e.target.value.split('-');\n                    const option = HistoryAnalysisService.getSortOptions().find(\n                      opt => opt.key === key && opt.direction === direction\n                    );\n                    if (option) setSortOption(option);\n                  }}\n                >\n                  {HistoryAnalysisService.getSortOptions().map(option => (\n                    <option key={`${option.key}-${option.direction}`} value={`${option.key}-${option.direction}`}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {conflicts.attributes.length > 0 || conflicts.methods.length > 0 ? (\n                <div style={styles.conflictsWarning}>\n                  <AlertTriangle size={16} />\n                  <span style={styles.conflictsText}>\n                    Conflits détectés: {conflicts.attributes.length} attribut(s), {conflicts.methods.length} méthode(s)\n                  </span>\n                </div>\n              ) : null}\n\n              <div style={styles.matchesList}>\n                {matches.map(match => (\n                  <div key={match.historyItem.id} style={styles.matchItem} className=\"history-match-item\">\n                    <div style={styles.matchItemHeader}>\n                      <input\n                        type=\"checkbox\"\n                        style={styles.checkbox}\n                        checked={selectedMatches.has(match.historyItem.id)}\n                        onChange={(e) => handleMatchSelection(match.historyItem.id, e.target.checked)}\n                      />\n                      <div style={styles.matchInfo}>\n                        <div style={styles.matchTitle}>{match.historyItem.title}</div>\n                        <div style={styles.matchMeta} className=\"history-match-meta\">\n                          <Clock size={12} />\n                          {match.historyItem.createdAt.toLocaleDateString('fr-FR')}\n                          <span style={styles.similarityBadge} className=\"history-similarity-badge\">\n                            {Math.round(match.similarity)}%\n                          </span>\n                          {getAccessIcon(match)}\n                          {match.isShared && <span style={{ fontSize: '11px', color: darkMode ? '#fbbf24' : '#d97706' }}>Partagé</span>}\n                        </div>\n                      </div>\n                      <div style={styles.actionButtons}>\n                        <button\n                          style={styles.actionButton}\n                          className=\"history-action-button\"\n                          onClick={() => handlePreview(match.historyItem.id)}\n                          title=\"Voir aperçu\"\n                        >\n                          <Eye size={14} />\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* Aperçu de la classe */}\n                    {showPreview === match.historyItem.id && previewData && (\n                      <div style={styles.previewContainer} className=\"history-preview-container\">\n                        <div style={styles.previewHeader}>\n                          <Eye size={16} />\n                          Aperçu de la classe \"{match.matchingClasses[0]?.name}\"\n                        </div>\n                        <div style={styles.previewContent} className=\"history-preview-content\">\n                          <div style={styles.previewSection}>\n                            <div style={styles.previewLabel}>Contenu de la classe:</div>\n                            <div>{previewData.previewData?.classContext}</div>\n                          </div>\n                          <div style={styles.previewSection}>\n                            <div style={styles.previewLabel}>Classes liées:</div>\n                            <div style={styles.relatedClassesList} className=\"history-related-classes-list\">\n                              {previewData.previewData?.relatedClasses.map((className, idx) => (\n                                <span key={idx} style={styles.relatedClassTag}>\n                                  {className}\n                                </span>\n                              ))}\n                              {(!previewData.previewData?.relatedClasses || previewData.previewData.relatedClasses.length === 0) && (\n                                <span style={{ fontStyle: 'italic', color: darkMode ? '#64748b' : '#9ca3af' }}>\n                                  Aucune classe liée détectée\n                                </span>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Sélection granulaire des attributs et méthodes */}\n                    {selectedMatches.has(match.historyItem.id) && (\n                      <div style={styles.granularSelection} className=\"history-granular-selection\">\n                        <div style={styles.granularHeader}>\n                          <label style={styles.checkAllLabel}>\n                            <input\n                              type=\"checkbox\"\n                              checked={isAllSelectedForMatch(match)}\n                              onChange={(e) => handleCheckAll(match, e.target.checked)}\n                              style={styles.checkAllCheckbox}\n                            />\n                            <span>Cocher tous</span>\n                          </label>\n                          <span style={styles.granularTitle}>Sélectionner les éléments à importer :</span>\n                        </div>\n\n                        <div style={styles.granularContent}>\n                          {/* Attributs */}\n                          {match.matchingClasses[0]?.attributes && match.matchingClasses[0].attributes.length > 0 && (\n                            <div style={styles.granularSection}>\n                              <div style={styles.granularSectionTitle}>Attributs :</div>\n                              <div style={styles.granularItems}>\n                                {match.matchingClasses[0].attributes.map((attribute, idx) => (\n                                  <label key={idx} style={styles.granularItem}>\n                                    <input\n                                      type=\"checkbox\"\n                                      style={styles.granularCheckbox}\n                                      checked={selectedAttributes.get(match.historyItem.id)?.has(attribute) || false}\n                                      onChange={(e) => handleAttributeSelection(match.historyItem.id, attribute, e.target.checked)}\n                                    />\n                                    <span style={styles.granularItemText}>{attribute}</span>\n                                  </label>\n                                ))}\n                              </div>\n                            </div>\n                          )}\n\n                          {/* Méthodes */}\n                          {match.matchingClasses[0]?.methods && match.matchingClasses[0].methods.length > 0 && (\n                            <div style={styles.granularSection}>\n                              <div style={styles.granularSectionTitle}>Méthodes :</div>\n                              <div style={styles.granularItems}>\n                                {match.matchingClasses[0].methods.map((method, idx) => (\n                                  <label key={idx} style={styles.granularItem}>\n                                    <input\n                                      type=\"checkbox\"\n                                      style={styles.granularCheckbox}\n                                      checked={selectedMethods.get(match.historyItem.id)?.has(method) || false}\n                                      onChange={(e) => handleMethodSelection(match.historyItem.id, method, e.target.checked)}\n                                    />\n                                    <span style={styles.granularItemText}>{method}</span>\n                                  </label>\n                                ))}\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n\n              <button\n                style={styles.importButton}\n                onClick={handleImport}\n                disabled={(() => {\n                  const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);\n                  const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);\n                  return totalAttributes + totalMethods === 0;\n                })()}\n              >\n                <Zap size={16} />\n                {(() => {\n                  const totalAttributes = Array.from(selectedAttributes.values()).reduce((sum, attrs) => sum + attrs.size, 0);\n                  const totalMethods = Array.from(selectedMethods.values()).reduce((sum, methods) => sum + methods.size, 0);\n                  const totalElements = totalAttributes + totalMethods;\n\n                  if (totalElements === 0) {\n                    return `Importer (${selectedMatches.size} diagramme${selectedMatches.size > 1 ? 's' : ''} sélectionné${selectedMatches.size > 1 ? 's' : ''})`;\n                  }\n\n                  return `Importer (${totalElements} élément${totalElements > 1 ? 's' : ''} sélectionné${totalElements > 1 ? 's' : ''})`;\n                })()}\n              </button>\n            </>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default HistoryAnalysisSection;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,sBAAsB,QAA6C,uCAAuC;AACnH,SAASC,WAAW,EAAEC,GAAG,EAAEC,KAAK,EAAEC,GAAG,EAAEC,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AAC/F,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAUtC,MAAMC,sBAA6D,GAAGA,CAAC;EACrEC,QAAQ;EACRC,eAAe;EACfC,gBAAgB;EAChBC,QAAQ;EACRC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAa,CAAC,GAAGtB,UAAU,CAAC,CAAC;EACrC,MAAM;IAAEuB;EAAY,CAAC,GAAGtB,OAAO,CAAC,CAAC;EACjC,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAiB,EAAE,CAAC;EAC1D,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAc,IAAIgC,GAAG,CAAC,CAAC,CAAC;EAC9E,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlC,QAAQ,CAA2B,IAAImC,GAAG,CAAC,CAAC,CAAC;EACjG,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAA2B,IAAImC,GAAG,CAAC,CAAC,CAAC;EAC3F,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAaI,sBAAsB,CAACoC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAA8C;IAAE6C,UAAU,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG,CAAC,CAAC;EACxH,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAsB,IAAI,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd,IAAI,CAACkB,eAAe,IAAI,CAACM,WAAW,EAAE;IAEtC,MAAMwB,YAAY,GAAG7C,sBAAsB,CAAC8C,oBAAoB,CAC9D/B,eAAe,EACfK,YAAY,EACZC,WAAW,CAAC0B,GAAG,EACf7B,kBACF,CAAC;IAED,MAAM8B,aAAa,GAAGhD,sBAAsB,CAACiD,WAAW,CAACJ,YAAY,EAAEX,UAAU,CAAC;IAClFT,UAAU,CAACuB,aAAa,CAAC;EAC3B,CAAC,EAAE,CAACjC,eAAe,EAAEK,YAAY,EAAEC,WAAW,EAAEa,UAAU,EAAEhB,kBAAkB,CAAC,CAAC;;EAEhF;EACArB,SAAS,CAAC,MAAM;IACd,MAAMqD,oBAAoB,GAAG1B,OAAO,CAAC2B,MAAM,CAACC,KAAK,IAAI1B,eAAe,CAAC2B,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,CAAC;IAC/F,MAAMC,eAAe,GAAGN,oBAAoB,CAACO,OAAO,CAACL,KAAK,IAAIA,KAAK,CAACM,eAAe,CAAC;IAEpF,IAAIF,eAAe,CAACG,MAAM,GAAG,CAAC,EAAE;MAC9B,MAAMC,iBAAiB,GAAG5D,sBAAsB,CAAC6D,eAAe,CAAC7C,gBAAgB,EAAEwC,eAAe,CAAC;MACnGhB,YAAY,CAACoB,iBAAiB,CAAC;IACjC,CAAC,MAAM;MACLpB,YAAY,CAAC;QAAEC,UAAU,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAAChB,eAAe,EAAEF,OAAO,EAAER,gBAAgB,CAAC,CAAC;EAEhD,MAAM8C,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvC,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMyC,oBAAoB,GAAGA,CAACC,OAAe,EAAEC,QAAiB,KAAK;IACnE,MAAMC,YAAY,GAAG,IAAItC,GAAG,CAACF,eAAe,CAAC;IAC7C,IAAIuC,QAAQ,EAAE;MACZC,YAAY,CAACC,GAAG,CAACH,OAAO,CAAC;IAC3B,CAAC,MAAM;MACLE,YAAY,CAACE,MAAM,CAACJ,OAAO,CAAC;MAC5B;MACA,MAAMK,qBAAqB,GAAG,IAAItC,GAAG,CAACF,kBAAkB,CAAC;MACzD,MAAMyC,kBAAkB,GAAG,IAAIvC,GAAG,CAACC,eAAe,CAAC;MACnDqC,qBAAqB,CAACD,MAAM,CAACJ,OAAO,CAAC;MACrCM,kBAAkB,CAACF,MAAM,CAACJ,OAAO,CAAC;MAClClC,qBAAqB,CAACuC,qBAAqB,CAAC;MAC5CpC,kBAAkB,CAACqC,kBAAkB,CAAC;IACxC;IACA3C,kBAAkB,CAACuC,YAAY,CAAC;EAClC,CAAC;EAED,MAAMK,wBAAwB,GAAGA,CAACP,OAAe,EAAEQ,SAAiB,EAAEP,QAAiB,KAAK;IAC1F,MAAMI,qBAAqB,GAAG,IAAItC,GAAG,CAACF,kBAAkB,CAAC;IACzD,MAAM4C,iBAAiB,GAAGJ,qBAAqB,CAACK,GAAG,CAACV,OAAO,CAAC,IAAI,IAAIpC,GAAG,CAAC,CAAC;IAEzE,IAAIqC,QAAQ,EAAE;MACZQ,iBAAiB,CAACN,GAAG,CAACK,SAAS,CAAC;IAClC,CAAC,MAAM;MACLC,iBAAiB,CAACL,MAAM,CAACI,SAAS,CAAC;IACrC;IAEA,IAAIC,iBAAiB,CAACE,IAAI,GAAG,CAAC,EAAE;MAC9BN,qBAAqB,CAACO,GAAG,CAACZ,OAAO,EAAES,iBAAiB,CAAC;IACvD,CAAC,MAAM;MACLJ,qBAAqB,CAACD,MAAM,CAACJ,OAAO,CAAC;IACvC;IAEAlC,qBAAqB,CAACuC,qBAAqB,CAAC;EAC9C,CAAC;EAED,MAAMQ,qBAAqB,GAAGA,CAACb,OAAe,EAAEc,MAAc,EAAEb,QAAiB,KAAK;IACpF,MAAMK,kBAAkB,GAAG,IAAIvC,GAAG,CAACC,eAAe,CAAC;IACnD,MAAM+C,cAAc,GAAGT,kBAAkB,CAACI,GAAG,CAACV,OAAO,CAAC,IAAI,IAAIpC,GAAG,CAAC,CAAC;IAEnE,IAAIqC,QAAQ,EAAE;MACZc,cAAc,CAACZ,GAAG,CAACW,MAAM,CAAC;IAC5B,CAAC,MAAM;MACLC,cAAc,CAACX,MAAM,CAACU,MAAM,CAAC;IAC/B;IAEA,IAAIC,cAAc,CAACJ,IAAI,GAAG,CAAC,EAAE;MAC3BL,kBAAkB,CAACM,GAAG,CAACZ,OAAO,EAAEe,cAAc,CAAC;IACjD,CAAC,MAAM;MACLT,kBAAkB,CAACF,MAAM,CAACJ,OAAO,CAAC;IACpC;IAEA/B,kBAAkB,CAACqC,kBAAkB,CAAC;EACxC,CAAC;;EAED;EACA,MAAMU,qBAAqB,GAAI5B,KAAmB,IAAc;IAC9D,MAAMY,OAAO,GAAGZ,KAAK,CAACE,WAAW,CAACC,EAAE;IACpC,MAAM0B,aAAa,GAAG7B,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC;IAE9C,IAAI,CAACuB,aAAa,EAAE,OAAO,KAAK;IAEhC,MAAMC,aAAa,GAAGrD,kBAAkB,CAAC6C,GAAG,CAACV,OAAO,CAAC,IAAI,IAAIpC,GAAG,CAAS,CAAC;IAC1E,MAAMuD,kBAAkB,GAAGnD,eAAe,CAAC0C,GAAG,CAACV,OAAO,CAAC,IAAI,IAAIpC,GAAG,CAAS,CAAC;IAE5E,MAAMwD,gBAAgB,GAAGH,aAAa,CAACxC,UAAU,CAAC4C,KAAK,CAACC,IAAI,IAAIJ,aAAa,CAAC7B,GAAG,CAACiC,IAAI,CAAC,CAAC;IACxF,MAAMC,kBAAkB,GAAGN,aAAa,CAACvC,OAAO,CAAC2C,KAAK,CAACP,MAAM,IAAIK,kBAAkB,CAAC9B,GAAG,CAACyB,MAAM,CAAC,CAAC;IAEhG,OAAOM,gBAAgB,IAAIG,kBAAkB,KACrCN,aAAa,CAACxC,UAAU,CAACkB,MAAM,GAAG,CAAC,IAAIsB,aAAa,CAACvC,OAAO,CAACiB,MAAM,GAAG,CAAC,CAAC;EAClF,CAAC;;EAED;EACA,MAAM6B,cAAc,GAAGA,CAACpC,KAAmB,EAAEqC,OAAgB,KAAK;IAChE,MAAMzB,OAAO,GAAGZ,KAAK,CAACE,WAAW,CAACC,EAAE;IACpC,MAAM0B,aAAa,GAAG7B,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC;IAE9C,IAAI,CAACuB,aAAa,EAAE;IAEpB,IAAIQ,OAAO,EAAE;MACX;MACA3D,qBAAqB,CAAC4D,IAAI,IAAI;QAC5B,MAAMC,MAAM,GAAG,IAAI5D,GAAG,CAAC2D,IAAI,CAAC;QAC5BC,MAAM,CAACf,GAAG,CAACZ,OAAO,EAAE,IAAIpC,GAAG,CAACqD,aAAa,CAACxC,UAAU,CAAC,CAAC;QACtD,OAAOkD,MAAM;MACf,CAAC,CAAC;MAEF1D,kBAAkB,CAACyD,IAAI,IAAI;QACzB,MAAMC,MAAM,GAAG,IAAI5D,GAAG,CAAC2D,IAAI,CAAC;QAC5BC,MAAM,CAACf,GAAG,CAACZ,OAAO,EAAE,IAAIpC,GAAG,CAACqD,aAAa,CAACvC,OAAO,CAAC,CAAC;QACnD,OAAOiD,MAAM;MACf,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA7D,qBAAqB,CAAC4D,IAAI,IAAI;QAC5B,MAAMC,MAAM,GAAG,IAAI5D,GAAG,CAAC2D,IAAI,CAAC;QAC5BC,MAAM,CAACf,GAAG,CAACZ,OAAO,EAAE,IAAIpC,GAAG,CAAS,CAAC,CAAC;QACtC,OAAO+D,MAAM;MACf,CAAC,CAAC;MAEF1D,kBAAkB,CAACyD,IAAI,IAAI;QACzB,MAAMC,MAAM,GAAG,IAAI5D,GAAG,CAAC2D,IAAI,CAAC;QAC5BC,MAAM,CAACf,GAAG,CAACZ,OAAO,EAAE,IAAIpC,GAAG,CAAS,CAAC,CAAC;QACtC,OAAO+D,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,MAAMC,kBAA4B,GAAG,EAAE;IACvC,MAAMC,eAAyB,GAAG,EAAE;;IAEpC;IACAjE,kBAAkB,CAACkE,OAAO,CAAC,CAACtD,UAAU,EAAEuB,OAAO,KAAK;MAClDvB,UAAU,CAACsD,OAAO,CAACT,IAAI,IAAI;QACzB,IAAI,CAACO,kBAAkB,CAACG,QAAQ,CAACV,IAAI,CAAC,EAAE;UACtCO,kBAAkB,CAACI,IAAI,CAACX,IAAI,CAAC;QAC/B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFtD,eAAe,CAAC+D,OAAO,CAAC,CAACrD,OAAO,EAAEsB,OAAO,KAAK;MAC5CtB,OAAO,CAACqD,OAAO,CAACjB,MAAM,IAAI;QACxB,IAAI,CAACgB,eAAe,CAACE,QAAQ,CAAClB,MAAM,CAAC,EAAE;UACrCgB,eAAe,CAACG,IAAI,CAACnB,MAAM,CAAC;QAC9B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAIe,kBAAkB,CAAClC,MAAM,GAAG,CAAC,IAAImC,eAAe,CAACnC,MAAM,GAAG,CAAC,EAAE;MAC/D,MAAMuC,YAAuB,GAAG;QAC9BC,IAAI,EAAEnF,gBAAgB,CAACmF,IAAI;QAC3B1D,UAAU,EAAEoD,kBAAkB;QAC9BnD,OAAO,EAAEoD;MACX,CAAC;MAED7E,QAAQ,CAACiF,YAAY,CAAC;;MAEtB;MACAvE,kBAAkB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;MAC7BE,qBAAqB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;MAChCE,kBAAkB,CAAC,IAAIF,GAAG,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,MAAMqE,aAAa,GAAIpC,OAAe,IAAK;IACzC,IAAI3B,WAAW,KAAK2B,OAAO,EAAE;MAC3B1B,cAAc,CAAC,IAAI,CAAC;MACpBM,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,MAAM;MACL,MAAMQ,KAAK,GAAG5B,OAAO,CAAC6E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChD,WAAW,CAACC,EAAE,KAAKS,OAAO,CAAC;MAC7D1B,cAAc,CAAC0B,OAAO,CAAC;MACvBpB,cAAc,CAACQ,KAAK,IAAI,IAAI,CAAC;IAC/B;EACF,CAAC;EAED,MAAMmD,aAAa,GAAInD,KAAmB,IAAK;IAC7C,MAAMoD,WAAW,GAAGxG,sBAAsB,CAACyG,cAAc,CAACrD,KAAK,CAACE,WAAW,EAAE,CAAAjC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE0B,GAAG,KAAI,EAAE,CAAC;IAEpG,QAAQyD,WAAW;MACjB,KAAK,OAAO;QACV,oBAAO9F,OAAA;UAAMgG,KAAK,EAAC,iBAAiB;UAAAC,QAAA,eAACjG,OAAA,CAACJ,MAAM;YAACqE,IAAI,EAAE,EAAG;YAACiC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAClF,KAAK,QAAQ;QACX,oBAAOtG,OAAA;UAAMgG,KAAK,EAAC,sBAAmB;UAAAC,QAAA,eAACjG,OAAA,CAACH,KAAK;YAACoE,IAAI,EAAE,EAAG;YAACiC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MACnF,KAAK,QAAQ;QACX,oBAAOtG,OAAA;UAAMgG,KAAK,EAAC,kBAAkB;UAAAC,QAAA,eAACjG,OAAA,CAACR,GAAG;YAACyE,IAAI,EAAE,EAAG;YAACiC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAChF;QACE,oBAAOtG,OAAA;UAAMgG,KAAK,EAAC,oBAAiB;UAAAC,QAAA,eAACjG,OAAA,CAACF,IAAI;YAACmE,IAAI,EAAE,EAAG;YAACiC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IAClF;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,MAAO;IAC9BC,SAAS,EAAE;MACTC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,aAAatG,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFuG,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAExG,QAAQ,GAChB,+EAA+E,GAC/E,qFAAqF;MACzFyG,cAAc,EAAE,YAAY;MAC5BC,oBAAoB,EAAE;IACxB,CAAC;IACDC,MAAM,EAAE;MACNC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,eAAe;MAC/BC,OAAO,EAAE,WAAW;MACpBC,MAAM,EAAE,SAAS;MACjBC,YAAY,EAAEzG,UAAU,GAAG,aAAaR,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B,EAAE,GAAG;IACjH,CAAC;IACDkH,UAAU,EAAE;MACVN,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACDvB,KAAK,EAAE;MACLwB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCsH,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLC,eAAe,EAAE9G,OAAO,CAACmC,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG7C,QAAQ,GAAG,SAAS,GAAG,SAAS;MAClF8F,KAAK,EAAE,SAAS;MAChBsB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBN,OAAO,EAAE,SAAS;MAClBR,YAAY,EAAE,MAAM;MACpBkB,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE;IACb,CAAC;IACDC,UAAU,EAAE;MACV7B,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC4H,UAAU,EAAE,qBAAqB;MACjCC,SAAS,EAAErH,UAAU,GAAG,gBAAgB,GAAG;IAC7C,CAAC;IACDsH,OAAO,EAAE;MACPf,OAAO,EAAEvG,UAAU,GAAG,MAAM,GAAG,GAAG;MAClCuH,SAAS,EAAEvH,UAAU,GAAG,OAAO,GAAG,GAAG;MACrCwH,QAAQ,EAAE,QAAiB;MAC3BJ,UAAU,EAAE;IACd,CAAC;IACDK,aAAa,EAAE;MACbrB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,MAAM;MACXd,YAAY,EAAE;IAChB,CAAC;IACD6B,SAAS,EAAE;MACTd,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCqH,UAAU,EAAE;IACd,CAAC;IACDc,UAAU,EAAE;MACVX,eAAe,EAAExH,QAAQ,GAAG,uBAAuB,GAAG,0BAA0B;MAChFsG,MAAM,EAAE,aAAatG,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFuG,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,UAAU;MACnBK,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCgH,MAAM,EAAE;IACV,CAAC;IACDoB,WAAW,EAAE;MACXL,SAAS,EAAE,OAAO;MAClBM,SAAS,EAAE,MAAe;MAC1BhC,YAAY,EAAE;IAChB,CAAC;IACDiC,SAAS,EAAE;MACT1B,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,YAAY;MACxB0B,aAAa,EAAE,QAAiB;MAChCpB,GAAG,EAAE,MAAM;MACXJ,OAAO,EAAE,MAAM;MACfV,YAAY,EAAE,KAAK;MACnBmB,eAAe,EAAExH,QAAQ,GAAG,uBAAuB,GAAG,0BAA0B;MAChFuG,YAAY,EAAE,KAAK;MACnBD,MAAM,EAAE,aAAatG,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B,EAAE;MACxF4H,UAAU,EAAE;IACd,CAAC;IACDY,eAAe,EAAE;MACf5B,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,MAAM;MACXsB,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE;MACRD,KAAK,EAAE,MAAM;MACbE,MAAM,EAAE,MAAM;MACdC,WAAW,EAAE,SAAS;MACtB5B,MAAM,EAAE;IACV,CAAC;IACD6B,SAAS,EAAE;MACTC,IAAI,EAAE;IACR,CAAC;IACDC,UAAU,EAAE;MACV3B,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCqG,YAAY,EAAE;IAChB,CAAC;IACD2C,SAAS,EAAE;MACT5B,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC4G,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACD8B,eAAe,EAAE;MACfzB,eAAe,EAAE,SAAS;MAC1B1B,KAAK,EAAE,SAAS;MAChBsB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBN,OAAO,EAAE,SAAS;MAClBR,YAAY,EAAE;IAChB,CAAC;IACD2C,aAAa,EAAE;MACbtC,OAAO,EAAE,MAAM;MACfO,GAAG,EAAE;IACP,CAAC;IACDgC,YAAY,EAAE;MACZ3B,eAAe,EAAE,aAAa;MAC9BlB,MAAM,EAAE,aAAatG,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFuG,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,SAAS;MACjBlB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC4H,UAAU,EAAE;IACd,CAAC;IACDwB,gBAAgB,EAAE;MAChB5B,eAAe,EAAExH,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B;MAClFsG,MAAM,EAAE,aAAatG,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFuG,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,MAAM;MACfV,YAAY,EAAE,MAAM;MACpBO,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACDkC,aAAa,EAAE;MACbjC,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCqH,UAAU,EAAE;IACd,CAAC;IACDiC,YAAY,EAAE;MACZ9B,eAAe,EAAE5G,eAAe,CAACiD,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG7D,QAAQ,GAAG,SAAS,GAAG,SAAS;MACxF8F,KAAK,EAAE,SAAS;MAChBQ,MAAM,EAAE,MAAM;MACdC,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,WAAW;MACpBK,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBL,MAAM,EAAEpG,eAAe,CAACiD,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG,aAAa;MAC5D+D,UAAU,EAAE,eAAe;MAC3BhB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE;IACP,CAAC;IACDoC,UAAU,EAAE;MACV7B,SAAS,EAAE,QAAiB;MAC5B5B,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCoH,QAAQ,EAAE,MAAM;MAChBoC,SAAS,EAAE,QAAQ;MACnBzC,OAAO,EAAE;IACX,CAAC;IACD0C,gBAAgB,EAAE;MAChBjC,eAAe,EAAExH,QAAQ,GAAG,uBAAuB,GAAG,0BAA0B;MAChFsG,MAAM,EAAE,aAAatG,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;MACvFuG,YAAY,EAAE,KAAK;MACnBQ,OAAO,EAAE,MAAM;MACf2C,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE;IACb,CAAC;IACDC,aAAa,EAAE;MACbhD,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,KAAK;MACVd,YAAY,EAAE,MAAM;MACpBe,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACD6J,cAAc,EAAE;MACdjD,OAAO,EAAE,MAAM;MACfkD,mBAAmB,EAAE,SAAS;MAC9B3C,GAAG,EAAE;IACP,CAAC;IACD4C,cAAc,EAAE;MACd3C,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACDgK,YAAY,EAAE;MACZ3C,UAAU,EAAE,KAAK;MACjBhB,YAAY,EAAE,KAAK;MACnBP,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACDiK,kBAAkB,EAAE;MAClBrD,OAAO,EAAE,MAAM;MACfsD,QAAQ,EAAE,MAAe;MACzB/C,GAAG,EAAE,KAAK;MACVuC,SAAS,EAAE;IACb,CAAC;IACDS,eAAe,EAAE;MACf3C,eAAe,EAAExH,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB;MACjF8F,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCoH,QAAQ,EAAE,MAAM;MAChBL,OAAO,EAAE,SAAS;MAClBR,YAAY,EAAE,KAAK;MACnBc,UAAU,EAAE;IACd,CAAC;IAED;IACA+C,iBAAiB,EAAE;MACjBV,SAAS,EAAE,MAAM;MACjB3C,OAAO,EAAE,MAAM;MACfS,eAAe,EAAExH,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B;MACnFsG,MAAM,EAAE,aAAatG,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B,EAAE;MACxFuG,YAAY,EAAE;IAChB,CAAC;IAED8D,cAAc,EAAE;MACdzD,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,eAAe;MAC/BM,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCqG,YAAY,EAAE;IAChB,CAAC;IAEDiE,aAAa,EAAE;MACb1D,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCgH,MAAM,EAAE;IACV,CAAC;IAEDuD,gBAAgB,EAAE;MAChB9B,KAAK,EAAE,MAAM;MACbE,MAAM,EAAE,MAAM;MACd3B,MAAM,EAAE;IACV,CAAC;IAEDwD,aAAa,EAAE;MACbpD,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IAEDyK,eAAe,EAAE;MACf7D,OAAO,EAAE,MAAM;MACf2B,aAAa,EAAE,QAAiB;MAChCpB,GAAG,EAAE;IACP,CAAC;IAEDuD,eAAe,EAAE;MACf9D,OAAO,EAAE,MAAM;MACf2B,aAAa,EAAE,QAAiB;MAChCpB,GAAG,EAAE;IACP,CAAC;IAEDwD,oBAAoB,EAAE;MACpBvD,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBvB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvC4K,aAAa,EAAE,WAAoB;MACnCC,aAAa,EAAE;IACjB,CAAC;IAEDC,aAAa,EAAE;MACblE,OAAO,EAAE,MAAM;MACf2B,aAAa,EAAE,QAAiB;MAChCpB,GAAG,EAAE,KAAK;MACV4D,WAAW,EAAE;IACf,CAAC;IAEDC,YAAY,EAAE;MACZpE,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBM,GAAG,EAAE,KAAK;MACVH,MAAM,EAAE,SAAS;MACjBD,OAAO,EAAE;IACX,CAAC;IAEDkE,gBAAgB,EAAE;MAChBxC,KAAK,EAAE,MAAM;MACbE,MAAM,EAAE,MAAM;MACd3B,MAAM,EAAE;IACV,CAAC;IAEDkE,gBAAgB,EAAE;MAChB9D,QAAQ,EAAE,MAAM;MAChBtB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCmL,UAAU,EAAE;IACd;EACF,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAGjF,gBAAgB,CAAC,CAAC;EAEjC,IAAI,CAAC5F,WAAW,EAAE,OAAO,IAAI;EAE7B,oBACEX,OAAA;IAAKyL,KAAK,EAAED,MAAM,CAAChF,SAAU;IAAAP,QAAA,gBAC3BjG,OAAA;MAAKyL,KAAK,EAAED,MAAM,CAACzE,MAAO;MAAC2E,OAAO,EAAEtI,kBAAmB;MAAA6C,QAAA,gBACrDjG,OAAA;QAAKyL,KAAK,EAAED,MAAM,CAAClE,UAAW;QAAArB,QAAA,gBAC5BjG,OAAA,CAACN,GAAG;UAACuE,IAAI,EAAE,EAAG;UAACiC,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG;QAAU;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DtG,OAAA;UAAIyL,KAAK,EAAED,MAAM,CAACxF,KAAM;UAAAC,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChDtG,OAAA;UAAMyL,KAAK,EAAED,MAAM,CAAC7D,KAAM;UAAA1B,QAAA,EAAEnF,OAAO,CAACmC;QAAM;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNtG,OAAA,CAACT,WAAW;QAAC0E,IAAI,EAAE,EAAG;QAACwH,KAAK,EAAED,MAAM,CAACzD;MAAW;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,EAEL1F,UAAU,iBACTZ,OAAA;MAAKyL,KAAK,EAAED,MAAM,CAACtD,OAAQ;MAAAjC,QAAA,EACxBnF,OAAO,CAACmC,MAAM,KAAK,CAAC,gBACnBjD,OAAA;QAAKyL,KAAK,EAAED,MAAM,CAAC7B,UAAW;QAAA1D,QAAA,GAAC,wDACqB,EAAC5F,eAAe,EAAC,IACrE;MAAA;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAENtG,OAAA,CAAAE,SAAA;QAAA+F,QAAA,gBACEjG,OAAA;UAAKyL,KAAK,EAAED,MAAM,CAACnD,aAAc;UAAApC,QAAA,gBAC/BjG,OAAA;YAAMyL,KAAK,EAAED,MAAM,CAAClD,SAAU;YAAArC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDtG,OAAA;YACEyL,KAAK,EAAED,MAAM,CAACjD,UAAW;YACzBoD,KAAK,EAAE,GAAGnK,UAAU,CAACoK,GAAG,IAAIpK,UAAU,CAACqK,SAAS,EAAG;YACnDC,QAAQ,EAAGC,CAAC,IAAK;cACf,MAAM,CAACH,GAAG,EAAEC,SAAS,CAAC,GAAGE,CAAC,CAACC,MAAM,CAACL,KAAK,CAACM,KAAK,CAAC,GAAG,CAAC;cAClD,MAAMC,MAAM,GAAG5M,sBAAsB,CAACoC,cAAc,CAAC,CAAC,CAACiE,IAAI,CACzDwG,GAAG,IAAIA,GAAG,CAACP,GAAG,KAAKA,GAAG,IAAIO,GAAG,CAACN,SAAS,KAAKA,SAC9C,CAAC;cACD,IAAIK,MAAM,EAAEzK,aAAa,CAACyK,MAAM,CAAC;YACnC,CAAE;YAAAjG,QAAA,EAED3G,sBAAsB,CAACoC,cAAc,CAAC,CAAC,CAAC0K,GAAG,CAACF,MAAM,iBACjDlM,OAAA;cAAkD2L,KAAK,EAAE,GAAGO,MAAM,CAACN,GAAG,IAAIM,MAAM,CAACL,SAAS,EAAG;cAAA5F,QAAA,EAC1FiG,MAAM,CAACG;YAAK,GADF,GAAGH,MAAM,CAACN,GAAG,IAAIM,MAAM,CAACL,SAAS,EAAE;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAExC,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELzE,SAAS,CAACE,UAAU,CAACkB,MAAM,GAAG,CAAC,IAAIpB,SAAS,CAACG,OAAO,CAACiB,MAAM,GAAG,CAAC,gBAC9DjD,OAAA;UAAKyL,KAAK,EAAED,MAAM,CAAChC,gBAAiB;UAAAvD,QAAA,gBAClCjG,OAAA,CAACL,aAAa;YAACsE,IAAI,EAAE;UAAG;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3BtG,OAAA;YAAMyL,KAAK,EAAED,MAAM,CAAC/B,aAAc;YAAAxD,QAAA,GAAC,2BACd,EAACpE,SAAS,CAACE,UAAU,CAACkB,MAAM,EAAC,gBAAc,EAACpB,SAAS,CAACG,OAAO,CAACiB,MAAM,EAAC,gBAC1F;UAAA;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,GACJ,IAAI,eAERtG,OAAA;UAAKyL,KAAK,EAAED,MAAM,CAAChD,WAAY;UAAAvC,QAAA,EAC5BnF,OAAO,CAACsL,GAAG,CAAC1J,KAAK;YAAA,IAAA4J,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YAAA,oBAChB3M,OAAA;cAAgCyL,KAAK,EAAED,MAAM,CAAC9C,SAAU;cAACkE,SAAS,EAAC,oBAAoB;cAAA3G,QAAA,gBACrFjG,OAAA;gBAAKyL,KAAK,EAAED,MAAM,CAAC5C,eAAgB;gBAAA3C,QAAA,gBACjCjG,OAAA;kBACE6M,IAAI,EAAC,UAAU;kBACfpB,KAAK,EAAED,MAAM,CAAC1C,QAAS;kBACvB/D,OAAO,EAAE/D,eAAe,CAAC2B,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAE;kBACnDiJ,QAAQ,EAAGC,CAAC,IAAK1I,oBAAoB,CAACX,KAAK,CAACE,WAAW,CAACC,EAAE,EAAEkJ,CAAC,CAACC,MAAM,CAACjH,OAAO;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eACFtG,OAAA;kBAAKyL,KAAK,EAAED,MAAM,CAACvC,SAAU;kBAAAhD,QAAA,gBAC3BjG,OAAA;oBAAKyL,KAAK,EAAED,MAAM,CAACrC,UAAW;oBAAAlD,QAAA,EAAEvD,KAAK,CAACE,WAAW,CAACoD;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9DtG,OAAA;oBAAKyL,KAAK,EAAED,MAAM,CAACpC,SAAU;oBAACwD,SAAS,EAAC,oBAAoB;oBAAA3G,QAAA,gBAC1DjG,OAAA,CAACP,KAAK;sBAACwE,IAAI,EAAE;oBAAG;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAClB5D,KAAK,CAACE,WAAW,CAACkK,SAAS,CAACC,kBAAkB,CAAC,OAAO,CAAC,eACxD/M,OAAA;sBAAMyL,KAAK,EAAED,MAAM,CAACnC,eAAgB;sBAACuD,SAAS,EAAC,0BAA0B;sBAAA3G,QAAA,GACtE+G,IAAI,CAACC,KAAK,CAACvK,KAAK,CAACwK,UAAU,CAAC,EAAC,GAChC;oBAAA;sBAAA/G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EACNT,aAAa,CAACnD,KAAK,CAAC,EACpBA,KAAK,CAACyK,QAAQ,iBAAInN,OAAA;sBAAMyL,KAAK,EAAE;wBAAEjE,QAAQ,EAAE,MAAM;wBAAEtB,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG;sBAAU,CAAE;sBAAA6F,QAAA,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtG,OAAA;kBAAKyL,KAAK,EAAED,MAAM,CAAClC,aAAc;kBAAArD,QAAA,eAC/BjG,OAAA;oBACEyL,KAAK,EAAED,MAAM,CAACjC,YAAa;oBAC3BqD,SAAS,EAAC,uBAAuB;oBACjClB,OAAO,EAAEA,CAAA,KAAMhG,aAAa,CAAChD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAE;oBACnDmD,KAAK,EAAC,gBAAa;oBAAAC,QAAA,eAEnBjG,OAAA,CAACR,GAAG;sBAACyE,IAAI,EAAE;oBAAG;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL3E,WAAW,KAAKe,KAAK,CAACE,WAAW,CAACC,EAAE,IAAIZ,WAAW,iBAClDjC,OAAA;gBAAKyL,KAAK,EAAED,MAAM,CAAC3B,gBAAiB;gBAAC+C,SAAS,EAAC,2BAA2B;gBAAA3G,QAAA,gBACxEjG,OAAA;kBAAKyL,KAAK,EAAED,MAAM,CAACxB,aAAc;kBAAA/D,QAAA,gBAC/BjG,OAAA,CAACR,GAAG;oBAACyE,IAAI,EAAE;kBAAG;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,6BACI,GAAAgG,qBAAA,GAAC5J,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,cAAAsJ,qBAAA,uBAAxBA,qBAAA,CAA0B7G,IAAI,EAAC,IACvD;gBAAA;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtG,OAAA;kBAAKyL,KAAK,EAAED,MAAM,CAACvB,cAAe;kBAAC2C,SAAS,EAAC,yBAAyB;kBAAA3G,QAAA,gBACpEjG,OAAA;oBAAKyL,KAAK,EAAED,MAAM,CAACrB,cAAe;oBAAAlE,QAAA,gBAChCjG,OAAA;sBAAKyL,KAAK,EAAED,MAAM,CAACpB,YAAa;sBAAAnE,QAAA,EAAC;oBAAqB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC5DtG,OAAA;sBAAAiG,QAAA,GAAAsG,qBAAA,GAAMtK,WAAW,CAACA,WAAW,cAAAsK,qBAAA,uBAAvBA,qBAAA,CAAyBa;oBAAY;sBAAAjH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACNtG,OAAA;oBAAKyL,KAAK,EAAED,MAAM,CAACrB,cAAe;oBAAAlE,QAAA,gBAChCjG,OAAA;sBAAKyL,KAAK,EAAED,MAAM,CAACpB,YAAa;sBAAAnE,QAAA,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrDtG,OAAA;sBAAKyL,KAAK,EAAED,MAAM,CAACnB,kBAAmB;sBAACuC,SAAS,EAAC,8BAA8B;sBAAA3G,QAAA,IAAAuG,sBAAA,GAC5EvK,WAAW,CAACA,WAAW,cAAAuK,sBAAA,uBAAvBA,sBAAA,CAAyBa,cAAc,CAACjB,GAAG,CAAC,CAACQ,SAAS,EAAEU,GAAG,kBAC1DtN,OAAA;wBAAgByL,KAAK,EAAED,MAAM,CAACjB,eAAgB;wBAAAtE,QAAA,EAC3C2G;sBAAS,GADDU,GAAG;wBAAAnH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAER,CACP,CAAC,EACD,CAAC,GAAAmG,sBAAA,GAACxK,WAAW,CAACA,WAAW,cAAAwK,sBAAA,eAAvBA,sBAAA,CAAyBY,cAAc,KAAIpL,WAAW,CAACA,WAAW,CAACoL,cAAc,CAACpK,MAAM,KAAK,CAAC,kBAC/FjD,OAAA;wBAAMyL,KAAK,EAAE;0BAAE7B,SAAS,EAAE,QAAQ;0BAAE1D,KAAK,EAAE9F,QAAQ,GAAG,SAAS,GAAG;wBAAU,CAAE;wBAAA6F,QAAA,EAAC;sBAE/E;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGAtF,eAAe,CAAC2B,GAAG,CAACD,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,iBACxC7C,OAAA;gBAAKyL,KAAK,EAAED,MAAM,CAAChB,iBAAkB;gBAACoC,SAAS,EAAC,4BAA4B;gBAAA3G,QAAA,gBAC1EjG,OAAA;kBAAKyL,KAAK,EAAED,MAAM,CAACf,cAAe;kBAAAxE,QAAA,gBAChCjG,OAAA;oBAAOyL,KAAK,EAAED,MAAM,CAACd,aAAc;oBAAAzE,QAAA,gBACjCjG,OAAA;sBACE6M,IAAI,EAAC,UAAU;sBACf9H,OAAO,EAAET,qBAAqB,CAAC5B,KAAK,CAAE;sBACtCoJ,QAAQ,EAAGC,CAAC,IAAKjH,cAAc,CAACpC,KAAK,EAAEqJ,CAAC,CAACC,MAAM,CAACjH,OAAO,CAAE;sBACzD0G,KAAK,EAAED,MAAM,CAACb;oBAAiB;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC,eACFtG,OAAA;sBAAAiG,QAAA,EAAM;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACRtG,OAAA;oBAAMyL,KAAK,EAAED,MAAM,CAACZ,aAAc;oBAAA3E,QAAA,EAAC;kBAAsC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eAENtG,OAAA;kBAAKyL,KAAK,EAAED,MAAM,CAACX,eAAgB;kBAAA5E,QAAA,GAEhC,EAAAyG,sBAAA,GAAAhK,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,cAAA0J,sBAAA,uBAAxBA,sBAAA,CAA0B3K,UAAU,KAAIW,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAACjB,UAAU,CAACkB,MAAM,GAAG,CAAC,iBACrFjD,OAAA;oBAAKyL,KAAK,EAAED,MAAM,CAACV,eAAgB;oBAAA7E,QAAA,gBACjCjG,OAAA;sBAAKyL,KAAK,EAAED,MAAM,CAACT,oBAAqB;sBAAA9E,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC1DtG,OAAA;sBAAKyL,KAAK,EAAED,MAAM,CAACN,aAAc;sBAAAjF,QAAA,EAC9BvD,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAACjB,UAAU,CAACqK,GAAG,CAAC,CAACtI,SAAS,EAAEwJ,GAAG;wBAAA,IAAAC,qBAAA;wBAAA,oBACtDvN,OAAA;0BAAiByL,KAAK,EAAED,MAAM,CAACJ,YAAa;0BAAAnF,QAAA,gBAC1CjG,OAAA;4BACE6M,IAAI,EAAC,UAAU;4BACfpB,KAAK,EAAED,MAAM,CAACH,gBAAiB;4BAC/BtG,OAAO,EAAE,EAAAwI,qBAAA,GAAApM,kBAAkB,CAAC6C,GAAG,CAACtB,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,cAAA0K,qBAAA,uBAA5CA,qBAAA,CAA8C5K,GAAG,CAACmB,SAAS,CAAC,KAAI,KAAM;4BAC/EgI,QAAQ,EAAGC,CAAC,IAAKlI,wBAAwB,CAACnB,KAAK,CAACE,WAAW,CAACC,EAAE,EAAEiB,SAAS,EAAEiI,CAAC,CAACC,MAAM,CAACjH,OAAO;0BAAE;4BAAAoB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC9F,CAAC,eACFtG,OAAA;4BAAMyL,KAAK,EAAED,MAAM,CAACF,gBAAiB;4BAAArF,QAAA,EAAEnC;0BAAS;4BAAAqC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA,GAP9CgH,GAAG;0BAAAnH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAQR,CAAC;sBAAA,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGA,EAAAqG,sBAAA,GAAAjK,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,cAAA2J,sBAAA,uBAAxBA,sBAAA,CAA0B3K,OAAO,KAAIU,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAAChB,OAAO,CAACiB,MAAM,GAAG,CAAC,iBAC/EjD,OAAA;oBAAKyL,KAAK,EAAED,MAAM,CAACV,eAAgB;oBAAA7E,QAAA,gBACjCjG,OAAA;sBAAKyL,KAAK,EAAED,MAAM,CAACT,oBAAqB;sBAAA9E,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzDtG,OAAA;sBAAKyL,KAAK,EAAED,MAAM,CAACN,aAAc;sBAAAjF,QAAA,EAC9BvD,KAAK,CAACM,eAAe,CAAC,CAAC,CAAC,CAAChB,OAAO,CAACoK,GAAG,CAAC,CAAChI,MAAM,EAAEkJ,GAAG;wBAAA,IAAAE,oBAAA;wBAAA,oBAChDxN,OAAA;0BAAiByL,KAAK,EAAED,MAAM,CAACJ,YAAa;0BAAAnF,QAAA,gBAC1CjG,OAAA;4BACE6M,IAAI,EAAC,UAAU;4BACfpB,KAAK,EAAED,MAAM,CAACH,gBAAiB;4BAC/BtG,OAAO,EAAE,EAAAyI,oBAAA,GAAAlM,eAAe,CAAC0C,GAAG,CAACtB,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,cAAA2K,oBAAA,uBAAzCA,oBAAA,CAA2C7K,GAAG,CAACyB,MAAM,CAAC,KAAI,KAAM;4BACzE0H,QAAQ,EAAGC,CAAC,IAAK5H,qBAAqB,CAACzB,KAAK,CAACE,WAAW,CAACC,EAAE,EAAEuB,MAAM,EAAE2H,CAAC,CAACC,MAAM,CAACjH,OAAO;0BAAE;4BAAAoB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxF,CAAC,eACFtG,OAAA;4BAAMyL,KAAK,EAAED,MAAM,CAACF,gBAAiB;4BAAArF,QAAA,EAAE7B;0BAAM;4BAAA+B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA,GAP3CgH,GAAG;0BAAAnH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAQR,CAAC;sBAAA,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA,GAzHO5D,KAAK,CAACE,WAAW,CAACC,EAAE;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0HzB,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtG,OAAA;UACEyL,KAAK,EAAED,MAAM,CAAC9B,YAAa;UAC3BgC,OAAO,EAAExG,YAAa;UACtBuI,QAAQ,EAAE,CAAC,MAAM;YACf,MAAMC,eAAe,GAAGC,KAAK,CAACC,IAAI,CAACzM,kBAAkB,CAAC0M,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAAC/J,IAAI,EAAE,CAAC,CAAC;YAC3G,MAAMgK,YAAY,GAAGN,KAAK,CAACC,IAAI,CAACtM,eAAe,CAACuM,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAE/L,OAAO,KAAK+L,GAAG,GAAG/L,OAAO,CAACiC,IAAI,EAAE,CAAC,CAAC;YACzG,OAAOyJ,eAAe,GAAGO,YAAY,KAAK,CAAC;UAC7C,CAAC,EAAE,CAAE;UAAAhI,QAAA,gBAELjG,OAAA,CAACN,GAAG;YAACuE,IAAI,EAAE;UAAG;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAChB,CAAC,MAAM;YACN,MAAMoH,eAAe,GAAGC,KAAK,CAACC,IAAI,CAACzM,kBAAkB,CAAC0M,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAAC/J,IAAI,EAAE,CAAC,CAAC;YAC3G,MAAMgK,YAAY,GAAGN,KAAK,CAACC,IAAI,CAACtM,eAAe,CAACuM,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAE/L,OAAO,KAAK+L,GAAG,GAAG/L,OAAO,CAACiC,IAAI,EAAE,CAAC,CAAC;YACzG,MAAMiK,aAAa,GAAGR,eAAe,GAAGO,YAAY;YAEpD,IAAIC,aAAa,KAAK,CAAC,EAAE;cACvB,OAAO,aAAalN,eAAe,CAACiD,IAAI,aAAajD,eAAe,CAACiD,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,eAAejD,eAAe,CAACiD,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG;YAC/I;YAEA,OAAO,aAAaiK,aAAa,WAAWA,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,eAAeA,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG;UACxH,CAAC,EAAE,CAAC;QAAA;UAAA/H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,eACT;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7F,EAAA,CApuBIN,sBAA6D;EAAA,QAOxCf,UAAU,EACXC,OAAO;AAAA;AAAA8O,EAAA,GAR3BhO,sBAA6D;AAsuBnE,eAAeA,sBAAsB;AAAC,IAAAgO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}