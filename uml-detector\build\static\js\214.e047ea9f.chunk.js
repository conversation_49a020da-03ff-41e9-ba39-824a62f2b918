"use strict";(self.webpackChunkuml_detector=self.webpackChunkuml_detector||[]).push([[214],{8691:(t,e,a)=>{function r(t,e){t.accDescr&&e.setAccDescription?.(t.accDescr),t.accTitle&&e.setAccTitle?.(t.accTitle),t.title&&e.setDiagramTitle?.(t.title)}a.d(e,{S:()=>r}),(0,a(7551).K2)(r,"populateCommonDb")},9214:(t,e,a)=>{a.d(e,{diagram:()=>w});var r=a(8691),n=a(6102),l=a(113),o=a(7551),i=a(1106),c={packet:[]},s=structuredClone(c),d=o.UI.packet,k=(0,o.K2)((()=>{const t=(0,n.$t)({...d,...(0,o.zj)().packet});return t.showBits&&(t.paddingY+=10),t}),"getConfig"),p=(0,o.K2)((()=>s.packet),"getPacket"),b={pushWord:(0,o.K2)((t=>{t.length>0&&s.packet.push(t)}),"pushWord"),getPacket:p,getConfig:k,clear:(0,o.K2)((()=>{(0,o.IU)(),s=structuredClone(c)}),"clear"),setAccTitle:o.SV,getAccTitle:o.iN,setDiagramTitle:o.ke,getDiagramTitle:o.ab,getAccDescription:o.m7,setAccDescription:o.EI},g=(0,o.K2)((t=>{(0,r.S)(t,b);let e=-1,a=[],n=1;const{bitsPerRow:l}=b.getConfig();for(let{start:r,end:i,label:c}of t.blocks){if(i&&i<r)throw new Error(`Packet block ${r} - ${i} is invalid. End must be greater than start.`);if(r!==e+1)throw new Error(`Packet block ${r} - ${i??r} is not contiguous. It should start from ${e+1}.`);for(e=i??r,o.Rm.debug(`Packet block ${r} - ${e} with label ${c}`);a.length<=l+1&&b.getPacket().length<1e4;){const[t,e]=h({start:r,end:i,label:c},n,l);if(a.push(t),t.end+1===n*l&&(b.pushWord(a),a=[],n++),!e)break;({start:r,end:i,label:c}=e)}}b.pushWord(a)}),"populate"),h=(0,o.K2)(((t,e,a)=>{if(void 0===t.end&&(t.end=t.start),t.start>t.end)throw new Error(`Block start ${t.start} is greater than block end ${t.end}.`);return t.end+1<=e*a?[t,void 0]:[{start:t.start,end:e*a-1,label:t.label},{start:e*a,end:t.end,label:t.label}]}),"getNextFittingBlock"),u={parse:(0,o.K2)((async t=>{const e=await(0,i.qg)("packet",t);o.Rm.debug(e),g(e)}),"parse")},f=(0,o.K2)(((t,e,a,r)=>{const n=r.db,i=n.getConfig(),{rowHeight:c,paddingY:s,bitWidth:d,bitsPerRow:k}=i,p=n.getPacket(),b=n.getDiagramTitle(),g=c+s,h=g*(p.length+1)-(b?0:c),u=d*k+2,f=(0,l.D)(e);f.attr("viewbox",`0 0 ${u} ${h}`),(0,o.a$)(f,h,u,i.useMaxWidth);for(const[l,o]of p.entries())$(f,o,l,i);f.append("text").text(b).attr("x",u/2).attr("y",h-g/2).attr("dominant-baseline","middle").attr("text-anchor","middle").attr("class","packetTitle")}),"draw"),$=(0,o.K2)(((t,e,a,r)=>{let{rowHeight:n,paddingX:l,paddingY:o,bitWidth:i,bitsPerRow:c,showBits:s}=r;const d=t.append("g"),k=a*(n+o)+o;for(const p of e){const t=p.start%c*i+1,e=(p.end-p.start+1)*i-l;if(d.append("rect").attr("x",t).attr("y",k).attr("width",e).attr("height",n).attr("class","packetBlock"),d.append("text").attr("x",t+e/2).attr("y",k+n/2).attr("class","packetLabel").attr("dominant-baseline","middle").attr("text-anchor","middle").text(p.label),!s)continue;const a=p.end===p.start,r=k-2;d.append("text").attr("x",t+(a?e/2:0)).attr("y",r).attr("class","packetByte start").attr("dominant-baseline","auto").attr("text-anchor",a?"middle":"start").text(p.start),a||d.append("text").attr("x",t+e).attr("y",r).attr("class","packetByte end").attr("dominant-baseline","auto").attr("text-anchor","end").text(p.end)}}),"drawWord"),m={byteFontSize:"10px",startByteColor:"black",endByteColor:"black",labelColor:"black",labelFontSize:"12px",titleColor:"black",titleFontSize:"14px",blockStrokeColor:"black",blockStrokeWidth:"1",blockFillColor:"#efefef"},w={parser:u,db:b,renderer:{draw:f},styles:(0,o.K2)((function(){let{packet:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const e=(0,n.$t)(m,t);return`\n\t.packetByte {\n\t\tfont-size: ${e.byteFontSize};\n\t}\n\t.packetByte.start {\n\t\tfill: ${e.startByteColor};\n\t}\n\t.packetByte.end {\n\t\tfill: ${e.endByteColor};\n\t}\n\t.packetLabel {\n\t\tfill: ${e.labelColor};\n\t\tfont-size: ${e.labelFontSize};\n\t}\n\t.packetTitle {\n\t\tfill: ${e.titleColor};\n\t\tfont-size: ${e.titleFontSize};\n\t}\n\t.packetBlock {\n\t\tstroke: ${e.blockStrokeColor};\n\t\tstroke-width: ${e.blockStrokeWidth};\n\t\tfill: ${e.blockFillColor};\n\t}\n\t`}),"styles")}}}]);
//# sourceMappingURL=214.e047ea9f.chunk.js.map