"use strict";(self.webpackChunkuml_detector=self.webpackChunkuml_detector||[]).push([[75],{1075:(e,r,t)=>{t.d(r,{diagram:()=>u});var a=t(1471),s=(t(7731),t(89),t(5616),t(594),t(8546),t(4190),t(590),t(1984),t(6102),t(7551)),u={parser:a.Zk,get db(){return new a.u4(2)},renderer:a.q7,styles:a.tM,init:(0,s.K2)((e=>{e.state||(e.state={}),e.state.arrowMarkerAbsolute=e.arrowMarkerAbsolute}),"init")}}}]);
//# sourceMappingURL=75.94918338.chunk.js.map