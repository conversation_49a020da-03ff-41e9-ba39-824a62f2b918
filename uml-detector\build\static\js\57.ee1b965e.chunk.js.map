{"version": 3, "file": "static/js/57.ee1b965e.chunk.js", "mappings": "sLA2BIA,EAAS,WACX,IAAIC,GAAoBC,EAAAA,EAAAA,KAAO,SAASC,EAAGC,EAAGC,EAAIC,GAChD,IAAKD,EAAKA,GAAM,CAAC,EAAGC,EAAIH,EAAEI,OAAQD,IAAKD,EAAGF,EAAEG,IAAMF,GAClD,OAAOC,CACT,GAAG,KAAMG,EAAM,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,GAAIC,EAAM,CAAC,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,IAAKC,EAAM,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAKC,EAAM,CAAC,EAAG,IACvpBC,EAAU,CACZC,OAAuBlC,EAAAA,EAAAA,KAAO,WAC9B,GAAG,SACHmC,GAAI,CAAC,EACLC,SAAU,CAAE,MAAS,EAAG,MAAS,EAAG,IAAO,EAAG,QAAW,EAAG,YAAe,EAAG,SAAY,EAAG,kBAAqB,EAAG,UAAa,EAAG,MAAS,GAAI,KAAQ,GAAI,OAAU,GAAI,WAAc,GAAI,OAAU,GAAI,WAAc,GAAI,KAAQ,GAAI,SAAY,GAAI,IAAO,GAAI,UAAa,GAAI,gBAAmB,GAAI,UAAa,GAAI,gBAAmB,GAAI,0BAA6B,GAAI,oBAAuB,GAAI,sBAAyB,GAAI,kBAAqB,GAAI,oBAAuB,GAAI,MAAS,GAAI,UAAa,GAAI,SAAY,GAAI,gBAAmB,GAAI,oBAAuB,GAAI,UAAa,GAAI,QAAW,GAAI,KAAQ,GAAI,IAAO,GAAI,SAAY,GAAI,IAAO,GAAI,OAAU,GAAI,cAAiB,GAAI,IAAO,GAAI,IAAO,GAAI,MAAS,GAAI,KAAQ,GAAI,OAAU,GAAI,KAAQ,GAAI,IAAO,GAAI,KAAQ,GAAI,MAAS,GAAI,WAAc,GAAI,QAAW,EAAG,KAAQ,GAC50BC,WAAY,CAAE,EAAG,QAAS,EAAG,UAAW,EAAG,oBAAqB,GAAI,QAAS,GAAI,SAAU,GAAI,SAAU,GAAI,OAAQ,GAAI,MAAO,GAAI,YAAa,GAAI,kBAAmB,GAAI,YAAa,GAAI,kBAAmB,GAAI,4BAA6B,GAAI,sBAAuB,GAAI,oBAAqB,GAAI,sBAAuB,GAAI,QAAS,GAAI,kBAAmB,GAAI,UAAW,GAAI,OAAQ,GAAI,MAAO,GAAI,MAAO,GAAI,SAAU,GAAI,MAAO,GAAI,MAAO,GAAI,QAAS,GAAI,OAAQ,GAAI,SAAU,GAAI,OAAQ,GAAI,MAAO,GAAI,OAAQ,GAAI,QAAS,GAAI,cACphBC,aAAc,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,EAAG,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,GAAI,CAAC,GAAI,IACpcC,eAA+BvC,EAAAA,EAAAA,KAAO,SAAmBwC,EAAQC,EAAQC,EAAUP,EAAIQ,EAASC,EAAIC,GAClG,IAAIC,EAAKF,EAAGvC,OAAS,EACrB,OAAQsC,GACN,KAAK,EACHR,EAAGY,eAAeH,EAAGE,IACrB,MACF,KAAK,EACHX,EAAGa,gBAAgBJ,EAAGE,GAAIG,KAAKC,QAC/B,MACF,KAAK,GACHf,EAAGgB,YAAY,CAAEF,KAAM,GAAIG,KAAM,QAAUR,EAAGE,IAC9C,MACF,KAAK,GACHX,EAAGgB,YAAYP,EAAGE,EAAK,GAAIF,EAAGE,IAC9B,MACF,KAAK,GACHX,EAAGkB,WAAW,CAAEJ,KAAM,GAAIG,KAAM,QAAUR,EAAGE,IAC7C,MACF,KAAK,GACHX,EAAGkB,WAAWT,EAAGE,EAAK,GAAIF,EAAGE,IAC7B,MACF,KAAK,GACHQ,KAAKC,EAAIX,EAAGE,GAAII,OAChBf,EAAGqB,YAAYF,KAAKC,GACpB,MACF,KAAK,GACL,KAAK,GACHD,KAAKC,EAAIX,EAAGE,GAAII,OAChBf,EAAGsB,kBAAkBH,KAAKC,GAC1B,MACF,KAAK,GAwBL,KAAK,GACHD,KAAKC,EAAIX,EAAGE,EAAK,GACjB,MAvBF,KAAK,GACHQ,KAAKC,EAAI,CAACG,OAAOd,EAAGE,EAAK,OAAQF,EAAGE,IACpC,MACF,KAAK,GACHQ,KAAKC,EAAI,CAACG,OAAOd,EAAGE,KACpB,MACF,KAAK,GACHX,EAAGwB,cAAcf,EAAGE,IACpB,MACF,KAAK,GACHX,EAAGwB,cAAcf,EAAGE,EAAK,IACzB,MACF,KAAK,GACHX,EAAGwB,cAAc,CAAEP,KAAM,OAAQH,KAAM,KACvC,MACF,KAAK,GACHd,EAAGyB,aAAahB,EAAGE,IACnB,MACF,KAAK,GACHX,EAAG0B,kBAAkBH,OAAOd,EAAGE,EAAK,IAAKY,OAAOd,EAAGE,KACnD,MAIF,KAAK,GACHQ,KAAKC,EAAI,CAACX,EAAGE,EAAK,MAAOF,EAAGE,IAC5B,MACF,KAAK,GACHQ,KAAKC,EAAI,CAACX,EAAGE,IACb,MACF,KAAK,GACHX,EAAG2B,cAAclB,EAAGE,IACpB,MACF,KAAK,GACHX,EAAG2B,cAAclB,EAAGE,EAAK,IACzB,MACF,KAAK,GACHX,EAAG2B,cAAc,CAAEV,KAAM,OAAQH,KAAM,KACvC,MACF,KAAK,GACHd,EAAG4B,kBAAkBL,OAAOd,EAAGE,EAAK,IAAKY,OAAOd,EAAGE,KACnD,MACF,KAAK,GAGL,KAAK,GACHQ,KAAKC,EAAI,CAAEN,KAAML,EAAGE,GAAKM,KAAM,QAC/B,MACF,KAAK,GACHE,KAAKC,EAAI,CAAEN,KAAML,EAAGE,GAAKM,KAAM,YAC/B,MACF,KAAK,GACHE,KAAKC,EAAIX,EAAGE,GACZ,MACF,KAAK,GACHQ,KAAKC,EAAIX,EAAGE,EAAK,GAAK,GAAKF,EAAGE,GAGpC,GAAG,aACHkB,MAAO,CAACjE,EAAEO,EAAKC,EAAK,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQ,CAAE,EAAG,CAAC,IAAMZ,EAAEO,EAAKC,EAAK,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQZ,EAAEO,EAAKC,EAAK,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAGC,EAAK,EAAG,CAAC,EAAG,IAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQ,CAAE,EAAG,CAAC,EAAG,GAAI,EAAG,GAAI,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAAOZ,EAAEa,EAAK,CAAC,EAAG,KAAMb,EAAEa,EAAK,CAAC,EAAG,KAAMb,EAAEa,EAAK,CAAC,EAAG,KAAM,CAAE,EAAG,CAAC,EAAG,IAAMb,EAAEO,EAAKC,EAAK,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQ,CAAE,EAAG,CAAC,EAAG,IAAMZ,EAAEa,EAAK,CAAC,EAAG,IAAKb,EAAEO,EAAK,CAAC,EAAG,GAAI,CAAE,EAAG,GAAI,GAAIG,EAAK,GAAIC,EAAK,GAAIC,IAAQ,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIE,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIb,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIG,EAAK,GAAI,GAAI,GAAI,GAAI,GAAId,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAII,EAAK,GAAI,GAAI,GAAIf,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAII,EAAK,GAAI,GAAI,GAAIf,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAOzB,EAAE8B,EAAK,CAAC,EAAG,KAAM,CAAE,EAAG,CAAC,EAAG,IAAM9B,EAAE8B,EAAK,CAAC,EAAG,IAAK9B,EAAE8B,EAAK,CAAC,EAAG,IAAK9B,EAAE+B,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAIf,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,IAAQzB,EAAE+B,EAAK,CAAC,EAAG,KAAM/B,EAAE+B,EAAK,CAAC,EAAG,KAAM/B,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAE8B,EAAK,CAAC,EAAG,KAAM9B,EAAE8B,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIJ,EAAK,GAAIC,IAAQ3B,EAAE8B,EAAK,CAAC,EAAG,KAAM9B,EAAE8B,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIhB,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAOzB,EAAE8B,EAAK,CAAC,EAAG,KAAM9B,EAAE8B,EAAK,CAAC,EAAG,IAAK,CAAE,GAAI,GAAI,GAAIF,IAAQ5B,EAAE8B,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO9B,EAAE8B,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,GAAI,GAAID,GAAO,CAAE,GAAI,GAAI,GAAII,GAAOjC,EAAE8B,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,GAAI,GAAID,GAAO7B,EAAE8B,EAAK,CAAC,EAAG,KAAM9B,EAAE8B,EAAK,CAAC,EAAG,KAAM9B,EAAEgC,EAAK,CAAC,EAAG,KAAMhC,EAAE8B,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAAO9B,EAAE8B,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO9B,EAAE8B,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAAO9B,EAAE8B,EAAK,CAAC,EAAG,KAAM9B,EAAE8B,EAAK,CAAC,EAAG,KAAM9B,EAAE8B,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIhB,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAOzB,EAAE8B,EAAK,CAAC,EAAG,KAAM9B,EAAE8B,EAAK,CAAC,EAAG,KAAM,CAAE,GAAI,GAAI,GAAIG,GAAO,CAAE,GAAI,CAAC,EAAG,KAAO,CAAE,GAAI,CAAC,EAAG,MACxtFiC,eAAgB,CAAE,EAAG,CAAC,EAAG,GAAI,GAAI,CAAC,EAAG,GAAI,GAAI,CAAC,EAAG,GAAI,GAAI,CAAC,EAAG,IAAK,GAAI,CAAC,EAAG,KAC1EC,YAA4BlE,EAAAA,EAAAA,KAAO,SAAoBmE,EAAKC,GAC1D,IAAIA,EAAKC,YAEF,CACL,IAAIC,EAAQ,IAAIC,MAAMJ,GAEtB,MADAG,EAAMF,KAAOA,EACPE,CACR,CALEhB,KAAKpB,MAAMiC,EAMf,GAAG,cACHK,OAAuBxE,EAAAA,EAAAA,KAAO,SAAeyE,GAC3C,IAAIC,EAAOpB,KAAMqB,EAAQ,CAAC,GAAIC,EAAS,GAAIC,EAAS,CAAC,MAAOC,EAAS,GAAId,EAAQV,KAAKU,MAAOxB,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAGsC,EAAa,EAC7IC,EAAOF,EAAOG,MAAMC,KAAKC,UAAW,GACpCC,EAASC,OAAOC,OAAOhC,KAAKiC,OAC5BC,EAAc,CAAErD,GAAI,CAAC,GACzB,IAAK,IAAIlC,KAAKqD,KAAKnB,GACbkD,OAAOI,UAAUC,eAAeR,KAAK5B,KAAKnB,GAAIlC,KAChDuF,EAAYrD,GAAGlC,GAAKqD,KAAKnB,GAAGlC,IAGhCmF,EAAOO,SAASlB,EAAOe,EAAYrD,IACnCqD,EAAYrD,GAAGoD,MAAQH,EACvBI,EAAYrD,GAAGrC,OAASwD,KACI,oBAAjB8B,EAAOQ,SAChBR,EAAOQ,OAAS,CAAC,GAEnB,IAAIC,EAAQT,EAAOQ,OACnBd,EAAOgB,KAAKD,GACZ,IAAIE,EAASX,EAAOY,SAAWZ,EAAOY,QAAQD,OAY9C,SAASE,IACP,IAAIC,EASJ,MAPqB,kBADrBA,EAAQtB,EAAOuB,OAASf,EAAOa,OA/BqI,KAiC9JC,aAAiBE,QAEnBF,GADAtB,EAASsB,GACMC,OAEjBD,EAAQxB,EAAKtC,SAAS8D,IAAUA,GAE3BA,CACT,CAtByC,oBAA9BV,EAAYrD,GAAG+B,WACxBZ,KAAKY,WAAasB,EAAYrD,GAAG+B,WAEjCZ,KAAKY,WAAamB,OAAOgB,eAAe/C,MAAMY,YAOhDlE,EAAAA,EAAAA,KALA,SAAkBsG,GAChB3B,EAAMtE,OAASsE,EAAMtE,OAAS,EAAIiG,EAClCzB,EAAOxE,OAASwE,EAAOxE,OAASiG,EAChCxB,EAAOzE,OAASyE,EAAOzE,OAASiG,CAClC,GACiB,aAajBtG,EAAAA,EAAAA,IAAOiG,EAAK,OAEZ,IADA,IAAIM,EAAQC,EAAgBC,EAAOC,EAAWC,EAAeC,EAAGC,EAAKC,EAAUC,EAA9BC,EAAQ,CAAC,IAC7C,CAUX,GATAP,EAAQ9B,EAAMA,EAAMtE,OAAS,GACzBiD,KAAKW,eAAewC,GACtBC,EAASpD,KAAKW,eAAewC,IAEd,OAAXF,GAAoC,oBAAVA,IAC5BA,EAASN,KAEXS,EAAS1C,EAAMyC,IAAUzC,EAAMyC,GAAOF,IAElB,qBAAXG,IAA2BA,EAAOrG,SAAWqG,EAAO,GAAI,CACjE,IAAIO,EAAS,GAEb,IAAKL,KADLG,EAAW,GACD/C,EAAMyC,GACVnD,KAAKjB,WAAWuE,IAAMA,EAzD6H,GA0DrJG,EAASjB,KAAK,IAAMxC,KAAKjB,WAAWuE,GAAK,KAI3CK,EADE7B,EAAO8B,aACA,wBAA0BxE,EAAW,GAAK,MAAQ0C,EAAO8B,eAAiB,eAAiBH,EAASI,KAAK,MAAQ,WAAa7D,KAAKjB,WAAWkE,IAAWA,GAAU,IAEnK,wBAA0B7D,EAAW,GAAK,iBAhE6G,GAgE1F6D,EAAgB,eAAiB,KAAOjD,KAAKjB,WAAWkE,IAAWA,GAAU,KAErJjD,KAAKY,WAAW+C,EAAQ,CACtBhE,KAAMmC,EAAOgC,MACblB,MAAO5C,KAAKjB,WAAWkE,IAAWA,EAClCc,KAAMjC,EAAO1C,SACb4E,IAAKzB,EACLkB,YAEJ,CACA,GAAIL,EAAO,aAAcN,OAASM,EAAOrG,OAAS,EAChD,MAAM,IAAIkE,MAAM,oDAAsDkC,EAAQ,YAAcF,GAE9F,OAAQG,EAAO,IACb,KAAK,EACH/B,EAAMmB,KAAKS,GACX1B,EAAOiB,KAAKV,EAAO5C,QACnBsC,EAAOgB,KAAKV,EAAOQ,QACnBjB,EAAMmB,KAAKY,EAAO,IAClBH,EAAS,KACJC,GASHD,EAASC,EACTA,EAAiB,OATjB/D,EAAS2C,EAAO3C,OAChBD,EAAS4C,EAAO5C,OAChBE,EAAW0C,EAAO1C,SAClBmD,EAAQT,EAAOQ,OACXb,EAAa,GACfA,KAMJ,MACF,KAAK,EAwBH,GAvBA8B,EAAMvD,KAAKhB,aAAaoE,EAAO,IAAI,GACnCM,EAAMzD,EAAIsB,EAAOA,EAAOxE,OAASwG,GACjCG,EAAMnE,GAAK,CACT0E,WAAYzC,EAAOA,EAAOzE,QAAUwG,GAAO,IAAIU,WAC/CC,UAAW1C,EAAOA,EAAOzE,OAAS,GAAGmH,UACrCC,aAAc3C,EAAOA,EAAOzE,QAAUwG,GAAO,IAAIY,aACjDC,YAAa5C,EAAOA,EAAOzE,OAAS,GAAGqH,aAErC3B,IACFiB,EAAMnE,GAAG8E,MAAQ,CACf7C,EAAOA,EAAOzE,QAAUwG,GAAO,IAAIc,MAAM,GACzC7C,EAAOA,EAAOzE,OAAS,GAAGsH,MAAM,KAYnB,qBATjBhB,EAAIrD,KAAKf,cAAcqF,MAAMZ,EAAO,CAClCxE,EACAC,EACAC,EACA8C,EAAYrD,GACZuE,EAAO,GACP7B,EACAC,GACA+C,OAAO7C,KAEP,OAAO2B,EAELE,IACFlC,EAAQA,EAAMM,MAAM,GAAI,EAAI4B,EAAM,GAClChC,EAASA,EAAOI,MAAM,GAAI,EAAI4B,GAC9B/B,EAASA,EAAOG,MAAM,GAAI,EAAI4B,IAEhClC,EAAMmB,KAAKxC,KAAKhB,aAAaoE,EAAO,IAAI,IACxC7B,EAAOiB,KAAKkB,EAAMzD,GAClBuB,EAAOgB,KAAKkB,EAAMnE,IAClBiE,EAAW9C,EAAMW,EAAMA,EAAMtE,OAAS,IAAIsE,EAAMA,EAAMtE,OAAS,IAC/DsE,EAAMmB,KAAKgB,GACX,MACF,KAAK,EACH,OAAO,EAEb,CACA,OAAO,CACT,GAAG,UAEDvB,EAAwB,WAmb1B,MAlba,CACXuC,IAAK,EACL5D,YAA4BlE,EAAAA,EAAAA,KAAO,SAAoBmE,EAAKC,GAC1D,IAAId,KAAKnB,GAAGrC,OAGV,MAAM,IAAIyE,MAAMJ,GAFhBb,KAAKnB,GAAGrC,OAAOoE,WAAWC,EAAKC,EAInC,GAAG,cAEHuB,UAA0B3F,EAAAA,EAAAA,KAAO,SAASyE,EAAOtC,GAiB/C,OAhBAmB,KAAKnB,GAAKA,GAAMmB,KAAKnB,IAAM,CAAC,EAC5BmB,KAAKyE,OAAStD,EACdnB,KAAK0E,MAAQ1E,KAAK2E,WAAa3E,KAAK4E,MAAO,EAC3C5E,KAAKZ,SAAWY,KAAKb,OAAS,EAC9Ba,KAAKd,OAASc,KAAK6E,QAAU7E,KAAK8D,MAAQ,GAC1C9D,KAAK8E,eAAiB,CAAC,WACvB9E,KAAKsC,OAAS,CACZ2B,WAAY,EACZE,aAAc,EACdD,UAAW,EACXE,YAAa,GAEXpE,KAAK0C,QAAQD,SACfzC,KAAKsC,OAAO+B,MAAQ,CAAC,EAAG,IAE1BrE,KAAK+E,OAAS,EACP/E,IACT,GAAG,YAEHmB,OAAuBzE,EAAAA,EAAAA,KAAO,WAC5B,IAAIsI,EAAKhF,KAAKyE,OAAO,GAiBrB,OAhBAzE,KAAKd,QAAU8F,EACfhF,KAAKb,SACLa,KAAK+E,SACL/E,KAAK8D,OAASkB,EACdhF,KAAK6E,SAAWG,EACJA,EAAGlB,MAAM,oBAEnB9D,KAAKZ,WACLY,KAAKsC,OAAO4B,aAEZlE,KAAKsC,OAAO8B,cAEVpE,KAAK0C,QAAQD,QACfzC,KAAKsC,OAAO+B,MAAM,KAEpBrE,KAAKyE,OAASzE,KAAKyE,OAAO9C,MAAM,GACzBqD,CACT,GAAG,SAEHC,OAAuBvI,EAAAA,EAAAA,KAAO,SAASsI,GACrC,IAAIzB,EAAMyB,EAAGjI,OACTmI,EAAQF,EAAGG,MAAM,iBACrBnF,KAAKyE,OAASO,EAAKhF,KAAKyE,OACxBzE,KAAKd,OAASc,KAAKd,OAAOkG,OAAO,EAAGpF,KAAKd,OAAOnC,OAASwG,GACzDvD,KAAK+E,QAAUxB,EACf,IAAI8B,EAAWrF,KAAK8D,MAAMqB,MAAM,iBAChCnF,KAAK8D,MAAQ9D,KAAK8D,MAAMsB,OAAO,EAAGpF,KAAK8D,MAAM/G,OAAS,GACtDiD,KAAK6E,QAAU7E,KAAK6E,QAAQO,OAAO,EAAGpF,KAAK6E,QAAQ9H,OAAS,GACxDmI,EAAMnI,OAAS,IACjBiD,KAAKZ,UAAY8F,EAAMnI,OAAS,GAElC,IAAIsG,EAAIrD,KAAKsC,OAAO+B,MAWpB,OAVArE,KAAKsC,OAAS,CACZ2B,WAAYjE,KAAKsC,OAAO2B,WACxBC,UAAWlE,KAAKZ,SAAW,EAC3B+E,aAAcnE,KAAKsC,OAAO6B,aAC1BC,YAAac,GAASA,EAAMnI,SAAWsI,EAAStI,OAASiD,KAAKsC,OAAO6B,aAAe,GAAKkB,EAASA,EAAStI,OAASmI,EAAMnI,QAAQA,OAASmI,EAAM,GAAGnI,OAASiD,KAAKsC,OAAO6B,aAAeZ,GAEtLvD,KAAK0C,QAAQD,SACfzC,KAAKsC,OAAO+B,MAAQ,CAAChB,EAAE,GAAIA,EAAE,GAAKrD,KAAKb,OAASoE,IAElDvD,KAAKb,OAASa,KAAKd,OAAOnC,OACnBiD,IACT,GAAG,SAEHsF,MAAsB5I,EAAAA,EAAAA,KAAO,WAE3B,OADAsD,KAAK0E,OAAQ,EACN1E,IACT,GAAG,QAEHuF,QAAwB7I,EAAAA,EAAAA,KAAO,WAC7B,OAAIsD,KAAK0C,QAAQ8C,iBACfxF,KAAK2E,YAAa,EAQb3E,MANEA,KAAKY,WAAW,0BAA4BZ,KAAKZ,SAAW,GAAK,mIAAqIY,KAAK4D,eAAgB,CAChOjE,KAAM,GACNiD,MAAO,KACPmB,KAAM/D,KAAKZ,UAIjB,GAAG,UAEHqG,MAAsB/I,EAAAA,EAAAA,KAAO,SAASsG,GACpChD,KAAKiF,MAAMjF,KAAK8D,MAAMnC,MAAMqB,GAC9B,GAAG,QAEH0C,WAA2BhJ,EAAAA,EAAAA,KAAO,WAChC,IAAIiJ,EAAO3F,KAAK6E,QAAQO,OAAO,EAAGpF,KAAK6E,QAAQ9H,OAASiD,KAAK8D,MAAM/G,QACnE,OAAQ4I,EAAK5I,OAAS,GAAK,MAAQ,IAAM4I,EAAKP,QAAQ,IAAIQ,QAAQ,MAAO,GAC3E,GAAG,aAEHC,eAA+BnJ,EAAAA,EAAAA,KAAO,WACpC,IAAIoJ,EAAO9F,KAAK8D,MAIhB,OAHIgC,EAAK/I,OAAS,KAChB+I,GAAQ9F,KAAKyE,OAAOW,OAAO,EAAG,GAAKU,EAAK/I,UAElC+I,EAAKV,OAAO,EAAG,KAAOU,EAAK/I,OAAS,GAAK,MAAQ,KAAK6I,QAAQ,MAAO,GAC/E,GAAG,iBAEHhC,cAA8BlH,EAAAA,EAAAA,KAAO,WACnC,IAAIqJ,EAAM/F,KAAK0F,YACXM,EAAI,IAAIlD,MAAMiD,EAAIhJ,OAAS,GAAG8G,KAAK,KACvC,OAAOkC,EAAM/F,KAAK6F,gBAAkB,KAAOG,EAAI,GACjD,GAAG,gBAEHC,YAA4BvJ,EAAAA,EAAAA,KAAO,SAASoH,EAAOoC,GACjD,IAAItD,EAAOsC,EAAOiB,EAmDlB,GAlDInG,KAAK0C,QAAQ8C,kBACfW,EAAS,CACP/G,SAAUY,KAAKZ,SACfkD,OAAQ,CACN2B,WAAYjE,KAAKsC,OAAO2B,WACxBC,UAAWlE,KAAKkE,UAChBC,aAAcnE,KAAKsC,OAAO6B,aAC1BC,YAAapE,KAAKsC,OAAO8B,aAE3BlF,OAAQc,KAAKd,OACb4E,MAAO9D,KAAK8D,MACZsC,QAASpG,KAAKoG,QACdvB,QAAS7E,KAAK6E,QACd1F,OAAQa,KAAKb,OACb4F,OAAQ/E,KAAK+E,OACbL,MAAO1E,KAAK0E,MACZD,OAAQzE,KAAKyE,OACb5F,GAAImB,KAAKnB,GACTiG,eAAgB9E,KAAK8E,eAAenD,MAAM,GAC1CiD,KAAM5E,KAAK4E,MAET5E,KAAK0C,QAAQD,SACf0D,EAAO7D,OAAO+B,MAAQrE,KAAKsC,OAAO+B,MAAM1C,MAAM,MAGlDuD,EAAQpB,EAAM,GAAGA,MAAM,sBAErB9D,KAAKZ,UAAY8F,EAAMnI,QAEzBiD,KAAKsC,OAAS,CACZ2B,WAAYjE,KAAKsC,OAAO4B,UACxBA,UAAWlE,KAAKZ,SAAW,EAC3B+E,aAAcnE,KAAKsC,OAAO8B,YAC1BA,YAAac,EAAQA,EAAMA,EAAMnI,OAAS,GAAGA,OAASmI,EAAMA,EAAMnI,OAAS,GAAG+G,MAAM,UAAU,GAAG/G,OAASiD,KAAKsC,OAAO8B,YAAcN,EAAM,GAAG/G,QAE/IiD,KAAKd,QAAU4E,EAAM,GACrB9D,KAAK8D,OAASA,EAAM,GACpB9D,KAAKoG,QAAUtC,EACf9D,KAAKb,OAASa,KAAKd,OAAOnC,OACtBiD,KAAK0C,QAAQD,SACfzC,KAAKsC,OAAO+B,MAAQ,CAACrE,KAAK+E,OAAQ/E,KAAK+E,QAAU/E,KAAKb,SAExDa,KAAK0E,OAAQ,EACb1E,KAAK2E,YAAa,EAClB3E,KAAKyE,OAASzE,KAAKyE,OAAO9C,MAAMmC,EAAM,GAAG/G,QACzCiD,KAAK6E,SAAWf,EAAM,GACtBlB,EAAQ5C,KAAKf,cAAc2C,KAAK5B,KAAMA,KAAKnB,GAAImB,KAAMkG,EAAclG,KAAK8E,eAAe9E,KAAK8E,eAAe/H,OAAS,IAChHiD,KAAK4E,MAAQ5E,KAAKyE,SACpBzE,KAAK4E,MAAO,GAEVhC,EACF,OAAOA,EACF,GAAI5C,KAAK2E,WAAY,CAC1B,IAAK,IAAIhI,KAAKwJ,EACZnG,KAAKrD,GAAKwJ,EAAOxJ,GAEnB,OAAO,CACT,CACA,OAAO,CACT,GAAG,cAEHmJ,MAAsBpJ,EAAAA,EAAAA,KAAO,WAC3B,GAAIsD,KAAK4E,KACP,OAAO5E,KAAKwE,IAKd,IAAI5B,EAAOkB,EAAOuC,EAAWC,EAHxBtG,KAAKyE,SACRzE,KAAK4E,MAAO,GAGT5E,KAAK0E,QACR1E,KAAKd,OAAS,GACdc,KAAK8D,MAAQ,IAGf,IADA,IAAIyC,EAAQvG,KAAKwG,gBACRC,EAAI,EAAGA,EAAIF,EAAMxJ,OAAQ0J,IAEhC,IADAJ,EAAYrG,KAAKyE,OAAOX,MAAM9D,KAAKuG,MAAMA,EAAME,SAC5B3C,GAASuC,EAAU,GAAGtJ,OAAS+G,EAAM,GAAG/G,QAAS,CAGlE,GAFA+G,EAAQuC,EACRC,EAAQG,EACJzG,KAAK0C,QAAQ8C,gBAAiB,CAEhC,IAAc,KADd5C,EAAQ5C,KAAKiG,WAAWI,EAAWE,EAAME,KAEvC,OAAO7D,EACF,GAAI5C,KAAK2E,WAAY,CAC1Bb,GAAQ,EACR,QACF,CACE,OAAO,CAEX,CAAO,IAAK9D,KAAK0C,QAAQgE,KACvB,KAEJ,CAEF,OAAI5C,GAEY,KADdlB,EAAQ5C,KAAKiG,WAAWnC,EAAOyC,EAAMD,MAE5B1D,EAIS,KAAhB5C,KAAKyE,OACAzE,KAAKwE,IAELxE,KAAKY,WAAW,0BAA4BZ,KAAKZ,SAAW,GAAK,yBAA2BY,KAAK4D,eAAgB,CACtHjE,KAAM,GACNiD,MAAO,KACPmB,KAAM/D,KAAKZ,UAGjB,GAAG,QAEHuD,KAAqBjG,EAAAA,EAAAA,KAAO,WAC1B,IAAI2G,EAAIrD,KAAK8F,OACb,OAAIzC,GAGKrD,KAAK2C,KAEhB,GAAG,OAEHgE,OAAuBjK,EAAAA,EAAAA,KAAO,SAAekK,GAC3C5G,KAAK8E,eAAetC,KAAKoE,EAC3B,GAAG,SAEHC,UAA0BnK,EAAAA,EAAAA,KAAO,WAE/B,OADQsD,KAAK8E,eAAe/H,OAAS,EAC7B,EACCiD,KAAK8E,eAAejC,MAEpB7C,KAAK8E,eAAe,EAE/B,GAAG,YAEH0B,eAA+B9J,EAAAA,EAAAA,KAAO,WACpC,OAAIsD,KAAK8E,eAAe/H,QAAUiD,KAAK8E,eAAe9E,KAAK8E,eAAe/H,OAAS,GAC1EiD,KAAK8G,WAAW9G,KAAK8E,eAAe9E,KAAK8E,eAAe/H,OAAS,IAAIwJ,MAErEvG,KAAK8G,WAAoB,QAAEP,KAEtC,GAAG,iBAEHQ,UAA0BrK,EAAAA,EAAAA,KAAO,SAAkBsG,GAEjD,OADAA,EAAIhD,KAAK8E,eAAe/H,OAAS,EAAIiK,KAAKC,IAAIjE,GAAK,KAC1C,EACAhD,KAAK8E,eAAe9B,GAEpB,SAEX,GAAG,YAEHkE,WAA2BxK,EAAAA,EAAAA,KAAO,SAAmBkK,GACnD5G,KAAK2G,MAAMC,EACb,GAAG,aAEHO,gBAAgCzK,EAAAA,EAAAA,KAAO,WACrC,OAAOsD,KAAK8E,eAAe/H,MAC7B,GAAG,kBACH2F,QAAS,CAAE,oBAAoB,GAC/BzD,eAA+BvC,EAAAA,EAAAA,KAAO,SAAmBmC,EAAIuI,EAAKC,EAA2BC,GAE3F,OAAQD,GACN,KAAK,EAEL,KAAK,EAaL,KAAK,EA4HL,KAAK,GACH,MAxIF,KAAK,EAIL,KAAK,EAEH,OADArH,KAAK6G,WACE,GAET,KAAK,EACH,OAAO,GAIT,KAAK,EACH,OAAO,GAET,KAAK,EAEH,OADA7G,KAAKkH,UAAU,aACR,GAET,KAAK,EAEH,OADAlH,KAAK6G,WACE,kBAET,KAAK,EAEH,OADA7G,KAAKkH,UAAU,aACR,GAET,KAAK,GAEH,OADAlH,KAAK6G,WACE,kBAET,KAAK,GACH7G,KAAKkH,UAAU,uBACf,MACF,KAAK,GA8CL,KAAK,GAML,KAAK,GACHlH,KAAK6G,WACL,MAnDF,KAAK,GACH,MAAO,4BAET,KAAK,GACH,OAAO,EAET,KAAK,GACH,OAAO,EAET,KAAK,GAEH,OADA7G,KAAKkH,UAAU,aACR,SAET,KAAK,GAEH,OADAlH,KAAKkH,UAAU,aACR,SAET,KAAK,GAEH,OADAlH,KAAKkH,UAAU,kBACR,GAET,KAAK,GACH,OAAO,GAET,KAAK,GAEH,OADAlH,KAAKkH,UAAU,QACR,GAET,KAAK,GAEH,OADAlH,KAAKkH,UAAU,QACR,GAET,KAAK,GAEH,OADAlH,KAAKkH,UAAU,cACR,GAET,KAAK,GACH,OAAO,GAET,KAAK,GAEH,OADAlH,KAAK6G,WACE,GAKT,KAAK,GACH7G,KAAKkH,UAAU,UACf,MAIF,KAAK,GACH,MAAO,MAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,MAAO,QAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAIT,KAAK,GACH,OAAO,GAET,KAAK,GACH,OAAO,GAGb,GAAG,aACHX,MAAO,CAAC,uBAAwB,sBAAuB,gBAAiB,gBAAiB,gBAAiB,iBAAkB,gBAAiB,wBAAyB,wBAAyB,wBAAyB,wBAAyB,yBAA0B,WAAY,eAAgB,uBAAwB,gCAAiC,iBAAkB,iBAAkB,WAAY,YAAa,eAAgB,cAAe,WAAY,qCAAsC,WAAY,iLAAkL,YAAa,YAAa,cAAe,WAAY,WAAY,kBAAmB,UAAW,WAAY,UAAW,UAAW,WAAY,UAAW,aAAc,WAAY,UAAW,UAAW,eAAgB,YAAa,UAAW,WAC55BO,WAAY,CAAE,WAAc,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,GAAQ,KAAQ,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,GAAQ,eAAkB,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,GAAQ,UAAa,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,GAAQ,oBAAuB,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,IAAK,WAAa,GAAS,UAAa,CAAE,MAAS,CAAC,GAAI,WAAa,GAAS,MAAS,CAAE,MAAS,GAAI,WAAa,GAAS,UAAa,CAAE,MAAS,GAAI,WAAa,GAAS,OAAU,CAAE,MAAS,CAAC,GAAI,IAAK,WAAa,GAAS,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,WAAa,IAGrsC,CApb4B,GAsb5B,SAASS,IACPvH,KAAKnB,GAAK,CAAC,CACb,CAIA,OAPAF,EAAQsD,MAAQA,GAIhBvF,EAAAA,EAAAA,IAAO6K,EAAQ,UACfA,EAAOpF,UAAYxD,EACnBA,EAAQ4I,OAASA,EACV,IAAIA,CACb,CA/rBa,GAgsBb/K,EAAOA,OAASA,EAChB,IAAIgL,EAAkBhL,EAGtB,SAASiL,EAAUC,GACjB,MAAqB,QAAdA,EAAK5H,IACd,CAEA,SAAS6H,EAAeD,GACtB,MAAqB,SAAdA,EAAK5H,IACd,CAEA,SAAS8H,EAAiBF,GACxB,MAAqB,WAAdA,EAAK5H,IACd,EAPApD,EAAAA,EAAAA,IAAO+K,EAAW,cAIlB/K,EAAAA,EAAAA,IAAOiL,EAAgB,mBAIvBjL,EAAAA,EAAAA,IAAOkL,EAAkB,oBAGzB,IAAIC,EAAkC,MACpCC,WAAAA,CAAYC,GACV/H,KAAK+H,YAAcA,CACrB,CAAC,eAECrL,EAAAA,EAAAA,IAAOsD,KAAM,mCAFd,GAIDgI,eAAAA,CAAgBC,EAAOC,GACrB,IAAKlI,KAAK+H,YACR,MAAO,CACLI,MAAOF,EAAMG,QAAO,CAACC,EAAKC,IAAQtB,KAAKuB,IAAID,EAAIvL,OAAQsL,IAAM,GAAKH,EAClEM,OAAQN,GAGZ,MAAMO,EAAY,CAChBN,MAAO,EACPK,OAAQ,GAEJE,EAAO1I,KAAK+H,YAAYY,OAAO,KAAKC,KAAK,aAAc,UAAUA,KAAK,YAAaV,GACzF,IAAK,MAAMW,KAAKZ,EAAO,CACrB,MAAMa,GAAOC,EAAAA,EAAAA,IAAuBL,EAAM,EAAGG,GACvCV,EAAQW,EAAOA,EAAKX,MAAQU,EAAE9L,OAASmL,EACvCM,EAASM,EAAOA,EAAKN,OAASN,EACpCO,EAAUN,MAAQnB,KAAKuB,IAAIE,EAAUN,MAAOA,GAC5CM,EAAUD,OAASxB,KAAKuB,IAAIE,EAAUD,OAAQA,EAChD,CAEA,OADAE,EAAKM,SACEP,CACT,GASEQ,EAAW,MACbnB,WAAAA,CAAYoB,EAAYC,EAAOC,EAAyBC,GACtDrJ,KAAKkJ,WAAaA,EAClBlJ,KAAKmJ,MAAQA,EACbnJ,KAAKoJ,wBAA0BA,EAC/BpJ,KAAKqJ,gBAAkBA,EACvBrJ,KAAKsJ,aAAe,CAAEC,EAAG,EAAGC,EAAG,EAAGrB,MAAO,EAAGK,OAAQ,GACpDxI,KAAKyJ,aAAe,OACpBzJ,KAAK0J,WAAY,EACjB1J,KAAK2J,WAAY,EACjB3J,KAAK4J,UAAW,EAChB5J,KAAK6J,cAAe,EACpB7J,KAAK8J,aAAe,EACpB9J,KAAK+J,gBAAkB,EACvB/J,KAAKgK,gBAAkB,EACvBhK,KAAKqE,MAAQ,CAAC,EAAG,IACjBrE,KAAKsJ,aAAe,CAAEC,EAAG,EAAGC,EAAG,EAAGrB,MAAO,EAAGK,OAAQ,GACpDxI,KAAKyJ,aAAe,MACtB,CAAC,eAEC/M,EAAAA,EAAAA,IAAOsD,KAAM,YAFd,GAIDiK,QAAAA,CAAS5F,GACPrE,KAAKqE,MAAQA,EACa,SAAtBrE,KAAKyJ,cAAiD,UAAtBzJ,KAAKyJ,aACvCzJ,KAAKsJ,aAAad,OAASnE,EAAM,GAAKA,EAAM,GAE5CrE,KAAKsJ,aAAanB,MAAQ9D,EAAM,GAAKA,EAAM,GAE7CrE,KAAKkK,kBACP,CACAC,QAAAA,GACE,MAAO,CAACnK,KAAKqE,MAAM,GAAKrE,KAAK8J,aAAc9J,KAAKqE,MAAM,GAAKrE,KAAK8J,aAClE,CACAM,eAAAA,CAAgBX,GACdzJ,KAAKyJ,aAAeA,EACpBzJ,KAAKiK,SAASjK,KAAKqE,MACrB,CACAgG,eAAAA,GACE,MAAMhG,EAAQrE,KAAKmK,WACnB,OAAOnD,KAAKC,IAAI5C,EAAM,GAAKA,EAAM,IAAMrE,KAAKsK,gBAAgBvN,MAC9D,CACAwN,mBAAAA,GACE,OAAOvK,KAAK8J,YACd,CACAU,iBAAAA,GACE,OAAOxK,KAAKoJ,wBAAwBpB,gBAClChI,KAAKsK,gBAAgBG,KAAKC,GAASA,EAAKC,aACxC3K,KAAKkJ,WAAW0B,cAEpB,CACAC,gCAAAA,GArDkC,GAsDI7K,KAAKqK,kBAAwC,EAApBrK,KAAK8J,eAChE9J,KAAK8J,aAAe9C,KAAK8D,MAvDK,GAuDiC9K,KAAKqK,kBAAoB,IAE1FrK,KAAKkK,kBACP,CACAa,iCAAAA,CAAkCC,GAChC,IAAIC,EAAkBD,EAAexC,OAKrC,GAJIxI,KAAKkJ,WAAWW,cAAgBoB,EAAkBjL,KAAKkJ,WAAWgC,gBACpED,GAAmBjL,KAAKkJ,WAAWgC,cACnClL,KAAK6J,cAAe,GAElB7J,KAAKkJ,WAAWS,UAAW,CAC7B,MAAMwB,EAAgBnL,KAAKwK,oBACrBY,EAlEkC,GAkEqBJ,EAAe7C,MAC5EnI,KAAK8J,aAAe9C,KAAKqE,IAAIF,EAAchD,MAAQ,EAAGiD,GACtD,MAAME,EAAiBH,EAAc3C,OAAwC,EAA/BxI,KAAKkJ,WAAWqC,aAC9DvL,KAAKgK,gBAAkBmB,EAAc3C,OACjC8C,GAAkBL,IACpBA,GAAmBK,EACnBtL,KAAK2J,WAAY,EAErB,CAKA,GAJI3J,KAAKkJ,WAAWU,UAAYqB,GAAmBjL,KAAKkJ,WAAWsC,aACjExL,KAAK4J,UAAW,EAChBqB,GAAmBjL,KAAKkJ,WAAWsC,YAEjCxL,KAAKkJ,WAAWQ,WAAa1J,KAAKmJ,MAAO,CAC3C,MAAMgC,EAAgBnL,KAAKoJ,wBAAwBpB,gBACjD,CAAChI,KAAKmJ,OACNnJ,KAAKkJ,WAAWuC,eAEZH,EAAiBH,EAAc3C,OAAwC,EAA/BxI,KAAKkJ,WAAWwC,aAC9D1L,KAAK+J,gBAAkBoB,EAAc3C,OACjC8C,GAAkBL,IACpBA,GAAmBK,EACnBtL,KAAK0J,WAAY,EAErB,CACA1J,KAAKsJ,aAAanB,MAAQ6C,EAAe7C,MACzCnI,KAAKsJ,aAAad,OAASwC,EAAexC,OAASyC,CACrD,CACAU,6BAAAA,CAA8BX,GAC5B,IAAIY,EAAiBZ,EAAe7C,MAKpC,GAJInI,KAAKkJ,WAAWW,cAAgB+B,EAAiB5L,KAAKkJ,WAAWgC,gBACnEU,GAAkB5L,KAAKkJ,WAAWgC,cAClClL,KAAK6J,cAAe,GAElB7J,KAAKkJ,WAAWS,UAAW,CAC7B,MAAMwB,EAAgBnL,KAAKwK,oBACrBY,EAtGkC,GAsGqBJ,EAAexC,OAC5ExI,KAAK8J,aAAe9C,KAAKqE,IAAIF,EAAc3C,OAAS,EAAG4C,GACvD,MAAMS,EAAgBV,EAAchD,MAAuC,EAA/BnI,KAAKkJ,WAAWqC,aACxDM,GAAiBD,IACnBA,GAAkBC,EAClB7L,KAAK2J,WAAY,EAErB,CAKA,GAJI3J,KAAKkJ,WAAWU,UAAYgC,GAAkB5L,KAAKkJ,WAAWsC,aAChExL,KAAK4J,UAAW,EAChBgC,GAAkB5L,KAAKkJ,WAAWsC,YAEhCxL,KAAKkJ,WAAWQ,WAAa1J,KAAKmJ,MAAO,CAC3C,MAAMgC,EAAgBnL,KAAKoJ,wBAAwBpB,gBACjD,CAAChI,KAAKmJ,OACNnJ,KAAKkJ,WAAWuC,eAEZI,EAAgBV,EAAc3C,OAAwC,EAA/BxI,KAAKkJ,WAAWwC,aAC7D1L,KAAK+J,gBAAkBoB,EAAc3C,OACjCqD,GAAiBD,IACnBA,GAAkBC,EAClB7L,KAAK0J,WAAY,EAErB,CACA1J,KAAKsJ,aAAanB,MAAQ6C,EAAe7C,MAAQyD,EACjD5L,KAAKsJ,aAAad,OAASwC,EAAexC,MAC5C,CACAsD,cAAAA,CAAed,GAOb,MAN0B,SAAtBhL,KAAKyJ,cAAiD,UAAtBzJ,KAAKyJ,aACvCzJ,KAAK2L,8BAA8BX,GAEnChL,KAAK+K,kCAAkCC,GAEzChL,KAAKkK,mBACE,CACL/B,MAAOnI,KAAKsJ,aAAanB,MACzBK,OAAQxI,KAAKsJ,aAAad,OAE9B,CACAuD,gBAAAA,CAAiBC,GACfhM,KAAKsJ,aAAaC,EAAIyC,EAAMzC,EAC5BvJ,KAAKsJ,aAAaE,EAAIwC,EAAMxC,CAC9B,CACAyC,8BAAAA,GACE,MAAMC,EAAkB,GACxB,GAAIlM,KAAK6J,aAAc,CACrB,MAAMN,EAAIvJ,KAAKsJ,aAAaC,EAAIvJ,KAAKsJ,aAAanB,MAAQnI,KAAKkJ,WAAWgC,cAAgB,EAC1FgB,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,YAAa,cAC1BzE,KAAM,CACJ,CACE0E,KAAM,KAAK7C,KAAKvJ,KAAKsJ,aAAaE,OAAOD,KAAKvJ,KAAKsJ,aAAaE,EAAIxJ,KAAKsJ,aAAad,UACtF6D,WAAYrM,KAAKqJ,gBAAgBiD,cACjCC,YAAavM,KAAKkJ,WAAWgC,iBAIrC,CAiBA,GAhBIlL,KAAK2J,WACPuC,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,YAAa,SAC1BzE,KAAM1H,KAAKsK,gBAAgBG,KAAKC,IAAI,CAClC/K,KAAM+K,EAAKC,WACXpB,EAAGvJ,KAAKsJ,aAAaC,EAAIvJ,KAAKsJ,aAAanB,OAASnI,KAAK2J,UAAY3J,KAAKkJ,WAAWqC,aAAe,IAAMvL,KAAK4J,SAAW5J,KAAKkJ,WAAWsC,WAAa,IAAMxL,KAAK6J,aAAe7J,KAAKkJ,WAAWgC,cAAgB,GACjN1B,EAAGxJ,KAAKwM,cAAc9B,GACtB+B,KAAMzM,KAAKqJ,gBAAgBqD,WAC3BxE,SAAUlI,KAAKkJ,WAAW0B,cAC1B+B,SAAU,EACVC,YAAa,SACbC,cAAe,cAIjB7M,KAAK4J,SAAU,CACjB,MAAML,EAAIvJ,KAAKsJ,aAAaC,EAAIvJ,KAAKsJ,aAAanB,OAASnI,KAAK6J,aAAe7J,KAAKkJ,WAAWgC,cAAgB,GAC/GgB,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,YAAa,SAC1BzE,KAAM1H,KAAKsK,gBAAgBG,KAAKC,IAAI,CAClC0B,KAAM,KAAK7C,KAAKvJ,KAAKwM,cAAc9B,QAAWnB,EAAIvJ,KAAKkJ,WAAWsC,cAAcxL,KAAKwM,cAAc9B,KACnG2B,WAAYrM,KAAKqJ,gBAAgByD,UACjCP,YAAavM,KAAKkJ,WAAW6D,eAGnC,CAmBA,OAlBI/M,KAAK0J,WACPwC,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,YAAa,SAC1BzE,KAAM,CACJ,CACE/H,KAAMK,KAAKmJ,MACXI,EAAGvJ,KAAKsJ,aAAaC,EAAIvJ,KAAKkJ,WAAWwC,aACzClC,EAAGxJ,KAAKsJ,aAAaE,EAAIxJ,KAAKsJ,aAAad,OAAS,EACpDiE,KAAMzM,KAAKqJ,gBAAgB2D,WAC3B9E,SAAUlI,KAAKkJ,WAAWuC,cAC1BkB,SAAU,IACVC,YAAa,MACbC,cAAe,aAKhBX,CACT,CACAe,gCAAAA,GACE,MAAMf,EAAkB,GACxB,GAAIlM,KAAK6J,aAAc,CACrB,MAAML,EAAIxJ,KAAKsJ,aAAaE,EAAIxJ,KAAKkJ,WAAWgC,cAAgB,EAChEgB,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,cAAe,aAC5BzE,KAAM,CACJ,CACE0E,KAAM,KAAKpM,KAAKsJ,aAAaC,KAAKC,OAAOxJ,KAAKsJ,aAAaC,EAAIvJ,KAAKsJ,aAAanB,SAASqB,IAC1F6C,WAAYrM,KAAKqJ,gBAAgBiD,cACjCC,YAAavM,KAAKkJ,WAAWgC,iBAIrC,CAiBA,GAhBIlL,KAAK2J,WACPuC,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,cAAe,SAC5BzE,KAAM1H,KAAKsK,gBAAgBG,KAAKC,IAAI,CAClC/K,KAAM+K,EAAKC,WACXpB,EAAGvJ,KAAKwM,cAAc9B,GACtBlB,EAAGxJ,KAAKsJ,aAAaE,EAAIxJ,KAAKkJ,WAAWqC,cAAgBvL,KAAK4J,SAAW5J,KAAKkJ,WAAWsC,WAAa,IAAMxL,KAAK6J,aAAe7J,KAAKkJ,WAAWgC,cAAgB,GAChKuB,KAAMzM,KAAKqJ,gBAAgBqD,WAC3BxE,SAAUlI,KAAKkJ,WAAW0B,cAC1B+B,SAAU,EACVC,YAAa,MACbC,cAAe,eAIjB7M,KAAK4J,SAAU,CACjB,MAAMJ,EAAIxJ,KAAKsJ,aAAaE,GAAKxJ,KAAK6J,aAAe7J,KAAKkJ,WAAWgC,cAAgB,GACrFgB,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,cAAe,SAC5BzE,KAAM1H,KAAKsK,gBAAgBG,KAAKC,IAAI,CAClC0B,KAAM,KAAKpM,KAAKwM,cAAc9B,MAASlB,OAAOxJ,KAAKwM,cAAc9B,MAASlB,EAAIxJ,KAAKkJ,WAAWsC,aAC9Fa,WAAYrM,KAAKqJ,gBAAgByD,UACjCP,YAAavM,KAAKkJ,WAAW6D,eAGnC,CAmBA,OAlBI/M,KAAK0J,WACPwC,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,cAAe,SAC5BzE,KAAM,CACJ,CACE/H,KAAMK,KAAKmJ,MACXI,EAAGvJ,KAAKqE,MAAM,IAAMrE,KAAKqE,MAAM,GAAKrE,KAAKqE,MAAM,IAAM,EACrDmF,EAAGxJ,KAAKsJ,aAAaE,EAAIxJ,KAAKsJ,aAAad,OAASxI,KAAKkJ,WAAWwC,aAAe1L,KAAK+J,gBACxF0C,KAAMzM,KAAKqJ,gBAAgB2D,WAC3B9E,SAAUlI,KAAKkJ,WAAWuC,cAC1BkB,SAAU,EACVC,YAAa,MACbC,cAAe,aAKhBX,CACT,CACAgB,6BAAAA,GACE,MAAMhB,EAAkB,GACxB,GAAIlM,KAAK6J,aAAc,CACrB,MAAML,EAAIxJ,KAAKsJ,aAAaE,EAAIxJ,KAAKsJ,aAAad,OAASxI,KAAKkJ,WAAWgC,cAAgB,EAC3FgB,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,WAAY,aACzBzE,KAAM,CACJ,CACE0E,KAAM,KAAKpM,KAAKsJ,aAAaC,KAAKC,OAAOxJ,KAAKsJ,aAAaC,EAAIvJ,KAAKsJ,aAAanB,SAASqB,IAC1F6C,WAAYrM,KAAKqJ,gBAAgBiD,cACjCC,YAAavM,KAAKkJ,WAAWgC,iBAIrC,CAiBA,GAhBIlL,KAAK2J,WACPuC,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,WAAY,SACzBzE,KAAM1H,KAAKsK,gBAAgBG,KAAKC,IAAI,CAClC/K,KAAM+K,EAAKC,WACXpB,EAAGvJ,KAAKwM,cAAc9B,GACtBlB,EAAGxJ,KAAKsJ,aAAaE,GAAKxJ,KAAK0J,UAAY1J,KAAK+J,gBAAiD,EAA/B/J,KAAKkJ,WAAWwC,aAAmB,GAAK1L,KAAKkJ,WAAWqC,aAC1HkB,KAAMzM,KAAKqJ,gBAAgBqD,WAC3BxE,SAAUlI,KAAKkJ,WAAW0B,cAC1B+B,SAAU,EACVC,YAAa,MACbC,cAAe,eAIjB7M,KAAK4J,SAAU,CACjB,MAAMJ,EAAIxJ,KAAKsJ,aAAaE,EAC5B0C,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,WAAY,SACzBzE,KAAM1H,KAAKsK,gBAAgBG,KAAKC,IAAI,CAClC0B,KAAM,KAAKpM,KAAKwM,cAAc9B,MAASlB,EAAIxJ,KAAKsJ,aAAad,QAAUxI,KAAK6J,aAAe7J,KAAKkJ,WAAWgC,cAAgB,QAAQlL,KAAKwM,cAAc9B,MAASlB,EAAIxJ,KAAKsJ,aAAad,OAASxI,KAAKkJ,WAAWsC,YAAcxL,KAAK6J,aAAe7J,KAAKkJ,WAAWgC,cAAgB,KAChRmB,WAAYrM,KAAKqJ,gBAAgByD,UACjCP,YAAavM,KAAKkJ,WAAW6D,eAGnC,CAmBA,OAlBI/M,KAAK0J,WACPwC,EAAgB1J,KAAK,CACnB1C,KAAM,OACNqM,WAAY,CAAC,WAAY,SACzBzE,KAAM,CACJ,CACE/H,KAAMK,KAAKmJ,MACXI,EAAGvJ,KAAKsJ,aAAaC,EAAIvJ,KAAKsJ,aAAanB,MAAQ,EACnDqB,EAAGxJ,KAAKsJ,aAAaE,EAAIxJ,KAAKkJ,WAAWwC,aACzCe,KAAMzM,KAAKqJ,gBAAgB2D,WAC3B9E,SAAUlI,KAAKkJ,WAAWuC,cAC1BkB,SAAU,EACVC,YAAa,MACbC,cAAe,aAKhBX,CACT,CACAiB,mBAAAA,GACE,GAA0B,SAAtBnN,KAAKyJ,aACP,OAAOzJ,KAAKiM,iCAEd,GAA0B,UAAtBjM,KAAKyJ,aACP,MAAMxI,MAAM,4CAEd,MAA0B,WAAtBjB,KAAKyJ,aACAzJ,KAAKiN,mCAEY,QAAtBjN,KAAKyJ,aACAzJ,KAAKkN,gCAEP,EACT,GAIEE,EAAW,cAAcnE,EAAS,eAElCvM,EAAAA,EAAAA,IAAOsD,KAAM,YAFqB,GAIpC8H,WAAAA,CAAYoB,EAAYG,EAAiBgE,EAAYlE,EAAOC,GAC1DkE,MAAMpE,EAAYC,EAAOC,EAAyBC,GAClDrJ,KAAKqN,WAAaA,EAClBrN,KAAKuN,OAAQC,EAAAA,EAAAA,MAAYC,OAAOzN,KAAKqN,YAAYhJ,MAAMrE,KAAKmK,WAC9D,CACAF,QAAAA,CAAS5F,GACPiJ,MAAMrD,SAAS5F,EACjB,CACA6F,gBAAAA,GACElK,KAAKuN,OAAQC,EAAAA,EAAAA,MAAYC,OAAOzN,KAAKqN,YAAYhJ,MAAMrE,KAAKmK,YAAYuD,aAAa,GAAGC,aAAa,GAAGC,MAAM,IAC9GC,EAAAA,GAAIjP,MAAM,0CAA2CoB,KAAKqN,WAAYrN,KAAKmK,WAC7E,CACAG,aAAAA,GACE,OAAOtK,KAAKqN,UACd,CACAb,aAAAA,CAAcsB,GACZ,OAAO9N,KAAKuN,MAAMO,IAAU9N,KAAKmK,WAAW,EAC9C,GAKE4D,EAAa,cAAc9E,EAAS,eAEpCvM,EAAAA,EAAAA,IAAOsD,KAAM,cAFuB,GAItC8H,WAAAA,CAAYoB,EAAYG,EAAiBoE,EAAQtE,EAAOC,GACtDkE,MAAMpE,EAAYC,EAAOC,EAAyBC,GAClDrJ,KAAKyN,OAASA,EACdzN,KAAKuN,OAAQS,EAAAA,EAAAA,OAAcP,OAAOzN,KAAKyN,QAAQpJ,MAAMrE,KAAKmK,WAC5D,CACAG,aAAAA,GACE,OAAOtK,KAAKuN,MAAMU,OACpB,CACA/D,gBAAAA,GACE,MAAMuD,EAAS,IAAIzN,KAAKyN,QACE,SAAtBzN,KAAKyJ,cACPgE,EAAOS,UAETlO,KAAKuN,OAAQS,EAAAA,EAAAA,OAAcP,OAAOA,GAAQpJ,MAAMrE,KAAKmK,WACvD,CACAqC,aAAAA,CAAcsB,GACZ,OAAO9N,KAAKuN,MAAMO,EACpB,GAIF,SAASK,EAAQzG,EAAMwB,EAAYG,EAAiB+E,GAClD,MAAMhF,EAA0B,IAAIvB,EAAgCuG,GACpE,OAAIzG,EAAeD,GACV,IAAI0F,EACTlE,EACAG,EACA3B,EAAK2F,WACL3F,EAAKyB,MACLC,GAGG,IAAI2E,EACT7E,EACAG,EACA,CAAC3B,EAAK2D,IAAK3D,EAAKa,KAChBb,EAAKyB,MACLC,EAEJ,EACA1M,EAAAA,EAAAA,IAAOyR,EAAS,WAGhB,IAAIE,EAAa,MACfvG,WAAAA,CAAYsB,EAAyBkF,EAAaC,EAAWC,GAC3DxO,KAAKoJ,wBAA0BA,EAC/BpJ,KAAKsO,YAAcA,EACnBtO,KAAKuO,UAAYA,EACjBvO,KAAKwO,iBAAmBA,EACxBxO,KAAKsJ,aAAe,CAClBC,EAAG,EACHC,EAAG,EACHrB,MAAO,EACPK,OAAQ,GAEVxI,KAAKyO,gBAAiB,CACxB,CAAC,eAEC/R,EAAAA,EAAAA,IAAOsD,KAAM,cAFd,GAID+L,gBAAAA,CAAiBC,GACfhM,KAAKsJ,aAAaC,EAAIyC,EAAMzC,EAC5BvJ,KAAKsJ,aAAaE,EAAIwC,EAAMxC,CAC9B,CACAsC,cAAAA,CAAed,GACb,MAAM0D,EAAiB1O,KAAKoJ,wBAAwBpB,gBAClD,CAAChI,KAAKuO,UAAUpF,OAChBnJ,KAAKsO,YAAY7C,eAEbI,EAAgB7E,KAAKuB,IAAImG,EAAevG,MAAO6C,EAAe7C,OAC9DmD,EAAiBoD,EAAelG,OAAS,EAAIxI,KAAKsO,YAAY5C,aAMpE,OALIgD,EAAevG,OAAS0D,GAAiB6C,EAAelG,QAAU8C,GAAkBtL,KAAKsO,YAAY5E,WAAa1J,KAAKuO,UAAUpF,QACnInJ,KAAKsJ,aAAanB,MAAQ0D,EAC1B7L,KAAKsJ,aAAad,OAAS8C,EAC3BtL,KAAKyO,gBAAiB,GAEjB,CACLtG,MAAOnI,KAAKsJ,aAAanB,MACzBK,OAAQxI,KAAKsJ,aAAad,OAE9B,CACA2E,mBAAAA,GACE,MAAMwB,EAAe,GAmBrB,OAlBI3O,KAAKyO,gBACPE,EAAanM,KAAK,CAChB2J,WAAY,CAAC,eACbrM,KAAM,OACN4H,KAAM,CACJ,CACEQ,SAAUlI,KAAKsO,YAAY7C,cAC3B9L,KAAMK,KAAKuO,UAAUpF,MACrByD,YAAa,SACbC,cAAe,SACftD,EAAGvJ,KAAKsJ,aAAaC,EAAIvJ,KAAKsJ,aAAanB,MAAQ,EACnDqB,EAAGxJ,KAAKsJ,aAAaE,EAAIxJ,KAAKsJ,aAAad,OAAS,EACpDiE,KAAMzM,KAAKwO,iBAAiBxB,WAC5BL,SAAU,MAKXgC,CACT,GAEF,SAASC,EAAuBN,EAAaC,EAAWC,EAAkBJ,GACxE,MAAMhF,EAA0B,IAAIvB,EAAgCuG,GACpE,OAAO,IAAIC,EAAWjF,EAAyBkF,EAAaC,EAAWC,EACzE,EACA9R,EAAAA,EAAAA,IAAOkS,EAAwB,0BAI/B,IAAIC,EAAW,MACb/G,WAAAA,CAAYgH,EAAUC,EAAOC,EAAOC,EAAaC,GAC/ClP,KAAK8O,SAAWA,EAChB9O,KAAK+O,MAAQA,EACb/O,KAAKgP,MAAQA,EACbhP,KAAKiP,YAAcA,EACnBjP,KAAKmP,UAAYD,CACnB,CAAC,eAECxS,EAAAA,EAAAA,IAAOsD,KAAM,YAFd,GAIDoP,kBAAAA,GACE,MAAMC,EAAYrP,KAAK8O,SAASpH,KAAK+C,KAAK6E,GAAM,CAC9CtP,KAAK+O,MAAMvC,cAAc8C,EAAE,IAC3BtP,KAAKgP,MAAMxC,cAAc8C,EAAE,OAE7B,IAAIlD,EAMJ,OAJEA,EADuB,eAArBpM,KAAKiP,aACAlL,EAAAA,EAAAA,OAAOyF,GAAG8F,GAAMA,EAAE,KAAI/F,GAAG+F,GAAMA,EAAE,IAAjCvL,CAAqCsL,IAErCtL,EAAAA,EAAAA,OAAOwF,GAAG+F,GAAMA,EAAE,KAAI9F,GAAG8F,GAAMA,EAAE,IAAjCvL,CAAqCsL,GAEzCjD,EAGE,CACL,CACED,WAAY,CAAC,OAAQ,aAAanM,KAAKmP,aACvCrP,KAAM,OACN4H,KAAM,CACJ,CACE0E,OACAC,WAAYrM,KAAK8O,SAASzC,WAC1BE,YAAavM,KAAK8O,SAASvC,gBAV1B,EAeX,GAIEgD,EAAU,MACZzH,WAAAA,CAAY0H,EAASlG,EAAcyF,EAAOC,EAAOC,EAAaC,GAC5DlP,KAAKwP,QAAUA,EACfxP,KAAKsJ,aAAeA,EACpBtJ,KAAK+O,MAAQA,EACb/O,KAAKgP,MAAQA,EACbhP,KAAKiP,YAAcA,EACnBjP,KAAKmP,UAAYD,CACnB,CAAC,eAECxS,EAAAA,EAAAA,IAAOsD,KAAM,WAFd,GAIDoP,kBAAAA,GACE,MAAMC,EAAYrP,KAAKwP,QAAQ9H,KAAK+C,KAAK6E,GAAM,CAC7CtP,KAAK+O,MAAMvC,cAAc8C,EAAE,IAC3BtP,KAAKgP,MAAMxC,cAAc8C,EAAE,OAGvBG,EAA0F,IAA/EzI,KAAKqE,IAAuC,EAAnCrL,KAAK+O,MAAMxE,sBAA2BvK,KAAK+O,MAAM1E,mBACrEqF,EAAeD,EAAW,EAChC,MAAyB,eAArBzP,KAAKiP,YACA,CACL,CACE9C,WAAY,CAAC,OAAQ,YAAYnM,KAAKmP,aACtCrP,KAAM,OACN4H,KAAM2H,EAAU5E,KAAK/C,IAAI,CACvB6B,EAAGvJ,KAAKsJ,aAAaC,EACrBC,EAAG9B,EAAK,GAAKgI,EACblH,OAAQiH,EACRtH,MAAOT,EAAK,GAAK1H,KAAKsJ,aAAaC,EACnCkD,KAAMzM,KAAKwP,QAAQ/C,KACnBF,YAAa,EACbF,WAAYrM,KAAKwP,QAAQ/C,WAK1B,CACL,CACEN,WAAY,CAAC,OAAQ,YAAYnM,KAAKmP,aACtCrP,KAAM,OACN4H,KAAM2H,EAAU5E,KAAK/C,IAAI,CACvB6B,EAAG7B,EAAK,GAAKgI,EACblG,EAAG9B,EAAK,GACRS,MAAOsH,EACPjH,OAAQxI,KAAKsJ,aAAaE,EAAIxJ,KAAKsJ,aAAad,OAASd,EAAK,GAC9D+E,KAAMzM,KAAKwP,QAAQ/C,KACnBF,YAAa,EACbF,WAAYrM,KAAKwP,QAAQ/C,UAIjC,GAIEkD,EAAW,MACb7H,WAAAA,CAAYwG,EAAaC,EAAWC,GAClCxO,KAAKsO,YAAcA,EACnBtO,KAAKuO,UAAYA,EACjBvO,KAAKwO,iBAAmBA,EACxBxO,KAAKsJ,aAAe,CAClBC,EAAG,EACHC,EAAG,EACHrB,MAAO,EACPK,OAAQ,EAEZ,CAAC,eAEC9L,EAAAA,EAAAA,IAAOsD,KAAM,YAFd,GAID4P,OAAAA,CAAQb,EAAOC,GACbhP,KAAK+O,MAAQA,EACb/O,KAAKgP,MAAQA,CACf,CACAjD,gBAAAA,CAAiBC,GACfhM,KAAKsJ,aAAaC,EAAIyC,EAAMzC,EAC5BvJ,KAAKsJ,aAAaE,EAAIwC,EAAMxC,CAC9B,CACAsC,cAAAA,CAAed,GAGb,OAFAhL,KAAKsJ,aAAanB,MAAQ6C,EAAe7C,MACzCnI,KAAKsJ,aAAad,OAASwC,EAAexC,OACnC,CACLL,MAAOnI,KAAKsJ,aAAanB,MACzBK,OAAQxI,KAAKsJ,aAAad,OAE9B,CACA2E,mBAAAA,GACE,IAAMnN,KAAK+O,QAAS/O,KAAKgP,MACvB,MAAM/N,MAAM,uCAEd,MAAM0N,EAAe,GACrB,IAAK,MAAOlI,EAAGoJ,KAAS7P,KAAKuO,UAAUuB,MAAMC,UAC3C,OAAQF,EAAK/P,MACX,IAAK,OACH,CACE,MAAMkQ,EAAW,IAAInB,EACnBgB,EACA7P,KAAK+O,MACL/O,KAAKgP,MACLhP,KAAKsO,YAAY2B,iBACjBxJ,GAEFkI,EAAanM,QAAQwN,EAASZ,qBAChC,CACA,MACF,IAAK,MACH,CACE,MAAMc,EAAU,IAAIX,EAClBM,EACA7P,KAAKsJ,aACLtJ,KAAK+O,MACL/O,KAAKgP,MACLhP,KAAKsO,YAAY2B,iBACjBxJ,GAEFkI,EAAanM,QAAQ0N,EAAQd,qBAC/B,EAIN,OAAOT,CACT,GAEF,SAASwB,EAAiB7B,EAAaC,EAAWC,GAChD,OAAO,IAAImB,EAASrB,EAAaC,EAAWC,EAC9C,EACA9R,EAAAA,EAAAA,IAAOyT,EAAkB,oBAGzB,IAiLIC,EAjLAC,EAAe,MACjBvI,WAAAA,CAAYwG,EAAaC,EAAWC,EAAkBJ,GACpDpO,KAAKsO,YAAcA,EACnBtO,KAAKuO,UAAYA,EACjBvO,KAAKsQ,eAAiB,CACpBnH,MAAOyF,EAAuBN,EAAaC,EAAWC,EAAkBJ,GACxEyB,KAAMM,EAAiB7B,EAAaC,EAAWC,GAC/CO,MAAOZ,EACLI,EAAUQ,MACVT,EAAYS,MACZ,CACE/B,WAAYwB,EAAiB+B,gBAC7B7D,WAAY8B,EAAiBgC,gBAC7B1D,UAAW0B,EAAiBiC,eAC5BnE,cAAekC,EAAiBkC,gBAElCtC,GAEFY,MAAOb,EACLI,EAAUS,MACVV,EAAYU,MACZ,CACEhC,WAAYwB,EAAiBmC,gBAC7BjE,WAAY8B,EAAiBoC,gBAC7B9D,UAAW0B,EAAiBqC,eAC5BvE,cAAekC,EAAiBsC,gBAElC1C,GAGN,CAAC,eAEC1R,EAAAA,EAAAA,IAAOsD,KAAM,gBAFd,GAID+Q,sBAAAA,GACE,IAAInF,EAAiB5L,KAAKsO,YAAYnG,MAClC8C,EAAkBjL,KAAKsO,YAAY9F,OACnCwI,EAAQ,EACRC,EAAQ,EACRC,EAAalK,KAAK8D,MAAMc,EAAiB5L,KAAKsO,YAAY6C,yBAA2B,KACrFC,EAAcpK,KAAK8D,MACrBG,EAAkBjL,KAAKsO,YAAY6C,yBAA2B,KAE5DE,EAAYrR,KAAKsQ,eAAeT,KAAK/D,eAAe,CACtD3D,MAAO+I,EACP1I,OAAQ4I,IAEVxF,GAAkByF,EAAUlJ,MAC5B8C,GAAmBoG,EAAU7I,OAC7B6I,EAAYrR,KAAKsQ,eAAenH,MAAM2C,eAAe,CACnD3D,MAAOnI,KAAKsO,YAAYnG,MACxBK,OAAQyC,IAEVgG,EAAQI,EAAU7I,OAClByC,GAAmBoG,EAAU7I,OAC7BxI,KAAKsQ,eAAevB,MAAM3E,gBAAgB,UAC1CiH,EAAYrR,KAAKsQ,eAAevB,MAAMjD,eAAe,CACnD3D,MAAOyD,EACPpD,OAAQyC,IAEVA,GAAmBoG,EAAU7I,OAC7BxI,KAAKsQ,eAAetB,MAAM5E,gBAAgB,QAC1CiH,EAAYrR,KAAKsQ,eAAetB,MAAMlD,eAAe,CACnD3D,MAAOyD,EACPpD,OAAQyC,IAEV+F,EAAQK,EAAUlJ,MAClByD,GAAkByF,EAAUlJ,MACxByD,EAAiB,IACnBsF,GAActF,EACdA,EAAiB,GAEfX,EAAkB,IACpBmG,GAAenG,EACfA,EAAkB,GAEpBjL,KAAKsQ,eAAeT,KAAK/D,eAAe,CACtC3D,MAAO+I,EACP1I,OAAQ4I,IAEVpR,KAAKsQ,eAAeT,KAAK9D,iBAAiB,CAAExC,EAAGyH,EAAOxH,EAAGyH,IACzDjR,KAAKsQ,eAAevB,MAAM9E,SAAS,CAAC+G,EAAOA,EAAQE,IACnDlR,KAAKsQ,eAAevB,MAAMhD,iBAAiB,CAAExC,EAAGyH,EAAOxH,EAAGyH,EAAQG,IAClEpR,KAAKsQ,eAAetB,MAAM/E,SAAS,CAACgH,EAAOA,EAAQG,IACnDpR,KAAKsQ,eAAetB,MAAMjD,iBAAiB,CAAExC,EAAG,EAAGC,EAAGyH,IAClDjR,KAAKuO,UAAUuB,MAAMwB,MAAMhO,GAAMmE,EAAUnE,MAC7CtD,KAAKsQ,eAAevB,MAAMlE,kCAE9B,CACA0G,wBAAAA,GACE,IAAI3F,EAAiB5L,KAAKsO,YAAYnG,MAClC8C,EAAkBjL,KAAKsO,YAAY9F,OACnCgJ,EAAY,EACZR,EAAQ,EACRC,EAAQ,EACRC,EAAalK,KAAK8D,MAAMc,EAAiB5L,KAAKsO,YAAY6C,yBAA2B,KACrFC,EAAcpK,KAAK8D,MACrBG,EAAkBjL,KAAKsO,YAAY6C,yBAA2B,KAE5DE,EAAYrR,KAAKsQ,eAAeT,KAAK/D,eAAe,CACtD3D,MAAO+I,EACP1I,OAAQ4I,IAEVxF,GAAkByF,EAAUlJ,MAC5B8C,GAAmBoG,EAAU7I,OAC7B6I,EAAYrR,KAAKsQ,eAAenH,MAAM2C,eAAe,CACnD3D,MAAOnI,KAAKsO,YAAYnG,MACxBK,OAAQyC,IAEVuG,EAAYH,EAAU7I,OACtByC,GAAmBoG,EAAU7I,OAC7BxI,KAAKsQ,eAAevB,MAAM3E,gBAAgB,QAC1CiH,EAAYrR,KAAKsQ,eAAevB,MAAMjD,eAAe,CACnD3D,MAAOyD,EACPpD,OAAQyC,IAEVW,GAAkByF,EAAUlJ,MAC5B6I,EAAQK,EAAUlJ,MAClBnI,KAAKsQ,eAAetB,MAAM5E,gBAAgB,OAC1CiH,EAAYrR,KAAKsQ,eAAetB,MAAMlD,eAAe,CACnD3D,MAAOyD,EACPpD,OAAQyC,IAEVA,GAAmBoG,EAAU7I,OAC7ByI,EAAQO,EAAYH,EAAU7I,OAC1BoD,EAAiB,IACnBsF,GAActF,EACdA,EAAiB,GAEfX,EAAkB,IACpBmG,GAAenG,EACfA,EAAkB,GAEpBjL,KAAKsQ,eAAeT,KAAK/D,eAAe,CACtC3D,MAAO+I,EACP1I,OAAQ4I,IAEVpR,KAAKsQ,eAAeT,KAAK9D,iBAAiB,CAAExC,EAAGyH,EAAOxH,EAAGyH,IACzDjR,KAAKsQ,eAAetB,MAAM/E,SAAS,CAAC+G,EAAOA,EAAQE,IACnDlR,KAAKsQ,eAAetB,MAAMjD,iBAAiB,CAAExC,EAAGyH,EAAOxH,EAAGgI,IAC1DxR,KAAKsQ,eAAevB,MAAM9E,SAAS,CAACgH,EAAOA,EAAQG,IACnDpR,KAAKsQ,eAAevB,MAAMhD,iBAAiB,CAAExC,EAAG,EAAGC,EAAGyH,IAClDjR,KAAKuO,UAAUuB,MAAMwB,MAAMhO,GAAMmE,EAAUnE,MAC7CtD,KAAKsQ,eAAevB,MAAMlE,kCAE9B,CACAiB,cAAAA,GAC4C,eAAtC9L,KAAKsO,YAAY2B,iBACnBjQ,KAAKuR,2BAELvR,KAAK+Q,wBAET,CACA3B,kBAAAA,GACEpP,KAAK8L,iBACL,MAAM6C,EAAe,GACrB3O,KAAKsQ,eAAeT,KAAKD,QAAQ5P,KAAKsQ,eAAevB,MAAO/O,KAAKsQ,eAAetB,OAChF,IAAK,MAAMyC,KAAa1P,OAAO2P,OAAO1R,KAAKsQ,gBACzC3B,EAAanM,QAAQiP,EAAUtE,uBAEjC,OAAOwB,CACT,GAIEgD,EAAiB,MAAM,eAEvBjV,EAAAA,EAAAA,IAAOsD,KAAM,kBAFU,GAIzB,YAAO4R,CAAMC,EAAQtD,EAAWC,EAAkBJ,GAEhD,OADqB,IAAIiC,EAAawB,EAAQtD,EAAWC,EAAkBJ,GACvDgB,oBACtB,GAIED,EAAY,EAEZ2C,EAAgBC,IAChBC,EAAqBC,IACrBC,EAAcC,IACdC,EAAmBJ,EAAmBI,iBAAiBjN,MAAM,KAAKsF,KAAK4H,GAAUA,EAAMzS,SACvF0S,GAAc,EACdC,GAAc,EAClB,SAASN,IACP,MAAMO,GAAwBC,EAAAA,EAAAA,MACxBZ,GAASa,EAAAA,EAAAA,MACf,OAAOC,EAAAA,EAAAA,IAAcH,EAAsBI,QAASf,EAAOgB,eAAeD,QAC5E,CAEA,SAASb,IACP,MAAMF,GAASa,EAAAA,EAAAA,MACf,OAAOC,EAAAA,EAAAA,IACLG,EAAAA,GAAsBF,QACtBf,EAAOe,QAEX,CAEA,SAAST,IACP,MAAO,CACLnD,MAAO,CACLlP,KAAM,SACNqJ,MAAO,GACPkC,IAAK0H,IACLxK,KAAMwK,KAERhE,MAAO,CACLjP,KAAM,OACNqJ,MAAO,GACPkE,WAAY,IAEdlE,MAAO,GACP2G,MAAO,GAEX,CAEA,SAASkD,EAAcrT,GACrB,MAAMkS,GAASa,EAAAA,EAAAA,MACf,OAAOO,EAAAA,EAAAA,IAAatT,EAAKC,OAAQiS,EACnC,CAEA,SAASqB,EAAWC,GAClB/C,EAAc+C,CAChB,CAEA,SAAS1T,EAAewP,GAEpB6C,EAAc7B,iBADI,eAAhBhB,EAC+B,aAEA,UAErC,CAEA,SAAS5O,EAAc8I,GACrB+I,EAAYnD,MAAM5F,MAAQ6J,EAAc7J,EAAMxJ,KAChD,CAEA,SAASY,EAAkB8K,EAAK9C,GAC9B2J,EAAYnD,MAAQ,CAAEjP,KAAM,SAAUqJ,MAAO+I,EAAYnD,MAAM5F,MAAOkC,MAAK9C,OAC3E+J,GAAc,CAChB,CAEA,SAAShS,EAAa+M,GACpB6E,EAAYnD,MAAQ,CAClBjP,KAAM,OACNqJ,MAAO+I,EAAYnD,MAAM5F,MACzBkE,WAAYA,EAAW5C,KAAKzE,GAAMgN,EAAchN,EAAErG,SAEpD2S,GAAc,CAChB,CAEA,SAAS9R,EAAc2I,GACrB+I,EAAYlD,MAAM7F,MAAQ6J,EAAc7J,EAAMxJ,KAChD,CAEA,SAASc,EAAkB4K,EAAK9C,GAC9B2J,EAAYlD,MAAQ,CAAElP,KAAM,SAAUqJ,MAAO+I,EAAYlD,MAAM7F,MAAOkC,MAAK9C,OAC3EgK,GAAc,CAChB,CAEA,SAASa,EAA0B1L,GACjC,MAAM2L,EAAWrM,KAAKqE,OAAO3D,GACvB4L,EAAWtM,KAAKuB,OAAOb,GACvB6L,EAAe3L,EAAiBsK,EAAYlD,OAASkD,EAAYlD,MAAM3D,IAAM0H,IAC7ES,EAAe5L,EAAiBsK,EAAYlD,OAASkD,EAAYlD,MAAMzG,KAAOwK,IACpFb,EAAYlD,MAAQ,CAClBlP,KAAM,SACNqJ,MAAO+I,EAAYlD,MAAM7F,MACzBkC,IAAKrE,KAAKqE,IAAIkI,EAAcF,GAC5B9K,IAAKvB,KAAKuB,IAAIiL,EAAcF,GAEhC,CAEA,SAASG,EAA6B/L,GACpC,IAAIgM,EAAU,GACd,GAAoB,IAAhBhM,EAAK3K,OACP,OAAO2W,EAET,IAAKpB,EAAa,CAChB,MAAMiB,EAAe3L,EAAiBsK,EAAYnD,OAASmD,EAAYnD,MAAM1D,IAAM0H,IAC7ES,EAAe5L,EAAiBsK,EAAYnD,OAASmD,EAAYnD,MAAMxG,KAAOwK,IACpFxS,EAAkByG,KAAKqE,IAAIkI,EAAc,GAAIvM,KAAKuB,IAAIiL,EAAc9L,EAAK3K,QAC3E,CAOA,GANKwV,GACHa,EAA0B1L,GAExBC,EAAeuK,EAAYnD,SAC7B2E,EAAUxB,EAAYnD,MAAM1B,WAAW5C,KAAI,CAACzE,EAAGS,IAAM,CAACT,EAAG0B,EAAKjB,OAE5DmB,EAAiBsK,EAAYnD,OAAQ,CACvC,MAAM1D,EAAM6G,EAAYnD,MAAM1D,IACxB9C,EAAM2J,EAAYnD,MAAMxG,IACxBoL,GAAQpL,EAAM8C,IAAQ3D,EAAK3K,OAAS,GACpCsQ,EAAa,GACnB,IAAK,IAAI5G,EAAI4E,EAAK5E,GAAK8B,EAAK9B,GAAKkN,EAC/BtG,EAAW7K,KAAK,GAAGiE,KAErBiN,EAAUrG,EAAW5C,KAAI,CAACzE,EAAGS,IAAM,CAACT,EAAG0B,EAAKjB,KAC9C,CACA,OAAOiN,CACT,CAEA,SAASE,EAAwB1E,GAC/B,OAAOkD,EAAgC,IAAflD,EAAmB,EAAIA,EAAakD,EAAiBrV,OAC/E,CAEA,SAAS8C,EAAYsJ,EAAOzB,GAC1B,MAAMoH,EAAW2E,EAA6B/L,GAC9CwK,EAAYpC,MAAMtN,KAAK,CACrB1C,KAAM,OACNuM,WAAYuH,EAAwBzE,GACpC5C,YAAa,EACb7E,KAAMoH,IAERK,GACF,CAEA,SAASpP,EAAWoJ,EAAOzB,GACzB,MAAMoH,EAAW2E,EAA6B/L,GAC9CwK,EAAYpC,MAAMtN,KAAK,CACrB1C,KAAM,MACN2M,KAAMmH,EAAwBzE,GAC9BzH,KAAMoH,IAERK,GACF,CAEA,SAAS0E,IACP,GAAiC,IAA7B3B,EAAYpC,MAAM/S,OACpB,MAAMkE,MAAM,2DAGd,OADAiR,EAAY/I,OAAQ2K,EAAAA,EAAAA,MACbnC,EAAeC,MAAME,EAAeI,EAAaF,EAAoB5B,EAC9E,CAEA,SAAS2D,IACP,OAAO/B,CACT,CAEA,SAASgC,IACP,OAAOlC,CACT,EAxJApV,EAAAA,EAAAA,IAAOuV,EAA4B,+BAQnCvV,EAAAA,EAAAA,IAAOqV,EAAuB,0BAkB9BrV,EAAAA,EAAAA,IAAOyV,EAAqB,wBAK5BzV,EAAAA,EAAAA,IAAOsW,EAAe,kBAItBtW,EAAAA,EAAAA,IAAOwW,EAAY,eAQnBxW,EAAAA,EAAAA,IAAO+C,EAAgB,mBAIvB/C,EAAAA,EAAAA,IAAO2D,EAAe,kBAKtB3D,EAAAA,EAAAA,IAAO6D,EAAmB,sBAS1B7D,EAAAA,EAAAA,IAAO4D,EAAc,iBAIrB5D,EAAAA,EAAAA,IAAO8D,EAAe,kBAKtB9D,EAAAA,EAAAA,IAAO+D,EAAmB,sBAa1B/D,EAAAA,EAAAA,IAAO0W,EAA2B,8BA6BlC1W,EAAAA,EAAAA,IAAO+W,EAA8B,iCAIrC/W,EAAAA,EAAAA,IAAOkX,EAAyB,4BAWhClX,EAAAA,EAAAA,IAAOmD,EAAa,gBAUpBnD,EAAAA,EAAAA,IAAOqD,EAAY,eAQnBrD,EAAAA,EAAAA,IAAOmX,EAAiB,oBAIxBnX,EAAAA,EAAAA,IAAOqX,EAAqB,wBAI5BrX,EAAAA,EAAAA,IAAOsX,EAAgB,kBACvB,IAmGIC,EAAU,CACZzX,OAAQgL,EACR0M,GA3FsB,CACtBL,kBACAM,OAZ2BzX,EAAAA,EAAAA,KAAO,YAClCyX,EAAAA,EAAAA,MACAhF,EAAY,EACZ2C,EAAgBC,IAChBG,EApJO,CACLlD,MAAO,CACLlP,KAAM,SACNqJ,MAAO,GACPkC,IAAK0H,IACLxK,KAAK,KAEPwG,MAAO,CACLjP,KAAM,OACNqJ,MAAO,GACPkE,WAAY,IAEdlE,MAAO,GACP2G,MAAO,IAwITkC,EAAqBC,IACrBG,EAAmBJ,EAAmBI,iBAAiBjN,MAAM,KAAKsF,KAAK4H,GAAUA,EAAMzS,SACvF0S,GAAc,EACdC,GAAc,CAChB,GAAG,SAIDrS,YAAW,KACXkU,YAAW,KACX1U,gBAAe,KACfoU,gBAAe,KACfO,kBAAiB,KACjBlU,kBAAiB,KACjBV,iBACAY,gBACAE,oBACAD,eACAE,gBACAC,oBACAZ,cACAE,aACAmT,aACAa,sBACAC,kBAyEAM,SAR4B,CAC5BC,MA9DyB7X,EAAAA,EAAAA,KAAO,CAAC8X,EAAKC,EAAIC,EAAUC,KACpD,MAAMT,EAAKS,EAAQT,GACbU,EAAcV,EAAGH,sBACjBzF,EAAc4F,EAAGF,iBACvB,SAASa,EAAoBhI,GAC3B,MAAyB,QAAlBA,EAA0B,mBAAqB,QACxD,CAEA,SAASiI,EAAclI,GACrB,MAAuB,SAAhBA,EAAyB,QAA0B,UAAhBA,EAA0B,MAAQ,QAC9E,CAEA,SAASmI,EAAsBrN,GAC7B,MAAO,aAAaA,EAAK6B,MAAM7B,EAAK8B,aAAa9B,EAAKiF,UAAY,IACpE,EAPAjQ,EAAAA,EAAAA,IAAOmY,EAAqB,wBAI5BnY,EAAAA,EAAAA,IAAOoY,EAAe,kBAItBpY,EAAAA,EAAAA,IAAOqY,EAAuB,yBAC9BlH,EAAAA,GAAImH,MAAM,4BAA8BR,GACxC,MAAMS,GAAMC,EAAAA,EAAAA,GAAiBT,GACvBU,EAAQF,EAAItM,OAAO,KAAKC,KAAK,QAAS,QACtCwM,EAAaD,EAAMxM,OAAO,QAAQC,KAAK,QAAS0F,EAAYnG,OAAOS,KAAK,SAAU0F,EAAY9F,QAAQI,KAAK,QAAS,eAC1HyM,EAAAA,EAAAA,IAAiBJ,EAAK3G,EAAY9F,OAAQ8F,EAAYnG,OAAO,GAC7D8M,EAAIrM,KAAK,UAAW,OAAO0F,EAAYnG,SAASmG,EAAY9F,UAC5D4M,EAAWxM,KAAK,OAAQgM,EAAYU,iBACpCpB,EAAGhB,WAAW+B,EAAItM,OAAO,KAAKC,KAAK,QAAS,sBAC5C,MAAM2M,EAASrB,EAAGL,kBACZ2B,EAAS,CAAC,EAChB,SAASC,EAASC,GAChB,IAAIhN,EAAOyM,EACPQ,EAAS,GACb,IAAK,MAAOlP,KAAMiP,EAAM3F,UAAW,CACjC,IAAI6F,EAAST,EACT1O,EAAI,GAAK+O,EAAOG,KAClBC,EAASJ,EAAOG,IAElBA,GAAUD,EAAMjP,GAChBiC,EAAO8M,EAAOG,GACTjN,IACHA,EAAO8M,EAAOG,GAAUC,EAAOjN,OAAO,KAAKC,KAAK,QAAS8M,EAAMjP,IAEnE,CACA,OAAOiC,CACT,EACAhM,EAAAA,EAAAA,IAAO+Y,EAAU,YACjB,IAAK,MAAMI,KAASN,EAAQ,CAC1B,GAA0B,IAAtBM,EAAMnO,KAAK3K,OACb,SAEF,MAAM+Y,EAAaL,EAASI,EAAM1J,YAClC,OAAQ0J,EAAM/V,MACZ,IAAK,OACHgW,EAAWC,UAAU,QAAQrO,KAAKmO,EAAMnO,MAAMsO,QAAQrN,OAAO,QAAQC,KAAK,KAAMlB,GAASA,EAAK6B,IAAGX,KAAK,KAAMlB,GAASA,EAAK8B,IAAGZ,KAAK,SAAUlB,GAASA,EAAKS,QAAOS,KAAK,UAAWlB,GAASA,EAAKc,SAAQI,KAAK,QAASlB,GAASA,EAAK+E,OAAM7D,KAAK,UAAWlB,GAASA,EAAK2E,aAAYzD,KAAK,gBAAiBlB,GAASA,EAAK6E,cACvT,MACF,IAAK,OACHuJ,EAAWC,UAAU,QAAQrO,KAAKmO,EAAMnO,MAAMsO,QAAQrN,OAAO,QAAQC,KAAK,IAAK,GAAGA,KAAK,IAAK,GAAGA,KAAK,QAASlB,GAASA,EAAK+E,OAAM7D,KAAK,aAAclB,GAASA,EAAKQ,WAAUU,KAAK,qBAAsBlB,GAASmN,EAAoBnN,EAAKkF,eAAchE,KAAK,eAAgBlB,GAASoN,EAAcpN,EAAKmF,iBAAgBjE,KAAK,aAAclB,GAASqN,EAAsBrN,KAAO/H,MAAM+H,GAASA,EAAK/H,OACrY,MACF,IAAK,OACHmW,EAAWC,UAAU,QAAQrO,KAAKmO,EAAMnO,MAAMsO,QAAQrN,OAAO,QAAQC,KAAK,KAAMlB,GAASA,EAAK0E,OAAMxD,KAAK,QAASlB,GAASA,EAAK+E,KAAO/E,EAAK+E,KAAO,SAAQ7D,KAAK,UAAWlB,GAASA,EAAK2E,aAAYzD,KAAK,gBAAiBlB,GAASA,EAAK6E,cAG/O,IACC,S", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-VJFVF3MP.mjs"], "sourcesContent": ["import {\n  computeDimensionOfText\n} from \"./chunk-C3MQ5ANM.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  getThemeVariables,\n  log,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/xychart/parser/xychart.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 10, 12, 14, 16, 18, 19, 21, 23], $V1 = [2, 6], $V2 = [1, 3], $V3 = [1, 5], $V4 = [1, 6], $V5 = [1, 7], $V6 = [1, 5, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $V7 = [1, 25], $V8 = [1, 26], $V9 = [1, 28], $Va = [1, 29], $Vb = [1, 30], $Vc = [1, 31], $Vd = [1, 32], $Ve = [1, 33], $Vf = [1, 34], $Vg = [1, 35], $Vh = [1, 36], $Vi = [1, 37], $Vj = [1, 43], $Vk = [1, 42], $Vl = [1, 47], $Vm = [1, 50], $Vn = [1, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $Vo = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36], $Vp = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50], $Vq = [1, 64];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"eol\": 4, \"XYCHART\": 5, \"chartConfig\": 6, \"document\": 7, \"CHART_ORIENTATION\": 8, \"statement\": 9, \"title\": 10, \"text\": 11, \"X_AXIS\": 12, \"parseXAxis\": 13, \"Y_AXIS\": 14, \"parseYAxis\": 15, \"LINE\": 16, \"plotData\": 17, \"BAR\": 18, \"acc_title\": 19, \"acc_title_value\": 20, \"acc_descr\": 21, \"acc_descr_value\": 22, \"acc_descr_multiline_value\": 23, \"SQUARE_BRACES_START\": 24, \"commaSeparatedNumbers\": 25, \"SQUARE_BRACES_END\": 26, \"NUMBER_WITH_DECIMAL\": 27, \"COMMA\": 28, \"xAxisData\": 29, \"bandData\": 30, \"ARROW_DELIMITER\": 31, \"commaSeparatedTexts\": 32, \"yAxisData\": 33, \"NEWLINE\": 34, \"SEMI\": 35, \"EOF\": 36, \"alphaNum\": 37, \"STR\": 38, \"MD_STR\": 39, \"alphaNumToken\": 40, \"AMP\": 41, \"NUM\": 42, \"ALPHA\": 43, \"PLUS\": 44, \"EQUALS\": 45, \"MULT\": 46, \"DOT\": 47, \"BRKT\": 48, \"MINUS\": 49, \"UNDERSCORE\": 50, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 5: \"XYCHART\", 8: \"CHART_ORIENTATION\", 10: \"title\", 12: \"X_AXIS\", 14: \"Y_AXIS\", 16: \"LINE\", 18: \"BAR\", 19: \"acc_title\", 20: \"acc_title_value\", 21: \"acc_descr\", 22: \"acc_descr_value\", 23: \"acc_descr_multiline_value\", 24: \"SQUARE_BRACES_START\", 26: \"SQUARE_BRACES_END\", 27: \"NUMBER_WITH_DECIMAL\", 28: \"COMMA\", 31: \"ARROW_DELIMITER\", 34: \"NEWLINE\", 35: \"SEMI\", 36: \"EOF\", 38: \"STR\", 39: \"MD_STR\", 41: \"AMP\", 42: \"NUM\", 43: \"ALPHA\", 44: \"PLUS\", 45: \"EQUALS\", 46: \"MULT\", 47: \"DOT\", 48: \"BRKT\", 49: \"MINUS\", 50: \"UNDERSCORE\" },\n    productions_: [0, [3, 2], [3, 3], [3, 2], [3, 1], [6, 1], [7, 0], [7, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 3], [9, 2], [9, 3], [9, 2], [9, 2], [9, 1], [17, 3], [25, 3], [25, 1], [13, 1], [13, 2], [13, 1], [29, 1], [29, 3], [30, 3], [32, 3], [32, 1], [15, 1], [15, 2], [15, 1], [33, 3], [4, 1], [4, 1], [4, 1], [11, 1], [11, 1], [11, 1], [37, 1], [37, 2], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 5:\n          yy.setOrientation($$[$0]);\n          break;\n        case 9:\n          yy.setDiagramTitle($$[$0].text.trim());\n          break;\n        case 12:\n          yy.setLineData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 13:\n          yy.setLineData($$[$0 - 1], $$[$0]);\n          break;\n        case 14:\n          yy.setBarData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 15:\n          yy.setBarData($$[$0 - 1], $$[$0]);\n          break;\n        case 16:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 17:\n        case 18:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 19:\n          this.$ = $$[$0 - 1];\n          break;\n        case 20:\n          this.$ = [Number($$[$0 - 2]), ...$$[$0]];\n          break;\n        case 21:\n          this.$ = [Number($$[$0])];\n          break;\n        case 22:\n          yy.setXAxisTitle($$[$0]);\n          break;\n        case 23:\n          yy.setXAxisTitle($$[$0 - 1]);\n          break;\n        case 24:\n          yy.setXAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 25:\n          yy.setXAxisBand($$[$0]);\n          break;\n        case 26:\n          yy.setXAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 27:\n          this.$ = $$[$0 - 1];\n          break;\n        case 28:\n          this.$ = [$$[$0 - 2], ...$$[$0]];\n          break;\n        case 29:\n          this.$ = [$$[$0]];\n          break;\n        case 30:\n          yy.setYAxisTitle($$[$0]);\n          break;\n        case 31:\n          yy.setYAxisTitle($$[$0 - 1]);\n          break;\n        case 32:\n          yy.setYAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 33:\n          yy.setYAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 37:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 38:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 39:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 40:\n          this.$ = $$[$0];\n          break;\n        case 41:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [o($V0, $V1, { 3: 1, 4: 2, 7: 4, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [3] }, o($V0, $V1, { 4: 2, 7: 4, 3: 8, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), o($V0, $V1, { 4: 2, 7: 4, 6: 9, 3: 10, 5: $V2, 8: [1, 11], 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 4], 9: 12, 10: [1, 13], 12: [1, 14], 14: [1, 15], 16: [1, 16], 18: [1, 17], 19: [1, 18], 21: [1, 19], 23: [1, 20] }, o($V6, [2, 34]), o($V6, [2, 35]), o($V6, [2, 36]), { 1: [2, 1] }, o($V0, $V1, { 4: 2, 7: 4, 3: 21, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 3] }, o($V6, [2, 5]), o($V0, [2, 7], { 4: 22, 34: $V3, 35: $V4, 36: $V5 }), { 11: 23, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 39, 13: 38, 24: $Vj, 27: $Vk, 29: 40, 30: 41, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 45, 15: 44, 27: $Vl, 33: 46, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 49, 17: 48, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 52, 17: 51, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 20: [1, 53] }, { 22: [1, 54] }, o($Vn, [2, 18]), { 1: [2, 2] }, o($Vn, [2, 8]), o($Vn, [2, 9]), o($Vo, [2, 37], { 40: 55, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }), o($Vo, [2, 38]), o($Vo, [2, 39]), o($Vp, [2, 40]), o($Vp, [2, 42]), o($Vp, [2, 43]), o($Vp, [2, 44]), o($Vp, [2, 45]), o($Vp, [2, 46]), o($Vp, [2, 47]), o($Vp, [2, 48]), o($Vp, [2, 49]), o($Vp, [2, 50]), o($Vp, [2, 51]), o($Vn, [2, 10]), o($Vn, [2, 22], { 30: 41, 29: 56, 24: $Vj, 27: $Vk }), o($Vn, [2, 24]), o($Vn, [2, 25]), { 31: [1, 57] }, { 11: 59, 32: 58, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 11]), o($Vn, [2, 30], { 33: 60, 27: $Vl }), o($Vn, [2, 32]), { 31: [1, 61] }, o($Vn, [2, 12]), { 17: 62, 24: $Vm }, { 25: 63, 27: $Vq }, o($Vn, [2, 14]), { 17: 65, 24: $Vm }, o($Vn, [2, 16]), o($Vn, [2, 17]), o($Vp, [2, 41]), o($Vn, [2, 23]), { 27: [1, 66] }, { 26: [1, 67] }, { 26: [2, 29], 28: [1, 68] }, o($Vn, [2, 31]), { 27: [1, 69] }, o($Vn, [2, 13]), { 26: [1, 70] }, { 26: [2, 21], 28: [1, 71] }, o($Vn, [2, 15]), o($Vn, [2, 26]), o($Vn, [2, 27]), { 11: 59, 32: 72, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 33]), o($Vn, [2, 19]), { 25: 73, 27: $Vq }, { 26: [2, 28] }, { 26: [2, 20] }],\n    defaultActions: { 8: [2, 1], 10: [2, 3], 21: [2, 2], 72: [2, 28], 73: [2, 20] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            this.popState();\n            return 34;\n            break;\n          case 3:\n            this.popState();\n            return 34;\n            break;\n          case 4:\n            return 34;\n            break;\n          case 5:\n            break;\n          case 6:\n            return 10;\n            break;\n          case 7:\n            this.pushState(\"acc_title\");\n            return 19;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.pushState(\"acc_descr\");\n            return 21;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.pushState(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 5;\n            break;\n          case 15:\n            return 8;\n            break;\n          case 16:\n            this.pushState(\"axis_data\");\n            return \"X_AXIS\";\n            break;\n          case 17:\n            this.pushState(\"axis_data\");\n            return \"Y_AXIS\";\n            break;\n          case 18:\n            this.pushState(\"axis_band_data\");\n            return 24;\n            break;\n          case 19:\n            return 31;\n            break;\n          case 20:\n            this.pushState(\"data\");\n            return 16;\n            break;\n          case 21:\n            this.pushState(\"data\");\n            return 18;\n            break;\n          case 22:\n            this.pushState(\"data_inner\");\n            return 24;\n            break;\n          case 23:\n            return 27;\n            break;\n          case 24:\n            this.popState();\n            return 26;\n            break;\n          case 25:\n            this.popState();\n            break;\n          case 26:\n            this.pushState(\"string\");\n            break;\n          case 27:\n            this.popState();\n            break;\n          case 28:\n            return \"STR\";\n            break;\n          case 29:\n            return 24;\n            break;\n          case 30:\n            return 26;\n            break;\n          case 31:\n            return 43;\n            break;\n          case 32:\n            return \"COLON\";\n            break;\n          case 33:\n            return 44;\n            break;\n          case 34:\n            return 28;\n            break;\n          case 35:\n            return 45;\n            break;\n          case 36:\n            return 46;\n            break;\n          case 37:\n            return 48;\n            break;\n          case 38:\n            return 50;\n            break;\n          case 39:\n            return 47;\n            break;\n          case 40:\n            return 41;\n            break;\n          case 41:\n            return 49;\n            break;\n          case 42:\n            return 42;\n            break;\n          case 43:\n            break;\n          case 44:\n            return 35;\n            break;\n          case 45:\n            return 36;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:(\\r?\\n))/i, /^(?:(\\r?\\n))/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:\\{)/i, /^(?:[^\\}]*)/i, /^(?:xychart-beta\\b)/i, /^(?:(?:vertical|horizontal))/i, /^(?:x-axis\\b)/i, /^(?:y-axis\\b)/i, /^(?:\\[)/i, /^(?:-->)/i, /^(?:line\\b)/i, /^(?:bar\\b)/i, /^(?:\\[)/i, /^(?:[+-]?(?:\\d+(?:\\.\\d+)?|\\.\\d+))/i, /^(?:\\])/i, /^(?:(?:`\\)                                    \\{ this\\.pushState\\(md_string\\); \\}\\n<md_string>\\(\\?:\\(\\?!`\"\\)\\.\\)\\+                  \\{ return MD_STR; \\}\\n<md_string>\\(\\?:`))/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:\\[)/i, /^(?:\\])/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s+)/i, /^(?:;)/i, /^(?:$)/i],\n      conditions: { \"data_inner\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"data\": { \"rules\": [0, 1, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 22, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_band_data\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_data\": { \"rules\": [0, 1, 2, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"title\": { \"rules\": [], \"inclusive\": false }, \"md_string\": { \"rules\": [], \"inclusive\": false }, \"string\": { \"rules\": [27, 28], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar xychart_default = parser;\n\n// src/diagrams/xychart/chartBuilder/interfaces.ts\nfunction isBarPlot(data) {\n  return data.type === \"bar\";\n}\n__name(isBarPlot, \"isBarPlot\");\nfunction isBandAxisData(data) {\n  return data.type === \"band\";\n}\n__name(isBandAxisData, \"isBandAxisData\");\nfunction isLinearAxisData(data) {\n  return data.type === \"linear\";\n}\n__name(isLinearAxisData, \"isLinearAxisData\");\n\n// src/diagrams/xychart/chartBuilder/textDimensionCalculator.ts\nvar TextDimensionCalculatorWithFont = class {\n  constructor(parentGroup) {\n    this.parentGroup = parentGroup;\n  }\n  static {\n    __name(this, \"TextDimensionCalculatorWithFont\");\n  }\n  getMaxDimension(texts, fontSize) {\n    if (!this.parentGroup) {\n      return {\n        width: texts.reduce((acc, cur) => Math.max(cur.length, acc), 0) * fontSize,\n        height: fontSize\n      };\n    }\n    const dimension = {\n      width: 0,\n      height: 0\n    };\n    const elem = this.parentGroup.append(\"g\").attr(\"visibility\", \"hidden\").attr(\"font-size\", fontSize);\n    for (const t of texts) {\n      const bbox = computeDimensionOfText(elem, 1, t);\n      const width = bbox ? bbox.width : t.length * fontSize;\n      const height = bbox ? bbox.height : fontSize;\n      dimension.width = Math.max(dimension.width, width);\n      dimension.height = Math.max(dimension.height, height);\n    }\n    elem.remove();\n    return dimension;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nimport { scaleBand } from \"d3\";\n\n// src/diagrams/xychart/chartBuilder/components/axis/baseAxis.ts\nvar BAR_WIDTH_TO_TICK_WIDTH_RATIO = 0.7;\nvar MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL = 0.2;\nvar BaseAxis = class {\n  constructor(axisConfig, title, textDimensionCalculator, axisThemeConfig) {\n    this.axisConfig = axisConfig;\n    this.title = title;\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.axisThemeConfig = axisThemeConfig;\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n    this.showTitle = false;\n    this.showLabel = false;\n    this.showTick = false;\n    this.showAxisLine = false;\n    this.outerPadding = 0;\n    this.titleTextHeight = 0;\n    this.labelTextHeight = 0;\n    this.range = [0, 10];\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n  }\n  static {\n    __name(this, \"BaseAxis\");\n  }\n  setRange(range) {\n    this.range = range;\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.boundingRect.height = range[1] - range[0];\n    } else {\n      this.boundingRect.width = range[1] - range[0];\n    }\n    this.recalculateScale();\n  }\n  getRange() {\n    return [this.range[0] + this.outerPadding, this.range[1] - this.outerPadding];\n  }\n  setAxisPosition(axisPosition) {\n    this.axisPosition = axisPosition;\n    this.setRange(this.range);\n  }\n  getTickDistance() {\n    const range = this.getRange();\n    return Math.abs(range[0] - range[1]) / this.getTickValues().length;\n  }\n  getAxisOuterPadding() {\n    return this.outerPadding;\n  }\n  getLabelDimension() {\n    return this.textDimensionCalculator.getMaxDimension(\n      this.getTickValues().map((tick) => tick.toString()),\n      this.axisConfig.labelFontSize\n    );\n  }\n  recalculateOuterPaddingToDrawBar() {\n    if (BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() > this.outerPadding * 2) {\n      this.outerPadding = Math.floor(BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() / 2);\n    }\n    this.recalculateScale();\n  }\n  calculateSpaceIfDrawnHorizontally(availableSpace) {\n    let availableHeight = availableSpace.height;\n    if (this.axisConfig.showAxisLine && availableHeight > this.axisConfig.axisLineWidth) {\n      availableHeight -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.width;\n      this.outerPadding = Math.min(spaceRequired.width / 2, maxPadding);\n      const heightRequired = spaceRequired.height + this.axisConfig.labelPadding * 2;\n      this.labelTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableHeight >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableHeight -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const heightRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height - availableHeight;\n  }\n  calculateSpaceIfDrawnVertical(availableSpace) {\n    let availableWidth = availableSpace.width;\n    if (this.axisConfig.showAxisLine && availableWidth > this.axisConfig.axisLineWidth) {\n      availableWidth -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.height;\n      this.outerPadding = Math.min(spaceRequired.height / 2, maxPadding);\n      const widthRequired = spaceRequired.width + this.axisConfig.labelPadding * 2;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableWidth >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableWidth -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const widthRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width - availableWidth;\n    this.boundingRect.height = availableSpace.height;\n  }\n  calculateSpace(availableSpace) {\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.calculateSpaceIfDrawnVertical(availableSpace);\n    } else {\n      this.calculateSpaceIfDrawnHorizontally(availableSpace);\n    }\n    this.recalculateScale();\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  getDrawableElementsForLeftAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const x = this.boundingRect.x + this.boundingRect.width - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"axisl-line\"],\n        data: [\n          {\n            path: `M ${x},${this.boundingRect.y} L ${x},${this.boundingRect.y + this.boundingRect.height} `,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.boundingRect.x + this.boundingRect.width - (this.showLabel ? this.axisConfig.labelPadding : 0) - (this.showTick ? this.axisConfig.tickLength : 0) - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          y: this.getScaleValue(tick),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"middle\",\n          horizontalPos: \"right\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const x = this.boundingRect.x + this.boundingRect.width - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${x},${this.getScaleValue(tick)} L ${x - this.axisConfig.tickLength},${this.getScaleValue(tick)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.axisConfig.titlePadding,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 270,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForBottomAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + this.axisConfig.labelPadding + (this.showTick ? this.axisConfig.tickLength : 0) + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y} L ${this.getScaleValue(tick)},${y + this.axisConfig.tickLength}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.range[0] + (this.range[1] - this.range[0]) / 2,\n            y: this.boundingRect.y + this.boundingRect.height - this.axisConfig.titlePadding - this.titleTextHeight,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForTopAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.boundingRect.height - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + (this.showTitle ? this.titleTextHeight + this.axisConfig.titlePadding * 2 : 0) + this.axisConfig.labelPadding,\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y + this.boundingRect.height - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)} L ${this.getScaleValue(tick)},${y + this.boundingRect.height - this.axisConfig.tickLength - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.axisConfig.titlePadding,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElements() {\n    if (this.axisPosition === \"left\") {\n      return this.getDrawableElementsForLeftAxis();\n    }\n    if (this.axisPosition === \"right\") {\n      throw Error(\"Drawing of right axis is not implemented\");\n    }\n    if (this.axisPosition === \"bottom\") {\n      return this.getDrawableElementsForBottomAxis();\n    }\n    if (this.axisPosition === \"top\") {\n      return this.getDrawableElementsForTopAxis();\n    }\n    return [];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nvar BandAxis = class extends BaseAxis {\n  static {\n    __name(this, \"BandAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, categories, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.categories = categories;\n    this.scale = scaleBand().domain(this.categories).range(this.getRange());\n  }\n  setRange(range) {\n    super.setRange(range);\n  }\n  recalculateScale() {\n    this.scale = scaleBand().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(0.5);\n    log.trace(\"BandAxis axis final categories, range: \", this.categories, this.getRange());\n  }\n  getTickValues() {\n    return this.categories;\n  }\n  getScaleValue(value) {\n    return this.scale(value) ?? this.getRange()[0];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/linearAxis.ts\nimport { scaleLinear } from \"d3\";\nvar LinearAxis = class extends BaseAxis {\n  static {\n    __name(this, \"LinearAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, domain, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.domain = domain;\n    this.scale = scaleLinear().domain(this.domain).range(this.getRange());\n  }\n  getTickValues() {\n    return this.scale.ticks();\n  }\n  recalculateScale() {\n    const domain = [...this.domain];\n    if (this.axisPosition === \"left\") {\n      domain.reverse();\n    }\n    this.scale = scaleLinear().domain(domain).range(this.getRange());\n  }\n  getScaleValue(value) {\n    return this.scale(value);\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/index.ts\nfunction getAxis(data, axisConfig, axisThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  if (isBandAxisData(data)) {\n    return new BandAxis(\n      axisConfig,\n      axisThemeConfig,\n      data.categories,\n      data.title,\n      textDimensionCalculator\n    );\n  }\n  return new LinearAxis(\n    axisConfig,\n    axisThemeConfig,\n    [data.min, data.max],\n    data.title,\n    textDimensionCalculator\n  );\n}\n__name(getAxis, \"getAxis\");\n\n// src/diagrams/xychart/chartBuilder/components/chartTitle.ts\nvar ChartTitle = class {\n  constructor(textDimensionCalculator, chartConfig, chartData, chartThemeConfig) {\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n    this.showChartTitle = false;\n  }\n  static {\n    __name(this, \"ChartTitle\");\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    const titleDimension = this.textDimensionCalculator.getMaxDimension(\n      [this.chartData.title],\n      this.chartConfig.titleFontSize\n    );\n    const widthRequired = Math.max(titleDimension.width, availableSpace.width);\n    const heightRequired = titleDimension.height + 2 * this.chartConfig.titlePadding;\n    if (titleDimension.width <= widthRequired && titleDimension.height <= heightRequired && this.chartConfig.showTitle && this.chartData.title) {\n      this.boundingRect.width = widthRequired;\n      this.boundingRect.height = heightRequired;\n      this.showChartTitle = true;\n    }\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    const drawableElem = [];\n    if (this.showChartTitle) {\n      drawableElem.push({\n        groupTexts: [\"chart-title\"],\n        type: \"text\",\n        data: [\n          {\n            fontSize: this.chartConfig.titleFontSize,\n            text: this.chartData.title,\n            verticalPos: \"middle\",\n            horizontalPos: \"center\",\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.chartThemeConfig.titleColor,\n            rotation: 0\n          }\n        ]\n      });\n    }\n    return drawableElem;\n  }\n};\nfunction getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  return new ChartTitle(textDimensionCalculator, chartConfig, chartData, chartThemeConfig);\n}\n__name(getChartTitleComponent, \"getChartTitleComponent\");\n\n// src/diagrams/xychart/chartBuilder/components/plot/linePlot.ts\nimport { line } from \"d3\";\nvar LinePlot = class {\n  constructor(plotData, xAxis, yAxis, orientation, plotIndex2) {\n    this.plotData = plotData;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    __name(this, \"LinePlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.plotData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    let path;\n    if (this.orientation === \"horizontal\") {\n      path = line().y((d) => d[0]).x((d) => d[1])(finalData);\n    } else {\n      path = line().x((d) => d[0]).y((d) => d[1])(finalData);\n    }\n    if (!path) {\n      return [];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `line-plot-${this.plotIndex}`],\n        type: \"path\",\n        data: [\n          {\n            path,\n            strokeFill: this.plotData.strokeFill,\n            strokeWidth: this.plotData.strokeWidth\n          }\n        ]\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/barPlot.ts\nvar BarPlot = class {\n  constructor(barData, boundingRect, xAxis, yAxis, orientation, plotIndex2) {\n    this.barData = barData;\n    this.boundingRect = boundingRect;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    __name(this, \"BarPlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.barData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    const barPaddingPercent = 0.05;\n    const barWidth = Math.min(this.xAxis.getAxisOuterPadding() * 2, this.xAxis.getTickDistance()) * (1 - barPaddingPercent);\n    const barWidthHalf = barWidth / 2;\n    if (this.orientation === \"horizontal\") {\n      return [\n        {\n          groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n          type: \"rect\",\n          data: finalData.map((data) => ({\n            x: this.boundingRect.x,\n            y: data[0] - barWidthHalf,\n            height: barWidth,\n            width: data[1] - this.boundingRect.x,\n            fill: this.barData.fill,\n            strokeWidth: 0,\n            strokeFill: this.barData.fill\n          }))\n        }\n      ];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n        type: \"rect\",\n        data: finalData.map((data) => ({\n          x: data[0] - barWidthHalf,\n          y: data[1],\n          width: barWidth,\n          height: this.boundingRect.y + this.boundingRect.height - data[1],\n          fill: this.barData.fill,\n          strokeWidth: 0,\n          strokeFill: this.barData.fill\n        }))\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/index.ts\nvar BasePlot = class {\n  constructor(chartConfig, chartData, chartThemeConfig) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  static {\n    __name(this, \"BasePlot\");\n  }\n  setAxes(xAxis, yAxis) {\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height;\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    if (!(this.xAxis && this.yAxis)) {\n      throw Error(\"Axes must be passed to render Plots\");\n    }\n    const drawableElem = [];\n    for (const [i, plot] of this.chartData.plots.entries()) {\n      switch (plot.type) {\n        case \"line\":\n          {\n            const linePlot = new LinePlot(\n              plot,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...linePlot.getDrawableElement());\n          }\n          break;\n        case \"bar\":\n          {\n            const barPlot = new BarPlot(\n              plot,\n              this.boundingRect,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...barPlot.getDrawableElement());\n          }\n          break;\n      }\n    }\n    return drawableElem;\n  }\n};\nfunction getPlotComponent(chartConfig, chartData, chartThemeConfig) {\n  return new BasePlot(chartConfig, chartData, chartThemeConfig);\n}\n__name(getPlotComponent, \"getPlotComponent\");\n\n// src/diagrams/xychart/chartBuilder/orchestrator.ts\nvar Orchestrator = class {\n  constructor(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.componentStore = {\n      title: getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2),\n      plot: getPlotComponent(chartConfig, chartData, chartThemeConfig),\n      xAxis: getAxis(\n        chartData.xAxis,\n        chartConfig.xAxis,\n        {\n          titleColor: chartThemeConfig.xAxisTitleColor,\n          labelColor: chartThemeConfig.xAxisLabelColor,\n          tickColor: chartThemeConfig.xAxisTickColor,\n          axisLineColor: chartThemeConfig.xAxisLineColor\n        },\n        tmpSVGGroup2\n      ),\n      yAxis: getAxis(\n        chartData.yAxis,\n        chartConfig.yAxis,\n        {\n          titleColor: chartThemeConfig.yAxisTitleColor,\n          labelColor: chartThemeConfig.yAxisLabelColor,\n          tickColor: chartThemeConfig.yAxisTickColor,\n          axisLineColor: chartThemeConfig.yAxisLineColor\n        },\n        tmpSVGGroup2\n      )\n    };\n  }\n  static {\n    __name(this, \"Orchestrator\");\n  }\n  calculateVerticalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    plotY = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"bottom\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    this.componentStore.yAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    plotX = spaceUsed.width;\n    availableWidth -= spaceUsed.width;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.xAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: plotX, y: plotY + chartHeight });\n    this.componentStore.yAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateHorizontalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let titleYEnd = 0;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    titleYEnd = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableWidth -= spaceUsed.width;\n    plotX = spaceUsed.width;\n    this.componentStore.yAxis.setAxisPosition(\"top\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    plotY = titleYEnd + spaceUsed.height;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.yAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: plotX, y: titleYEnd });\n    this.componentStore.xAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateSpace() {\n    if (this.chartConfig.chartOrientation === \"horizontal\") {\n      this.calculateHorizontalSpace();\n    } else {\n      this.calculateVerticalSpace();\n    }\n  }\n  getDrawableElement() {\n    this.calculateSpace();\n    const drawableElem = [];\n    this.componentStore.plot.setAxes(this.componentStore.xAxis, this.componentStore.yAxis);\n    for (const component of Object.values(this.componentStore)) {\n      drawableElem.push(...component.getDrawableElements());\n    }\n    return drawableElem;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/index.ts\nvar XYChartBuilder = class {\n  static {\n    __name(this, \"XYChartBuilder\");\n  }\n  static build(config, chartData, chartThemeConfig, tmpSVGGroup2) {\n    const orchestrator = new Orchestrator(config, chartData, chartThemeConfig, tmpSVGGroup2);\n    return orchestrator.getDrawableElement();\n  }\n};\n\n// src/diagrams/xychart/xychartDb.ts\nvar plotIndex = 0;\nvar tmpSVGGroup;\nvar xyChartConfig = getChartDefaultConfig();\nvar xyChartThemeConfig = getChartDefaultThemeConfig();\nvar xyChartData = getChartDefaultData();\nvar plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\nvar hasSetXAxis = false;\nvar hasSetYAxis = false;\nfunction getChartDefaultThemeConfig() {\n  const defaultThemeVariables = getThemeVariables();\n  const config = getConfig();\n  return cleanAndMerge(defaultThemeVariables.xyChart, config.themeVariables.xyChart);\n}\n__name(getChartDefaultThemeConfig, \"getChartDefaultThemeConfig\");\nfunction getChartDefaultConfig() {\n  const config = getConfig();\n  return cleanAndMerge(\n    defaultConfig_default.xyChart,\n    config.xyChart\n  );\n}\n__name(getChartDefaultConfig, \"getChartDefaultConfig\");\nfunction getChartDefaultData() {\n  return {\n    yAxis: {\n      type: \"linear\",\n      title: \"\",\n      min: Infinity,\n      max: -Infinity\n    },\n    xAxis: {\n      type: \"band\",\n      title: \"\",\n      categories: []\n    },\n    title: \"\",\n    plots: []\n  };\n}\n__name(getChartDefaultData, \"getChartDefaultData\");\nfunction textSanitizer(text) {\n  const config = getConfig();\n  return sanitizeText(text.trim(), config);\n}\n__name(textSanitizer, \"textSanitizer\");\nfunction setTmpSVGG(SVGG) {\n  tmpSVGGroup = SVGG;\n}\n__name(setTmpSVGG, \"setTmpSVGG\");\nfunction setOrientation(orientation) {\n  if (orientation === \"horizontal\") {\n    xyChartConfig.chartOrientation = \"horizontal\";\n  } else {\n    xyChartConfig.chartOrientation = \"vertical\";\n  }\n}\n__name(setOrientation, \"setOrientation\");\nfunction setXAxisTitle(title) {\n  xyChartData.xAxis.title = textSanitizer(title.text);\n}\n__name(setXAxisTitle, \"setXAxisTitle\");\nfunction setXAxisRangeData(min, max) {\n  xyChartData.xAxis = { type: \"linear\", title: xyChartData.xAxis.title, min, max };\n  hasSetXAxis = true;\n}\n__name(setXAxisRangeData, \"setXAxisRangeData\");\nfunction setXAxisBand(categories) {\n  xyChartData.xAxis = {\n    type: \"band\",\n    title: xyChartData.xAxis.title,\n    categories: categories.map((c) => textSanitizer(c.text))\n  };\n  hasSetXAxis = true;\n}\n__name(setXAxisBand, \"setXAxisBand\");\nfunction setYAxisTitle(title) {\n  xyChartData.yAxis.title = textSanitizer(title.text);\n}\n__name(setYAxisTitle, \"setYAxisTitle\");\nfunction setYAxisRangeData(min, max) {\n  xyChartData.yAxis = { type: \"linear\", title: xyChartData.yAxis.title, min, max };\n  hasSetYAxis = true;\n}\n__name(setYAxisRangeData, \"setYAxisRangeData\");\nfunction setYAxisRangeFromPlotData(data) {\n  const minValue = Math.min(...data);\n  const maxValue = Math.max(...data);\n  const prevMinValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.min : Infinity;\n  const prevMaxValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.max : -Infinity;\n  xyChartData.yAxis = {\n    type: \"linear\",\n    title: xyChartData.yAxis.title,\n    min: Math.min(prevMinValue, minValue),\n    max: Math.max(prevMaxValue, maxValue)\n  };\n}\n__name(setYAxisRangeFromPlotData, \"setYAxisRangeFromPlotData\");\nfunction transformDataWithoutCategory(data) {\n  let retData = [];\n  if (data.length === 0) {\n    return retData;\n  }\n  if (!hasSetXAxis) {\n    const prevMinValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.min : Infinity;\n    const prevMaxValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.max : -Infinity;\n    setXAxisRangeData(Math.min(prevMinValue, 1), Math.max(prevMaxValue, data.length));\n  }\n  if (!hasSetYAxis) {\n    setYAxisRangeFromPlotData(data);\n  }\n  if (isBandAxisData(xyChartData.xAxis)) {\n    retData = xyChartData.xAxis.categories.map((c, i) => [c, data[i]]);\n  }\n  if (isLinearAxisData(xyChartData.xAxis)) {\n    const min = xyChartData.xAxis.min;\n    const max = xyChartData.xAxis.max;\n    const step = (max - min) / (data.length - 1);\n    const categories = [];\n    for (let i = min; i <= max; i += step) {\n      categories.push(`${i}`);\n    }\n    retData = categories.map((c, i) => [c, data[i]]);\n  }\n  return retData;\n}\n__name(transformDataWithoutCategory, \"transformDataWithoutCategory\");\nfunction getPlotColorFromPalette(plotIndex2) {\n  return plotColorPalette[plotIndex2 === 0 ? 0 : plotIndex2 % plotColorPalette.length];\n}\n__name(getPlotColorFromPalette, \"getPlotColorFromPalette\");\nfunction setLineData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"line\",\n    strokeFill: getPlotColorFromPalette(plotIndex),\n    strokeWidth: 2,\n    data: plotData\n  });\n  plotIndex++;\n}\n__name(setLineData, \"setLineData\");\nfunction setBarData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"bar\",\n    fill: getPlotColorFromPalette(plotIndex),\n    data: plotData\n  });\n  plotIndex++;\n}\n__name(setBarData, \"setBarData\");\nfunction getDrawableElem() {\n  if (xyChartData.plots.length === 0) {\n    throw Error(\"No Plot to render, please provide a plot with some data\");\n  }\n  xyChartData.title = getDiagramTitle();\n  return XYChartBuilder.build(xyChartConfig, xyChartData, xyChartThemeConfig, tmpSVGGroup);\n}\n__name(getDrawableElem, \"getDrawableElem\");\nfunction getChartThemeConfig() {\n  return xyChartThemeConfig;\n}\n__name(getChartThemeConfig, \"getChartThemeConfig\");\nfunction getChartConfig() {\n  return xyChartConfig;\n}\n__name(getChartConfig, \"getChartConfig\");\nvar clear2 = /* @__PURE__ */ __name(function() {\n  clear();\n  plotIndex = 0;\n  xyChartConfig = getChartDefaultConfig();\n  xyChartData = getChartDefaultData();\n  xyChartThemeConfig = getChartDefaultThemeConfig();\n  plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\n  hasSetXAxis = false;\n  hasSetYAxis = false;\n}, \"clear\");\nvar xychartDb_default = {\n  getDrawableElem,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  setOrientation,\n  setXAxisTitle,\n  setXAxisRangeData,\n  setXAxisBand,\n  setYAxisTitle,\n  setYAxisRangeData,\n  setLineData,\n  setBarData,\n  setTmpSVGG,\n  getChartThemeConfig,\n  getChartConfig\n};\n\n// src/diagrams/xychart/xychartRenderer.ts\nvar draw = /* @__PURE__ */ __name((txt, id, _version, diagObj) => {\n  const db = diagObj.db;\n  const themeConfig = db.getChartThemeConfig();\n  const chartConfig = db.getChartConfig();\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"text-before-edge\" : \"middle\";\n  }\n  __name(getDominantBaseLine, \"getDominantBaseLine\");\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : verticalPos === \"right\" ? \"end\" : \"middle\";\n  }\n  __name(getTextAnchor, \"getTextAnchor\");\n  function getTextTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  __name(getTextTransformation, \"getTextTransformation\");\n  log.debug(\"Rendering xychart chart\\n\" + txt);\n  const svg = selectSvgElement(id);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const background = group.append(\"rect\").attr(\"width\", chartConfig.width).attr(\"height\", chartConfig.height).attr(\"class\", \"background\");\n  configureSvgSize(svg, chartConfig.height, chartConfig.width, true);\n  svg.attr(\"viewBox\", `0 0 ${chartConfig.width} ${chartConfig.height}`);\n  background.attr(\"fill\", themeConfig.backgroundColor);\n  db.setTmpSVGG(svg.append(\"g\").attr(\"class\", \"mermaid-tmp-group\"));\n  const shapes = db.getDrawableElem();\n  const groups = {};\n  function getGroup(gList) {\n    let elem = group;\n    let prefix = \"\";\n    for (const [i] of gList.entries()) {\n      let parent = group;\n      if (i > 0 && groups[prefix]) {\n        parent = groups[prefix];\n      }\n      prefix += gList[i];\n      elem = groups[prefix];\n      if (!elem) {\n        elem = groups[prefix] = parent.append(\"g\").attr(\"class\", gList[i]);\n      }\n    }\n    return elem;\n  }\n  __name(getGroup, \"getGroup\");\n  for (const shape of shapes) {\n    if (shape.data.length === 0) {\n      continue;\n    }\n    const shapeGroup = getGroup(shape.groupTexts);\n    switch (shape.type) {\n      case \"rect\":\n        shapeGroup.selectAll(\"rect\").data(shape.data).enter().append(\"rect\").attr(\"x\", (data) => data.x).attr(\"y\", (data) => data.y).attr(\"width\", (data) => data.width).attr(\"height\", (data) => data.height).attr(\"fill\", (data) => data.fill).attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        break;\n      case \"text\":\n        shapeGroup.selectAll(\"text\").data(shape.data).enter().append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", (data) => data.fill).attr(\"font-size\", (data) => data.fontSize).attr(\"dominant-baseline\", (data) => getDominantBaseLine(data.verticalPos)).attr(\"text-anchor\", (data) => getTextAnchor(data.horizontalPos)).attr(\"transform\", (data) => getTextTransformation(data)).text((data) => data.text);\n        break;\n      case \"path\":\n        shapeGroup.selectAll(\"path\").data(shape.data).enter().append(\"path\").attr(\"d\", (data) => data.path).attr(\"fill\", (data) => data.fill ? data.fill : \"none\").attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        break;\n    }\n  }\n}, \"draw\");\nvar xychartRenderer_default = {\n  draw\n};\n\n// src/diagrams/xychart/xychartDiagram.ts\nvar diagram = {\n  parser: xychart_default,\n  db: xychartDb_default,\n  renderer: xychartRenderer_default\n};\nexport {\n  diagram\n};\n"], "names": ["parser", "o", "__name", "k", "v", "o2", "l", "length", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "parser2", "trace", "yy", "symbols_", "terminals_", "productions_", "performAction", "yytext", "yyleng", "y<PERSON><PERSON>o", "yystate", "$$", "_$", "$0", "setOrientation", "setDiagramTitle", "text", "trim", "setLineData", "type", "setBarData", "this", "$", "setAccTitle", "setAccDescription", "Number", "setXAxisTitle", "setXAxisBand", "setXAxisRangeData", "setYAxisTitle", "setYAxisRangeData", "table", "defaultActions", "parseError", "str", "hash", "recoverable", "error", "Error", "parse", "input", "self", "stack", "tstack", "vstack", "lstack", "recovering", "args", "slice", "call", "arguments", "lexer2", "Object", "create", "lexer", "sharedState", "prototype", "hasOwnProperty", "setInput", "yylloc", "yyloc", "push", "ranges", "options", "lex", "token", "pop", "Array", "getPrototypeOf", "n", "symbol", "preErrorSymbol", "state", "action", "r", "p", "len", "newState", "expected", "yyval", "errStr", "showPosition", "join", "match", "line", "loc", "first_line", "last_line", "first_column", "last_column", "range", "apply", "concat", "EOF", "_input", "_more", "_backtrack", "done", "matched", "conditionStack", "offset", "ch", "unput", "lines", "split", "substr", "oldLines", "more", "reject", "backtrack_lexer", "less", "pastInput", "past", "replace", "upcomingInput", "next", "pre", "c", "test_match", "indexed_rule", "backup", "matches", "tempMatch", "index", "rules", "_currentRules", "i", "flex", "begin", "condition", "popState", "conditions", "topState", "Math", "abs", "pushState", "stateStackSize", "yy_", "$avoiding_name_collisions", "YY_START", "<PERSON><PERSON><PERSON>", "xychart_default", "isBarPlot", "data", "isBandAxisData", "isLinearAxisData", "TextDimensionCalculatorWithFont", "constructor", "parentGroup", "getMaxDimension", "texts", "fontSize", "width", "reduce", "acc", "cur", "max", "height", "dimension", "elem", "append", "attr", "t", "bbox", "computeDimensionOfText", "remove", "BaseAxis", "axisConfig", "title", "textDimensionCalculator", "axisThemeConfig", "boundingRect", "x", "y", "axisPosition", "showTitle", "showLabel", "showTick", "showAxisLine", "outerPadding", "titleTextHeight", "labelTextHeight", "setRang<PERSON>", "recalculateScale", "getRange", "setAxisPosition", "getTickDistance", "getTick<PERSON><PERSON>ues", "getAxisOuterPadding", "getLabelDimension", "map", "tick", "toString", "labelFontSize", "recalculateOuterPaddingToDrawBar", "floor", "calculateSpaceIfDrawnHorizontally", "availableSpace", "availableHeight", "axisLineWidth", "spaceRequired", "maxPadding", "min", "heightRequired", "labelPadding", "tick<PERSON><PERSON>th", "titleFontSize", "titlePadding", "calculateSpaceIfDrawnVertical", "availableWidth", "widthRequired", "calculateSpace", "setBoundingBoxXY", "point", "getDrawableElementsForLeftAxis", "drawableElement", "groupTexts", "path", "strokeFill", "axisLineColor", "strokeWidth", "getScaleValue", "fill", "labelColor", "rotation", "verticalPos", "horizontalPos", "tickColor", "tickWidth", "titleColor", "getDrawableElementsForBottomAxis", "getDrawableElementsForTopAxis", "getDrawableElements", "BandAxis", "categories", "super", "scale", "scaleBand", "domain", "paddingInner", "paddingOuter", "align", "log", "value", "LinearAxis", "scaleLinear", "ticks", "reverse", "getAxis", "tmpSVGGroup2", "ChartTitle", "chartConfig", "chartData", "chartThemeConfig", "showChartTitle", "titleDimension", "drawableElem", "getChartTitleComponent", "LinePlot", "plotData", "xAxis", "yAxis", "orientation", "plotIndex2", "plotIndex", "getDrawableElement", "finalData", "d", "BarPlot", "barData", "<PERSON><PERSON><PERSON><PERSON>", "bar<PERSON><PERSON>thHalf", "BasePlot", "setAxes", "plot", "plots", "entries", "linePlot", "chartOrientation", "barPlot", "getPlotComponent", "tmpSVGGroup", "Orchestrator", "componentStore", "xAxisTitleColor", "xAxisLabelColor", "xAxisTickColor", "xAxisLineColor", "yAxisTitleColor", "yAxisLabelColor", "yAxisTickColor", "yAxisLineColor", "calculateVerticalSpace", "plotX", "plotY", "chartWidth", "plotReservedSpacePercent", "chartHeight", "spaceUsed", "some", "calculateHorizontalSpace", "titleYEnd", "component", "values", "XYChartBuilder", "build", "config", "xyChartConfig", "getChartDefaultConfig", "xyChartThemeConfig", "getChartDefaultThemeConfig", "xyChartData", "getChartDefaultData", "plotColorPalette", "color", "hasSetXAxis", "hasSetYAxis", "defaultThemeVariables", "getThemeVariables", "getConfig", "cleanAndMerge", "xyChart", "themeVariables", "defaultConfig_default", "Infinity", "textSanitizer", "sanitizeText", "setTmpSVGG", "SVGG", "setYAxisRangeFromPlotData", "minValue", "maxValue", "prevMinValue", "prevMaxValue", "transformDataWithoutCategory", "retData", "step", "getPlotColorFromPalette", "getDrawableElem", "getDiagramTitle", "getChartThemeConfig", "getChartConfig", "diagram", "db", "clear", "getAccTitle", "getAccDescription", "renderer", "draw", "txt", "id", "_version", "diagObj", "themeConfig", "getDominantBaseLine", "getTextAnchor", "getTextTransformation", "debug", "svg", "selectSvgElement", "group", "background", "configureSvgSize", "backgroundColor", "shapes", "groups", "getGroup", "gList", "prefix", "parent", "shape", "shapeGroup", "selectAll", "enter"], "sourceRoot": ""}