{"version": 3, "file": "static/js/449.d6367e94.chunk.js", "mappings": "6KAcIA,EAAS,CACXC,OAAuBC,EAAAA,EAAAA,KAAOC,UAC5B,MAAMC,QAAYH,EAAAA,EAAAA,IAAM,OAAQI,GAChCC,EAAAA,GAAIC,MAAMH,EAAI,GACb,UAIDI,EAAkB,CAAEC,QAASC,EAAAA,EAAgBD,SAiB7CE,EAAU,CACZX,SACAY,GAjBO,CACPC,YAF+BX,EAAAA,EAAAA,KAAO,IAAMM,EAAgBC,SAAS,eAmBrEK,SANa,CAAEC,MAPUb,EAAAA,EAAAA,KAAO,CAACc,EAAMC,EAAIR,KAC3CH,EAAAA,GAAIC,MAAM,2BAA6BS,GACvC,MAAME,GAAMC,EAAAA,EAAAA,GAAiBF,IAC7BG,EAAAA,EAAAA,IAAiBF,EAAK,IAAK,KAAK,GAClBA,EAAIG,OAAO,KACnBA,OAAO,QAAQC,KAAK,IAAK,KAAKA,KAAK,IAAK,IAAIA,KAAK,QAAS,WAAWA,KAAK,YAAa,IAAIC,MAAM,cAAe,UAAUP,KAAK,IAAIP,IAAU,GAClJ,S", "sources": ["../../node_modules/mermaid/dist/chunks/mermaid.core/infoDiagram-PH2N3AL5.mjs"], "sourcesContent": ["import {\n  package_default\n} from \"./chunk-5NNNAHNI.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/info/infoParser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"info\", input);\n    log.debug(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/info/infoDb.ts\nvar DEFAULT_INFO_DB = { version: package_default.version };\nvar getVersion = /* @__PURE__ */ __name(() => DEFAULT_INFO_DB.version, \"getVersion\");\nvar db = {\n  getVersion\n};\n\n// src/diagrams/info/infoRenderer.ts\nvar draw = /* @__PURE__ */ __name((text, id, version) => {\n  log.debug(\"rendering info diagram\\n\" + text);\n  const svg = selectSvgElement(id);\n  configureSvgSize(svg, 100, 400, true);\n  const group = svg.append(\"g\");\n  group.append(\"text\").attr(\"x\", 100).attr(\"y\", 40).attr(\"class\", \"version\").attr(\"font-size\", 32).style(\"text-anchor\", \"middle\").text(`v${version}`);\n}, \"draw\");\nvar renderer = { draw };\n\n// src/diagrams/info/infoDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer\n};\nexport {\n  diagram\n};\n"], "names": ["parser", "parse", "__name", "async", "ast", "input", "log", "debug", "DEFAULT_INFO_DB", "version", "package_default", "diagram", "db", "getVersion", "renderer", "draw", "text", "id", "svg", "selectSvgElement", "configureSvgSize", "append", "attr", "style"], "sourceRoot": ""}