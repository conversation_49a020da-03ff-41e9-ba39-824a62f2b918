{"ast": null, "code": "// EntityPopup.tsx\nimport React from'react';import{getEntityPopupStyles}from'./EntityPopupStyles';import HistoryAnalysisSection from'./HistoryAnalysisSection';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EntityPopup=_ref=>{let{darkMode,entityName,position,onClose,onModify,memoryAttributes=[],memoryMethods=[],extractedAttributes=[],extractedMethods=[],currentDiagramText}=_ref;// État pour les checkboxes\nconst[selectedAttributes,setSelectedAttributes]=React.useState([]);const[selectedMethods,setSelectedMethods]=React.useState([]);// États pour l'analyse historique - Résolution de conflits supprimée\n// Données de classe actuelle pour l'analyse historique\nconst currentClassData=React.useMemo(()=>({name:entityName,attributes:[...memoryAttributes,...extractedAttributes],methods:[...memoryMethods,...extractedMethods]}),[entityName,memoryAttributes,extractedAttributes,memoryMethods,extractedMethods]);// Filtrer pour n'afficher que les attributs et méthodes qui ne sont pas dans le texte extrait\n// Modifier la logique de filtrage pour être insensible à la casse\nconst uniqueAttributes=memoryAttributes.filter(attr=>{// Extraire le nom de l'attribut sans le type ou la visibilité\nconst attrName=attr.replace(/^[+-]?\\s*/,'').split(':')[0].trim().toLowerCase();return!extractedAttributes.some(extractedAttr=>{// Extraire le nom de l'attribut extrait\nconst extractedAttrName=extractedAttr.replace(/^[+-]?\\s*/,'').split(':')[0].trim().toLowerCase();return extractedAttrName===attrName;});});const uniqueMethods=memoryMethods.filter(method=>{// Extraire le nom de la méthode sans les paramètres\nconst methodName=method.replace(/^[+-]?\\s*/,'').split('(')[0].trim().toLowerCase();return!extractedMethods.some(extractedMethod=>{// Extraire le nom de la méthode extraite\nconst extractedMethodName=extractedMethod.replace(/^[+-]?\\s*/,'').split('(')[0].trim().toLowerCase();return extractedMethodName===methodName;});});// Gestionnaires pour les checkboxes\nconst handleAttributeToggle=attribute=>{setSelectedAttributes(prev=>prev.includes(attribute)?prev.filter(attr=>attr!==attribute):[...prev,attribute]);};const handleMethodToggle=method=>{setSelectedMethods(prev=>prev.includes(method)?prev.filter(m=>m!==method):[...prev,method]);};// Fonction pour gérer l'import depuis l'historique\nconst handleHistoryImport=importedData=>{// Import direct sans résolution de conflits\n// Ajouter les éléments importés aux sélections actuelles\nconst newSelectedAttributes=[...selectedAttributes];const newSelectedMethods=[...selectedMethods];// Ajouter les attributs importés (éviter les doublons)\nimportedData.attributes.forEach(attr=>{if(!newSelectedAttributes.includes(attr)){newSelectedAttributes.push(attr);}});// Ajouter les méthodes importées (éviter les doublons)\nimportedData.methods.forEach(method=>{if(!newSelectedMethods.includes(method)){newSelectedMethods.push(method);}});// Mettre à jour les sélections\nsetSelectedAttributes(newSelectedAttributes);setSelectedMethods(newSelectedMethods);};// Fonction pour gérer le clic sur le bouton Modifier\nconst handleModifyClick=()=>{onModify(selectedAttributes,selectedMethods);};// Gestionnaires d'événements pour les effets hover\nconst handleMouseEnter=e=>{if(e.target===e.currentTarget){e.target.style.backgroundColor=darkMode?'#475569':'#f3f4f6';}};const handleMouseLeave=e=>{if(e.target===e.currentTarget){e.target.style.backgroundColor=darkMode?'#334155':'#f8fafc';}};const handleModifyHover=(e,isEnter)=>{e.target.style.backgroundColor=isEnter?'#2563eb':'#3b82f6';e.target.style.transform=isEnter?'translateY(-1px)':'translateY(0)';};const handleCancelHover=(e,isEnter)=>{e.target.style.backgroundColor=isEnter?darkMode?'#374151':'#f9fafb':'transparent';};// Gérer le clic sur l'overlay pour fermer le popup\nconst handleOverlayClick=e=>{// Fermer seulement si on clique sur l'overlay, pas sur le popup\nif(e.target===e.currentTarget){onClose();}};// Empêcher la propagation du clic depuis le popup vers l'overlay\nconst handlePopupClick=e=>{e.stopPropagation();};// Obtenir les styles\nconst styles=getEntityPopupStyles(darkMode);return/*#__PURE__*/_jsx(\"div\",{style:styles.overlay,onClick:handleOverlayClick,children:/*#__PURE__*/_jsxs(\"div\",{style:styles.popup,onClick:handlePopupClick,children:[/*#__PURE__*/_jsxs(\"div\",{style:styles.header,children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{style:styles.title,children:entityName}),/*#__PURE__*/_jsx(\"div\",{style:styles.subtitle,children:\"UML IA propose plusieurs choix possibles pour cette entit\\xE9.\"})]}),/*#__PURE__*/_jsx(\"button\",{style:styles.closeButton,onClick:onClose,onMouseEnter:handleMouseEnter,onMouseLeave:handleMouseLeave,children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:styles.content,children:[/*#__PURE__*/_jsx(HistoryAnalysisSection,{darkMode:darkMode,targetClassName:entityName,currentClassData:currentClassData,onImport:handleHistoryImport,currentDiagramText:currentDiagramText}),/*#__PURE__*/_jsx(\"div\",{style:styles.tableContainer,children:/*#__PURE__*/_jsxs(\"table\",{style:styles.table,children:[/*#__PURE__*/_jsx(\"thead\",{style:styles.tableHeader,children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{style:styles.tableHeaderCell,children:\"ATTRIBUTS\"}),/*#__PURE__*/_jsx(\"th\",{style:styles.tableHeaderCell,children:\"M\\xC9THODES\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{style:styles.tableBody,children:/*#__PURE__*/_jsxs(\"tr\",{style:styles.tableRow,children:[/*#__PURE__*/_jsx(\"td\",{style:styles.tableCell,children:uniqueAttributes.length>0?uniqueAttributes.map((attr,index)=>/*#__PURE__*/_jsxs(\"div\",{style:styles.checkboxContainer,children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:`attr-${index}`,checked:selectedAttributes.includes(attr),onChange:()=>handleAttributeToggle(attr),style:styles.checkbox}),/*#__PURE__*/_jsx(\"label\",{htmlFor:`attr-${index}`,style:styles.checkboxLabel,children:attr})]},index)):/*#__PURE__*/_jsx(\"div\",{style:styles.emptyState,children:\"Aucun attribut suppl\\xE9mentaire\"})}),/*#__PURE__*/_jsx(\"td\",{style:styles.tableCell,children:uniqueMethods.length>0?uniqueMethods.map((method,index)=>/*#__PURE__*/_jsxs(\"div\",{style:styles.checkboxContainer,children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:`method-${index}`,checked:selectedMethods.includes(method),onChange:()=>handleMethodToggle(method),style:styles.checkbox}),/*#__PURE__*/_jsx(\"label\",{htmlFor:`method-${index}`,style:styles.checkboxLabel,children:method})]},index)):/*#__PURE__*/_jsx(\"div\",{style:styles.emptyState,children:\"Aucune m\\xE9thode suppl\\xE9mentaire\"})})]})})]})}),/*#__PURE__*/_jsxs(\"div\",{style:styles.buttonContainer,children:[/*#__PURE__*/_jsxs(\"button\",{style:styles.modifyButton,onClick:handleModifyClick,onMouseEnter:e=>handleModifyHover(e,true),onMouseLeave:e=>handleModifyHover(e,false),children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u270F\\uFE0F\"}),\"Modifier l'entit\\xE9\"]}),/*#__PURE__*/_jsx(\"button\",{style:styles.cancelButton,onClick:onClose,onMouseEnter:e=>handleCancelHover(e,true),onMouseLeave:e=>handleCancelHover(e,false),children:\"Annuler\"})]})]})]})});};export default EntityPopup;", "map": {"version": 3, "names": ["React", "getEntityPopupStyles", "HistoryAnalysisSection", "jsx", "_jsx", "jsxs", "_jsxs", "EntityPopup", "_ref", "darkMode", "entityName", "position", "onClose", "onModify", "memoryAttributes", "memoryMethods", "extractedAttributes", "extractedMethods", "currentDiagramText", "selectedAttributes", "setSelectedAttributes", "useState", "selectedMethods", "setSelectedMethods", "currentClassData", "useMemo", "name", "attributes", "methods", "uniqueAttributes", "filter", "attr", "attrName", "replace", "split", "trim", "toLowerCase", "some", "extractedAttr", "extractedAttrName", "uniqueMethods", "method", "methodName", "extractedMethod", "extractedMethodName", "handleAttributeToggle", "attribute", "prev", "includes", "handleMethodToggle", "m", "handleHistoryImport", "importedData", "newSelectedAttributes", "newSelectedMethods", "for<PERSON>ach", "push", "handleModifyClick", "handleMouseEnter", "e", "target", "currentTarget", "style", "backgroundColor", "handleMouseLeave", "handleModifyHover", "isEnter", "transform", "handleCancelHover", "handleOverlayClick", "handlePopupClick", "stopPropagation", "styles", "overlay", "onClick", "children", "popup", "header", "title", "subtitle", "closeButton", "onMouseEnter", "onMouseLeave", "content", "targetClassName", "onImport", "tableContainer", "table", "tableHeader", "tableHeaderCell", "tableBody", "tableRow", "tableCell", "length", "map", "index", "checkboxContainer", "type", "id", "checked", "onChange", "checkbox", "htmlFor", "checkboxLabel", "emptyState", "buttonContainer", "modifyButton", "cancelButton"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/imageUp/EntityPopup.tsx"], "sourcesContent": ["// EntityPopup.tsx\r\nimport React from 'react';\r\nimport { getEntityPopupStyles } from './EntityPopupStyles';\r\nimport HistoryAnalysisSection from './HistoryAnalysisSection';\r\n\r\nimport { ClassData } from '../../services/HistoryAnalysisService';\r\n\r\ninterface EntityPopupProps {\r\n  darkMode: boolean;\r\n  entityName: string;\r\n  position: { x: number; y: number };\r\n  onClose: () => void;\r\n  onModify: (selectedAttributes: string[], selectedMethods: string[]) => void;\r\n  memoryAttributes: string[]; // Attributs de la classe depuis memoire.txt\r\n  memoryMethods: string[];    // Méthodes de la classe depuis memoire.txt\r\n  extractedAttributes: string[]; // Attributs extraits du texte actuel\r\n  extractedMethods: string[];    // Méthodes extraites du texte actuel\r\n  currentDiagramText?: string; // Texte du diagramme actuel pour exclure de l'analyse historique\r\n}\r\n\r\nconst EntityPopup: React.FC<EntityPopupProps> = ({\r\n  darkMode,\r\n  entityName,\r\n  position,\r\n  onClose,\r\n  onModify,\r\n  memoryAttributes = [],\r\n  memoryMethods = [],\r\n  extractedAttributes = [],\r\n  extractedMethods = [],\r\n  currentDiagramText\r\n}) => {\r\n  // État pour les checkboxes\r\n  const [selectedAttributes, setSelectedAttributes] = React.useState<string[]>([]);\r\n  const [selectedMethods, setSelectedMethods] = React.useState<string[]>([]);\r\n\r\n  // États pour l'analyse historique - Résolution de conflits supprimée\r\n\r\n  // Données de classe actuelle pour l'analyse historique\r\n  const currentClassData: ClassData = React.useMemo(() => ({\r\n    name: entityName,\r\n    attributes: [...memoryAttributes, ...extractedAttributes],\r\n    methods: [...memoryMethods, ...extractedMethods]\r\n  }), [entityName, memoryAttributes, extractedAttributes, memoryMethods, extractedMethods]);\r\n\r\n  // Filtrer pour n'afficher que les attributs et méthodes qui ne sont pas dans le texte extrait\r\n  // Modifier la logique de filtrage pour être insensible à la casse\r\n  const uniqueAttributes = memoryAttributes.filter(attr => {\r\n    // Extraire le nom de l'attribut sans le type ou la visibilité\r\n    const attrName = attr.replace(/^[+-]?\\s*/, '').split(':')[0].trim().toLowerCase();\r\n    \r\n    return !extractedAttributes.some(extractedAttr => {\r\n      // Extraire le nom de l'attribut extrait\r\n      const extractedAttrName = extractedAttr.replace(/^[+-]?\\s*/, '').split(':')[0].trim().toLowerCase();\r\n      return extractedAttrName === attrName;\r\n    });\r\n  });\r\n\r\n  const uniqueMethods = memoryMethods.filter(method => {\r\n    // Extraire le nom de la méthode sans les paramètres\r\n    const methodName = method.replace(/^[+-]?\\s*/, '').split('(')[0].trim().toLowerCase();\r\n    \r\n    return !extractedMethods.some(extractedMethod => {\r\n      // Extraire le nom de la méthode extraite\r\n      const extractedMethodName = extractedMethod.replace(/^[+-]?\\s*/, '').split('(')[0].trim().toLowerCase();\r\n      return extractedMethodName === methodName;\r\n    });\r\n  });\r\n\r\n  // Gestionnaires pour les checkboxes\r\n  const handleAttributeToggle = (attribute: string) => {\r\n    setSelectedAttributes(prev => \r\n      prev.includes(attribute) \r\n        ? prev.filter(attr => attr !== attribute) \r\n        : [...prev, attribute]\r\n    );\r\n  };\r\n\r\n  const handleMethodToggle = (method: string) => {\r\n    setSelectedMethods(prev =>\r\n      prev.includes(method)\r\n        ? prev.filter(m => m !== method)\r\n        : [...prev, method]\r\n    );\r\n  };\r\n\r\n  // Fonction pour gérer l'import depuis l'historique\r\n  const handleHistoryImport = (importedData: ClassData) => {\r\n    // Import direct sans résolution de conflits\r\n    // Ajouter les éléments importés aux sélections actuelles\r\n    const newSelectedAttributes = [...selectedAttributes];\r\n    const newSelectedMethods = [...selectedMethods];\r\n\r\n    // Ajouter les attributs importés (éviter les doublons)\r\n    importedData.attributes.forEach(attr => {\r\n      if (!newSelectedAttributes.includes(attr)) {\r\n        newSelectedAttributes.push(attr);\r\n      }\r\n    });\r\n\r\n    // Ajouter les méthodes importées (éviter les doublons)\r\n    importedData.methods.forEach(method => {\r\n      if (!newSelectedMethods.includes(method)) {\r\n        newSelectedMethods.push(method);\r\n      }\r\n    });\r\n\r\n    // Mettre à jour les sélections\r\n    setSelectedAttributes(newSelectedAttributes);\r\n    setSelectedMethods(newSelectedMethods);\r\n  };\r\n\r\n\r\n  \r\n  // Fonction pour gérer le clic sur le bouton Modifier\r\n  const handleModifyClick = () => {\r\n    onModify(selectedAttributes, selectedMethods);\r\n  };\r\n\r\n  // Gestionnaires d'événements pour les effets hover\r\n  const handleMouseEnter = (e: React.MouseEvent) => {\r\n    if (e.target === e.currentTarget) {\r\n      (e.target as HTMLButtonElement).style.backgroundColor = darkMode ? '#475569' : '#f3f4f6';\r\n    }\r\n  };\r\n\r\n  const handleMouseLeave = (e: React.MouseEvent) => {\r\n    if (e.target === e.currentTarget) {\r\n      (e.target as HTMLButtonElement).style.backgroundColor = darkMode ? '#334155' : '#f8fafc';\r\n    }\r\n  };\r\n\r\n  const handleModifyHover = (e: React.MouseEvent, isEnter: boolean) => {\r\n    (e.target as HTMLButtonElement).style.backgroundColor = isEnter ? '#2563eb' : '#3b82f6';\r\n    (e.target as HTMLButtonElement).style.transform = isEnter ? 'translateY(-1px)' : 'translateY(0)';\r\n  };\r\n\r\n  const handleCancelHover = (e: React.MouseEvent, isEnter: boolean) => {\r\n    (e.target as HTMLButtonElement).style.backgroundColor = isEnter \r\n      ? (darkMode ? '#374151' : '#f9fafb') \r\n      : 'transparent';\r\n  };\r\n\r\n  // Gérer le clic sur l'overlay pour fermer le popup\r\n  const handleOverlayClick = (e: React.MouseEvent) => {\r\n    // Fermer seulement si on clique sur l'overlay, pas sur le popup\r\n    if (e.target === e.currentTarget) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  // Empêcher la propagation du clic depuis le popup vers l'overlay\r\n  const handlePopupClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n  };\r\n\r\n  // Obtenir les styles\r\n  const styles = getEntityPopupStyles(darkMode);\r\n\r\n  return (\r\n    <div style={styles.overlay} onClick={handleOverlayClick}>\r\n      <div style={styles.popup} onClick={handlePopupClick}>\r\n        <div style={styles.header}>\r\n          <div>\r\n            <h3 style={styles.title}>{entityName}</h3>\r\n            <div style={styles.subtitle}>UML IA propose plusieurs choix possibles pour cette entité.</div>\r\n          </div>\r\n          <button \r\n            style={styles.closeButton} \r\n            onClick={onClose}\r\n            onMouseEnter={handleMouseEnter}\r\n            onMouseLeave={handleMouseLeave}\r\n          >\r\n            ×\r\n          </button>\r\n        </div>\r\n\r\n        <div style={styles.content}>\r\n          {/* Section d'analyse historique */}\r\n          <HistoryAnalysisSection\r\n            darkMode={darkMode}\r\n            targetClassName={entityName}\r\n            currentClassData={currentClassData}\r\n            onImport={handleHistoryImport}\r\n            currentDiagramText={currentDiagramText}\r\n          />\r\n          <div style={styles.tableContainer}>\r\n            <table style={styles.table}>\r\n              <thead style={styles.tableHeader}>\r\n                <tr>\r\n                  <th style={styles.tableHeaderCell}>ATTRIBUTS</th>\r\n                  <th style={styles.tableHeaderCell}>MÉTHODES</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody style={styles.tableBody}>\r\n                <tr style={styles.tableRow}>\r\n                  <td style={styles.tableCell}>\r\n                    {uniqueAttributes.length > 0 ? (\r\n                      uniqueAttributes.map((attr, index) => (\r\n                        <div key={index} style={styles.checkboxContainer}>\r\n                          <input \r\n                            type=\"checkbox\" \r\n                            id={`attr-${index}`}\r\n                            checked={selectedAttributes.includes(attr)}\r\n                            onChange={() => handleAttributeToggle(attr)}\r\n                            style={styles.checkbox}\r\n                          />\r\n                          <label \r\n                            htmlFor={`attr-${index}`}\r\n                            style={styles.checkboxLabel}\r\n                          >\r\n                            {attr}\r\n                          </label>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <div style={styles.emptyState}>\r\n                        Aucun attribut supplémentaire\r\n                      </div>\r\n                    )}\r\n                  </td>\r\n                  <td style={styles.tableCell}>\r\n                    {uniqueMethods.length > 0 ? (\r\n                      uniqueMethods.map((method, index) => (\r\n                        <div key={index} style={styles.checkboxContainer}>\r\n                          <input \r\n                            type=\"checkbox\" \r\n                            id={`method-${index}`}\r\n                            checked={selectedMethods.includes(method)}\r\n                            onChange={() => handleMethodToggle(method)}\r\n                            style={styles.checkbox}\r\n                          />\r\n                          <label \r\n                            htmlFor={`method-${index}`}\r\n                            style={styles.checkboxLabel}\r\n                          >\r\n                            {method}\r\n                          </label>\r\n                        </div>\r\n                      ))\r\n                    ) : (\r\n                      <div style={styles.emptyState}>\r\n                        Aucune méthode supplémentaire\r\n                      </div>\r\n                    )}\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n          \r\n          <div style={styles.buttonContainer}>\r\n            <button \r\n              style={styles.modifyButton} \r\n              onClick={handleModifyClick}\r\n              onMouseEnter={(e) => handleModifyHover(e, true)}\r\n              onMouseLeave={(e) => handleModifyHover(e, false)}\r\n            >\r\n              <span>✏️</span>\r\n              Modifier l'entité\r\n            </button>\r\n            <button \r\n              style={styles.cancelButton} \r\n              onClick={onClose}\r\n              onMouseEnter={(e) => handleCancelHover(e, true)}\r\n              onMouseLeave={(e) => handleCancelHover(e, false)}\r\n            >\r\n              Annuler\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EntityPopup;"], "mappings": "AAAA;AACA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,oBAAoB,KAAQ,qBAAqB,CAC1D,MAAO,CAAAC,sBAAsB,KAAM,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAiB9D,KAAM,CAAAC,WAAuC,CAAGC,IAAA,EAW1C,IAX2C,CAC/CC,QAAQ,CACRC,UAAU,CACVC,QAAQ,CACRC,OAAO,CACPC,QAAQ,CACRC,gBAAgB,CAAG,EAAE,CACrBC,aAAa,CAAG,EAAE,CAClBC,mBAAmB,CAAG,EAAE,CACxBC,gBAAgB,CAAG,EAAE,CACrBC,kBACF,CAAC,CAAAV,IAAA,CACC;AACA,KAAM,CAACW,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGpB,KAAK,CAACqB,QAAQ,CAAW,EAAE,CAAC,CAChF,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGvB,KAAK,CAACqB,QAAQ,CAAW,EAAE,CAAC,CAE1E;AAEA;AACA,KAAM,CAAAG,gBAA2B,CAAGxB,KAAK,CAACyB,OAAO,CAAC,KAAO,CACvDC,IAAI,CAAEhB,UAAU,CAChBiB,UAAU,CAAE,CAAC,GAAGb,gBAAgB,CAAE,GAAGE,mBAAmB,CAAC,CACzDY,OAAO,CAAE,CAAC,GAAGb,aAAa,CAAE,GAAGE,gBAAgB,CACjD,CAAC,CAAC,CAAE,CAACP,UAAU,CAAEI,gBAAgB,CAAEE,mBAAmB,CAAED,aAAa,CAAEE,gBAAgB,CAAC,CAAC,CAEzF;AACA;AACA,KAAM,CAAAY,gBAAgB,CAAGf,gBAAgB,CAACgB,MAAM,CAACC,IAAI,EAAI,CACvD;AACA,KAAM,CAAAC,QAAQ,CAAGD,IAAI,CAACE,OAAO,CAAC,WAAW,CAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAEjF,MAAO,CAACpB,mBAAmB,CAACqB,IAAI,CAACC,aAAa,EAAI,CAChD;AACA,KAAM,CAAAC,iBAAiB,CAAGD,aAAa,CAACL,OAAO,CAAC,WAAW,CAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnG,MAAO,CAAAG,iBAAiB,GAAKP,QAAQ,CACvC,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,KAAM,CAAAQ,aAAa,CAAGzB,aAAa,CAACe,MAAM,CAACW,MAAM,EAAI,CACnD;AACA,KAAM,CAAAC,UAAU,CAAGD,MAAM,CAACR,OAAO,CAAC,WAAW,CAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAErF,MAAO,CAACnB,gBAAgB,CAACoB,IAAI,CAACM,eAAe,EAAI,CAC/C;AACA,KAAM,CAAAC,mBAAmB,CAAGD,eAAe,CAACV,OAAO,CAAC,WAAW,CAAE,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACvG,MAAO,CAAAQ,mBAAmB,GAAKF,UAAU,CAC3C,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAG,qBAAqB,CAAIC,SAAiB,EAAK,CACnD1B,qBAAqB,CAAC2B,IAAI,EACxBA,IAAI,CAACC,QAAQ,CAACF,SAAS,CAAC,CACpBC,IAAI,CAACjB,MAAM,CAACC,IAAI,EAAIA,IAAI,GAAKe,SAAS,CAAC,CACvC,CAAC,GAAGC,IAAI,CAAED,SAAS,CACzB,CAAC,CACH,CAAC,CAED,KAAM,CAAAG,kBAAkB,CAAIR,MAAc,EAAK,CAC7ClB,kBAAkB,CAACwB,IAAI,EACrBA,IAAI,CAACC,QAAQ,CAACP,MAAM,CAAC,CACjBM,IAAI,CAACjB,MAAM,CAACoB,CAAC,EAAIA,CAAC,GAAKT,MAAM,CAAC,CAC9B,CAAC,GAAGM,IAAI,CAAEN,MAAM,CACtB,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAAU,mBAAmB,CAAIC,YAAuB,EAAK,CACvD;AACA;AACA,KAAM,CAAAC,qBAAqB,CAAG,CAAC,GAAGlC,kBAAkB,CAAC,CACrD,KAAM,CAAAmC,kBAAkB,CAAG,CAAC,GAAGhC,eAAe,CAAC,CAE/C;AACA8B,YAAY,CAACzB,UAAU,CAAC4B,OAAO,CAACxB,IAAI,EAAI,CACtC,GAAI,CAACsB,qBAAqB,CAACL,QAAQ,CAACjB,IAAI,CAAC,CAAE,CACzCsB,qBAAqB,CAACG,IAAI,CAACzB,IAAI,CAAC,CAClC,CACF,CAAC,CAAC,CAEF;AACAqB,YAAY,CAACxB,OAAO,CAAC2B,OAAO,CAACd,MAAM,EAAI,CACrC,GAAI,CAACa,kBAAkB,CAACN,QAAQ,CAACP,MAAM,CAAC,CAAE,CACxCa,kBAAkB,CAACE,IAAI,CAACf,MAAM,CAAC,CACjC,CACF,CAAC,CAAC,CAEF;AACArB,qBAAqB,CAACiC,qBAAqB,CAAC,CAC5C9B,kBAAkB,CAAC+B,kBAAkB,CAAC,CACxC,CAAC,CAID;AACA,KAAM,CAAAG,iBAAiB,CAAGA,CAAA,GAAM,CAC9B5C,QAAQ,CAACM,kBAAkB,CAAEG,eAAe,CAAC,CAC/C,CAAC,CAED;AACA,KAAM,CAAAoC,gBAAgB,CAAIC,CAAmB,EAAK,CAChD,GAAIA,CAAC,CAACC,MAAM,GAAKD,CAAC,CAACE,aAAa,CAAE,CAC/BF,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,CAAGtD,QAAQ,CAAG,SAAS,CAAG,SAAS,CAC1F,CACF,CAAC,CAED,KAAM,CAAAuD,gBAAgB,CAAIL,CAAmB,EAAK,CAChD,GAAIA,CAAC,CAACC,MAAM,GAAKD,CAAC,CAACE,aAAa,CAAE,CAC/BF,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,CAAGtD,QAAQ,CAAG,SAAS,CAAG,SAAS,CAC1F,CACF,CAAC,CAED,KAAM,CAAAwD,iBAAiB,CAAGA,CAACN,CAAmB,CAAEO,OAAgB,GAAK,CAClEP,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,CAAGG,OAAO,CAAG,SAAS,CAAG,SAAS,CACtFP,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACK,SAAS,CAAGD,OAAO,CAAG,kBAAkB,CAAG,eAAe,CAClG,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAGA,CAACT,CAAmB,CAAEO,OAAgB,GAAK,CAClEP,CAAC,CAACC,MAAM,CAAuBE,KAAK,CAACC,eAAe,CAAGG,OAAO,CAC1DzD,QAAQ,CAAG,SAAS,CAAG,SAAS,CACjC,aAAa,CACnB,CAAC,CAED;AACA,KAAM,CAAA4D,kBAAkB,CAAIV,CAAmB,EAAK,CAClD;AACA,GAAIA,CAAC,CAACC,MAAM,GAAKD,CAAC,CAACE,aAAa,CAAE,CAChCjD,OAAO,CAAC,CAAC,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAA0D,gBAAgB,CAAIX,CAAmB,EAAK,CAChDA,CAAC,CAACY,eAAe,CAAC,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAAC,MAAM,CAAGvE,oBAAoB,CAACQ,QAAQ,CAAC,CAE7C,mBACEL,IAAA,QAAK0D,KAAK,CAAEU,MAAM,CAACC,OAAQ,CAACC,OAAO,CAAEL,kBAAmB,CAAAM,QAAA,cACtDrE,KAAA,QAAKwD,KAAK,CAAEU,MAAM,CAACI,KAAM,CAACF,OAAO,CAAEJ,gBAAiB,CAAAK,QAAA,eAClDrE,KAAA,QAAKwD,KAAK,CAAEU,MAAM,CAACK,MAAO,CAAAF,QAAA,eACxBrE,KAAA,QAAAqE,QAAA,eACEvE,IAAA,OAAI0D,KAAK,CAAEU,MAAM,CAACM,KAAM,CAAAH,QAAA,CAAEjE,UAAU,CAAK,CAAC,cAC1CN,IAAA,QAAK0D,KAAK,CAAEU,MAAM,CAACO,QAAS,CAAAJ,QAAA,CAAC,gEAA2D,CAAK,CAAC,EAC3F,CAAC,cACNvE,IAAA,WACE0D,KAAK,CAAEU,MAAM,CAACQ,WAAY,CAC1BN,OAAO,CAAE9D,OAAQ,CACjBqE,YAAY,CAAEvB,gBAAiB,CAC/BwB,YAAY,CAAElB,gBAAiB,CAAAW,QAAA,CAChC,MAED,CAAQ,CAAC,EACN,CAAC,cAENrE,KAAA,QAAKwD,KAAK,CAAEU,MAAM,CAACW,OAAQ,CAAAR,QAAA,eAEzBvE,IAAA,CAACF,sBAAsB,EACrBO,QAAQ,CAAEA,QAAS,CACnB2E,eAAe,CAAE1E,UAAW,CAC5Bc,gBAAgB,CAAEA,gBAAiB,CACnC6D,QAAQ,CAAElC,mBAAoB,CAC9BjC,kBAAkB,CAAEA,kBAAmB,CACxC,CAAC,cACFd,IAAA,QAAK0D,KAAK,CAAEU,MAAM,CAACc,cAAe,CAAAX,QAAA,cAChCrE,KAAA,UAAOwD,KAAK,CAAEU,MAAM,CAACe,KAAM,CAAAZ,QAAA,eACzBvE,IAAA,UAAO0D,KAAK,CAAEU,MAAM,CAACgB,WAAY,CAAAb,QAAA,cAC/BrE,KAAA,OAAAqE,QAAA,eACEvE,IAAA,OAAI0D,KAAK,CAAEU,MAAM,CAACiB,eAAgB,CAAAd,QAAA,CAAC,WAAS,CAAI,CAAC,cACjDvE,IAAA,OAAI0D,KAAK,CAAEU,MAAM,CAACiB,eAAgB,CAAAd,QAAA,CAAC,aAAQ,CAAI,CAAC,EAC9C,CAAC,CACA,CAAC,cACRvE,IAAA,UAAO0D,KAAK,CAAEU,MAAM,CAACkB,SAAU,CAAAf,QAAA,cAC7BrE,KAAA,OAAIwD,KAAK,CAAEU,MAAM,CAACmB,QAAS,CAAAhB,QAAA,eACzBvE,IAAA,OAAI0D,KAAK,CAAEU,MAAM,CAACoB,SAAU,CAAAjB,QAAA,CACzB9C,gBAAgB,CAACgE,MAAM,CAAG,CAAC,CAC1BhE,gBAAgB,CAACiE,GAAG,CAAC,CAAC/D,IAAI,CAAEgE,KAAK,gBAC/BzF,KAAA,QAAiBwD,KAAK,CAAEU,MAAM,CAACwB,iBAAkB,CAAArB,QAAA,eAC/CvE,IAAA,UACE6F,IAAI,CAAC,UAAU,CACfC,EAAE,CAAE,QAAQH,KAAK,EAAG,CACpBI,OAAO,CAAEhF,kBAAkB,CAAC6B,QAAQ,CAACjB,IAAI,CAAE,CAC3CqE,QAAQ,CAAEA,CAAA,GAAMvD,qBAAqB,CAACd,IAAI,CAAE,CAC5C+B,KAAK,CAAEU,MAAM,CAAC6B,QAAS,CACxB,CAAC,cACFjG,IAAA,UACEkG,OAAO,CAAE,QAAQP,KAAK,EAAG,CACzBjC,KAAK,CAAEU,MAAM,CAAC+B,aAAc,CAAA5B,QAAA,CAE3B5C,IAAI,CACA,CAAC,GAbAgE,KAcL,CACN,CAAC,cAEF3F,IAAA,QAAK0D,KAAK,CAAEU,MAAM,CAACgC,UAAW,CAAA7B,QAAA,CAAC,kCAE/B,CAAK,CACN,CACC,CAAC,cACLvE,IAAA,OAAI0D,KAAK,CAAEU,MAAM,CAACoB,SAAU,CAAAjB,QAAA,CACzBnC,aAAa,CAACqD,MAAM,CAAG,CAAC,CACvBrD,aAAa,CAACsD,GAAG,CAAC,CAACrD,MAAM,CAAEsD,KAAK,gBAC9BzF,KAAA,QAAiBwD,KAAK,CAAEU,MAAM,CAACwB,iBAAkB,CAAArB,QAAA,eAC/CvE,IAAA,UACE6F,IAAI,CAAC,UAAU,CACfC,EAAE,CAAE,UAAUH,KAAK,EAAG,CACtBI,OAAO,CAAE7E,eAAe,CAAC0B,QAAQ,CAACP,MAAM,CAAE,CAC1C2D,QAAQ,CAAEA,CAAA,GAAMnD,kBAAkB,CAACR,MAAM,CAAE,CAC3CqB,KAAK,CAAEU,MAAM,CAAC6B,QAAS,CACxB,CAAC,cACFjG,IAAA,UACEkG,OAAO,CAAE,UAAUP,KAAK,EAAG,CAC3BjC,KAAK,CAAEU,MAAM,CAAC+B,aAAc,CAAA5B,QAAA,CAE3BlC,MAAM,CACF,CAAC,GAbAsD,KAcL,CACN,CAAC,cAEF3F,IAAA,QAAK0D,KAAK,CAAEU,MAAM,CAACgC,UAAW,CAAA7B,QAAA,CAAC,qCAE/B,CAAK,CACN,CACC,CAAC,EACH,CAAC,CACA,CAAC,EACH,CAAC,CACL,CAAC,cAENrE,KAAA,QAAKwD,KAAK,CAAEU,MAAM,CAACiC,eAAgB,CAAA9B,QAAA,eACjCrE,KAAA,WACEwD,KAAK,CAAEU,MAAM,CAACkC,YAAa,CAC3BhC,OAAO,CAAEjB,iBAAkB,CAC3BwB,YAAY,CAAGtB,CAAC,EAAKM,iBAAiB,CAACN,CAAC,CAAE,IAAI,CAAE,CAChDuB,YAAY,CAAGvB,CAAC,EAAKM,iBAAiB,CAACN,CAAC,CAAE,KAAK,CAAE,CAAAgB,QAAA,eAEjDvE,IAAA,SAAAuE,QAAA,CAAM,cAAE,CAAM,CAAC,uBAEjB,EAAQ,CAAC,cACTvE,IAAA,WACE0D,KAAK,CAAEU,MAAM,CAACmC,YAAa,CAC3BjC,OAAO,CAAE9D,OAAQ,CACjBqE,YAAY,CAAGtB,CAAC,EAAKS,iBAAiB,CAACT,CAAC,CAAE,IAAI,CAAE,CAChDuB,YAAY,CAAGvB,CAAC,EAAKS,iBAAiB,CAACT,CAAC,CAAE,KAAK,CAAE,CAAAgB,QAAA,CAClD,SAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CAGH,CAAC,CAEV,CAAC,CAED,cAAe,CAAApE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}