=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 13 boîtes détectées
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.92
  [OK] Classe acceptée avec confiance 0.92 >= 0.25
  Détection: class, confiance: 0.90
  [OK] Classe acceptée avec confiance 0.90 >= 0.25
  Détection: class, confiance: 0.89
  [OK] Classe acceptée avec confiance 0.89 >= 0.25
  Détection: class, confiance: 0.88
  [OK] Classe acceptée avec confiance 0.88 >= 0.25
  Détection: arrow, confiance: 0.88
  [OK] Flèche acceptée avec confiance 0.88 >= 0.4
  Détection: class, confiance: 0.83
  [OK] Classe acceptée avec confiance 0.83 >= 0.25
  Détection: arrow, confiance: 0.75
  [OK] Flèche acceptée avec confiance 0.75 >= 0.4
  Détection: arrow, confiance: 0.64
  [OK] Flèche acceptée avec confiance 0.64 >= 0.4
  Détection: arrow, confiance: 0.56
  [OK] Flèche acceptée avec confiance 0.56 >= 0.4
  Détection: arrow, confiance: 0.54
  [OK] Flèche acceptée avec confiance 0.54 >= 0.4
  Détection: arrow, confiance: 0.29
  [!] Confiance trop basse pour arrow: 0.29 < 0.4

Exécution du modèle 2 (ONNX)...
Passage de l'image brute (numpy array) au modèle ONNX...
Résultats modèle 2: 1 éléments traités
  Nombre de détections: 9
  Classe détectée: composition
  Classe détectée: one-way-association
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: one-way-association
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
Modèle 2: 9 boîtes détectées
  Box 0: coords=163,153,196,169 class_idx=5 conf=0.76
  Détection: composition, confiance: 0.76
  Box 1: coords=186,524,211,544 class_idx=4 conf=0.55
  Détection: one-way-association, confiance: 0.55
  Box 2: coords=615,366,628,379 class_idx=3 conf=0.55
  Détection: endpoin, confiance: 0.55
  Box 3: coords=496,309,513,324 class_idx=3 conf=0.51
  Détection: endpoin, confiance: 0.51
  Box 4: coords=821,493,844,511 class_idx=4 conf=0.50
  Détection: one-way-association, confiance: 0.50
  Box 5: coords=615,494,628,506 class_idx=3 conf=0.49
  Détection: endpoin, confiance: 0.49
  Box 6: coords=496,179,509,192 class_idx=3 conf=0.41
  Détection: endpoin, confiance: 0.41
  Box 7: coords=381,82,393,94 class_idx=3 conf=0.36
  Détection: endpoin, confiance: 0.36
  Box 8: coords=384,244,397,257 class_idx=3 conf=0.34
  Détection: endpoin, confiance: 0.34

Traitement des relations entre classes...

Résumé des détections:
  Modèle 1: {'class': 7, 'arrow': 5}
  Modèle 2: {'composition': 1, 'one-way-association': 2, 'endpoin': 6}

=== FIN DÉTECTION ===
