=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 30 boîtes détectées
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.91
  [OK] Classe acceptée avec confiance 0.91 >= 0.25
  Détection: arrow, confiance: 0.91
  [OK] Flèche acceptée avec confiance 0.91 >= 0.4
  Détection: arrow, confiance: 0.90
  [OK] Flèche acceptée avec confiance 0.90 >= 0.4
  Détection: arrow, confiance: 0.90
  [OK] Flèche acceptée avec confiance 0.90 >= 0.4
  Détection: arrow, confiance: 0.88
  [OK] Flèche acceptée avec confiance 0.88 >= 0.4
  Détection: arrow, confiance: 0.86
  [OK] Flèche acceptée avec confiance 0.86 >= 0.4
  Détection: arrow, confiance: 0.79
  [OK] Flèche acceptée avec confiance 0.79 >= 0.4
  Détection: arrow, confiance: 0.78
  [OK] Flèche acceptée avec confiance 0.78 >= 0.4
  Détection: arrow, confiance: 0.75
  [OK] Flèche acceptée avec confiance 0.75 >= 0.4
  Détection: arrow, confiance: 0.74
  [OK] Flèche acceptée avec confiance 0.74 >= 0.4
  Détection: arrow, confiance: 0.71
  [OK] Flèche acceptée avec confiance 0.71 >= 0.4
  Détection: arrow, confiance: 0.70
  [OK] Flèche acceptée avec confiance 0.70 >= 0.4
  Détection: arrow, confiance: 0.68
  [OK] Flèche acceptée avec confiance 0.68 >= 0.4
  Détection: arrow, confiance: 0.66
  [OK] Flèche acceptée avec confiance 0.66 >= 0.4
  Détection: arrow, confiance: 0.64
  [OK] Flèche acceptée avec confiance 0.64 >= 0.4
  Détection: arrow, confiance: 0.63
  [OK] Flèche acceptée avec confiance 0.63 >= 0.4
  Détection: arrow, confiance: 0.57
  [OK] Flèche acceptée avec confiance 0.57 >= 0.4
  Détection: arrow, confiance: 0.55
  [OK] Flèche acceptée avec confiance 0.55 >= 0.4
ERREUR: 2
STACK TRACE: Traceback (most recent call last):
  File "C:\Users\<USER>\FixTorchUMLDGM\uml-detector\BackEnd\server.py", line 278, in detect_objects
    class_name = MODEL1_NAMES[class_index]
KeyError: 2


Mise à jour du texte: class 1:

NOM_CLASSE: Car

ATTRIBUTS: 


Model: String

Color: String
brand: String
type: String
MÉT...

Mise à jour du texte: class 1:

NOM_CLASSE: Car

ATTRIBUTS: 


Model: String

Color: String
brand: String
type: String
MÉT...

Mise à jour du texte: class 1:

NOM_CLASSE: Car

ATTRIBUTS: 


Model: String

Color: String
brand: String
type: String
MÉT...

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 

MÉTHODES:








void ajouter()
void supprimer()
void mo...

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 


-idClient: int
-nomClient: String
-prenomClient: String
...

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 


-idClient: int
-nomClient: String
-prenomClient: String
...

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 



-idClient: int
-nomClient: String
-prenomClient: String...

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 




-idClient: int
-nomClient: String
-prenomClient: Strin...

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 


-idClient: int
-nomClient: String
-prenomClient: String
...

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 


-idClient: int
-nomClient: String
-prenomClient: String
...

Mise à jour du texte: class 1:

NOM_CLASSE: Implementon

ATTRIBUTS: 

MÉTHODES:



class 2:

NOM_CLASSE: Concrete Implemen...

Mise à jour du texte: class 1:

NOM_CLASSE: OrderItem

ATTRIBUTS: 

- productId: int

- product: String

- price: int

- q...

Mise à jour du texte: class 1:

NOM_CLASSE: Commande

ATTRIBUTS: 

- numBonCommande: int

- dateCommande: Date

- dateRegl...

Mise à jour du texte: class 1:

NOM_CLASSE: Account

ATTRIBUTS: 


accountNumber : StringValue

clientAddress : StringValu...

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 


-idClient: int
-nomClient: String
-prenomClient: String
...

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 


-idClient: int
-nomClient: String
-prenomClient: String
...

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 


-prenomClient: String
MÉTHODES:







----- RELATIONS D...

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 



-prenomClient: String
-idClient: int
-nomClient: String...
