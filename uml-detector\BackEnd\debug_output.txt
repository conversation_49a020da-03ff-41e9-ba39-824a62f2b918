=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 16 boîtes détectées
  Détection: class, confiance: 0.95
  [OK] Classe acceptée avec confiance 0.95 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.91
  [OK] Classe acceptée avec confiance 0.91 >= 0.25
  Détection: arrow, confiance: 0.57
  [OK] Flèche acceptée avec confiance 0.57 >= 0.4
  Détection: arrow, confiance: 0.56
  [OK] Flèche acceptée avec confiance 0.56 >= 0.4
  Détection: arrow, confiance: 0.52
  [OK] Flèche acceptée avec confiance 0.52 >= 0.4
  Détection: arrow, confiance: 0.48
  [OK] Flèche acceptée avec confiance 0.48 >= 0.4
  Détection: arrow, confiance: 0.48
  [OK] Flèche acceptée avec confiance 0.48 >= 0.4
  Détection: arrow, confiance: 0.42
  [OK] Flèche acceptée avec confiance 0.42 >= 0.4
  Détection: arrow, confiance: 0.34
  [!] Confiance trop basse pour arrow: 0.34 < 0.4

Exécution du modèle 2 (ONNX)...
Passage de l'image brute (numpy array) au modèle ONNX...
Résultats modèle 2: 1 éléments traités
  Nombre de détections: 8
  Classe détectée: generalization
  Classe détectée: one-way-association
  Classe détectée: generalization
  Classe détectée: one-way-association
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
Modèle 2: 8 boîtes détectées
  Box 0: coords=1083,325,1100,342 class_idx=2 conf=0.47
  Détection: generalization, confiance: 0.47
  Box 1: coords=675,270,690,284 class_idx=4 conf=0.36
  Détection: one-way-association, confiance: 0.36
  Box 2: coords=156,127,171,141 class_idx=2 conf=0.35
  Détection: generalization, confiance: 0.35
  Box 3: coords=965,312,979,326 class_idx=4 conf=0.33
  Détection: one-way-association, confiance: 0.33
  Box 4: coords=966,214,979,227 class_idx=3 conf=0.31
  Détection: endpoin, confiance: 0.31
  Box 5: coords=571,466,582,477 class_idx=3 conf=0.31
  Détection: endpoin, confiance: 0.31
  Box 6: coords=424,74,437,87 class_idx=3 conf=0.29
  Détection: endpoin, confiance: 0.29
  Box 7: coords=270,129,284,142 class_idx=3 conf=0.25
  Détection: endpoin, confiance: 0.25

Traitement des relations entre classes...

Résumé des détections:
  Modèle 1: {'class': 9, 'arrow': 6}
  Modèle 2: {'generalization': 2, 'one-way-association': 2, 'endpoin': 4}

=== FIN DÉTECTION ===

Mise à jour du texte: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 


uid : Integer
email : varchar
password : varchar
nom : v...
