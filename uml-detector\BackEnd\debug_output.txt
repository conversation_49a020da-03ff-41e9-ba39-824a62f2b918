=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 1 boîtes détectées
  Détection: class, confiance: 0.91
  [OK] Classe acceptée avec confiance 0.91 >= 0.25

Exécution du modèle 2 (ONNX)...
Passage de l'image brute (numpy array) au modèle ONNX...
Résultats modèle 2: 1 éléments traités
  Nombre de détections: 0
Modèle 2: 0 boîtes détectées

Traitement des relations entre classes...

Résumé des détections:
  Modèle 1: {'class': 1}
  Modèle 2: {}

=== FIN DÉTECTION ===
