{"version": 3, "file": "static/js/107.8daa6325.chunk.js", "mappings": "6JAwBA,QAbA,SAAoBA,GAClB,OAAO,SAASC,EAAYC,EAAWC,GACrC,IAAIC,EAAWC,OAAOJ,GACtB,KAAKK,EAAAA,EAAAA,GAAYL,GAAa,CAC5B,IAAIM,GAAWC,EAAAA,EAAAA,GAAaN,EAAW,GACvCD,GAAaQ,EAAAA,EAAAA,GAAKR,GAClBC,EAAY,SAASQ,GAAO,OAAOH,EAASH,EAASM,GAAMA,EAAKN,EAAW,CAC7E,CACA,IAAIO,EAAQX,EAAcC,EAAYC,EAAWC,GACjD,OAAOQ,GAAS,EAAIP,EAASG,EAAWN,EAAWU,GAASA,QAASC,CACvE,CACF,E,uBCjBIC,EAAYC,KAAKC,ICoCrB,QAFWC,GDGX,SAAmBC,EAAOf,EAAWC,GACnC,IAAIe,EAAkB,MAATD,EAAgB,EAAIA,EAAMC,OACvC,IAAKA,EACH,OAAQ,EAEV,IAAIP,EAAqB,MAAbR,EAAoB,GAAIgB,EAAAA,EAAAA,GAAUhB,GAI9C,OAHIQ,EAAQ,IACVA,EAAQE,EAAUK,EAASP,EAAO,KAE7BS,EAAAA,EAAAA,GAAcH,GAAOT,EAAAA,EAAAA,GAAaN,EAAW,GAAIS,EAC1D,G,oHExBA,QANA,SAAaM,GACX,OAAQA,GAASA,EAAMC,QACnBG,EAAAA,EAAAA,GAAaJ,EAAOK,EAAAA,EAAUC,EAAAA,QAC9BX,CACN,C,0DCLA,QAVA,SAAiBX,EAAYM,GAC3B,IAAII,GAAS,EACTa,GAASlB,EAAAA,EAAAA,GAAYL,GAAcwB,MAAMxB,EAAWiB,QAAU,GAKlE,OAHAQ,EAAAA,EAAAA,GAASzB,GAAY,SAAS0B,EAAOjB,EAAKT,GACxCuB,IAASb,GAASJ,EAASoB,EAAOjB,EAAKT,EACzC,IACOuB,CACT,C,gDCEA,QALA,SAAiBP,GAEf,OADsB,MAATA,EAAgB,EAAIA,EAAMC,SACvBU,EAAAA,EAAAA,GAAYX,EAAO,GAAK,EAC1C,C,kECUA,QALA,SAAkBU,GAChB,MAAuB,iBAATA,KACVE,EAAAA,EAAAA,GAAQF,KAAUG,EAAAA,EAAAA,GAAaH,IArBrB,oBAqB+BI,EAAAA,EAAAA,GAAWJ,EAC1D,C,gDCIA,QAnBA,SAAsBV,EAAOV,EAAUyB,GAIrC,IAHA,IAAIrB,GAAS,EACTO,EAASD,EAAMC,SAEVP,EAAQO,GAAQ,CACvB,IAAIS,EAAQV,EAAMN,GACdsB,EAAU1B,EAASoB,GAEvB,GAAe,MAAXM,SAAiCrB,IAAbsB,EACfD,IAAYA,KAAYE,EAAAA,EAAAA,GAASF,GAClCD,EAAWC,EAASC,IAE1B,IAAIA,EAAWD,EACXT,EAASG,CAEjB,CACA,OAAOH,CACT,C,oJCJA,SAJYY,EAAAA,EAAAA,IAAS,SAASC,GAC5B,OAAOC,EAAAA,EAAAA,IAASV,EAAAA,EAAAA,GAAYS,EAAQ,EAAGE,EAAAA,GAAmB,GAC5D,I,wBCpBIC,EAAa,KAsBV,MAAMC,EACXC,WAAAA,GAAuB,IAAXC,EAAIC,UAAA1B,OAAA,QAAAN,IAAAgC,UAAA,GAAAA,UAAA,GAAG,CAAC,EAClBC,KAAKC,aAAczC,OAAO0C,UAAUC,eAAeC,KAAKN,EAAM,aAC1DA,EAAKO,SAETL,KAAKM,gBAAgB9C,OAAO0C,UAAUC,eAAeC,KAAKN,EAAM,eAC5DA,EAAKS,WAETP,KAAKQ,cAAchD,OAAO0C,UAAUC,eAAeC,KAAKN,EAAM,aAC1DA,EAAKW,SAITT,KAAKU,YAAS3C,EAGdiC,KAAKW,oBAAsBC,EAAAA,OAAW7C,GAGtCiC,KAAKa,oBAAsBD,EAAAA,OAAW7C,GAGtCiC,KAAKc,OAAS,CAAC,EAEXd,KAAKQ,cAEPR,KAAKe,QAAU,CAAC,EAGhBf,KAAKgB,UAAY,CAAC,EAClBhB,KAAKgB,UAAUrB,GAAc,CAAC,GAIhCK,KAAKiB,IAAM,CAAC,EAGZjB,KAAKkB,OAAS,CAAC,EAGflB,KAAKmB,KAAO,CAAC,EAGbnB,KAAKoB,MAAQ,CAAC,EAGdpB,KAAKqB,UAAY,CAAC,EAGlBrB,KAAKsB,YAAc,CAAC,CACtB,CAEAC,UAAAA,GACE,OAAOvB,KAAKC,WACd,CACAuB,YAAAA,GACE,OAAOxB,KAAKM,aACd,CACAmB,UAAAA,GACE,OAAOzB,KAAKQ,WACd,CACAkB,QAAAA,CAASC,GAEP,OADA3B,KAAKU,OAASiB,EACP3B,IACT,CACA4B,KAAAA,GACE,OAAO5B,KAAKU,MACd,CAEAmB,mBAAAA,CAAoBC,GAKlB,OAJKlB,EAAAA,EAAakB,KAChBA,EAAalB,EAAAA,EAAWkB,IAE1B9B,KAAKW,oBAAsBmB,EACpB9B,IACT,CACA+B,SAAAA,GACE,OAAO/B,KAAKgC,UACd,CACAC,KAAAA,GACE,OAAOrB,EAAAA,EAAOZ,KAAKc,OACrB,CACAoB,OAAAA,GACE,IAAIC,EAAOnC,KACX,OAAOY,EAAAA,EAASZ,KAAKiC,SAAS,SAAUG,GACtC,OAAOxB,EAAAA,EAAUuB,EAAKlB,IAAImB,GAC5B,GACF,CACAC,KAAAA,GACE,IAAIF,EAAOnC,KACX,OAAOY,EAAAA,EAASZ,KAAKiC,SAAS,SAAUG,GACtC,OAAOxB,EAAAA,EAAUuB,EAAKhB,KAAKiB,GAC7B,GACF,CACAE,QAAAA,CAASC,EAAIzD,GACX,IAAI0D,EAAOzC,UACPoC,EAAOnC,KAQX,OAPAY,EAAAA,EAAO2B,GAAI,SAAUH,GACfI,EAAKnE,OAAS,EAChB8D,EAAKM,QAAQL,EAAGtD,GAEhBqD,EAAKM,QAAQL,EAEjB,IACOpC,IACT,CACAyC,OAAAA,CAAQL,EAAGtD,GACT,OAAItB,OAAO0C,UAAUC,eAAeC,KAAKJ,KAAKc,OAAQsB,IAChDrC,UAAU1B,OAAS,IACrB2B,KAAKc,OAAOsB,GAAKtD,GAEZkB,OAITA,KAAKc,OAAOsB,GAAKrC,UAAU1B,OAAS,EAAIS,EAAQkB,KAAKW,oBAAoByB,GACrEpC,KAAKQ,cACPR,KAAKe,QAAQqB,GAAKzC,EAClBK,KAAKgB,UAAUoB,GAAK,CAAC,EACrBpC,KAAKgB,UAAUrB,GAAYyC,IAAK,GAElCpC,KAAKiB,IAAImB,GAAK,CAAC,EACfpC,KAAKkB,OAAOkB,GAAK,CAAC,EAClBpC,KAAKmB,KAAKiB,GAAK,CAAC,EAChBpC,KAAKoB,MAAMgB,GAAK,CAAC,IACfpC,KAAKgC,WACAhC,KACT,CACA0C,IAAAA,CAAKN,GACH,OAAOpC,KAAKc,OAAOsB,EACrB,CACAO,OAAAA,CAAQP,GACN,OAAO5E,OAAO0C,UAAUC,eAAeC,KAAKJ,KAAKc,OAAQsB,EAC3D,CACAQ,UAAAA,CAAWR,GACT,GAAI5E,OAAO0C,UAAUC,eAAeC,KAAKJ,KAAKc,OAAQsB,GAAI,CACxD,IAAIS,EAAcC,GAAM9C,KAAK6C,WAAW7C,KAAKqB,UAAUyB,WAChD9C,KAAKc,OAAOsB,GACfpC,KAAKQ,cACPR,KAAK+C,4BAA4BX,UAC1BpC,KAAKe,QAAQqB,GACpBxB,EAAAA,EAAOZ,KAAKgD,SAASZ,IAAKa,IACxBjD,KAAKkD,UAAUD,EAAM,WAEhBjD,KAAKgB,UAAUoB,IAExBxB,EAAAA,EAAOA,EAAAA,EAAOZ,KAAKiB,IAAImB,IAAKS,UACrB7C,KAAKiB,IAAImB,UACTpC,KAAKkB,OAAOkB,GACnBxB,EAAAA,EAAOA,EAAAA,EAAOZ,KAAKmB,KAAKiB,IAAKS,UACtB7C,KAAKmB,KAAKiB,UACVpC,KAAKoB,MAAMgB,KAChBpC,KAAKgC,UACT,CACA,OAAOhC,IACT,CACAkD,SAAAA,CAAUd,EAAGe,GACX,IAAKnD,KAAKQ,YACR,MAAM,IAAI4C,MAAM,6CAGlB,GAAIxC,EAAAA,EAAcuC,GAChBA,EAASxD,MACJ,CAGL,IAAK,IAAI0D,EADTF,GAAU,IACmBvC,EAAAA,EAAcyC,GAAWA,EAAWrD,KAAKmD,OAAOE,GAC3E,GAAIA,IAAajB,EACf,MAAM,IAAIgB,MAAM,WAAaD,EAAS,iBAAmBf,EAAI,yBAIjEpC,KAAKyC,QAAQU,EACf,CAMA,OAJAnD,KAAKyC,QAAQL,GACbpC,KAAK+C,4BAA4BX,GACjCpC,KAAKe,QAAQqB,GAAKe,EAClBnD,KAAKgB,UAAUmC,GAAQf,IAAK,EACrBpC,IACT,CACA+C,2BAAAA,CAA4BX,UACnBpC,KAAKgB,UAAUhB,KAAKe,QAAQqB,IAAIA,EACzC,CACAe,MAAAA,CAAOf,GACL,GAAIpC,KAAKQ,YAAa,CACpB,IAAI2C,EAASnD,KAAKe,QAAQqB,GAC1B,GAAIe,IAAWxD,EACb,OAAOwD,CAEX,CACF,CACAH,QAAAA,CAASZ,GAKP,GAJIxB,EAAAA,EAAcwB,KAChBA,EAAIzC,GAGFK,KAAKQ,YAAa,CACpB,IAAIwC,EAAWhD,KAAKgB,UAAUoB,GAC9B,GAAIY,EACF,OAAOpC,EAAAA,EAAOoC,EAElB,KAAO,IAAIZ,IAAMzC,EACf,OAAOK,KAAKiC,QACP,GAAIjC,KAAK2C,QAAQP,GACtB,MAAO,EACT,CACF,CACAkB,YAAAA,CAAalB,GACX,IAAImB,EAASvD,KAAKkB,OAAOkB,GACzB,GAAImB,EACF,OAAO3C,EAAAA,EAAO2C,EAElB,CACAC,UAAAA,CAAWpB,GACT,IAAIqB,EAAQzD,KAAKoB,MAAMgB,GACvB,GAAIqB,EACF,OAAO7C,EAAAA,EAAO6C,EAElB,CACAC,SAAAA,CAAUtB,GACR,IAAIuB,EAAQ3D,KAAKsD,aAAalB,GAC9B,GAAIuB,EACF,OAAO/C,EAAQ+C,EAAO3D,KAAKwD,WAAWpB,GAE1C,CACAwB,MAAAA,CAAOxB,GAOL,OAA4B,KALxBpC,KAAKuB,aACKvB,KAAKwD,WAAWpB,GAEhBpC,KAAK0D,UAAUtB,IAEZ/D,MACnB,CACAwF,WAAAA,CAAYC,GAEV,IAAIC,EAAO,IAAI/D,KAAKH,YAAY,CAC9BQ,SAAUL,KAAKC,YACfM,WAAYP,KAAKM,cACjBG,SAAUT,KAAKQ,cAGjBuD,EAAKrC,SAAS1B,KAAK4B,SAEnB,IAAIO,EAAOnC,KACXY,EAAAA,EAAOZ,KAAKc,QAAQ,SAAUhC,EAAOsD,GAC/B0B,EAAO1B,IACT2B,EAAKtB,QAAQL,EAAGtD,EAEpB,IAEA8B,EAAAA,EAAOZ,KAAKqB,WAAW,SAAUyB,GAE3BiB,EAAKpB,QAAQG,EAAEV,IAAM2B,EAAKpB,QAAQG,EAAEkB,IACtCD,EAAKE,QAAQnB,EAAGX,EAAK+B,KAAKpB,GAE9B,IAEA,IAAIqB,EAAU,CAAC,EACf,SAASC,EAAWhC,GAClB,IAAIe,EAAShB,EAAKgB,OAAOf,GACzB,YAAerE,IAAXoF,GAAwBY,EAAKpB,QAAQQ,IACvCgB,EAAQ/B,GAAKe,EACNA,GACEA,KAAUgB,EACZA,EAAQhB,GAERiB,EAAWjB,EAEtB,CAQA,OANInD,KAAKQ,aACPI,EAAAA,EAAOmD,EAAK9B,SAAS,SAAUG,GAC7B2B,EAAKb,UAAUd,EAAGgC,EAAWhC,GAC/B,IAGK2B,CACT,CAEAM,mBAAAA,CAAoBvC,GAKlB,OAJKlB,EAAAA,EAAakB,KAChBA,EAAalB,EAAAA,EAAWkB,IAE1B9B,KAAKa,oBAAsBiB,EACpB9B,IACT,CACAsE,SAAAA,GACE,OAAOtE,KAAKuE,UACd,CACAC,KAAAA,GACE,OAAO5D,EAAAA,EAASZ,KAAKqB,UACvB,CACAoD,OAAAA,CAAQlC,EAAIzD,GACV,IAAIqD,EAAOnC,KACPwC,EAAOzC,UASX,OARAa,EAAAA,EAAS2B,GAAI,SAAUH,EAAG4B,GAMxB,OALIxB,EAAKnE,OAAS,EAChB8D,EAAK8B,QAAQ7B,EAAG4B,EAAGlF,GAEnBqD,EAAK8B,QAAQ7B,EAAG4B,GAEXA,CACT,IACOhE,IACT,CAKAiE,OAAAA,GACE,IAAI7B,EAAG4B,EAAGU,EAAM5F,EACZ6F,GAAiB,EACjBC,EAAO7E,UAAU,GAED,kBAAT6E,GAA8B,OAATA,GAAiB,MAAOA,GACtDxC,EAAIwC,EAAKxC,EACT4B,EAAIY,EAAKZ,EACTU,EAAOE,EAAKF,KACa,IAArB3E,UAAU1B,SACZS,EAAQiB,UAAU,GAClB4E,GAAiB,KAGnBvC,EAAIwC,EACJZ,EAAIjE,UAAU,GACd2E,EAAO3E,UAAU,GACbA,UAAU1B,OAAS,IACrBS,EAAQiB,UAAU,GAClB4E,GAAiB,IAIrBvC,EAAI,GAAKA,EACT4B,EAAI,GAAKA,EACJpD,EAAAA,EAAc8D,KACjBA,EAAO,GAAKA,GAGd,IAAI5B,EAAI+B,EAAa7E,KAAKC,YAAamC,EAAG4B,EAAGU,GAC7C,GAAIlH,OAAO0C,UAAUC,eAAeC,KAAKJ,KAAKsB,YAAawB,GAIzD,OAHI6B,IACF3E,KAAKsB,YAAYwB,GAAKhE,GAEjBkB,KAGT,IAAKY,EAAAA,EAAc8D,KAAU1E,KAAKM,cAChC,MAAM,IAAI8C,MAAM,qDAKlBpD,KAAKyC,QAAQL,GACbpC,KAAKyC,QAAQuB,GAGbhE,KAAKsB,YAAYwB,GAAK6B,EAAiB7F,EAAQkB,KAAKa,oBAAoBuB,EAAG4B,EAAGU,GAE9E,IAAII,EA8GR,SAAuBvD,EAAYwD,EAAIC,EAAIN,GACzC,IAAItC,EAAI,GAAK2C,EACTf,EAAI,GAAKgB,EACb,IAAKzD,GAAca,EAAI4B,EAAG,CACxB,IAAIiB,EAAM7C,EACVA,EAAI4B,EACJA,EAAIiB,CACN,CACA,IAAIH,EAAU,CAAE1C,EAAGA,EAAG4B,EAAGA,GACrBU,IACFI,EAAQJ,KAAOA,GAEjB,OAAOI,CACT,CA3HkBI,CAAclF,KAAKC,YAAamC,EAAG4B,EAAGU,GAYpD,OAVAtC,EAAI0C,EAAQ1C,EACZ4B,EAAIc,EAAQd,EAEZxG,OAAO2H,OAAOL,GACd9E,KAAKqB,UAAUyB,GAAKgC,EACpBM,EAAqBpF,KAAKkB,OAAO8C,GAAI5B,GACrCgD,EAAqBpF,KAAKoB,MAAMgB,GAAI4B,GACpChE,KAAKiB,IAAI+C,GAAGlB,GAAKgC,EACjB9E,KAAKmB,KAAKiB,GAAGU,GAAKgC,EAClB9E,KAAKuE,aACEvE,IACT,CACAkE,IAAAA,CAAK9B,EAAG4B,EAAGU,GACT,IAAI5B,EACmB,IAArB/C,UAAU1B,OACNgH,EAAYrF,KAAKC,YAAaF,UAAU,IACxC8E,EAAa7E,KAAKC,YAAamC,EAAG4B,EAAGU,GAC3C,OAAO1E,KAAKsB,YAAYwB,EAC1B,CACAwC,OAAAA,CAAQlD,EAAG4B,EAAGU,GACZ,IAAI5B,EACmB,IAArB/C,UAAU1B,OACNgH,EAAYrF,KAAKC,YAAaF,UAAU,IACxC8E,EAAa7E,KAAKC,YAAamC,EAAG4B,EAAGU,GAC3C,OAAOlH,OAAO0C,UAAUC,eAAeC,KAAKJ,KAAKsB,YAAawB,EAChE,CACAD,UAAAA,CAAWT,EAAG4B,EAAGU,GACf,IAAI5B,EACmB,IAArB/C,UAAU1B,OACNgH,EAAYrF,KAAKC,YAAaF,UAAU,IACxC8E,EAAa7E,KAAKC,YAAamC,EAAG4B,EAAGU,GACvCR,EAAOlE,KAAKqB,UAAUyB,GAY1B,OAXIoB,IACF9B,EAAI8B,EAAK9B,EACT4B,EAAIE,EAAKF,SACFhE,KAAKsB,YAAYwB,UACjB9C,KAAKqB,UAAUyB,GACtByC,EAAuBvF,KAAKkB,OAAO8C,GAAI5B,GACvCmD,EAAuBvF,KAAKoB,MAAMgB,GAAI4B,UAC/BhE,KAAKiB,IAAI+C,GAAGlB,UACZ9C,KAAKmB,KAAKiB,GAAGU,GACpB9C,KAAKuE,cAEAvE,IACT,CACAwF,OAAAA,CAAQpD,EAAGqD,GACT,IAAIC,EAAM1F,KAAKiB,IAAImB,GACnB,GAAIsD,EAAK,CACP,IAAIlB,EAAQ5D,EAAAA,EAAS8E,GACrB,OAAKD,EAGE7E,EAAAA,EAAS4D,GAAO,SAAUN,GAC/B,OAAOA,EAAK9B,IAAMqD,CACpB,IAJSjB,CAKX,CACF,CACAmB,QAAAA,CAASvD,EAAG4B,GACV,IAAI4B,EAAO5F,KAAKmB,KAAKiB,GACrB,GAAIwD,EAAM,CACR,IAAIpB,EAAQ5D,EAAAA,EAASgF,GACrB,OAAK5B,EAGEpD,EAAAA,EAAS4D,GAAO,SAAUN,GAC/B,OAAOA,EAAKF,IAAMA,CACpB,IAJSQ,CAKX,CACF,CACAqB,SAAAA,CAAUzD,EAAG4B,GACX,IAAIwB,EAAUxF,KAAKwF,QAAQpD,EAAG4B,GAC9B,GAAIwB,EACF,OAAOA,EAAQM,OAAO9F,KAAK2F,SAASvD,EAAG4B,GAE3C,EASF,SAASoB,EAAqBW,EAAKC,GAC7BD,EAAIC,GACND,EAAIC,KAEJD,EAAIC,GAAK,CAEb,CAEA,SAAST,EAAuBQ,EAAKC,KAC5BD,EAAIC,WACFD,EAAIC,EAEf,CAEA,SAASnB,EAAatD,EAAYwD,EAAIC,EAAIN,GACxC,IAAItC,EAAI,GAAK2C,EACTf,EAAI,GAAKgB,EACb,IAAKzD,GAAca,EAAI4B,EAAG,CACxB,IAAIiB,EAAM7C,EACVA,EAAI4B,EACJA,EAAIiB,CACN,CACA,OAAO7C,EAxeY,OAweS4B,EAxeT,QAwe+BpD,EAAAA,EAAc8D,GA1e1C,KA0esEA,EAC9F,CAiBA,SAASW,EAAY9D,EAAYuD,GAC/B,OAAOD,EAAatD,EAAYuD,EAAQ1C,EAAG0C,EAAQd,EAAGc,EAAQJ,KAChE,CA/CA9E,EAAMM,UAAU8B,WAAa,EAG7BpC,EAAMM,UAAUqE,WAAa,C,kCCnd7B,IAAI0B,EAAe,KAiBnB,QAPA,SAAyBC,GAGvB,IAFA,IAAIpI,EAAQoI,EAAO7H,OAEZP,KAAWmI,EAAaE,KAAKD,EAAOE,OAAOtI,MAClD,OAAOA,CACT,ECbA,IAAIuI,EAAc,OAelB,QANA,SAAkBH,GAChB,OAAOA,EACHA,EAAOI,MAAM,EAAGC,EAAgBL,GAAU,GAAGM,QAAQH,EAAa,IAClEH,CACN,E,wBCRIO,EAAa,qBAGbC,EAAa,aAGbC,EAAY,cAGZC,EAAeC,SA8CnB,QArBA,SAAkB/H,GAChB,GAAoB,iBAATA,EACT,OAAOA,EAET,IAAIQ,EAAAA,EAAAA,GAASR,GACX,OA1CM,IA4CR,IAAIgI,EAAAA,EAAAA,GAAShI,GAAQ,CACnB,IAAIiI,EAAgC,mBAAjBjI,EAAMkI,QAAwBlI,EAAMkI,UAAYlI,EACnEA,GAAQgI,EAAAA,EAAAA,GAASC,GAAUA,EAAQ,GAAMA,CAC3C,CACA,GAAoB,iBAATjI,EACT,OAAiB,IAAVA,EAAcA,GAASA,EAEhCA,EAAQmI,EAASnI,GACjB,IAAIoI,EAAWR,EAAWP,KAAKrH,GAC/B,OAAQoI,GAAYP,EAAUR,KAAKrH,GAC/B8H,EAAa9H,EAAMwH,MAAM,GAAIY,EAAW,EAAI,GAC3CT,EAAWN,KAAKrH,GAvDb,KAuD6BA,CACvC,EC1DA,IAAIqI,EAAW,IAsCf,QAZA,SAAkBrI,GAChB,OAAKA,GAGLA,EAAQsI,EAAStI,MACHqI,GAAYrI,KAAU,IA9BpB,uBA+BFA,EAAQ,GAAK,EAAI,GAGxBA,IAAUA,EAAQA,EAAQ,EAPd,IAAVA,EAAcA,EAAQ,CAQjC,C,kCC1BA,QAJA,SAAgBA,EAAOiI,GACrB,OAAOjI,EAAQiI,CACjB,C,iGCuCA,QAlCA,SAAiBM,EAAQC,EAAMxI,EAAOyI,GACpC,KAAKT,EAAAA,EAAAA,GAASO,GACZ,OAAOA,EAST,IALA,IAAIvJ,GAAS,EACTO,GAHJiJ,GAAOE,EAAAA,EAAAA,GAASF,EAAMD,IAGJhJ,OACdoJ,EAAYpJ,EAAS,EACrBqJ,EAASL,EAEI,MAAVK,KAAoB5J,EAAQO,GAAQ,CACzC,IAAIR,GAAM8J,EAAAA,EAAAA,GAAML,EAAKxJ,IACjB8J,EAAW9I,EAEf,GAAY,cAARjB,GAA+B,gBAARA,GAAiC,cAARA,EAClD,OAAOwJ,EAGT,GAAIvJ,GAAS2J,EAAW,CACtB,IAAII,EAAWH,EAAO7J,QAELE,KADjB6J,EAAWL,EAAaA,EAAWM,EAAUhK,EAAK6J,QAAU3J,KAE1D6J,GAAWd,EAAAA,EAAAA,GAASe,GAChBA,GACCC,EAAAA,EAAAA,GAAQR,EAAKxJ,EAAQ,IAAM,GAAK,CAAC,EAE1C,EACAiK,EAAAA,EAAAA,GAAYL,EAAQ7J,EAAK+J,GACzBF,EAASA,EAAO7J,EAClB,CACA,OAAOwJ,CACT,ECnBA,QAhBA,SAAoBA,EAAQW,EAAO3K,GAKjC,IAJA,IAAIS,GAAS,EACTO,EAAS2J,EAAM3J,OACfM,EAAS,CAAC,IAELb,EAAQO,GAAQ,CACvB,IAAIiJ,EAAOU,EAAMlK,GACbgB,GAAQmJ,EAAAA,EAAAA,GAAQZ,EAAQC,GAExBjK,EAAUyB,EAAOwI,IACnBY,EAAQvJ,GAAQ6I,EAAAA,EAAAA,GAASF,EAAMD,GAASvI,EAE5C,CACA,OAAOH,CACT,C,kCCRA,QALA,SAAcP,GACZ,IAAIC,EAAkB,MAATD,EAAgB,EAAIA,EAAMC,OACvC,OAAOA,EAASD,EAAMC,EAAS,QAAKN,CACtC,C,gDCkBA,QAPA,SAAmBe,GACjB,IAAIH,GAASwJ,EAAAA,EAAAA,GAASrJ,GAClBsJ,EAAYzJ,EAAS,EAEzB,OAAOA,IAAWA,EAAUyJ,EAAYzJ,EAASyJ,EAAYzJ,EAAU,CACzE,C,4DC9BI0J,EAAY,EAwBhB,QALA,SAAkBC,GAChB,IAAIC,IAAOF,EACX,OAAOG,EAAAA,EAAAA,GAASF,GAAUC,CAC5B,E,kCCxBIE,EAAaxK,KAAKyK,KAClB1K,EAAYC,KAAKC,IAyBrB,QAZA,SAAmByK,EAAOC,EAAKC,EAAMC,GAKnC,IAJA,IAAIhL,GAAS,EACTO,EAASL,EAAUyK,GAAYG,EAAMD,IAAUE,GAAQ,IAAK,GAC5DlK,EAASC,MAAMP,GAEZA,KACLM,EAAOmK,EAAYzK,IAAWP,GAAS6K,EACvCA,GAASE,EAEX,OAAOlK,CACT,E,wBCoBA,QClCA,SAAqBmK,GACnB,OAAO,SAASH,EAAOC,EAAKC,GAa1B,OAZIA,GAAuB,iBAARA,IAAoBE,EAAAA,EAAAA,GAAeJ,EAAOC,EAAKC,KAChED,EAAMC,OAAO9K,GAGf4K,GAAQR,EAAAA,EAAAA,GAASQ,QACL5K,IAAR6K,GACFA,EAAMD,EACNA,EAAQ,GAERC,GAAMT,EAAAA,EAAAA,GAASS,GAEjBC,OAAgB9K,IAAT8K,EAAsBF,EAAQC,EAAM,GAAK,GAAKT,EAAAA,EAAAA,GAASU,GACvDG,EAAUL,EAAOC,EAAKC,EAAMC,EACrC,CACF,CDgBYG,G,aEpCZ,MAAMC,EACJrJ,WAAAA,GACE,IAAIsJ,EAAW,CAAC,EAChBA,EAASC,MAAQD,EAASE,MAAQF,EAClCnJ,KAAKsJ,UAAYH,CACnB,CACAI,OAAAA,GACE,IAAIJ,EAAWnJ,KAAKsJ,UAChBE,EAAQL,EAASE,MACrB,GAAIG,IAAUL,EAEZ,OADAM,EAAOD,GACAA,CAEX,CACAE,OAAAA,CAAQF,GACN,IAAIL,EAAWnJ,KAAKsJ,UAChBE,EAAMH,OAASG,EAAMJ,OACvBK,EAAOD,GAETA,EAAMJ,MAAQD,EAASC,MACvBD,EAASC,MAAMC,MAAQG,EACvBL,EAASC,MAAQI,EACjBA,EAAMH,MAAQF,CAChB,CACAX,QAAAA,GAIE,IAHA,IAAImB,EAAO,GACPR,EAAWnJ,KAAKsJ,UAChBM,EAAOT,EAASE,MACbO,IAAST,GACdQ,EAAKE,KAAKC,KAAKC,UAAUH,EAAMI,IAC/BJ,EAAOA,EAAKP,MAEd,MAAO,IAAMM,EAAKM,KAAK,MAAQ,GACjC,EAGF,SAASR,EAAOD,GACdA,EAAMH,MAAMD,MAAQI,EAAMJ,MAC1BI,EAAMJ,MAAMC,MAAQG,EAAMH,aACnBG,EAAMJ,aACNI,EAAMH,KACf,CAEA,SAASW,EAAehE,EAAG5D,GACzB,GAAU,UAAN4D,GAAuB,UAANA,EACnB,OAAO5D,CAEX,CCzCA,IAAI8H,EAAoBtJ,EAAAA,EAAW,GAEnC,SAASuJ,EAAUC,EAAGC,GACpB,GAAID,EAAErI,aAAe,EACnB,MAAO,GAET,IAAIuI,EAkEN,SAAoBF,EAAGC,GACrB,IAAIE,EAAW,IAAI3K,EAAAA,EACf4K,EAAQ,EACRC,EAAS,EAEb7J,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7BmI,EAAS9H,QAAQL,EAAG,CAAEA,EAAGA,EAAGsI,GAAI,EAAGC,IAAK,GAC1C,IAIA/J,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAAI8H,EAAaL,EAASrG,KAAKpB,EAAEV,EAAGU,EAAEkB,IAAM,EACxC6G,EAASR,EAASvH,GAClBgI,EAAaF,EAAaC,EAC9BN,EAAStG,QAAQnB,EAAEV,EAAGU,EAAEkB,EAAG8G,GAC3BL,EAASxM,KAAKC,IAAIuM,EAASF,EAAS7H,KAAKI,EAAEV,GAAGuI,KAAOE,GACrDL,EAAQvM,KAAKC,IAAIsM,EAAQD,EAAS7H,KAAKI,EAAEkB,GAAO,IAAK6G,EACvD,IAEA,IAAIE,EAAUnK,EAAQ6J,EAASD,EAAQ,GAAGzE,KAAI,WAC5C,OAAO,IAAImD,CACb,IACI8B,EAAUR,EAAQ,EAMtB,OAJA5J,EAAAA,EAAU2J,EAAStI,SAAS,SAAUG,GACpC6I,EAAaF,EAASC,EAAST,EAAS7H,KAAKN,GAC/C,IAEO,CAAER,MAAO2I,EAAUQ,QAASA,EAASC,QAASA,EACvD,CAhGcE,CAAWd,EAAGC,GAAYH,GAClCiB,EAUN,SAAqBf,EAAGW,EAASC,GAC/B,IAIIxB,EAJA2B,EAAU,GACVjJ,EAAU6I,EAAQA,EAAQ1M,OAAS,GACnCgE,EAAQ0I,EAAQ,GAGpB,KAAOX,EAAErI,aAAa,CACpB,KAAQyH,EAAQnH,EAAMkH,WACpB3G,EAAWwH,EAAGW,EAASC,EAASxB,GAElC,KAAQA,EAAQtH,EAAQqH,WACtB3G,EAAWwH,EAAGW,EAASC,EAASxB,GAElC,GAAIY,EAAErI,YACJ,IAAK,IAAIqJ,EAAIL,EAAQ1M,OAAS,EAAG+M,EAAI,IAAKA,EAExC,GADA5B,EAAQuB,EAAQK,GAAG7B,UACR,CACT4B,EAAUA,EAAQrF,OAAOlD,EAAWwH,EAAGW,EAASC,EAASxB,GAAO,IAChE,KACF,CAGN,CAEA,OAAO2B,CACT,CAnCgBE,CAAYf,EAAM1I,MAAO0I,EAAMS,QAAST,EAAMU,SAG5D,OAAOpK,EAAAA,EACLA,EAAAA,EAAMuK,GAAS,SAAUrI,GACvB,OAAOsH,EAAEzE,SAAS7C,EAAEV,EAAGU,EAAEkB,EAC3B,IAEJ,CA6BA,SAASpB,EAAWwH,EAAGW,EAASC,EAASxB,EAAO8B,GAC9C,IAAIH,EAAUG,EAAsB,QAAKvN,EAwBzC,OAtBA6C,EAAAA,EAAUwJ,EAAE5E,QAAQgE,EAAMpH,IAAI,SAAU8B,GACtC,IAAI2G,EAAST,EAAElG,KAAKA,GAChBqH,EAASnB,EAAE1H,KAAKwB,EAAK9B,GAErBkJ,GACFH,EAAQtB,KAAK,CAAEzH,EAAG8B,EAAK9B,EAAG4B,EAAGE,EAAKF,IAGpCuH,EAAOZ,KAAOE,EACdI,EAAaF,EAASC,EAASO,EACjC,IAEA3K,EAAAA,EAAUwJ,EAAEzE,SAAS6D,EAAMpH,IAAI,SAAU8B,GACvC,IAAI2G,EAAST,EAAElG,KAAKA,GAChBF,EAAIE,EAAKF,EACTwH,EAASpB,EAAE1H,KAAKsB,GACpBwH,EAAW,IAAKX,EAChBI,EAAaF,EAASC,EAASQ,EACjC,IAEApB,EAAExH,WAAW4G,EAAMpH,GAEZ+I,CACT,CAkCA,SAASF,EAAaF,EAASC,EAASxB,GACjCA,EAAMmB,IAECnB,EAAU,GAGpBuB,EAAQvB,EAAMmB,IAAMnB,EAAU,GAAIwB,GAAStB,QAAQF,GAFnDuB,EAAQA,EAAQ1M,OAAS,GAAGqL,QAAQF,GAFpCuB,EAAQ,GAAGrB,QAAQF,EAMvB,CCxHA,SAASiC,EAAIrB,GACX,IAAIsB,EAA8B,WAAxBtB,EAAExI,QAAQ+J,UAAyBxB,EAAUC,EASvD,SAAkBA,GAChB,OAAO,SAAUtH,GACf,OAAOsH,EAAElG,KAAKpB,GAAG+H,MACnB,CACF,CAb0DR,CAASD,IAgBrE,SAAgBA,GACd,IAAIsB,EAAM,GACNE,EAAQ,CAAC,EACTC,EAAU,CAAC,EAEf,SAASC,EAAI1J,GACP5E,OAAO0C,UAAUC,eAAeC,KAAKyL,EAASzJ,KAGlDyJ,EAAQzJ,IAAK,EACbwJ,EAAMxJ,IAAK,EACXxB,EAAAA,EAAUwJ,EAAEzE,SAASvD,IAAI,SAAUU,GAC7BtF,OAAO0C,UAAUC,eAAeC,KAAKwL,EAAO9I,EAAEkB,GAChD0H,EAAI7B,KAAK/G,GAETgJ,EAAIhJ,EAAEkB,EAEV,WACO4H,EAAMxJ,GACf,CAGA,OADAxB,EAAAA,EAAUwJ,EAAEnI,QAAS6J,GACdJ,CACT,CAvC2EK,CAAO3B,GAChFxJ,EAAAA,EAAU8K,GAAK,SAAU5I,GACvB,IAAInB,EAAQyI,EAAElG,KAAKpB,GACnBsH,EAAEvH,WAAWC,GACbnB,EAAMqK,YAAclJ,EAAE4B,KACtB/C,EAAMsK,UAAW,EACjB7B,EAAEnG,QAAQnB,EAAEkB,EAAGlB,EAAEV,EAAGT,EAAOf,EAAW,OACxC,GAOF,C,kCCFA,QANA,SAAkByG,EAAQW,GACxB,OAAOkE,EAAAA,EAAAA,GAAW7E,EAAQW,GAAO,SAASlJ,EAAOwI,GAC/C,OAAO6E,EAAAA,EAAAA,GAAM9E,EAAQC,EACvB,GACF,E,wBCQA,QCbA,SAAkB8E,GAChB,OAAOC,EAAAA,EAAAA,IAAYC,EAAAA,EAAAA,GAASF,OAAMrO,EAAWwO,EAAAA,GAAUH,EAAO,GAChE,CDOWI,EAAS,SAASnF,EAAQW,GACnC,OAAiB,MAAVX,EAAiB,CAAC,EAAIoF,EAASpF,EAAQW,EAChD,I,wBETA,QAJA,SAAgBlJ,EAAOiI,GACrB,OAAOjI,EAAQiI,CACjB,E,cCiBA,QANA,SAAa3I,GACX,OAAQA,GAASA,EAAMC,QACnBG,EAAAA,EAAAA,GAAaJ,EAAOK,EAAAA,EAAUiO,QAC9B3O,CACN,E,4CCgBA,QAVA,SAAmBsJ,EAAQ3J,GACzB,IAAIiB,EAAS,CAAC,EAMd,OALAjB,GAAWC,EAAAA,EAAAA,GAAaD,EAAU,IAElCiP,EAAAA,EAAAA,GAAWtF,GAAQ,SAASvI,EAAOjB,EAAKwJ,IACtCuF,EAAAA,EAAAA,GAAgBjO,EAAQd,EAAKH,EAASoB,EAAOjB,EAAKwJ,GACpD,IACO1I,CACT,E,4CClBA,QAJU,WACR,OAAOkO,EAAAA,EAAKC,KAAKC,KACnB,ECGA,SAASC,EAAa5C,EAAG6C,EAAMC,EAAOxI,GACpC,IAAItC,EACJ,GACEA,EAAIxB,EAAW8D,SACR0F,EAAEzH,QAAQP,IAInB,OAFA8K,EAAMC,MAAQF,EACd7C,EAAE3H,QAAQL,EAAG8K,GACN9K,CACT,CAsBA,SAASgL,EAAmBhD,GAC1B,IAAIiD,EAAa,IAAIzN,EAAAA,EAAM,CAAEW,WAAY6J,EAAE5I,iBAAkBE,SAAS0I,EAAExI,SASxE,OARAhB,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GACxBgI,EAAEpH,SAASZ,GAAG/D,QACjBgP,EAAW5K,QAAQL,EAAGgI,EAAE1H,KAAKN,GAEjC,IACAxB,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7BuK,EAAWpJ,QAAQnB,EAAGsH,EAAElG,KAAKpB,GAC/B,IACOuK,CACT,CA4BA,SAASC,EAAcC,EAAMC,GAC3B,IAcIC,EAAIC,EAdJC,EAAIJ,EAAKI,EACTC,EAAIL,EAAKK,EAITC,EAAKL,EAAMG,EAAIA,EACfG,EAAKN,EAAMI,EAAIA,EACf5J,EAAIuJ,EAAKQ,MAAQ,EACjBC,EAAIT,EAAKU,OAAS,EAEtB,IAAKJ,IAAOC,EACV,MAAM,IAAI1K,MAAM,6DAoBlB,OAhBInF,KAAKiQ,IAAIJ,GAAM9J,EAAI/F,KAAKiQ,IAAIL,GAAMG,GAEhCF,EAAK,IACPE,GAAKA,GAEPP,EAAMO,EAAIH,EAAMC,EAChBJ,EAAKM,IAGDH,EAAK,IACP7J,GAAKA,GAEPyJ,EAAKzJ,EACL0J,EAAM1J,EAAI8J,EAAMD,GAGX,CAAEF,EAAGA,EAAIF,EAAIG,EAAGA,EAAIF,EAC7B,CAMA,SAASS,EAAiB/D,GACxB,IAAIgE,EAAWxN,EAAAA,EAAMA,EAAQyN,EAAQjE,GAAK,IAAI,WAC5C,MAAO,EACT,IAQA,OAPAxJ,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7B,IAAIM,EAAO0H,EAAE1H,KAAKN,GACdkM,EAAO5L,EAAK4L,KACX1N,EAAAA,EAAc0N,KACjBF,EAASE,GAAM5L,EAAK6L,OAASnM,EAEjC,IACOgM,CACT,CAkDA,SAASI,EAAcpE,EAAG9B,EAAQgG,EAAMC,GACtC,IAAI7L,EAAO,CACTqL,MAAO,EACPE,OAAQ,GAMV,OAJIlO,UAAU1B,QAAU,IACtBqE,EAAK4L,KAAOA,EACZ5L,EAAK6L,MAAQA,GAERvB,EAAa5C,EAAG,SAAU1H,EAAM4F,EACzC,CAEA,SAAS+F,EAAQjE,GACf,OAAOxJ,EACLA,EAAAA,EAAMwJ,EAAEnI,SAAS,SAAUG,GACzB,IAAIkM,EAAOlE,EAAE1H,KAAKN,GAAGkM,KACrB,IAAK1N,EAAAA,EAAc0N,GACjB,OAAOA,CAEX,IAEJ,CAuBA,SAASG,EAAK/J,EAAMgK,GAClB,IAAI/F,EAAQ/H,IACZ,IACE,OAAO8N,GACT,CAAE,QACAC,QAAQC,IAAIlK,EAAO,WAAa9D,IAAU+H,GAAS,KACrD,CACF,CAEA,SAASkG,GAAOnK,EAAMgK,GACpB,OAAOA,GACT,CC/NA,SAASF,GAAcpE,EAAG0E,EAAMxG,EAAQyG,EAAIC,EAAQV,GAClD,IAAI3M,EAAQ,CAAEoM,MAAO,EAAGE,OAAQ,EAAGK,KAAMA,EAAMW,WAAYH,GACvDI,EAAOF,EAAOF,GAAMR,EAAO,GAC3B1E,EAAOuF,EAAkB/E,EAAG,SAAUzI,EAAO2G,GACjD0G,EAAOF,GAAMR,GAAQ1E,EACrBQ,EAAElH,UAAU0G,EAAMmF,GACdG,GACF9E,EAAEnG,QAAQiL,EAAMtF,EAAM,CAAEiB,OAAQ,GAEpC,CCxBA,SAASuE,GAAKhF,GACZ,IAAIiF,EAAUjF,EAAExI,QAAQ0N,QAAQC,cAChB,OAAZF,GAAgC,OAAZA,GAyB1B,SAAkBjF,GAChBxJ,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7BoN,GAAYpF,EAAE1H,KAAKN,GACrB,IAEAxB,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAAIoB,EAAOkG,EAAElG,KAAKpB,GAClBlC,EAAAA,EAAUsD,EAAKuL,OAAQD,IACnBhS,OAAO0C,UAAUC,eAAeC,KAAK8D,EAAM,MAC7CsL,GAAYtL,EAEhB,GACF,CApCIwL,CAAStF,GAGK,OAAZiF,GAAgC,OAAZA,KAuC1B,SAAgBjF,GACdxJ,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7BuN,GAAUvF,EAAE1H,KAAKN,GACnB,IAEAxB,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAAIoB,EAAOkG,EAAElG,KAAKpB,GAClBlC,EAAAA,EAAUsD,EAAKuL,OAAQE,IACnBnS,OAAO0C,UAAUC,eAAeC,KAAK8D,EAAM,MAC7CyL,GAAUzL,EAEd,GACF,CAlDI0L,CAAOxF,GACPyF,GAAgBzF,GAEpB,CAEA,SAASyF,GAAgBzF,GACvBxJ,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7B0N,GAAmB1F,EAAE1H,KAAKN,GAC5B,IACAxB,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7BgN,GAAmB1F,EAAElG,KAAKpB,GAC5B,GACF,CAEA,SAASgN,GAAmB5C,GAC1B,IAAIlJ,EAAIkJ,EAAMa,MACdb,EAAMa,MAAQb,EAAMe,OACpBf,EAAMe,OAASjK,CACjB,CAgBA,SAASwL,GAAYtC,GACnBA,EAAMU,GAAKV,EAAMU,CACnB,CAgBA,SAAS+B,GAAUzC,GACjB,IAAIS,EAAIT,EAAMS,EACdT,EAAMS,EAAIT,EAAMU,EAChBV,EAAMU,EAAID,CACZ,CChDA,SAASlC,GAAIrB,GACXA,EAAExI,QAAQmO,YAAc,GACxBnP,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAUN,IAQjC,SAAuBkG,EAAGtH,GACxB,IAAIV,EAAIU,EAAEV,EACN4N,EAAQ5F,EAAE1H,KAAKN,GAAGkM,KAClBtK,EAAIlB,EAAEkB,EACNiM,EAAQ7F,EAAE1H,KAAKsB,GAAGsK,KAClB5J,EAAO5B,EAAE4B,KACTwL,EAAY9F,EAAElG,KAAKpB,GACnBqN,EAAYD,EAAUC,UAE1B,GAAIF,IAAUD,EAAQ,EAAG,OAEzB5F,EAAEvH,WAAWC,GAcb,IACIqK,EAAO/B,EADP8B,OAAQnP,EAEZ,IAAKqN,EAAI,IAAK4E,EAAOA,EAAQC,IAAS7E,IAAK4E,EACzCE,EAAUT,OAAS,GAQnBtC,EAAQgC,EAAkB/E,EAAG,OAP7B8C,EAAQ,CACNa,MAAO,EACPE,OAAQ,EACRiC,UAAWA,EACXpL,QAAShC,EACTwL,KAAM0B,GAEoC,MACxCA,IAAUG,IACZjD,EAAMa,MAAQmC,EAAUnC,MACxBb,EAAMe,OAASiC,EAAUjC,OACzBf,EAAMC,MAAQ,aACdD,EAAMkD,SAAWF,EAAUE,UAE7BhG,EAAEnG,QAAQ7B,EAAG+K,EAAO,CAAEtC,OAAQqF,EAAUrF,QAAUnG,GACxC,IAAN0G,GACFhB,EAAExI,QAAQmO,YAAYlG,KAAKsD,GAE7B/K,EAAI+K,EAGN/C,EAAEnG,QAAQ7B,EAAG4B,EAAG,CAAE6G,OAAQqF,EAAUrF,QAAUnG,EAChD,CA1DI2L,CAAcjG,EAAGlG,EACnB,GACF,C,eCEA,SANA,SAAe9F,EAAOV,GACpB,OAAQU,GAASA,EAAMC,QACnBG,EAAAA,EAAAA,GAAaJ,GAAOT,EAAAA,EAAAA,GAAaD,EAAU,GAAIgB,GAAAA,QAC/CX,CACN,ECNA,SAASuS,GAAYlG,GACnB,IAAIyB,EAAU,CAAC,EA2BfjL,EAAAA,EAAUwJ,EAAElI,WAzBZ,SAAS4J,EAAI1J,GACX,IAAIT,EAAQyI,EAAE1H,KAAKN,GACnB,GAAI5E,OAAO0C,UAAUC,eAAeC,KAAKyL,EAASzJ,GAChD,OAAOT,EAAM2M,KAEfzC,EAAQzJ,IAAK,EAEb,IAAIkM,EAAO1N,EAAAA,EACTA,EAAAA,EAAMwJ,EAAEzE,SAASvD,IAAI,SAAUU,GAC7B,OAAOgJ,EAAIhJ,EAAEkB,GAAKoG,EAAElG,KAAKpB,GAAGyN,MAC9B,KAYF,OAREjC,IAASkC,OAAOC,wBACP1S,IAATuQ,GACS,OAATA,IAGAA,EAAO,GAGD3M,EAAM2M,KAAOA,CACvB,GAGF,CAMA,SAASoC,GAAMtG,EAAGtH,GAChB,OAAOsH,EAAE1H,KAAKI,EAAEkB,GAAGsK,KAAOlE,EAAE1H,KAAKI,EAAEV,GAAGkM,KAAOlE,EAAElG,KAAKpB,GAAGyN,MACzD,CC/BA,SAASI,GAAavG,GACpB,IAOIlG,EAAM0M,EAPNC,EAAI,IAAIjR,EAAAA,EAAM,CAAES,UAAU,IAG1BsI,EAAQyB,EAAEnI,QAAQ,GAClB6O,EAAO1G,EAAErI,YAIb,IAHA8O,EAAEpO,QAAQkG,EAAO,CAAC,GAGXoI,GAAUF,EAAGzG,GAAK0G,GACvB5M,EAAO8M,GAAiBH,EAAGzG,GAC3BwG,EAAQC,EAAElO,QAAQuB,EAAK9B,GAAKsO,GAAMtG,EAAGlG,IAASwM,GAAMtG,EAAGlG,GACvD+M,GAAWJ,EAAGzG,EAAGwG,GAGnB,OAAOC,CACT,CAMA,SAASE,GAAUF,EAAGzG,GAcpB,OADAxJ,EAAAA,EAAUiQ,EAAE5O,SAZZ,SAAS6J,EAAI1J,GACXxB,EAAAA,EAAUwJ,EAAEvE,UAAUzD,IAAI,SAAUU,GAClC,IAAIoO,EAAQpO,EAAEV,EACZ4B,EAAI5B,IAAM8O,EAAQpO,EAAEkB,EAAIkN,EACrBL,EAAElO,QAAQqB,IAAO0M,GAAMtG,EAAGtH,KAC7B+N,EAAEpO,QAAQuB,EAAG,CAAC,GACd6M,EAAE5M,QAAQ7B,EAAG4B,EAAG,CAAC,GACjB8H,EAAI9H,GAER,GACF,IAGO6M,EAAE9O,WACX,CAMA,SAASiP,GAAiBH,EAAGzG,GAC3B,OAAOxJ,GAAQwJ,EAAE5F,SAAS,SAAU1B,GAClC,GAAI+N,EAAElO,QAAQG,EAAEV,KAAOyO,EAAElO,QAAQG,EAAEkB,GACjC,OAAO0M,GAAMtG,EAAGtH,EAEpB,GACF,CAEA,SAASmO,GAAWJ,EAAGzG,EAAGwG,GACxBhQ,EAAAA,EAAUiQ,EAAE5O,SAAS,SAAUG,GAC7BgI,EAAE1H,KAAKN,GAAGkM,MAAQsC,CACpB,GACF,C,yBCjF0BhQ,EAAAA,EAAW,GCDXA,EAAAA,EAAW,G,iCCKrBuQ,E,OAAAA,GAAa,UCGVC,OAAO,uFCX1B,IAAIC,GAAgB,kBAQhBC,GAAW,IAAMD,GAAgB,IACjCE,GAAU,kDACVC,GAAS,2BAETC,GAAc,KAAOJ,GAAgB,IACrCK,GAAa,kCACbC,GAAa,qCAIbC,GAPa,MAAQL,GAAU,IAAMC,GAAS,IAOtB,IACxBK,GAAW,oBAEXC,GAAQD,GAAWD,IADP,gBAAwB,CAACH,GAAaC,GAAYC,IAAY1H,KAAK,KAAO,IAAM4H,GAAWD,GAAW,MAElHG,GAAW,MAAQ,CAACN,GAAcF,GAAU,IAAKA,GAASG,GAAYC,GAAYL,IAAUrH,KAAK,KAAO,IAG5FmH,OAAOI,GAAS,MAAQA,GAAS,KAAOO,GAAWD,GAAO,KCQ1E,SAASE,KAAkB,CAC3BA,GAAe9R,UAAY,IAAIkD,M,cCvB/B,SAAS0I,GAAI1B,EAAG7H,EAAIgM,GACb3N,GAAAA,EAAU2B,KACbA,EAAK,CAACA,IAGR,IAAI0P,GAAc7H,EAAE7I,aAAe6I,EAAE5G,WAAa4G,EAAE1G,WAAWwO,KAAK9H,GAEhE+H,EAAM,GACNtG,EAAU,CAAC,EAQf,OAPAjL,EAAAA,EAAO2B,GAAI,SAAUH,GACnB,IAAKgI,EAAEzH,QAAQP,GACb,MAAM,IAAIgB,MAAM,6BAA+BhB,GAGjDgQ,GAAMhI,EAAGhI,EAAa,SAAVmM,EAAkB1C,EAASoG,EAAYE,EACrD,IACOA,CACT,CAEA,SAASC,GAAMhI,EAAGhI,EAAGiQ,EAAWxG,EAASoG,EAAYE,GAC9C3U,OAAO0C,UAAUC,eAAeC,KAAKyL,EAASzJ,KACjDyJ,EAAQzJ,IAAK,EAERiQ,GACHF,EAAItI,KAAKzH,GAEXxB,EAAAA,EAAOqR,EAAW7P,IAAI,SAAU4B,GAC9BoO,GAAMhI,EAAGpG,EAAGqO,EAAWxG,EAASoG,EAAYE,EAC9C,IACIE,GACFF,EAAItI,KAAKzH,GAGf,C,QCIA,SAASkQ,GAAelI,GACtBA,EdZF,SAAkBA,GAChB,IAAIiD,GAAa,IAAIzN,EAAAA,GAAQ8B,SAAS0I,EAAExI,SAYxC,OAXAhB,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7BiL,EAAW5K,QAAQL,EAAGgI,EAAE1H,KAAKN,GAC/B,IACAxB,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAAIyP,EAAclF,EAAWnJ,KAAKpB,EAAEV,EAAGU,EAAEkB,IAAM,CAAE6G,OAAQ,EAAG0F,OAAQ,GAChE5O,EAAQyI,EAAElG,KAAKpB,GACnBuK,EAAWpJ,QAAQnB,EAAEV,EAAGU,EAAEkB,EAAG,CAC3B6G,OAAQ0H,EAAY1H,OAASlJ,EAAMkJ,OACnC0F,OAAQtS,KAAKC,IAAIqU,EAAYhC,OAAQ5O,EAAM4O,SAE/C,IACOlD,CACT,CcFMmF,CAASpI,GACbkG,GAAYlG,GACZ,IAIItH,EAJA+N,EAAIF,GAAavG,GAKrB,IAJAqI,GAAiB5B,GACjB6B,GAAc7B,EAAGzG,GAGTtH,EAAI6P,GAAU9B,IAEpB+B,GAAc/B,EAAGzG,EAAGtH,EADhB+P,GAAUhC,EAAGzG,EAAGtH,GAGxB,CAKA,SAAS4P,GAAc7B,EAAGzG,GACxB,IAAI7H,EC/DN,SAAmB6H,EAAG7H,GACpB,OAAOuJ,GAAI1B,EAAG7H,EAAI,OACpB,CD6DWuQ,CAAcjC,EAAGA,EAAE5O,SAC5BM,EAAKA,EAAG+D,MAAM,EAAG/D,EAAGlE,OAAS,GAC7BuC,EAAAA,EAAU2B,GAAI,SAAUH,IAK1B,SAAwByO,EAAGzG,EAAGnH,GAC5B,IAAI8P,EAAWlC,EAAEnO,KAAKO,GAClBE,EAAS4P,EAAS5P,OACtB0N,EAAE3M,KAAKjB,EAAOE,GAAQ6P,SAAWC,GAAapC,EAAGzG,EAAGnH,EACtD,CARIiQ,CAAerC,EAAGzG,EAAGhI,EACvB,GACF,CAYA,SAAS6Q,GAAapC,EAAGzG,EAAGnH,GAC1B,IACIE,EADW0N,EAAEnO,KAAKO,GACAE,OAElBgQ,GAAc,EAEdC,EAAYhJ,EAAElG,KAAKjB,EAAOE,GAE1BkQ,EAAW,EAyBf,OAvBKD,IACHD,GAAc,EACdC,EAAYhJ,EAAElG,KAAKf,EAAQF,IAG7BoQ,EAAWD,EAAUvI,OAErBjK,EAAAA,EAAUwJ,EAAEvE,UAAU5C,IAAQ,SAAUH,GACtC,IA2HsB2C,EAAGrD,EA3HrBkR,EAAYxQ,EAAEV,IAAMa,EACtB8D,EAAQuM,EAAYxQ,EAAEkB,EAAIlB,EAAEV,EAE9B,GAAI2E,IAAU5D,EAAQ,CACpB,IAAIoQ,EAAeD,IAAcH,EAC/BK,EAAcpJ,EAAElG,KAAKpB,GAAG+H,OAG1B,GADAwI,GAAYE,EAAeC,GAAeA,EAoHtB/N,EAnHFxC,EAmHKb,EAnHE2E,EAAV8J,EAoHPvL,QAAQG,EAAGrD,GApHc,CAC/B,IAAIqR,EAAgB5C,EAAE3M,KAAKjB,EAAO8D,GAAOiM,SACzCK,GAAYE,GAAgBE,EAAgBA,CAC9C,CACF,CACF,IAEOJ,CACT,CAEA,SAASZ,GAAiBiB,EAAM7G,GAC1B9M,UAAU1B,OAAS,IACrBwO,EAAO6G,EAAKzR,QAAQ,IAEtB0R,GAAgBD,EAAM,CAAC,EAAG,EAAG7G,EAC/B,CAEA,SAAS8G,GAAgBD,EAAM7H,EAAS+H,EAASxR,EAAGe,GAClD,IAAI0Q,EAAMD,EACNjS,EAAQ+R,EAAKhR,KAAKN,GAkBtB,OAhBAyJ,EAAQzJ,IAAK,EACbxB,EAAAA,EAAU8S,EAAKhQ,UAAUtB,IAAI,SAAU4B,GAChCxG,OAAO0C,UAAUC,eAAeC,KAAKyL,EAAS7H,KACjD4P,EAAUD,GAAgBD,EAAM7H,EAAS+H,EAAS5P,EAAG5B,GAEzD,IAEAT,EAAMkS,IAAMA,EACZlS,EAAMmS,IAAMF,IACRzQ,EACFxB,EAAMwB,OAASA,SAGRxB,EAAMwB,OAGRyQ,CACT,CAEA,SAASjB,GAAUe,GACjB,OAAO9S,GAAAA,EAAO8S,EAAKlP,SAAS,SAAU1B,GACpC,OAAO4Q,EAAKxP,KAAKpB,GAAGkQ,SAAW,CACjC,GACF,CAEA,SAASH,GAAUhC,EAAGzG,EAAGlG,GACvB,IAAI9B,EAAI8B,EAAK9B,EACT4B,EAAIE,EAAKF,EAKRoG,EAAE9E,QAAQlD,EAAG4B,KAChB5B,EAAI8B,EAAKF,EACTA,EAAIE,EAAK9B,GAGX,IAAI2R,EAASlD,EAAEnO,KAAKN,GAChB4R,EAASnD,EAAEnO,KAAKsB,GAChBiQ,EAAYF,EACZG,GAAO,EAIPH,EAAOD,IAAME,EAAOF,MACtBG,EAAYD,EACZE,GAAO,GAGT,IAAIC,EAAavT,GAAAA,EAASwJ,EAAE5F,SAAS,SAAUN,GAC7C,OACEgQ,IAASE,GAAavD,EAAGA,EAAEnO,KAAKwB,EAAK9B,GAAI6R,IACzCC,IAASE,GAAavD,EAAGA,EAAEnO,KAAKwB,EAAKF,GAAIiQ,EAE7C,IAEA,OAAOrT,GAAQuT,GAAY,SAAUjQ,GACnC,OAAOwM,GAAMtG,EAAGlG,EAClB,GACF,CAEA,SAAS0O,GAAc/B,EAAGzG,EAAGtH,EAAGuR,GAC9B,IAAIjS,EAAIU,EAAEV,EACN4B,EAAIlB,EAAEkB,EACV6M,EAAEhO,WAAWT,EAAG4B,GAChB6M,EAAE5M,QAAQoQ,EAAEjS,EAAGiS,EAAErQ,EAAG,CAAC,GACrByO,GAAiB5B,GACjB6B,GAAc7B,EAAGzG,GAInB,SAAqByG,EAAGzG,GACtB,IAAIyC,EAAOjM,GAAAA,EAAOiQ,EAAE5O,SAAS,SAAUG,GACrC,OAAQgI,EAAE1H,KAAKN,GAAGe,MACpB,IACIZ,EE1MN,SAAkB6H,EAAG7H,GACnB,OAAOuJ,GAAI1B,EAAG7H,EAAI,MACpB,CFwMWuQ,CAAajC,EAAGhE,GACzBtK,EAAKA,EAAG+D,MAAM,GACd1F,EAAAA,EAAU2B,GAAI,SAAUH,GACtB,IAAIe,EAAS0N,EAAEnO,KAAKN,GAAGe,OACrBe,EAAOkG,EAAElG,KAAK9B,EAAGe,GACjBmR,GAAU,EAEPpQ,IACHA,EAAOkG,EAAElG,KAAKf,EAAQf,GACtBkS,GAAU,GAGZlK,EAAE1H,KAAKN,GAAGkM,KAAOlE,EAAE1H,KAAKS,GAAQmL,MAAQgG,EAAUpQ,EAAKqM,QAAUrM,EAAKqM,OACxE,GACF,CArBEgE,CAAY1D,EAAGzG,EACjB,CAiCA,SAASgK,GAAaV,EAAMK,EAAQS,GAClC,OAAOA,EAAUX,KAAOE,EAAOD,KAAOC,EAAOD,KAAOU,EAAUV,GAChE,CGlNA,SAASxF,GAAKlE,GACZ,OAAQA,EAAExI,QAAQ6S,QAChB,IAAK,kBASL,QACEC,GAAqBtK,SAPvB,IAAK,cAcT,SAAyBA,GACvBkG,GAAYlG,GACZuG,GAAavG,EACf,CAhBMuK,CAAgBvK,GAChB,MACF,IAAK,eACHwK,GAAkBxK,GAKxB,CH9BAkI,GAAeG,iBAAmBA,GAClCH,GAAeI,cAAgBA,GAC/BJ,GAAeW,aAAeA,GAC9BX,GAAeK,UAAYA,GAC3BL,GAAeO,UAAYA,GAC3BP,GAAeM,cAAgBA,GG4B/B,IAAIgC,GAAoBtE,GAOxB,SAASoE,GAAqBtK,GAC5BkI,GAAelI,EACjB,C,0BCvBA,SAASqB,GAAIrB,GACX,IAAIyC,EAAOsC,EAAkB/E,EAAG,OAAQ,CAAC,EAAG,SACxCyK,EAqEN,SAAoBzK,GAClB,IAAIyK,EAAS,CAAC,EACd,SAAS/I,EAAI1J,EAAG0S,GACd,IAAI9R,EAAWoH,EAAEpH,SAASZ,GACtBY,GAAYA,EAAS3E,QACvBuC,EAAAA,EAAUoC,GAAU,SAAUC,GAC5B6I,EAAI7I,EAAO6R,EAAQ,EACrB,IAEFD,EAAOzS,GAAK0S,CACd,CAIA,OAHAlU,EAAAA,EAAUwJ,EAAEpH,YAAY,SAAUZ,GAChC0J,EAAI1J,EAAG,EACT,IACOyS,CACT,CApFeE,CAAW3K,GACpB6D,EAASrN,EAAMA,GAAAA,EAASiU,IAAW,EACnCG,EAAU,EAAI/G,EAAS,EAE3B7D,EAAExI,QAAQqT,YAAcpI,EAGxBjM,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7BsH,EAAElG,KAAKpB,GAAGyN,QAAUyE,CACtB,IAGA,IAAInK,EA0EN,SAAoBT,GAClB,OAAOxJ,GAAAA,EACLwJ,EAAE5F,SACF,SAAU2N,EAAKrP,GACb,OAAOqP,EAAM/H,EAAElG,KAAKpB,GAAG+H,MACzB,GACA,EAEJ,CAlFeqK,CAAW9K,GAAK,EAG7BxJ,EAAAA,EAAUwJ,EAAEpH,YAAY,SAAUC,GAChC6I,GAAI1B,EAAGyC,EAAMmI,EAASnK,EAAQoD,EAAQ4G,EAAQ5R,EAChD,IAIAmH,EAAExI,QAAQuT,eAAiBH,CAC7B,CAEA,SAASlJ,GAAI1B,EAAGyC,EAAMmI,EAASnK,EAAQoD,EAAQ4G,EAAQzS,GACrD,IAAIY,EAAWoH,EAAEpH,SAASZ,GAC1B,GAAKY,EAAS3E,OAAd,CAOA,IAAI+W,EAAMjG,EAAmB/E,EAAG,OAC5BiL,EAASlG,EAAmB/E,EAAG,OAC/BzI,EAAQyI,EAAE1H,KAAKN,GAEnBgI,EAAElH,UAAUkS,EAAKhT,GACjBT,EAAM2T,UAAYF,EAClBhL,EAAElH,UAAUmS,EAAQjT,GACpBT,EAAM4T,aAAeF,EAErBzU,EAAAA,EAAUoC,GAAU,SAAUC,GAC5B6I,GAAI1B,EAAGyC,EAAMmI,EAASnK,EAAQoD,EAAQ4G,EAAQ5R,GAE9C,IAAIuS,EAAYpL,EAAE1H,KAAKO,GACnBwS,EAAWD,EAAUF,UAAYE,EAAUF,UAAYrS,EACvDyS,EAAcF,EAAUD,aAAeC,EAAUD,aAAetS,EAChE0S,EAAaH,EAAUF,UAAYzK,EAAS,EAAIA,EAChD0F,EAASkF,IAAaC,EAAc,EAAIzH,EAAS4G,EAAOzS,GAAK,EAEjEgI,EAAEnG,QAAQmR,EAAKK,EAAU,CACvB5K,OAAQ8K,EACRpF,OAAQA,EACRqF,aAAa,IAGfxL,EAAEnG,QAAQyR,EAAaL,EAAQ,CAC7BxK,OAAQ8K,EACRpF,OAAQA,EACRqF,aAAa,GAEjB,IAEKxL,EAAEjH,OAAOf,IACZgI,EAAEnG,QAAQ4I,EAAMuI,EAAK,CAAEvK,OAAQ,EAAG0F,OAAQtC,EAAS4G,EAAOzS,IAlC5D,MAJMA,IAAMyK,GACRzC,EAAEnG,QAAQ4I,EAAMzK,EAAG,CAAEyI,OAAQ,EAAG0F,OAAQyE,GAuC9C,C,eCrEA,SAJA,SAAmBlW,GACjB,OAAO+W,EAAAA,GAAAA,GAAU/W,EAAOgX,EAC1B,ECSA,SAASC,GAAgB3L,EAAGkE,EAAM0H,GAChC,IAAInJ,EAmCN,SAAwBzC,GACtB,IAAIhI,EACJ,KAAOgI,EAAEzH,QAASP,EAAIxB,EAAW,YACjC,OAAOwB,CACT,CAvCa6T,CAAe7L,GACxBzL,EAAS,IAAIiB,EAAAA,EAAM,CAAEa,UAAU,IAC5BiB,SAAS,CAAEmL,KAAMA,IACjBhL,qBAAoB,SAAUO,GAC7B,OAAOgI,EAAE1H,KAAKN,EAChB,IA2BJ,OAzBAxB,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7B,IAAIM,EAAO0H,EAAE1H,KAAKN,GAChBe,EAASiH,EAAEjH,OAAOf,IAEhBM,EAAK4L,OAASA,GAAS5L,EAAKwT,SAAW5H,GAAQA,GAAQ5L,EAAK2L,WAC9D1P,EAAO8D,QAAQL,GACfzD,EAAOuE,UAAUd,EAAGe,GAAU0J,GAG9BjM,EAAAA,EAAUwJ,EAAE4L,GAAc5T,IAAI,SAAUU,GACtC,IAAI2C,EAAI3C,EAAEV,IAAMA,EAAIU,EAAEkB,EAAIlB,EAAEV,EAC1B8B,EAAOvF,EAAOuF,KAAKuB,EAAGrD,GACtByI,EAAUjK,EAAAA,EAAcsD,GAAsB,EAAdA,EAAK2G,OACvClM,EAAOsF,QAAQwB,EAAGrD,EAAG,CAAEyI,OAAQT,EAAElG,KAAKpB,GAAG+H,OAASA,GACpD,IAEIrN,OAAO0C,UAAUC,eAAeC,KAAKsC,EAAM,YAC7C/D,EAAO8D,QAAQL,EAAG,CAChB+T,WAAYzT,EAAKyT,WAAW7H,GAC5B8H,YAAa1T,EAAK0T,YAAY9H,KAItC,IAEO3P,CACT,C,eC/CA,SAbA,SAAuB0X,EAAOC,EAAQC,GAMpC,IALA,IAAIzY,GAAS,EACTO,EAASgY,EAAMhY,OACfmY,EAAaF,EAAOjY,OACpBM,EAAS,CAAC,IAELb,EAAQO,GAAQ,CACvB,IAAIS,EAAQhB,EAAQ0Y,EAAaF,EAAOxY,QAASC,EACjDwY,EAAW5X,EAAQ0X,EAAMvY,GAAQgB,EACnC,CACA,OAAOH,CACT,ECGA,SAJA,SAAmB0X,EAAOC,GACxB,OAAOG,GAAcJ,GAAS,GAAIC,GAAU,GAAIvO,GAAAA,EAClD,E,+CCDA,SAVA,SAAoB3J,EAAOsY,GACzB,IAAIrY,EAASD,EAAMC,OAGnB,IADAD,EAAMuY,KAAKD,GACJrY,KACLD,EAAMC,GAAUD,EAAMC,GAAQS,MAEhC,OAAOV,CACT,E,0BCsBA,SA9BA,SAA0BU,EAAOiI,GAC/B,GAAIjI,IAAUiI,EAAO,CACnB,IAAI6P,OAAyB7Y,IAAVe,EACf+X,EAAsB,OAAV/X,EACZgY,EAAiBhY,IAAUA,EAC3BiY,GAAczX,EAAAA,GAAAA,GAASR,GAEvBkY,OAAyBjZ,IAAVgJ,EACfkQ,EAAsB,OAAVlQ,EACZmQ,EAAiBnQ,IAAUA,EAC3BoQ,GAAc7X,EAAAA,GAAAA,GAASyH,GAE3B,IAAMkQ,IAAcE,IAAgBJ,GAAejY,EAAQiI,GACtDgQ,GAAeC,GAAgBE,IAAmBD,IAAcE,GAChEN,GAAaG,GAAgBE,IAC5BN,GAAgBM,IACjBJ,EACH,OAAO,EAET,IAAMD,IAAcE,IAAgBI,GAAerY,EAAQiI,GACtDoQ,GAAeP,GAAgBE,IAAmBD,IAAcE,GAChEE,GAAaL,GAAgBE,IAC5BE,GAAgBF,IACjBI,EACH,OAAQ,CAEZ,CACA,OAAO,CACT,ECKA,SA3BA,SAAyB7P,EAAQN,EAAOqQ,GAOtC,IANA,IAAItZ,GAAS,EACTuZ,EAAchQ,EAAOiQ,SACrBC,EAAcxQ,EAAMuQ,SACpBjZ,EAASgZ,EAAYhZ,OACrBmZ,EAAeJ,EAAO/Y,SAEjBP,EAAQO,GAAQ,CACvB,IAAIM,EAAS8Y,GAAiBJ,EAAYvZ,GAAQyZ,EAAYzZ,IAC9D,GAAIa,EACF,OAAIb,GAAS0Z,EACJ7Y,EAGFA,GAAmB,QADdyY,EAAOtZ,IACiB,EAAI,EAE5C,CAQA,OAAOuJ,EAAOvJ,MAAQiJ,EAAMjJ,KAC9B,ECOA,SA7BA,SAAqBV,EAAYsa,EAAWN,GAExCM,EADEA,EAAUrZ,QACAsZ,EAAAA,GAAAA,GAASD,GAAW,SAASha,GACvC,OAAIsB,EAAAA,GAAAA,GAAQtB,GACH,SAASoB,GACd,OAAOmJ,EAAAA,GAAAA,GAAQnJ,EAA2B,IAApBpB,EAASW,OAAeX,EAAS,GAAKA,EAC9D,EAEKA,CACT,IAEY,CAACe,EAAAA,GAGf,IAAIX,GAAS,EACb4Z,GAAYC,EAAAA,GAAAA,GAASD,GAAWE,EAAAA,GAAAA,GAAUja,EAAAA,IAE1C,IAAIgB,GAASkZ,EAAAA,GAAAA,GAAQza,GAAY,SAAS0B,EAAOjB,EAAKT,GAIpD,MAAO,CAAE,UAHMua,EAAAA,GAAAA,GAASD,GAAW,SAASha,GAC1C,OAAOA,EAASoB,EAClB,IAC+B,QAAWhB,EAAO,MAASgB,EAC5D,IAEA,OAAOgZ,GAAWnZ,GAAQ,SAAS0I,EAAQN,GACzC,OAAOgR,GAAgB1Q,EAAQN,EAAOqQ,EACxC,GACF,ECCA,UAba7X,E,QAAAA,IAAS,SAASnC,EAAYsa,GACzC,GAAkB,MAAdta,EACF,MAAO,GAET,IAAIiB,EAASqZ,EAAUrZ,OAMvB,OALIA,EAAS,IAAK0K,EAAAA,EAAAA,GAAe3L,EAAYsa,EAAU,GAAIA,EAAU,IACnEA,EAAY,GACHrZ,EAAS,IAAK0K,EAAAA,EAAAA,GAAe2O,EAAU,GAAIA,EAAU,GAAIA,EAAU,MAC5EA,EAAY,CAACA,EAAU,KAElBM,GAAY5a,GAAY2B,EAAAA,GAAAA,GAAY2Y,EAAW,GAAI,GAC5D,ICzBA,SAASO,GAAW7N,EAAGgE,GAErB,IADA,IAAI8J,EAAK,EACA9M,EAAI,EAAGA,EAAIgD,EAAS/P,SAAU+M,EACrC8M,GAAMC,GAAmB/N,EAAGgE,EAAShD,EAAI,GAAIgD,EAAShD,IAExD,OAAO8M,CACT,CAEA,SAASC,GAAmB/N,EAAGgO,EAAYC,GAuBzC,IAnBA,IAAIC,EAAW1X,GACbyX,EACAzX,EAAAA,EAAMyX,GAAY,SAAUjW,EAAGgJ,GAC7B,OAAOA,CACT,KAEEmN,EAAe3X,EAAAA,EACjBA,EAAAA,EAAMwX,GAAY,SAAUhW,GAC1B,OAAOxB,GACLA,EAAAA,EAAMwJ,EAAEzE,SAASvD,IAAI,SAAUU,GAC7B,MAAO,CAAE0V,IAAKF,EAASxV,EAAEkB,GAAI6G,OAAQT,EAAElG,KAAKpB,GAAG+H,OACjD,IACA,MAEJ,KAIE4N,EAAa,EACVA,EAAaJ,EAAWha,QAAQoa,IAAe,EACtD,IAAIC,EAAW,EAAID,EAAa,EAChCA,GAAc,EACd,IAAI/E,EAAO9S,EAAAA,EAAM,IAAIhC,MAAM8Z,IAAW,WACpC,OAAO,CACT,IAGIR,EAAK,EAqBT,OApBAtX,EAAAA,EAEE2X,EAAaI,SAAQ,SAAUnP,GAC7B,IAAI1L,EAAQ0L,EAAMgP,IAAMC,EACxB/E,EAAK5V,IAAU0L,EAAMqB,OAGrB,IAFA,IAAI+N,EAAY,EAET9a,EAAQ,GAETA,EAAQ,IACV8a,GAAalF,EAAK5V,EAAQ,IAI5B4V,EADA5V,EAASA,EAAQ,GAAM,IACR0L,EAAMqB,OAEvBqN,GAAM1O,EAAMqB,OAAS+N,CACvB,KAGKV,CACT,CCpDA,SAASW,GAAiBC,EAASC,GACjC,IAAIC,EAAgB,CAAC,EA+BrB,OA9BApY,EAAAA,EAAUkY,GAAS,SAAUtP,EAAO4B,GAClC,IAAInG,EAAO+T,EAAcxP,EAAMpH,GAAK,CAClC6W,SAAU,EACVvO,GAAI,GACJC,IAAK,GACLpI,GAAI,CAACiH,EAAMpH,GACXgJ,EAAGA,GAEAxK,EAAAA,EAAc4I,EAAM0P,cAEvBjU,EAAIiU,WAAa1P,EAAM0P,WAEvBjU,EAAI4F,OAASrB,EAAMqB,OAEvB,IAEAjK,EAAAA,EAAUmY,EAAGvU,SAAS,SAAU1B,GAC9B,IAAIqW,EAASH,EAAclW,EAAEV,GACzBgX,EAASJ,EAAclW,EAAEkB,GACxBpD,EAAAA,EAAcuY,IAAYvY,EAAAA,EAAcwY,KAC3CA,EAAOH,WACPE,EAAOxO,IAAId,KAAKmP,EAAclW,EAAEkB,IAEpC,IAUF,SAA4BqV,GAC1B,IAAIP,EAAU,GAEd,SAASQ,EAASC,GAChB,OAAO,SAAUhO,GACXA,EAAOiO,SAIT5Y,EAAAA,EAAc2K,EAAO2N,aACrBtY,EAAAA,EAAc2Y,EAAOL,aACrB3N,EAAO2N,YAAcK,EAAOL,aAiCpC,SAAsBO,EAAQC,GAC5B,IAAIC,EAAM,EACN9O,EAAS,EAET4O,EAAO5O,SACT8O,GAAOF,EAAOP,WAAaO,EAAO5O,OAClCA,GAAU4O,EAAO5O,QAGf6O,EAAO7O,SACT8O,GAAOD,EAAOR,WAAaQ,EAAO7O,OAClCA,GAAU6O,EAAO7O,QAGnB4O,EAAOlX,GAAKmX,EAAOnX,GAAGuD,OAAO2T,EAAOlX,IACpCkX,EAAOP,WAAaS,EAAM9O,EAC1B4O,EAAO5O,OAASA,EAChB4O,EAAOrO,EAAInN,KAAK2b,IAAIF,EAAOtO,EAAGqO,EAAOrO,GACrCsO,EAAOF,QAAS,CAClB,CAlDQK,CAAaN,EAAQhO,EAEzB,CACF,CAEA,SAASuO,EAAUP,GACjB,OAAO,SAAU/N,GACfA,EAAW,GAAE3B,KAAK0P,GACQ,MAApB/N,EAAOyN,UACXI,EAAUxP,KAAK2B,EAEnB,CACF,CAEA,KAAO6N,EAAUhb,QAAQ,CACvB,IAAImL,EAAQ6P,EAAUU,MACtBjB,EAAQjP,KAAKL,GACb5I,EAAAA,EAAU4I,EAAU,GAAEwQ,UAAWV,EAAS9P,IAC1C5I,EAAAA,EAAU4I,EAAMmB,IAAKmP,EAAUtQ,GACjC,CAEA,OAAO5I,EAAAA,EACLA,GAAAA,EAASkY,GAAS,SAAUtP,GAC1B,OAAQA,EAAMgQ,MAChB,KACA,SAAUhQ,GACR,OAAO5I,EAAO4I,EAAO,CAAC,KAAM,IAAK,aAAc,UACjD,GAEJ,CA7CSyQ,CALSrZ,GAAAA,EAASoY,GAAe,SAAUxP,GAEhD,OAAQA,EAAMyP,QAChB,IAGF,CCzDA,SAAStC,GAAKmC,EAASoB,GACrB,IA0CuBC,EA1CnBC,E9BwNN,SAAmBhd,EAAYsR,GAC7B,IAAI/P,EAAS,CAAE0b,IAAK,GAAIC,IAAK,IAQ7B,OAPA1Z,EAAAA,EAAUxD,GAAY,SAAU0B,GAC1B4P,EAAG5P,GACLH,EAAO0b,IAAIxQ,KAAK/K,GAEhBH,EAAO2b,IAAIzQ,KAAK/K,EAEpB,IACOH,CACT,C8BlOcwQ,CAAe2J,GAAS,SAAUtP,GAC5C,OAAOhM,OAAO0C,UAAUC,eAAeC,KAAKoJ,EAAO,aACrD,IACI+Q,EAAWH,EAAMC,IACnBG,EAAa5Z,GAASwZ,EAAME,KAAK,SAAU9Q,GACzC,OAAQA,EAAM4B,CAChB,IACA7I,EAAK,GACLoX,EAAM,EACN9O,EAAS,EACT4P,EAAU,EAEZF,EAAS5D,MA8BcwD,IA9BSD,EA+BzB,SAAUf,EAAQC,GACvB,OAAID,EAAOD,WAAaE,EAAOF,YACrB,EACCC,EAAOD,WAAaE,EAAOF,WAC7B,EAGDiB,EAA6Bf,EAAOhO,EAAI+N,EAAO/N,EAAxC+N,EAAO/N,EAAIgO,EAAOhO,CACnC,IArCAqP,EAAUC,GAAkBnY,EAAIiY,EAAYC,GAE5C7Z,EAAAA,EAAU2Z,GAAU,SAAU/Q,GAC5BiR,GAAWjR,EAAMjH,GAAGlE,OACpBkE,EAAGsH,KAAKL,EAAMjH,IACdoX,GAAOnQ,EAAM0P,WAAa1P,EAAMqB,OAChCA,GAAUrB,EAAMqB,OAChB4P,EAAUC,GAAkBnY,EAAIiY,EAAYC,EAC9C,IAEA,IAAI9b,EAAS,CAAE4D,GAAI3B,EAAAA,EAAU2B,IAK7B,OAJIsI,IACFlM,EAAOua,WAAaS,EAAM9O,EAC1BlM,EAAOkM,OAASA,GAEXlM,CACT,CAEA,SAAS+b,GAAkBnY,EAAIiY,EAAY1c,GAEzC,IADA,IAAI6c,EACGH,EAAWnc,SAAWsc,EAAO/Z,EAAAA,EAAO4Z,IAAapP,GAAKtN,GAC3D0c,EAAWT,MACXxX,EAAGsH,KAAK8Q,EAAKpY,IACbzE,IAEF,OAAOA,CACT,CCvCA,SAAS8c,GAAaxQ,EAAGhI,EAAG2W,EAAImB,GAC9B,IAAIW,EAAUzQ,EAAEpH,SAASZ,GACrBM,EAAO0H,EAAE1H,KAAKN,GACd0Y,EAAKpY,EAAOA,EAAKyT,gBAAapY,EAC9Bgd,EAAKrY,EAAOA,EAAK0T,iBAAcrY,EAC/Bid,EAAY,CAAC,EAEbF,IACFD,EAAUja,GAAAA,EAASia,GAAS,SAAU7W,GACpC,OAAOA,IAAM8W,GAAM9W,IAAM+W,CAC3B,KAGF,IAAIE,EChBN,SAAoB7Q,EAAGyQ,GACrB,OAAOja,EAAAA,EAAMia,GAAS,SAAUzY,GAC9B,IAAIsD,EAAM0E,EAAE5E,QAAQpD,GACpB,GAAKsD,EAAIrH,OAEF,CACL,IAAIM,EAASiC,GAAAA,EACX8E,GACA,SAAUyM,EAAKrP,GACb,IAAIoB,EAAOkG,EAAElG,KAAKpB,GAChBoY,EAAQ9Q,EAAE1H,KAAKI,EAAEV,GACnB,MAAO,CACLuX,IAAKxH,EAAIwH,IAAMzV,EAAK2G,OAASqQ,EAAM3M,MACnC1D,OAAQsH,EAAItH,OAAS3G,EAAK2G,OAE9B,GACA,CAAE8O,IAAK,EAAG9O,OAAQ,IAGpB,MAAO,CACLzI,EAAGA,EACH8W,WAAYva,EAAOgb,IAAMhb,EAAOkM,OAChCA,OAAQlM,EAAOkM,OAEnB,CApBE,MAAO,CAAEzI,EAAGA,EAqBhB,GACF,CDVoB8W,CAAW9O,EAAGyQ,GAChCja,EAAAA,EAAUqa,GAAa,SAAUzR,GAC/B,GAAIY,EAAEpH,SAASwG,EAAMpH,GAAG/D,OAAQ,CAC9B,IAAI8c,EAAiBP,GAAaxQ,EAAGZ,EAAMpH,EAAG2W,EAAImB,GAClDc,EAAUxR,EAAMpH,GAAK+Y,EACjB3d,OAAO0C,UAAUC,eAAeC,KAAK+a,EAAgB,gBA0CrC1B,EAzCDjQ,EAyCSzC,EAzCFoU,EA0CzBva,EAAAA,EAAc6Y,EAAOP,aAMxBO,EAAOP,WAAanS,EAAMmS,WAC1BO,EAAO5O,OAAS9D,EAAM8D,SANtB4O,EAAOP,YACJO,EAAOP,WAAaO,EAAO5O,OAAS9D,EAAMmS,WAAanS,EAAM8D,SAC7D4O,EAAO5O,OAAS9D,EAAM8D,QACzB4O,EAAO5O,QAAU9D,EAAM8D,QA5CvB,CAuCJ,IAA0B4O,EAAQ1S,CAtChC,IAEA,IAAI+R,EAAUD,GAAiBoC,EAAalC,IAuB9C,SAAyBD,EAASkC,GAChCpa,EAAAA,EAAUkY,GAAS,SAAUtP,GAC3BA,EAAMjH,GAAK3B,EAAAA,EACT4I,EAAMjH,GAAGwD,KAAI,SAAU3D,GACrB,OAAI4Y,EAAU5Y,GACL4Y,EAAU5Y,GAAGG,GAEfH,CACT,IAEJ,GACF,CAjCEgZ,CAAgBtC,EAASkC,GAEzB,IAAIrc,EAASgY,GAAKmC,EAASoB,GAE3B,GAAIY,IACFnc,EAAO4D,GAAK3B,EAAAA,EAAU,CAACka,EAAInc,EAAO4D,GAAIwY,IAClC3Q,EAAE9G,aAAawX,GAAIzc,QAAQ,CAC7B,IAAIgd,EAASjR,EAAE1H,KAAK0H,EAAE9G,aAAawX,GAAI,IACrCQ,EAASlR,EAAE1H,KAAK0H,EAAE9G,aAAayX,GAAI,IAChCvd,OAAO0C,UAAUC,eAAeC,KAAKzB,EAAQ,gBAChDA,EAAOua,WAAa,EACpBva,EAAOkM,OAAS,GAElBlM,EAAOua,YACJva,EAAOua,WAAava,EAAOkM,OAASwQ,EAAO9M,MAAQ+M,EAAO/M,QAAU5P,EAAOkM,OAAS,GACvFlM,EAAOkM,QAAU,CACnB,CAGF,OAAOlM,CACT,CE1BA,SAAS4P,GAAMnE,GACb,IAAIiE,EAAUc,EAAa/E,GACzBmR,EAAkBC,GAAiBpR,EAAGxJ,EAAQ,EAAGyN,EAAU,GAAI,WAC/DoN,EAAgBD,GAAiBpR,EAAGxJ,EAAQyN,EAAU,GAAI,GAAI,GAAI,YAEhED,EClBC,SAAmBhE,GACxB,IAAIyB,EAAU,CAAC,EACX6P,EAAc9a,GAAAA,EAASwJ,EAAEnI,SAAS,SAAUG,GAC9C,OAAQgI,EAAEpH,SAASZ,GAAG/D,MACxB,IACIgQ,EAAUzN,EACZA,EAAAA,EAAM8a,GAAa,SAAUtZ,GAC3B,OAAOgI,EAAE1H,KAAKN,GAAGkM,IACnB,KAEEqN,EAAS/a,EAAAA,EAAMA,EAAQyN,EAAU,IAAI,WACvC,MAAO,EACT,IAUIuN,EAAYhb,GAAS8a,GAAa,SAAUtZ,GAC9C,OAAOgI,EAAE1H,KAAKN,GAAGkM,IACnB,IAGA,OAFA1N,EAAAA,EAAUgb,GAXV,SAAS9P,EAAI1J,GACX,IAAIxB,EAAAA,EAAMiL,EAASzJ,GAAnB,CACAyJ,EAAQzJ,IAAK,EACb,IAAIM,EAAO0H,EAAE1H,KAAKN,GAClBuZ,EAAOjZ,EAAK4L,MAAMzE,KAAKzH,GACvBxB,EAAAA,EAAUwJ,EAAE5G,WAAWpB,GAAI0J,EAJE,CAK/B,IAOO6P,CACT,CDViBE,CAAUzR,GACzB0R,GAAY1R,EAAGgE,GAKf,IAHA,IACE2N,EADEC,EAASxL,OAAOC,kBAGXrF,EAAI,EAAG6Q,EAAW,EAAGA,EAAW,IAAK7Q,IAAK6Q,EAAU,CAC3DC,GAAiB9Q,EAAI,EAAImQ,EAAkBE,EAAerQ,EAAI,GAAK,GAGnE,IAAI8M,EAAKD,GAAW7N,EADpBgE,EAAWe,EAAsB/E,IAE7B8N,EAAK8D,IACPC,EAAW,EACXF,EAAOnb,GAAYwN,GACnB4N,EAAS9D,EAEb,CAEA4D,GAAY1R,EAAG2R,EACjB,CAEA,SAASP,GAAiBpR,EAAG+R,EAAOnG,GAClC,OAAOpV,EAAAA,EAAMub,GAAO,SAAU7N,GAC5B,OAAOyH,GAAgB3L,EAAGkE,EAAM0H,EAClC,GACF,CAEA,SAASkG,GAAiBE,EAAalC,GACrC,IAAInB,EAAK,IAAInZ,EAAAA,EACbgB,EAAAA,EAAUwb,GAAa,SAAUC,GAC/B,IAAIxP,EAAOwP,EAAGza,QAAQiL,KAClByP,EAAS1B,GAAayB,EAAIxP,EAAMkM,EAAImB,GACxCtZ,EAAAA,EAAU0b,EAAO/Z,IAAI,SAAUH,EAAGgJ,GAChCiR,EAAG3Z,KAAKN,GAAGmM,MAAQnD,CACrB,IE7DJ,SAAgChB,EAAG2O,EAAIxW,GACrC,IACEga,EADErN,EAAO,CAAC,EAGZtO,EAAAA,EAAU2B,GAAI,SAAUH,GAItB,IAHA,IACEe,EACAqZ,EAFEvZ,EAAQmH,EAAEjH,OAAOf,GAGda,GAAO,CASZ,IARAE,EAASiH,EAAEjH,OAAOF,KAEhBuZ,EAAYtN,EAAK/L,GACjB+L,EAAK/L,GAAUF,IAEfuZ,EAAYD,EACZA,EAAWtZ,GAETuZ,GAAaA,IAAcvZ,EAE7B,YADA8V,EAAG9U,QAAQuY,EAAWvZ,GAGxBA,EAAQE,CACV,CACF,GAyBF,CFcIsZ,CAAuBJ,EAAItD,EAAIuD,EAAO/Z,GACxC,GACF,CAEA,SAASuZ,GAAY1R,EAAGgE,GACtBxN,EAAAA,EAAUwN,GAAU,SAAUsO,GAC5B9b,EAAAA,EAAU8b,GAAO,SAAUta,EAAGgJ,GAC5BhB,EAAE1H,KAAKN,GAAGmM,MAAQnD,CACpB,GACF,GACF,CGxEA,SAASuR,GAAkBvS,GACzB,IAAIwS,EAoEN,SAAmBxS,GACjB,IAAIzL,EAAS,CAAC,EACVmV,EAAM,EAEV,SAAShI,EAAI1J,GACX,IAAIyR,EAAMC,EACVlT,EAAAA,EAAUwJ,EAAEpH,SAASZ,GAAI0J,GACzBnN,EAAOyD,GAAK,CAAEyR,IAAKA,EAAKC,IAAKA,IAC/B,CAGA,OAFAlT,EAAAA,EAAUwJ,EAAEpH,WAAY8I,GAEjBnN,CACT,CAhFsB0T,CAAUjI,GAE9BxJ,EAAAA,EAAUwJ,EAAExI,QAAQmO,aAAa,SAAU3N,GAUzC,IATA,IAAIM,EAAO0H,EAAE1H,KAAKN,GACd0C,EAAUpC,EAAKoC,QACf+X,EAsCR,SAAkBzS,EAAGwS,EAAexa,EAAG4B,GACrC,IAIIb,EACA2Z,EALAC,EAAQ,GACRC,EAAQ,GACRnJ,EAAM5V,KAAK2b,IAAIgD,EAAcxa,GAAGyR,IAAK+I,EAAc5Y,GAAG6P,KACtDC,EAAM7V,KAAKC,IAAI0e,EAAcxa,GAAG0R,IAAK8I,EAAc5Y,GAAG8P,KAK1D3Q,EAASf,EACT,GACEe,EAASiH,EAAEjH,OAAOA,GAClB4Z,EAAMlT,KAAK1G,SACJA,IAAWyZ,EAAczZ,GAAQ0Q,IAAMA,GAAOC,EAAM8I,EAAczZ,GAAQ2Q,MACnFgJ,EAAM3Z,EAGNA,EAASa,EACT,MAAQb,EAASiH,EAAEjH,OAAOA,MAAa2Z,GACrCE,EAAMnT,KAAK1G,GAGb,MAAO,CAAEmE,KAAMyV,EAAMjX,OAAOkX,EAAMhD,WAAY8C,IAAKA,EACrD,CA7DmBG,CAAS7S,EAAGwS,EAAe9X,EAAQ1C,EAAG0C,EAAQd,GACzDsD,EAAOuV,EAASvV,KAChBwV,EAAMD,EAASC,IACfI,EAAU,EACVC,EAAQ7V,EAAK4V,GACbE,GAAY,EAEThb,IAAM0C,EAAQd,GAAG,CAGtB,GAFAtB,EAAO0H,EAAE1H,KAAKN,GAEVgb,EAAW,CACb,MAAQD,EAAQ7V,EAAK4V,MAAcJ,GAAO1S,EAAE1H,KAAKya,GAAO9O,QAAU3L,EAAK4L,MACrE4O,IAGEC,IAAUL,IACZM,GAAY,EAEhB,CAEA,IAAKA,EAAW,CACd,KACEF,EAAU5V,EAAKjJ,OAAS,GACxB+L,EAAE1H,KAAMya,EAAQ7V,EAAK4V,EAAU,IAAKhH,SAAWxT,EAAK4L,MAEpD4O,IAEFC,EAAQ7V,EAAK4V,EACf,CAEA9S,EAAElH,UAAUd,EAAG+a,GACf/a,EAAIgI,EAAE5G,WAAWpB,GAAG,EACtB,CACF,GACF,C,eCTA,SAJA,SAAgBiF,EAAQ3J,GACtB,OAAO2J,IAAUsF,EAAAA,EAAAA,GAAWtF,GAAQgW,EAAAA,GAAAA,GAAa3f,GACnD,E,0BCKA,SANA,SAAe2J,EAAQ3J,GACrB,OAAiB,MAAV2J,EACHA,GACAiW,EAAAA,GAAAA,GAAQjW,GAAQgW,EAAAA,GAAAA,GAAa3f,GAAW6f,GAAAA,EAC9C,ECGA,SAASC,GAAmBpT,EAAGgE,GAC7B,IAAIqP,EAAY,CAAC,EAoCjB,OADA7c,GAAAA,EAASwN,GAjCT,SAAoBsP,EAAWhB,GAC7B,IAEEiB,EAAK,EAGLC,EAAU,EACVC,EAAkBH,EAAUrf,OAC5Byf,EAAWld,EAAAA,EAAO8b,GAsBpB,OApBA9b,EAAAA,EAAU8b,GAAO,SAAUta,EAAGgJ,GAC5B,IAAIpH,EAsEV,SAAmCoG,EAAGhI,GACpC,GAAIgI,EAAE1H,KAAKN,GAAG+K,MACZ,OAAOvM,GAAAA,EAAOwJ,EAAE9G,aAAalB,IAAI,SAAUqD,GACzC,OAAO2E,EAAE1H,KAAK+C,GAAG0H,KACnB,GAEJ,CA5Ec4Q,CAA0B3T,EAAGhI,GACnC4b,EAAKha,EAAIoG,EAAE1H,KAAKsB,GAAGuK,MAAQsP,GAEzB7Z,GAAK5B,IAAM0b,KACbld,EAAAA,EAAU8b,EAAMpW,MAAMsX,EAASxS,EAAI,IAAI,SAAU6S,GAC/Crd,EAAAA,EAAUwJ,EAAE9G,aAAa2a,IAAW,SAAUxY,GAC5C,IAAIyY,EAAS9T,EAAE1H,KAAK+C,GAClB0Y,EAAOD,EAAO3P,QACX4P,EAAOR,GAAMK,EAAKG,IAAWD,EAAO/Q,OAAS/C,EAAE1H,KAAKub,GAAU9Q,OACjEiR,GAAYX,EAAWhY,EAAGwY,EAE9B,GACF,IAEAL,EAAUxS,EAAI,EACduS,EAAKK,EAET,IAEOtB,CACT,IAGOe,CACT,CAsDA,SAASW,GAAYX,EAAWrb,EAAG4B,GACjC,GAAI5B,EAAI4B,EAAG,CACT,IAAIiB,EAAM7C,EACVA,EAAI4B,EACJA,EAAIiB,CACN,CAEA,IAAIoZ,EAAaZ,EAAUrb,GACtBic,IACHZ,EAAUrb,GAAKic,EAAa,CAAC,GAE/BA,EAAWra,IAAK,CAClB,CAEA,SAASsa,GAAYb,EAAWrb,EAAG4B,GACjC,GAAI5B,EAAI4B,EAAG,CACT,IAAIiB,EAAM7C,EACVA,EAAI4B,EACJA,EAAIiB,CACN,CACA,QAASwY,EAAUrb,IAAM5E,OAAO0C,UAAUC,eAAeC,KAAKqd,EAAUrb,GAAI4B,EAC9E,CAkDA,SAASua,GAAqBnU,EAAGgE,EAAUvB,EAAM2R,EAAOC,GAMtD,IAAIC,EAAK,CAAC,EACRC,EAkDJ,SAAyBvU,EAAGgE,EAAUvB,EAAM4R,GAC1C,IAAIG,EAAa,IAAIhf,EAAAA,EACnBif,EAAazU,EAAExI,QACfkd,EAgHJ,SAAa9J,EAAS+J,EAASN,GAC7B,OAAO,SAAUrU,EAAGhI,EAAG4B,GACrB,IAGI4M,EAHAmD,EAAS3J,EAAE1H,KAAKN,GAChB4R,EAAS5J,EAAE1H,KAAKsB,GAChB2V,EAAM,EAIV,GADAA,GAAO5F,EAAOhG,MAAQ,EAClBvQ,OAAO0C,UAAUC,eAAeC,KAAK2T,EAAQ,YAC/C,OAAQA,EAAO3D,SAASb,eACtB,IAAK,IACHqB,GAASmD,EAAOhG,MAAQ,EACxB,MACF,IAAK,IACH6C,EAAQmD,EAAOhG,MAAQ,EAa7B,GATI6C,IACF+I,GAAO8E,EAAa7N,GAASA,GAE/BA,EAAQ,EAER+I,IAAQ5F,EAAO5G,MAAQ4R,EAAU/J,GAAW,EAC5C2E,IAAQ3F,EAAO7G,MAAQ4R,EAAU/J,GAAW,EAE5C2E,GAAO3F,EAAOjG,MAAQ,EAClBvQ,OAAO0C,UAAUC,eAAeC,KAAK4T,EAAQ,YAC/C,OAAQA,EAAO5D,SAASb,eACtB,IAAK,IACHqB,EAAQoD,EAAOjG,MAAQ,EACvB,MACF,IAAK,IACH6C,GAASoD,EAAOjG,MAAQ,EAS9B,OALI6C,IACF+I,GAAO8E,EAAa7N,GAASA,GAE/BA,EAAQ,EAED+I,CACT,CACF,CA5JYqF,CAAIH,EAAWI,QAASJ,EAAWK,QAAST,GAgBtD,OAdA7d,EAAAA,EAAUwN,GAAU,SAAUsO,GAC5B,IAAIjX,EACJ7E,EAAAA,EAAU8b,GAAO,SAAUta,GACzB,IAAI+c,EAAQtS,EAAKzK,GAEjB,GADAwc,EAAWnc,QAAQ0c,GACf1Z,EAAG,CACL,IAAI2Z,EAAQvS,EAAKpH,GACf4Z,EAAUT,EAAW1a,KAAKkb,EAAOD,GACnCP,EAAW3a,QAAQmb,EAAOD,EAAOlhB,KAAKC,IAAI4gB,EAAM1U,EAAGhI,EAAGqD,GAAI4Z,GAAW,GACvE,CACA5Z,EAAIrD,CACN,GACF,IAEOwc,CACT,CAtEaU,CAAgBlV,EAAGgE,EAAUvB,EAAM4R,GAC5CxP,EAAawP,EAAa,aAAe,cAE3C,SAASc,EAAQC,EAAWC,GAI1B,IAHA,IAAI7T,EAAQ+S,EAAO1c,QACfyd,EAAO9T,EAAMmO,MACblO,EAAU,CAAC,EACR6T,GACD7T,EAAQ6T,GACVF,EAAUE,IAEV7T,EAAQ6T,IAAQ,EAChB9T,EAAM/B,KAAK6V,GACX9T,EAAQA,EAAM9F,OAAO2Z,EAAcC,KAGrCA,EAAO9T,EAAMmO,KAEjB,CA6BA,OARAwF,GAlBA,SAAeG,GACbhB,EAAGgB,GAAQf,EAAOnZ,QAAQka,GAAMC,QAAO,SAAUxN,EAAKrP,GACpD,OAAO7E,KAAKC,IAAIiU,EAAKuM,EAAG5b,EAAEV,GAAKuc,EAAOza,KAAKpB,GAC7C,GAAG,EACL,GAce6b,EAAOrb,aAAa4O,KAAKyM,IACxCY,GAZA,SAAeG,GACb,IAAI9F,EAAM+E,EAAOhZ,SAAS+Z,GAAMC,QAAO,SAAUxN,EAAKrP,GACpD,OAAO7E,KAAK2b,IAAIzH,EAAKuM,EAAG5b,EAAEkB,GAAK2a,EAAOza,KAAKpB,GAC7C,GAAG0N,OAAOC,mBAEN/N,EAAO0H,EAAE1H,KAAKgd,GACd9F,IAAQpJ,OAAOC,mBAAqB/N,EAAKuM,aAAeA,IAC1DyP,EAAGgB,GAAQzhB,KAAKC,IAAIwgB,EAAGgB,GAAO9F,GAElC,GAGe+E,EAAOnb,WAAW0O,KAAKyM,IAGtC/d,EAAAA,EAAU4d,GAAO,SAAUpc,GACzBsc,EAAGtc,GAAKsc,EAAG7R,EAAKzK,GAClB,IAEOsc,CACT,CAqFA,SAASkB,GAAUxV,GACjB,IAIIyV,EAJAzR,EAAWe,EAAsB/E,GACjCqT,EAAY7c,EAAAA,EAAQ4c,GAAmBpT,EAAGgE,GAzQhD,SAA4BhE,EAAGgE,GAC7B,IAAIqP,EAAY,CAAC,EAEjB,SAASqC,EAAKC,EAAOzH,EAAU0H,EAAUC,EAAiBC,GACxD,IAAI9d,EACJxB,EAAAA,EAAUA,EAAQ0X,EAAU0H,IAAW,SAAU5U,GAC/ChJ,EAAI2d,EAAM3U,GACNhB,EAAE1H,KAAKN,GAAG+K,OACZvM,EAAAA,EAAUwJ,EAAE9G,aAAalB,IAAI,SAAUqD,GACrC,IAAI0a,EAAQ/V,EAAE1H,KAAK+C,GACf0a,EAAMhT,QAAUgT,EAAM5R,MAAQ0R,GAAmBE,EAAM5R,MAAQ2R,IACjE9B,GAAYX,EAAWhY,EAAGrD,EAE9B,GAEJ,GACF,CAyBA,OADAxB,GAAAA,EAASwN,GAtBT,SAAoBgS,EAAOL,GACzB,IACEM,EADEC,GAAgB,EAElBhI,EAAW,EAgBb,OAdA1X,EAAAA,EAAUmf,GAAO,SAAU3d,EAAGme,GAC5B,GAAwB,WAApBnW,EAAE1H,KAAKN,GAAG+K,MAAoB,CAChC,IAAI7J,EAAe8G,EAAE9G,aAAalB,GAC9BkB,EAAajF,SACfgiB,EAAejW,EAAE1H,KAAKY,EAAa,IAAIiL,MACvCuR,EAAKC,EAAOzH,EAAUiI,EAAgBD,EAAcD,GAEpD/H,EAAWiI,EACXD,EAAeD,EAEnB,CACAP,EAAKC,EAAOzH,EAAUyH,EAAM1hB,OAAQgiB,EAAcD,EAAM/hB,OAC1D,IAEO0hB,CACT,IAGOtC,CACT,CA+N2D+C,CAAmBpW,EAAGgE,IAE3EqS,EAAM,CAAC,EAEX7f,EAAAA,EAAU,CAAC,IAAK,MAAM,SAAU8f,GAC9Bb,EAA4B,MAATa,EAAetS,EAAWxN,GAAAA,EAASwN,GAAU4L,UAChEpZ,EAAAA,EAAU,CAAC,IAAK,MAAM,SAAU+f,GAChB,MAAVA,IACFd,EAAmBjf,EAAAA,EAAMif,GAAkB,SAAUe,GACnD,OAAOhgB,GAAAA,EAASggB,GAAO5G,SACzB,KAGF,IAAI6G,GAAuB,MAATH,EAAetW,EAAE9G,aAAe8G,EAAE5G,YAAY0O,KAAK9H,GACjEoU,EApMV,SAA2BpU,EAAGgE,EAAUqP,EAAWoD,GACjD,IAAIhU,EAAO,CAAC,EACV2R,EAAQ,CAAC,EACThG,EAAM,CAAC,EAkCT,OA7BA5X,EAAAA,EAAUwN,GAAU,SAAUsO,GAC5B9b,EAAAA,EAAU8b,GAAO,SAAUta,EAAGmM,GAC5B1B,EAAKzK,GAAKA,EACVoc,EAAMpc,GAAKA,EACXoW,EAAIpW,GAAKmM,CACX,GACF,IAEA3N,EAAAA,EAAUwN,GAAU,SAAUsO,GAC5B,IAAIoE,GAAW,EACflgB,EAAAA,EAAU8b,GAAO,SAAUta,GACzB,IAAI2e,EAAKF,EAAWze,GACpB,GAAI2e,EAAG1iB,OAAQ,CACb0iB,EAAKngB,GAASmgB,GAAI,SAAU/c,GAC1B,OAAOwU,EAAIxU,EACb,IAEA,IADA,IAAIgd,GAAMD,EAAG1iB,OAAS,GAAK,EAClB+M,EAAInN,KAAKgjB,MAAMD,GAAKE,EAAKjjB,KAAKyK,KAAKsY,GAAK5V,GAAK8V,IAAM9V,EAAG,CAC7D,IAAIpH,EAAI+c,EAAG3V,GACPoT,EAAMpc,KAAOA,GAAK0e,EAAUtI,EAAIxU,KAAOsa,GAAYb,EAAWrb,EAAG4B,KACnEwa,EAAMxa,GAAK5B,EACXoc,EAAMpc,GAAKyK,EAAKzK,GAAKyK,EAAK7I,GAC1B8c,EAAUtI,EAAIxU,GAElB,CACF,CACF,GACF,IAEO,CAAE6I,KAAMA,EAAM2R,MAAOA,EAC9B,CA8JkB2C,CAAkB/W,EAAGyV,EAAkBpC,EAAWoD,GAC1DnC,EAAKH,GAAqBnU,EAAGyV,EAAkBrB,EAAM3R,KAAM2R,EAAMA,MAAiB,MAAVmC,GAC9D,MAAVA,IACFjC,EAAK9d,EAAY8d,GAAI,SAAU/Q,GAC7B,OAAQA,CACV,KAEF8S,EAAIC,EAAOC,GAASjC,CACtB,GACF,IAEA,IAAI0C,EArFN,SAAoChX,EAAGqW,GACrC,OAAO7f,GAAQA,GAAAA,EAAS6f,IAAM,SAAU/B,GACtC,IAAIxgB,EAAMsS,OAAO6Q,kBACbzH,EAAMpJ,OAAOC,kBASjB,OAPA7P,GAAQ8d,GAAI,SAAU/Q,EAAGvL,GACvB,IAAIkf,EAkIV,SAAelX,EAAGhI,GAChB,OAAOgI,EAAE1H,KAAKN,GAAG2L,KACnB,CApIsBA,CAAM3D,EAAGhI,GAAK,EAE9BlE,EAAMD,KAAKC,IAAIyP,EAAI2T,EAAWpjB,GAC9B0b,EAAM3b,KAAK2b,IAAIjM,EAAI2T,EAAW1H,EAChC,IAEO1b,EAAM0b,CACf,GACF,CAuEsB2H,CAA2BnX,EAAGqW,GAElD,OAhEF,SAA0BA,EAAKe,GAC7B,IAAIC,EAAc7gB,GAAAA,EAAS4gB,GACzBE,EAAa9gB,EAAAA,EAAM6gB,GACnBE,EAAa/gB,EAAM6gB,GAErB7gB,EAAAA,EAAU,CAAC,IAAK,MAAM,SAAU8f,GAC9B9f,EAAAA,EAAU,CAAC,IAAK,MAAM,SAAU+f,GAC9B,IAEE/P,EAFEgR,EAAYlB,EAAOC,EACrBjC,EAAK+B,EAAImB,GAEX,GAAIlD,IAAO8C,EAAX,CAEA,IAAIK,EAASjhB,GAAAA,EAAS8d,IACtB9N,EAAkB,MAAV+P,EAAgBe,EAAa9gB,EAAAA,EAAMihB,GAAUF,EAAa/gB,EAAMihB,MAGtEpB,EAAImB,GAAahhB,EAAY8d,GAAI,SAAU/Q,GACzC,OAAOA,EAAIiD,CACb,IARwB,CAU5B,GACF,GACF,CAyCEkR,CAAiBrB,EAAKW,GAvCxB,SAAiBX,EAAKjC,GACpB,OAAO5d,EAAY6f,EAAIsB,IAAI,SAAUC,EAAQ5f,GAC3C,GAAIoc,EACF,OAAOiC,EAAIjC,EAAMjP,eAAenN,GAEhC,IAAIsc,EAAK9d,GAASA,EAAAA,EAAM6f,EAAKre,IAC7B,OAAQsc,EAAG,GAAKA,EAAG,IAAM,CAE7B,GACF,CA+BSuD,CAAQxB,EAAKrW,EAAExI,QAAQ4c,MAChC,CC9WA,SAAS0D,GAAS9X,IASlB,SAAmBA,GACjB,IAAIgE,EAAWe,EAAsB/E,GACjC+X,EAAU/X,EAAExI,QAAQwgB,QACpBC,EAAQ,EACZzhB,EAAAA,EAAUwN,GAAU,SAAUsO,GAC5B,IAAI4F,EAAY1hB,EACdA,EAAAA,EAAM8b,GAAO,SAAUta,GACrB,OAAOgI,EAAE1H,KAAKN,GAAG6L,MACnB,KAEFrN,EAAAA,EAAU8b,GAAO,SAAUta,GACzBgI,EAAE1H,KAAKN,GAAGwL,EAAIyU,EAAQC,EAAY,CACpC,IACAD,GAASC,EAAYH,CACvB,GACF,EArBEI,CAFAnY,EAAI+E,EAAwB/E,IAG5BxJ,GAASgf,GAAUxV,IAAI,SAAUuD,EAAGvL,GAClCgI,EAAE1H,KAAKN,GAAGuL,EAAIA,CAChB,GACF,CCEA,SAAS6U,GAAOpY,EAAGtK,GACjB,IAAI2O,EAAO3O,GAAQA,EAAK2iB,YAActT,EAAYA,GAClDV,EAAK,UAAU,KACb,IAAIiU,EAAcjU,EAAK,sBAAsB,IA+FjD,SAA0BkU,GACxB,IAAIvY,EAAI,IAAIxK,EAAAA,EAAM,CAAEW,YAAY,EAAME,UAAU,IAC5CmB,EAAQghB,GAAaD,EAAW/gB,SAoBpC,OAlBAwI,EAAE1I,SACAd,EAAAA,EAAQ,CAAC,EAAGiiB,GAAeC,GAAkBlhB,EAAOmhB,IAAgBniB,EAAOgB,EAAOohB,MAGpFpiB,EAAAA,EAAU+hB,EAAW1gB,SAAS,SAAUG,GACtC,IAAIM,EAAOkgB,GAAaD,EAAWjgB,KAAKN,IACxCgI,EAAE3H,QAAQL,EAAGxB,EAAAA,EAAWkiB,GAAkBpgB,EAAMugB,IAAeC,KAC/D9Y,EAAElH,UAAUd,EAAGugB,EAAWxf,OAAOf,GACnC,IAEAxB,EAAAA,EAAU+hB,EAAWne,SAAS,SAAU1B,GACtC,IAAIoB,EAAO0e,GAAaD,EAAWze,KAAKpB,IACxCsH,EAAEnG,QACAnB,EACAlC,EAAAA,EAAQ,CAAC,EAAGuiB,GAAcL,GAAkB5e,EAAMkf,IAAexiB,EAAOsD,EAAMmf,KAElF,IAEOjZ,CACT,CAtHuDkZ,CAAiBlZ,KACpEqE,EAAK,eAAe,IAKxB,SAAmBrE,EAAGqE,GACpBA,EAAK,8BAA8B,IAyHrC,SAAgCrE,GAC9B,IAAIxI,EAAQwI,EAAExI,QACdA,EAAMwgB,SAAW,EACjBxhB,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAAIoB,EAAOkG,EAAElG,KAAKpB,GAClBoB,EAAKqM,QAAU,EACqB,MAAhCrM,EAAKkM,SAASb,gBACM,OAAlB3N,EAAM0N,SAAsC,OAAlB1N,EAAM0N,QAClCpL,EAAK6J,OAAS7J,EAAKqf,YAEnBrf,EAAK+J,QAAU/J,EAAKqf,YAG1B,GACF,CAvI2CC,CAAuBpZ,KAChEqE,EAAK,uBAAuB,IAiT9B,SAAyBrE,GACvBxJ,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,GAAIA,EAAEV,IAAMU,EAAEkB,EAAG,CACf,IAAItB,EAAO0H,EAAE1H,KAAKI,EAAEV,GACfM,EAAK+gB,YACR/gB,EAAK+gB,UAAY,IAEnB/gB,EAAK+gB,UAAU5Z,KAAK,CAAE/G,EAAGA,EAAGnB,MAAOyI,EAAElG,KAAKpB,KAC1CsH,EAAEvH,WAAWC,EACf,CACF,GACF,CA5ToC4gB,CAAgBtZ,KAClDqE,EAAK,eAAe,IAAMkV,EAAYvZ,KACtCqE,EAAK,wBAAwB,IAAMmV,GAAiBxZ,KACpDqE,EAAK,YAAY,IAAMH,GAAKa,EAAwB/E,MACpDqE,EAAK,8BAA8B,IA0IrC,SAAgCrE,GAC9BxJ,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAAIoB,EAAOkG,EAAElG,KAAKpB,GAClB,GAAIoB,EAAK6J,OAAS7J,EAAK+J,OAAQ,CAC7B,IAAI7L,EAAIgI,EAAE1H,KAAKI,EAAEV,GAEbT,EAAQ,CAAE2M,MADNlE,EAAE1H,KAAKI,EAAEkB,GACMsK,KAAOlM,EAAEkM,MAAQ,EAAIlM,EAAEkM,KAAMxL,EAAGA,GACvDqM,EAAkB/E,EAAG,aAAczI,EAAO,MAC5C,CACF,GACF,CApJ2CkiB,CAAuBzZ,KAChEqE,EAAK,wBAAwB,IzCqI/B,SAA0BrE,GAExB,IAAI0Z,EAASljB,EAAAA,EACXA,EAAAA,EAAMwJ,EAAEnI,SAAS,SAAUG,GACzB,OAAOgI,EAAE1H,KAAKN,GAAGkM,IACnB,KAGEqN,EAAS,GACb/a,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7B,IAAIkM,EAAOlE,EAAE1H,KAAKN,GAAGkM,KAAOwV,EACvBnI,EAAOrN,KACVqN,EAAOrN,GAAQ,IAEjBqN,EAAOrN,GAAMzE,KAAKzH,EACpB,IAEA,IAAIwO,EAAQ,EACRuE,EAAiB/K,EAAExI,QAAQuT,eAC/BvU,EAAAA,EAAU+a,GAAQ,SAAUpZ,EAAI6I,GAC1BxK,EAAAA,EAAc2B,IAAO6I,EAAI+J,IAAmB,IAC5CvE,EACOA,GACThQ,EAAAA,EAAU2B,GAAI,SAAUH,GACtBgI,EAAE1H,KAAKN,GAAGkM,MAAQsC,CACpB,GAEJ,GACF,CyCjKqCzB,CAAsB/E,KACzDqE,EAAK,4BAA4B,IvB8FnC,SAAiBrE,GACf,IAAIyU,EAAazU,EAAExI,QACnBwI,EAAExH,WAAWic,EAAW5J,oBACjB4J,EAAW5J,YAClBrU,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAClBsH,EAAElG,KAAKpB,GACT8S,aACPxL,EAAEvH,WAAWC,EAEjB,GACF,CuBxGyC8gB,CAAqBxZ,KAC5DqE,EAAK,sBAAsB,IzCqH7B,SAAwBrE,GACtB,IAAIwP,EAAMhZ,EAAAA,EACRA,EAAAA,EAAMwJ,EAAEnI,SAAS,SAAUG,GACzB,OAAOgI,EAAE1H,KAAKN,GAAGkM,IACnB,KAEF1N,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7B,IAAIM,EAAO0H,EAAE1H,KAAKN,GACdxB,EAAAA,EAAM8B,EAAM,UACdA,EAAK4L,MAAQsL,EAEjB,GACF,CyCjImCzK,CAAoB/E,KACrDqE,EAAK,wBAAwB,IAkJ/B,SAA0BrE,GACxB,IAAIiE,EAAU,EACdzN,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7B,IAAIM,EAAO0H,EAAE1H,KAAKN,GACdM,EAAK4S,YACP5S,EAAKwT,QAAU9L,EAAE1H,KAAKA,EAAK4S,WAAWhH,KACtC5L,EAAK2L,QAAUjE,EAAE1H,KAAKA,EAAK6S,cAAcjH,KAEzCD,EAAUzN,EAAMyN,EAAS3L,EAAK2L,SAElC,IACAjE,EAAExI,QAAQyM,QAAUA,CACtB,CA9JqC0V,CAAiB3Z,KACpDqE,EAAK,8BAA8B,IA+JrC,SAAgCrE,GAC9BxJ,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7B,IAAIM,EAAO0H,EAAE1H,KAAKN,GACC,eAAfM,EAAKyK,QACP/C,EAAElG,KAAKxB,EAAKI,GAAGqN,UAAYzN,EAAK4L,KAChClE,EAAExH,WAAWR,GAEjB,GACF,CAvK2C4hB,CAAuB5Z,KAChEqE,EAAK,qBAAqB,IAAMwV,GAAc7Z,KAC9CqE,EAAK,yBAAyB,IAAMkO,GAAkBvS,KACtDqE,EAAK,yBAAyB,IxCjChC,SAA2BrE,GAkBzBxJ,EAAAA,EAAUwJ,EAAEpH,YAjBZ,SAAS8I,EAAI1J,GACX,IAAIY,EAAWoH,EAAEpH,SAASZ,GACtBM,EAAO0H,EAAE1H,KAAKN,GAKlB,GAJIY,EAAS3E,QACXuC,EAAAA,EAAUoC,EAAU8I,GAGlBtO,OAAO0C,UAAUC,eAAeC,KAAKsC,EAAM,WAAY,CACzDA,EAAKyT,WAAa,GAClBzT,EAAK0T,YAAc,GACnB,IAAK,IAAI9H,EAAO5L,EAAKwT,QAAS7H,EAAU3L,EAAK2L,QAAU,EAAGC,EAAOD,IAAWC,EAC1EE,GAAcpE,EAAG,aAAc,MAAOhI,EAAGM,EAAM4L,GAC/CE,GAAcpE,EAAG,cAAe,MAAOhI,EAAGM,EAAM4L,EAEpD,CACF,GAGF,CwCcsC4V,CAAkB9Z,KACtDqE,EAAK,aAAa,IAAMF,GAAMnE,KAC9BqE,EAAK,uBAAuB,IAgT9B,SAAyBrE,GACvB,IAAIuR,EAASxM,EAAsB/E,GACnCxJ,EAAAA,EAAU+a,GAAQ,SAAUe,GAC1B,IAAIyH,EAAa,EACjBvjB,EAAAA,EAAU8b,GAAO,SAAUta,EAAGgJ,GAC5B,IAAI1I,EAAO0H,EAAE1H,KAAKN,GAClBM,EAAK6L,MAAQnD,EAAI+Y,EACjBvjB,EAAAA,EAAU8B,EAAK+gB,WAAW,SAAUW,GAClCjV,EACE/E,EACA,WACA,CACE2D,MAAOqW,EAASziB,MAAMoM,MACtBE,OAAQmW,EAASziB,MAAMsM,OACvBK,KAAM5L,EAAK4L,KACXC,MAAOnD,KAAM+Y,EACbrhB,EAAGshB,EAASthB,EACZnB,MAAOyiB,EAASziB,OAElB,MAEJ,WACOe,EAAK+gB,SACd,GACF,GACF,CAzUoCY,CAAgBja,KAClDqE,EAAK,8BAA8B,IvCrCrC,SAAgBrE,GACd,IAAIiF,EAAUjF,EAAExI,QAAQ0N,QAAQC,cAChB,OAAZF,GAAgC,OAAZA,GACtBQ,GAAgBzF,EAEpB,CuCgC2Cka,CAAwBla,KACjEqE,EAAK,gBAAgB,IAAMyT,GAAS9X,KACpCqE,EAAK,yBAAyB,IAwUhC,SAA2BrE,GACzBxJ,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7B,IAAIM,EAAO0H,EAAE1H,KAAKN,GAClB,GAAmB,aAAfM,EAAKyK,MAAsB,CAC7B,IAAIoX,EAAWna,EAAE1H,KAAKA,EAAKI,EAAEV,GACzBuL,EAAI4W,EAAS5W,EAAI4W,EAASxW,MAAQ,EAClCH,EAAI2W,EAAS3W,EACbC,EAAKnL,EAAKiL,EAAIA,EACdG,EAAKyW,EAAStW,OAAS,EAC3B7D,EAAEnG,QAAQvB,EAAKI,EAAGJ,EAAKf,OACvByI,EAAExH,WAAWR,GACbM,EAAKf,MAAM8N,OAAS,CAClB,CAAE9B,EAAGA,EAAK,EAAIE,EAAM,EAAGD,EAAGA,EAAIE,GAC9B,CAAEH,EAAGA,EAAK,EAAIE,EAAM,EAAGD,EAAGA,EAAIE,GAC9B,CAAEH,EAAGA,EAAIE,EAAID,EAAGA,GAChB,CAAED,EAAGA,EAAK,EAAIE,EAAM,EAAGD,EAAGA,EAAIE,GAC9B,CAAEH,EAAGA,EAAK,EAAIE,EAAM,EAAGD,EAAGA,EAAIE,IAEhCpL,EAAKf,MAAMgM,EAAIjL,EAAKiL,EACpBjL,EAAKf,MAAMiM,EAAIlL,EAAKkL,CACtB,CACF,GACF,CA9VsC4W,CAAkBpa,KACtDqE,EAAK,yBAAyB,IAwQhC,SAA2BrE,GACzBxJ,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7B,GAAIgI,EAAEpH,SAASZ,GAAG/D,OAAQ,CACxB,IAAIqE,EAAO0H,EAAE1H,KAAKN,GACdyO,EAAIzG,EAAE1H,KAAKA,EAAK4S,WAChBmP,EAAIra,EAAE1H,KAAKA,EAAK6S,cAChBmP,EAAIta,EAAE1H,KAAK9B,EAAAA,EAAO8B,EAAKyT,aACvBwO,EAAIva,EAAE1H,KAAK9B,EAAAA,EAAO8B,EAAK0T,cAE3B1T,EAAKqL,MAAQ9P,KAAKiQ,IAAIyW,EAAEhX,EAAI+W,EAAE/W,GAC9BjL,EAAKuL,OAAShQ,KAAKiQ,IAAIuW,EAAE7W,EAAIiD,EAAEjD,GAC/BlL,EAAKiL,EAAI+W,EAAE/W,EAAIjL,EAAKqL,MAAQ,EAC5BrL,EAAKkL,EAAIiD,EAAEjD,EAAIlL,EAAKuL,OAAS,CAC/B,CACF,IAEArN,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GACL,WAApBgI,EAAE1H,KAAKN,GAAG+K,OACZ/C,EAAExH,WAAWR,EAEjB,GACF,CA7RsCwiB,CAAkBxa,KACtDqE,EAAK,sBAAsB,ItC4C7B,SAAcrE,GACZxJ,EAAAA,EAAUwJ,EAAExI,QAAQmO,aAAa,SAAU3N,GACzC,IAEI4B,EAFAtB,EAAO0H,EAAE1H,KAAKN,GACdyiB,EAAYniB,EAAKwN,UAGrB,IADA9F,EAAEnG,QAAQvB,EAAKoC,QAAS+f,GACjBniB,EAAKyK,OACVnJ,EAAIoG,EAAE5G,WAAWpB,GAAG,GACpBgI,EAAExH,WAAWR,GACbyiB,EAAUpV,OAAO5F,KAAK,CAAE8D,EAAGjL,EAAKiL,EAAGC,EAAGlL,EAAKkL,IACxB,eAAflL,EAAKyK,QACP0X,EAAUlX,EAAIjL,EAAKiL,EACnBkX,EAAUjX,EAAIlL,EAAKkL,EACnBiX,EAAU9W,MAAQrL,EAAKqL,MACvB8W,EAAU5W,OAASvL,EAAKuL,QAE1B7L,EAAI4B,EACJtB,EAAO0H,EAAE1H,KAAKN,EAElB,GACF,CsChEmC6hB,CAAe7Z,KAChDqE,EAAK,4BAA4B,IA0OnC,SAA8BrE,GAC5BxJ,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAAIoB,EAAOkG,EAAElG,KAAKpB,GAClB,GAAItF,OAAO0C,UAAUC,eAAeC,KAAK8D,EAAM,KAI7C,OAHsB,MAAlBA,EAAKkM,UAAsC,MAAlBlM,EAAKkM,WAChClM,EAAK6J,OAAS7J,EAAKqf,aAEbrf,EAAKkM,UACX,IAAK,IACHlM,EAAKyJ,GAAKzJ,EAAK6J,MAAQ,EAAI7J,EAAKqf,YAChC,MACF,IAAK,IACHrf,EAAKyJ,GAAKzJ,EAAK6J,MAAQ,EAAI7J,EAAKqf,YAIxC,GACF,CA3PyCuB,CAAqB1a,KAC5DqE,EAAK,4BAA4B,IAAM6V,GAAsBla,KAC7DqE,EAAK,sBAAsB,IA4J7B,SAAwBrE,GACtB,IAAI2a,EAAOvU,OAAOC,kBACduU,EAAO,EACPC,EAAOzU,OAAOC,kBACdyU,EAAO,EACPrG,EAAazU,EAAExI,QACfujB,EAAUtG,EAAWuG,SAAW,EAChCC,EAAUxG,EAAWyG,SAAW,EAEpC,SAASC,EAAYrY,GACnB,IAAIS,EAAIT,EAAMS,EACVC,EAAIV,EAAMU,EACV5J,EAAIkJ,EAAMa,MACVC,EAAId,EAAMe,OACd8W,EAAO9mB,KAAK2b,IAAImL,EAAMpX,EAAI3J,EAAI,GAC9BghB,EAAO/mB,KAAKC,IAAI8mB,EAAMrX,EAAI3J,EAAI,GAC9BihB,EAAOhnB,KAAK2b,IAAIqL,EAAMrX,EAAII,EAAI,GAC9BkX,EAAOjnB,KAAKC,IAAIgnB,EAAMtX,EAAII,EAAI,EAChC,CAEApN,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7BmjB,EAAYnb,EAAE1H,KAAKN,GACrB,IACAxB,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAAIoB,EAAOkG,EAAElG,KAAKpB,GACdtF,OAAO0C,UAAUC,eAAeC,KAAK8D,EAAM,MAC7CqhB,EAAYrhB,EAEhB,IAEA6gB,GAAQI,EACRF,GAAQI,EAERzkB,EAAAA,EAAUwJ,EAAEnI,SAAS,SAAUG,GAC7B,IAAIM,EAAO0H,EAAE1H,KAAKN,GAClBM,EAAKiL,GAAKoX,EACVriB,EAAKkL,GAAKqX,CACZ,IAEArkB,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAAIoB,EAAOkG,EAAElG,KAAKpB,GAClBlC,EAAAA,EAAUsD,EAAKuL,QAAQ,SAAU+V,GAC/BA,EAAE7X,GAAKoX,EACPS,EAAE5X,GAAKqX,CACT,IACIznB,OAAO0C,UAAUC,eAAeC,KAAK8D,EAAM,OAC7CA,EAAKyJ,GAAKoX,GAERvnB,OAAO0C,UAAUC,eAAeC,KAAK8D,EAAM,OAC7CA,EAAK0J,GAAKqX,EAEd,IAEApG,EAAW9Q,MAAQiX,EAAOD,EAAOI,EACjCtG,EAAW5Q,OAASiX,EAAOD,EAAOI,CACpC,CAnNmCI,CAAerb,KAChDqE,EAAK,4BAA4B,IAoNnC,SAA8BrE,GAC5BxJ,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAGI4iB,EAAIC,EAHJzhB,EAAOkG,EAAElG,KAAKpB,GACd8iB,EAAQxb,EAAE1H,KAAKI,EAAEV,GACjByjB,EAAQzb,EAAE1H,KAAKI,EAAEkB,GAEhBE,EAAKuL,QAKRiW,EAAKxhB,EAAKuL,OAAO,GACjBkW,EAAKzhB,EAAKuL,OAAOvL,EAAKuL,OAAOpR,OAAS,KALtC6F,EAAKuL,OAAS,GACdiW,EAAKG,EACLF,EAAKC,GAKP1hB,EAAKuL,OAAOqW,QAAQ3W,EAAmByW,EAAOF,IAC9CxhB,EAAKuL,OAAO5F,KAAKsF,EAAmB0W,EAAOF,GAC7C,GACF,CArOyCI,CAAqB3b,KAC5DqE,EAAK,qBAAqB,IAyP5B,SAAuCrE,GACrCxJ,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAAIoB,EAAOkG,EAAElG,KAAKpB,GACdoB,EAAK+H,UACP/H,EAAKuL,OAAOuK,SAEhB,GACF,CAhQkCgM,CAA8B5b,KAC9DqE,EAAK,oBAAoB,IjDJ3B,SAAcrE,GACZxJ,EAAAA,EAAUwJ,EAAE5F,SAAS,SAAU1B,GAC7B,IAAInB,EAAQyI,EAAElG,KAAKpB,GACnB,GAAInB,EAAMsK,SAAU,CAClB7B,EAAEvH,WAAWC,GAEb,IAAIkJ,EAAcrK,EAAMqK,mBACjBrK,EAAMsK,gBACNtK,EAAMqK,YACb5B,EAAEnG,QAAQnB,EAAEkB,EAAGlB,EAAEV,EAAGT,EAAOqK,EAC7B,CACF,GACF,CiDRiC2X,CAAavZ,IAC9C,CAjC8B6b,CAAUvD,EAAajU,KACjDA,EAAK,sBAAsB,IAwC/B,SAA0BkU,EAAYD,GACpC9hB,EAAAA,EAAU+hB,EAAW1gB,SAAS,SAAUG,GACtC,IAAI8jB,EAAavD,EAAWjgB,KAAKN,GAC7B+jB,EAAczD,EAAYhgB,KAAKN,GAE/B8jB,IACFA,EAAWvY,EAAIwY,EAAYxY,EAC3BuY,EAAWtY,EAAIuY,EAAYvY,EAEvB8U,EAAY1f,SAASZ,GAAG/D,SAC1B6nB,EAAWnY,MAAQoY,EAAYpY,MAC/BmY,EAAWjY,OAASkY,EAAYlY,QAGtC,IAEArN,EAAAA,EAAU+hB,EAAWne,SAAS,SAAU1B,GACtC,IAAIojB,EAAavD,EAAWze,KAAKpB,GAC7BqjB,EAAczD,EAAYxe,KAAKpB,GAEnCojB,EAAWzW,OAAS0W,EAAY1W,OAC5BjS,OAAO0C,UAAUC,eAAeC,KAAK+lB,EAAa,OACpDD,EAAWvY,EAAIwY,EAAYxY,EAC3BuY,EAAWtY,EAAIuY,EAAYvY,EAE/B,IAEA+U,EAAW/gB,QAAQmM,MAAQ2U,EAAY9gB,QAAQmM,MAC/C4U,EAAW/gB,QAAQqM,OAASyU,EAAY9gB,QAAQqM,MAClD,CArEqCmY,CAAiBhc,EAAGsY,IAAa,GAEtE,CAqEA,IAAIK,GAAgB,CAAC,UAAW,UAAW,UAAW,UAAW,WAC7DF,GAAgB,CAAET,QAAS,GAAIlD,QAAS,GAAID,QAAS,GAAI3P,QAAS,MAClE0T,GAAa,CAAC,YAAa,SAAU,UAAW,SAChDC,GAAe,CAAC,QAAS,UACzBC,GAAe,CAAEnV,MAAO,EAAGE,OAAQ,GACnCmV,GAAe,CAAC,SAAU,SAAU,QAAS,SAAU,eACvDD,GAAe,CACjB5S,OAAQ,EACR1F,OAAQ,EACRkD,MAAO,EACPE,OAAQ,EACRsV,YAAa,GACbnT,SAAU,KAERiT,GAAY,CAAC,YAkSjB,SAASP,GAAkBuD,EAAKnZ,GAC9B,OAAOtM,EAAYA,EAAOylB,EAAKnZ,GAAQsD,OACzC,CAEA,SAASoS,GAAa1V,GACpB,IAAIoZ,EAAW,CAAC,EAIhB,OAHA1lB,EAAAA,EAAUsM,GAAO,SAAU9K,EAAG4D,GAC5BsgB,EAAStgB,EAAEuJ,eAAiBnN,CAC9B,IACOkkB,CACT,C,8EC/YIC,EAAc/oB,OAAO0C,UAGrBC,EAAiBomB,EAAYpmB,eAsDjC,SA/BeZ,EAAAA,EAAAA,IAAS,SAAS8H,EAAQnF,GACvCmF,EAAS7J,OAAO6J,GAEhB,IAAIvJ,GAAS,EACTO,EAAS6D,EAAQ7D,OACjBmoB,EAAQnoB,EAAS,EAAI6D,EAAQ,QAAKnE,EAMtC,IAJIyoB,IAASzd,EAAAA,EAAAA,GAAe7G,EAAQ,GAAIA,EAAQ,GAAIskB,KAClDnoB,EAAS,KAGFP,EAAQO,GAMf,IALA,IAAIqb,EAASxX,EAAQpE,GACjBuY,GAAQkH,EAAAA,EAAAA,GAAO7D,GACf+M,GAAc,EACdC,EAAcrQ,EAAMhY,SAEfooB,EAAaC,GAAa,CACjC,IAAI7oB,EAAMwY,EAAMoQ,GACZ3nB,EAAQuI,EAAOxJ,SAELE,IAAVe,IACC6nB,EAAAA,EAAAA,GAAG7nB,EAAOynB,EAAY1oB,MAAUsC,EAAeC,KAAKiH,EAAQxJ,MAC/DwJ,EAAOxJ,GAAO6b,EAAO7b,GAEzB,CAGF,OAAOwJ,CACT,G,6ECTA,QALA,SAAajK,EAAYM,GAEvB,QADWsB,EAAAA,EAAAA,GAAQ5B,GAAcua,EAAAA,EAAWE,EAAAA,GAChCza,GAAYO,EAAAA,EAAAA,GAAaD,EAAU,GACjD,C,kCCjDA,IAGIyC,EAHc3C,OAAO0C,UAGQC,eAcjC,QAJA,SAAiBkH,EAAQxJ,GACvB,OAAiB,MAAVwJ,GAAkBlH,EAAeC,KAAKiH,EAAQxJ,EACvD,E,cCkBA,QAJA,SAAawJ,EAAQC,GACnB,OAAiB,MAAVD,IAAkBuf,EAAAA,EAAAA,GAAQvf,EAAQC,EAAMuf,EACjD,C", "sources": ["../../node_modules/lodash-es/_createFind.js", "../../node_modules/lodash-es/findIndex.js", "../../node_modules/lodash-es/find.js", "../../node_modules/lodash-es/min.js", "../../node_modules/lodash-es/_baseMap.js", "../../node_modules/lodash-es/flatten.js", "../../node_modules/lodash-es/isString.js", "../../node_modules/lodash-es/_baseExtremum.js", "../../node_modules/lodash-es/union.js", "../../node_modules/dagre-d3-es/src/graphlib/graph.js", "../../node_modules/lodash-es/_trimmedEndIndex.js", "../../node_modules/lodash-es/_baseTrim.js", "../../node_modules/lodash-es/toNumber.js", "../../node_modules/lodash-es/toFinite.js", "../../node_modules/lodash-es/_baseLt.js", "../../node_modules/lodash-es/_baseSet.js", "../../node_modules/lodash-es/_basePickBy.js", "../../node_modules/lodash-es/last.js", "../../node_modules/lodash-es/toInteger.js", "../../node_modules/lodash-es/uniqueId.js", "../../node_modules/lodash-es/_baseRange.js", "../../node_modules/lodash-es/range.js", "../../node_modules/lodash-es/_createRange.js", "../../node_modules/dagre-d3-es/src/dagre/data/list.js", "../../node_modules/dagre-d3-es/src/dagre/greedy-fas.js", "../../node_modules/dagre-d3-es/src/dagre/acyclic.js", "../../node_modules/lodash-es/_basePick.js", "../../node_modules/lodash-es/pick.js", "../../node_modules/lodash-es/_flatRest.js", "../../node_modules/lodash-es/_baseGt.js", "../../node_modules/lodash-es/max.js", "../../node_modules/lodash-es/mapValues.js", "../../node_modules/lodash-es/now.js", "../../node_modules/dagre-d3-es/src/dagre/util.js", "../../node_modules/dagre-d3-es/src/dagre/add-border-segments.js", "../../node_modules/dagre-d3-es/src/dagre/coordinate-system.js", "../../node_modules/dagre-d3-es/src/dagre/normalize.js", "../../node_modules/lodash-es/minBy.js", "../../node_modules/dagre-d3-es/src/dagre/rank/util.js", "../../node_modules/dagre-d3-es/src/dagre/rank/feasible-tree.js", "../../node_modules/dagre-d3-es/src/graphlib/alg/dijkstra.js", "../../node_modules/dagre-d3-es/src/graphlib/alg/floyd-warshall.js", "../../node_modules/lodash-es/_asciiSize.js", "../../node_modules/lodash-es/_hasUnicode.js", "../../node_modules/lodash-es/_unicodeSize.js", "../../node_modules/dagre-d3-es/src/graphlib/alg/topsort.js", "../../node_modules/dagre-d3-es/src/graphlib/alg/dfs.js", "../../node_modules/dagre-d3-es/src/dagre/rank/network-simplex.js", "../../node_modules/dagre-d3-es/src/graphlib/alg/postorder.js", "../../node_modules/dagre-d3-es/src/graphlib/alg/preorder.js", "../../node_modules/dagre-d3-es/src/dagre/rank/index.js", "../../node_modules/dagre-d3-es/src/dagre/nesting-graph.js", "../../node_modules/lodash-es/cloneDeep.js", "../../node_modules/dagre-d3-es/src/dagre/order/build-layer-graph.js", "../../node_modules/lodash-es/_baseZipObject.js", "../../node_modules/lodash-es/zipObject.js", "../../node_modules/lodash-es/_baseSortBy.js", "../../node_modules/lodash-es/_compareAscending.js", "../../node_modules/lodash-es/_compareMultiple.js", "../../node_modules/lodash-es/_baseOrderBy.js", "../../node_modules/lodash-es/sortBy.js", "../../node_modules/dagre-d3-es/src/dagre/order/cross-count.js", "../../node_modules/dagre-d3-es/src/dagre/order/resolve-conflicts.js", "../../node_modules/dagre-d3-es/src/dagre/order/sort.js", "../../node_modules/dagre-d3-es/src/dagre/order/sort-subgraph.js", "../../node_modules/dagre-d3-es/src/dagre/order/barycenter.js", "../../node_modules/dagre-d3-es/src/dagre/order/index.js", "../../node_modules/dagre-d3-es/src/dagre/order/init-order.js", "../../node_modules/dagre-d3-es/src/dagre/order/add-subgraph-constraints.js", "../../node_modules/dagre-d3-es/src/dagre/parent-dummy-chains.js", "../../node_modules/lodash-es/forOwn.js", "../../node_modules/lodash-es/forIn.js", "../../node_modules/dagre-d3-es/src/dagre/position/bk.js", "../../node_modules/dagre-d3-es/src/dagre/position/index.js", "../../node_modules/dagre-d3-es/src/dagre/layout.js", "../../node_modules/lodash-es/defaults.js", "../../node_modules/lodash-es/map.js", "../../node_modules/lodash-es/_baseHas.js", "../../node_modules/lodash-es/has.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport isArrayLike from './isArrayLike.js';\nimport keys from './keys.js';\n\n/**\n * Creates a `_.find` or `_.findLast` function.\n *\n * @private\n * @param {Function} findIndexFunc The function to find the collection index.\n * @returns {Function} Returns the new find function.\n */\nfunction createFind(findIndexFunc) {\n  return function(collection, predicate, fromIndex) {\n    var iterable = Object(collection);\n    if (!isArrayLike(collection)) {\n      var iteratee = baseIteratee(predicate, 3);\n      collection = keys(collection);\n      predicate = function(key) { return iteratee(iterable[key], key, iterable); };\n    }\n    var index = findIndexFunc(collection, predicate, fromIndex);\n    return index > -1 ? iterable[iteratee ? collection[index] : index] : undefined;\n  };\n}\n\nexport default createFind;\n", "import baseFindIndex from './_baseFindIndex.js';\nimport baseIteratee from './_baseIteratee.js';\nimport toInteger from './toInteger.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * This method is like `_.find` except that it returns the index of the first\n * element `predicate` returns truthy for instead of the element itself.\n *\n * @static\n * @memberOf _\n * @since 1.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param {number} [fromIndex=0] The index to search from.\n * @returns {number} Returns the index of the found element, else `-1`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': false },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': true }\n * ];\n *\n * _.findIndex(users, function(o) { return o.user == 'barney'; });\n * // => 0\n *\n * // The `_.matches` iteratee shorthand.\n * _.findIndex(users, { 'user': 'fred', 'active': false });\n * // => 1\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.findIndex(users, ['active', false]);\n * // => 0\n *\n * // The `_.property` iteratee shorthand.\n * _.findIndex(users, 'active');\n * // => 2\n */\nfunction findIndex(array, predicate, fromIndex) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return -1;\n  }\n  var index = fromIndex == null ? 0 : toInteger(fromIndex);\n  if (index < 0) {\n    index = nativeMax(length + index, 0);\n  }\n  return baseFindIndex(array, baseIteratee(predicate, 3), index);\n}\n\nexport default findIndex;\n", "import createFind from './_createFind.js';\nimport findIndex from './findIndex.js';\n\n/**\n * Iterates over elements of `collection`, returning the first element\n * `predicate` returns truthy for. The predicate is invoked with three\n * arguments: (value, index|key, collection).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param {number} [fromIndex=0] The index to search from.\n * @returns {*} Returns the matched element, else `undefined`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'age': 36, 'active': true },\n *   { 'user': 'fred',    'age': 40, 'active': false },\n *   { 'user': 'pebbles', 'age': 1,  'active': true }\n * ];\n *\n * _.find(users, function(o) { return o.age < 40; });\n * // => object for 'barney'\n *\n * // The `_.matches` iteratee shorthand.\n * _.find(users, { 'age': 1, 'active': true });\n * // => object for 'pebbles'\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.find(users, ['active', false]);\n * // => object for 'fred'\n *\n * // The `_.property` iteratee shorthand.\n * _.find(users, 'active');\n * // => object for 'barney'\n */\nvar find = createFind(findIndex);\n\nexport default find;\n", "import baseExtremum from './_baseExtremum.js';\nimport baseLt from './_baseLt.js';\nimport identity from './identity.js';\n\n/**\n * Computes the minimum value of `array`. If `array` is empty or falsey,\n * `undefined` is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {*} Returns the minimum value.\n * @example\n *\n * _.min([4, 2, 8, 6]);\n * // => 2\n *\n * _.min([]);\n * // => undefined\n */\nfunction min(array) {\n  return (array && array.length)\n    ? baseExtremum(array, identity, baseLt)\n    : undefined;\n}\n\nexport default min;\n", "import baseEach from './_baseEach.js';\nimport isArrayLike from './isArrayLike.js';\n\n/**\n * The base implementation of `_.map` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction baseMap(collection, iteratee) {\n  var index = -1,\n      result = isArrayLike(collection) ? Array(collection.length) : [];\n\n  baseEach(collection, function(value, key, collection) {\n    result[++index] = iteratee(value, key, collection);\n  });\n  return result;\n}\n\nexport default baseMap;\n", "import baseFlatten from './_baseFlatten.js';\n\n/**\n * Flattens `array` a single level deep.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flatten([1, [2, [3, [4]], 5]]);\n * // => [1, 2, [3, [4]], 5]\n */\nfunction flatten(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, 1) : [];\n}\n\nexport default flatten;\n", "import baseGetTag from './_baseGetTag.js';\nimport isArray from './isArray.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar stringTag = '[object String]';\n\n/**\n * Checks if `value` is classified as a `String` primitive or object.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a string, else `false`.\n * @example\n *\n * _.isString('abc');\n * // => true\n *\n * _.isString(1);\n * // => false\n */\nfunction isString(value) {\n  return typeof value == 'string' ||\n    (!isArray(value) && isObjectLike(value) && baseGetTag(value) == stringTag);\n}\n\nexport default isString;\n", "import isSymbol from './isSymbol.js';\n\n/**\n * The base implementation of methods like `_.max` and `_.min` which accepts a\n * `comparator` to determine the extremum value.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The iteratee invoked per iteration.\n * @param {Function} comparator The comparator used to compare values.\n * @returns {*} Returns the extremum value.\n */\nfunction baseExtremum(array, iteratee, comparator) {\n  var index = -1,\n      length = array.length;\n\n  while (++index < length) {\n    var value = array[index],\n        current = iteratee(value);\n\n    if (current != null && (computed === undefined\n          ? (current === current && !isSymbol(current))\n          : comparator(current, computed)\n        )) {\n      var computed = current,\n          result = value;\n    }\n  }\n  return result;\n}\n\nexport default baseExtremum;\n", "import baseFlatten from './_baseFlatten.js';\nimport baseRest from './_baseRest.js';\nimport baseUniq from './_baseUniq.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\n\n/**\n * Creates an array of unique values, in order, from all given arrays using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @returns {Array} Returns the new array of combined values.\n * @example\n *\n * _.union([2], [1, 2]);\n * // => [2, 1]\n */\nvar union = baseRest(function(arrays) {\n  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true));\n});\n\nexport default union;\n", "import * as _ from 'lodash-es';\n\nvar DEFAULT_EDGE_NAME = '\\x00';\nvar GRAPH_NODE = '\\x00';\nvar EDGE_KEY_DELIM = '\\x01';\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\nexport class Graph {\n  constructor(opts = {}) {\n    this._isDirected = Object.prototype.hasOwnProperty.call(opts, 'directed')\n      ? opts.directed\n      : true;\n    this._isMultigraph = Object.prototype.hasOwnProperty.call(opts, 'multigraph')\n      ? opts.multigraph\n      : false;\n    this._isCompound = Object.prototype.hasOwnProperty.call(opts, 'compound')\n      ? opts.compound\n      : false;\n\n    // Label for the graph itself\n    this._label = undefined;\n\n    // Defaults to be set when creating a new node\n    this._defaultNodeLabelFn = _.constant(undefined);\n\n    // Defaults to be set when creating a new edge\n    this._defaultEdgeLabelFn = _.constant(undefined);\n\n    // v -> label\n    this._nodes = {};\n\n    if (this._isCompound) {\n      // v -> parent\n      this._parent = {};\n\n      // v -> children\n      this._children = {};\n      this._children[GRAPH_NODE] = {};\n    }\n\n    // v -> edgeObj\n    this._in = {};\n\n    // u -> v -> Number\n    this._preds = {};\n\n    // v -> edgeObj\n    this._out = {};\n\n    // v -> w -> Number\n    this._sucs = {};\n\n    // e -> edgeObj\n    this._edgeObjs = {};\n\n    // e -> label\n    this._edgeLabels = {};\n  }\n  /* === Graph functions ========= */\n  isDirected() {\n    return this._isDirected;\n  }\n  isMultigraph() {\n    return this._isMultigraph;\n  }\n  isCompound() {\n    return this._isCompound;\n  }\n  setGraph(label) {\n    this._label = label;\n    return this;\n  }\n  graph() {\n    return this._label;\n  }\n  /* === Node functions ========== */\n  setDefaultNodeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultNodeLabelFn = newDefault;\n    return this;\n  }\n  nodeCount() {\n    return this._nodeCount;\n  }\n  nodes() {\n    return _.keys(this._nodes);\n  }\n  sources() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._in[v]);\n    });\n  }\n  sinks() {\n    var self = this;\n    return _.filter(this.nodes(), function (v) {\n      return _.isEmpty(self._out[v]);\n    });\n  }\n  setNodes(vs, value) {\n    var args = arguments;\n    var self = this;\n    _.each(vs, function (v) {\n      if (args.length > 1) {\n        self.setNode(v, value);\n      } else {\n        self.setNode(v);\n      }\n    });\n    return this;\n  }\n  setNode(v, value) {\n    if (Object.prototype.hasOwnProperty.call(this._nodes, v)) {\n      if (arguments.length > 1) {\n        this._nodes[v] = value;\n      }\n      return this;\n    }\n\n    // @ts-expect-error\n    this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);\n    if (this._isCompound) {\n      this._parent[v] = GRAPH_NODE;\n      this._children[v] = {};\n      this._children[GRAPH_NODE][v] = true;\n    }\n    this._in[v] = {};\n    this._preds[v] = {};\n    this._out[v] = {};\n    this._sucs[v] = {};\n    ++this._nodeCount;\n    return this;\n  }\n  node(v) {\n    return this._nodes[v];\n  }\n  hasNode(v) {\n    return Object.prototype.hasOwnProperty.call(this._nodes, v);\n  }\n  removeNode(v) {\n    if (Object.prototype.hasOwnProperty.call(this._nodes, v)) {\n      var removeEdge = (e) => this.removeEdge(this._edgeObjs[e]);\n      delete this._nodes[v];\n      if (this._isCompound) {\n        this._removeFromParentsChildList(v);\n        delete this._parent[v];\n        _.each(this.children(v), (child) => {\n          this.setParent(child);\n        });\n        delete this._children[v];\n      }\n      _.each(_.keys(this._in[v]), removeEdge);\n      delete this._in[v];\n      delete this._preds[v];\n      _.each(_.keys(this._out[v]), removeEdge);\n      delete this._out[v];\n      delete this._sucs[v];\n      --this._nodeCount;\n    }\n    return this;\n  }\n  setParent(v, parent) {\n    if (!this._isCompound) {\n      throw new Error('Cannot set parent in a non-compound graph');\n    }\n\n    if (_.isUndefined(parent)) {\n      parent = GRAPH_NODE;\n    } else {\n      // Coerce parent to string\n      parent += '';\n      for (var ancestor = parent; !_.isUndefined(ancestor); ancestor = this.parent(ancestor)) {\n        if (ancestor === v) {\n          throw new Error('Setting ' + parent + ' as parent of ' + v + ' would create a cycle');\n        }\n      }\n\n      this.setNode(parent);\n    }\n\n    this.setNode(v);\n    this._removeFromParentsChildList(v);\n    this._parent[v] = parent;\n    this._children[parent][v] = true;\n    return this;\n  }\n  _removeFromParentsChildList(v) {\n    delete this._children[this._parent[v]][v];\n  }\n  parent(v) {\n    if (this._isCompound) {\n      var parent = this._parent[v];\n      if (parent !== GRAPH_NODE) {\n        return parent;\n      }\n    }\n  }\n  children(v) {\n    if (_.isUndefined(v)) {\n      v = GRAPH_NODE;\n    }\n\n    if (this._isCompound) {\n      var children = this._children[v];\n      if (children) {\n        return _.keys(children);\n      }\n    } else if (v === GRAPH_NODE) {\n      return this.nodes();\n    } else if (this.hasNode(v)) {\n      return [];\n    }\n  }\n  predecessors(v) {\n    var predsV = this._preds[v];\n    if (predsV) {\n      return _.keys(predsV);\n    }\n  }\n  successors(v) {\n    var sucsV = this._sucs[v];\n    if (sucsV) {\n      return _.keys(sucsV);\n    }\n  }\n  neighbors(v) {\n    var preds = this.predecessors(v);\n    if (preds) {\n      return _.union(preds, this.successors(v));\n    }\n  }\n  isLeaf(v) {\n    var neighbors;\n    if (this.isDirected()) {\n      neighbors = this.successors(v);\n    } else {\n      neighbors = this.neighbors(v);\n    }\n    return neighbors.length === 0;\n  }\n  filterNodes(filter) {\n    // @ts-expect-error\n    var copy = new this.constructor({\n      directed: this._isDirected,\n      multigraph: this._isMultigraph,\n      compound: this._isCompound,\n    });\n\n    copy.setGraph(this.graph());\n\n    var self = this;\n    _.each(this._nodes, function (value, v) {\n      if (filter(v)) {\n        copy.setNode(v, value);\n      }\n    });\n\n    _.each(this._edgeObjs, function (e) {\n      // @ts-expect-error\n      if (copy.hasNode(e.v) && copy.hasNode(e.w)) {\n        copy.setEdge(e, self.edge(e));\n      }\n    });\n\n    var parents = {};\n    function findParent(v) {\n      var parent = self.parent(v);\n      if (parent === undefined || copy.hasNode(parent)) {\n        parents[v] = parent;\n        return parent;\n      } else if (parent in parents) {\n        return parents[parent];\n      } else {\n        return findParent(parent);\n      }\n    }\n\n    if (this._isCompound) {\n      _.each(copy.nodes(), function (v) {\n        copy.setParent(v, findParent(v));\n      });\n    }\n\n    return copy;\n  }\n  /* === Edge functions ========== */\n  setDefaultEdgeLabel(newDefault) {\n    if (!_.isFunction(newDefault)) {\n      newDefault = _.constant(newDefault);\n    }\n    this._defaultEdgeLabelFn = newDefault;\n    return this;\n  }\n  edgeCount() {\n    return this._edgeCount;\n  }\n  edges() {\n    return _.values(this._edgeObjs);\n  }\n  setPath(vs, value) {\n    var self = this;\n    var args = arguments;\n    _.reduce(vs, function (v, w) {\n      if (args.length > 1) {\n        self.setEdge(v, w, value);\n      } else {\n        self.setEdge(v, w);\n      }\n      return w;\n    });\n    return this;\n  }\n  /*\n   * setEdge(v, w, [value, [name]])\n   * setEdge({ v, w, [name] }, [value])\n   */\n  setEdge() {\n    var v, w, name, value;\n    var valueSpecified = false;\n    var arg0 = arguments[0];\n\n    if (typeof arg0 === 'object' && arg0 !== null && 'v' in arg0) {\n      v = arg0.v;\n      w = arg0.w;\n      name = arg0.name;\n      if (arguments.length === 2) {\n        value = arguments[1];\n        valueSpecified = true;\n      }\n    } else {\n      v = arg0;\n      w = arguments[1];\n      name = arguments[3];\n      if (arguments.length > 2) {\n        value = arguments[2];\n        valueSpecified = true;\n      }\n    }\n\n    v = '' + v;\n    w = '' + w;\n    if (!_.isUndefined(name)) {\n      name = '' + name;\n    }\n\n    var e = edgeArgsToId(this._isDirected, v, w, name);\n    if (Object.prototype.hasOwnProperty.call(this._edgeLabels, e)) {\n      if (valueSpecified) {\n        this._edgeLabels[e] = value;\n      }\n      return this;\n    }\n\n    if (!_.isUndefined(name) && !this._isMultigraph) {\n      throw new Error('Cannot set a named edge when isMultigraph = false');\n    }\n\n    // It didn't exist, so we need to create it.\n    // First ensure the nodes exist.\n    this.setNode(v);\n    this.setNode(w);\n\n    // @ts-expect-error\n    this._edgeLabels[e] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);\n\n    var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);\n    // Ensure we add undirected edges in a consistent way.\n    v = edgeObj.v;\n    w = edgeObj.w;\n\n    Object.freeze(edgeObj);\n    this._edgeObjs[e] = edgeObj;\n    incrementOrInitEntry(this._preds[w], v);\n    incrementOrInitEntry(this._sucs[v], w);\n    this._in[w][e] = edgeObj;\n    this._out[v][e] = edgeObj;\n    this._edgeCount++;\n    return this;\n  }\n  edge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return this._edgeLabels[e];\n  }\n  hasEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    return Object.prototype.hasOwnProperty.call(this._edgeLabels, e);\n  }\n  removeEdge(v, w, name) {\n    var e =\n      arguments.length === 1\n        ? edgeObjToId(this._isDirected, arguments[0])\n        : edgeArgsToId(this._isDirected, v, w, name);\n    var edge = this._edgeObjs[e];\n    if (edge) {\n      v = edge.v;\n      w = edge.w;\n      delete this._edgeLabels[e];\n      delete this._edgeObjs[e];\n      decrementOrRemoveEntry(this._preds[w], v);\n      decrementOrRemoveEntry(this._sucs[v], w);\n      delete this._in[w][e];\n      delete this._out[v][e];\n      this._edgeCount--;\n    }\n    return this;\n  }\n  inEdges(v, u) {\n    var inV = this._in[v];\n    if (inV) {\n      var edges = _.values(inV);\n      if (!u) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.v === u;\n      });\n    }\n  }\n  outEdges(v, w) {\n    var outV = this._out[v];\n    if (outV) {\n      var edges = _.values(outV);\n      if (!w) {\n        return edges;\n      }\n      return _.filter(edges, function (edge) {\n        return edge.w === w;\n      });\n    }\n  }\n  nodeEdges(v, w) {\n    var inEdges = this.inEdges(v, w);\n    if (inEdges) {\n      return inEdges.concat(this.outEdges(v, w));\n    }\n  }\n}\n\n/* Number of nodes in the graph. Should only be changed by the implementation. */\nGraph.prototype._nodeCount = 0;\n\n/* Number of edges in the graph. Should only be changed by the implementation. */\nGraph.prototype._edgeCount = 0;\n\nfunction incrementOrInitEntry(map, k) {\n  if (map[k]) {\n    map[k]++;\n  } else {\n    map[k] = 1;\n  }\n}\n\nfunction decrementOrRemoveEntry(map, k) {\n  if (!--map[k]) {\n    delete map[k];\n  }\n}\n\nfunction edgeArgsToId(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM + (_.isUndefined(name) ? DEFAULT_EDGE_NAME : name);\n}\n\nfunction edgeArgsToObj(isDirected, v_, w_, name) {\n  var v = '' + v_;\n  var w = '' + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  var edgeObj = { v: v, w: w };\n  if (name) {\n    edgeObj.name = name;\n  }\n  return edgeObj;\n}\n\nfunction edgeObjToId(isDirected, edgeObj) {\n  return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);\n}\n", "/** Used to match a single whitespace character. */\nvar reWhitespace = /\\s/;\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\nfunction trimmedEndIndex(string) {\n  var index = string.length;\n\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n  return index;\n}\n\nexport default trimmedEndIndex;\n", "import trimmedEndIndex from './_trimmedEndIndex.js';\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string\n    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')\n    : string;\n}\n\nexport default baseTrim;\n", "import baseTrim from './_baseTrim.js';\nimport isObject from './isObject.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = baseTrim(value);\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nexport default toNumber;\n", "import toNumber from './toNumber.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n    MAX_INTEGER = 1.7976931348623157e+308;\n\n/**\n * Converts `value` to a finite number.\n *\n * @static\n * @memberOf _\n * @since 4.12.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted number.\n * @example\n *\n * _.toFinite(3.2);\n * // => 3.2\n *\n * _.toFinite(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toFinite(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toFinite('3.2');\n * // => 3.2\n */\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === INFINITY || value === -INFINITY) {\n    var sign = (value < 0 ? -1 : 1);\n    return sign * MAX_INTEGER;\n  }\n  return value === value ? value : 0;\n}\n\nexport default toFinite;\n", "/**\n * The base implementation of `_.lt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is less than `other`,\n *  else `false`.\n */\nfunction baseLt(value, other) {\n  return value < other;\n}\n\nexport default baseLt;\n", "import assignValue from './_assignValue.js';\nimport castPath from './_castPath.js';\nimport isIndex from './_isIndex.js';\nimport isObject from './isObject.js';\nimport toKey from './_toKey.js';\n\n/**\n * The base implementation of `_.set`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseSet(object, path, value, customizer) {\n  if (!isObject(object)) {\n    return object;\n  }\n  path = castPath(path, object);\n\n  var index = -1,\n      length = path.length,\n      lastIndex = length - 1,\n      nested = object;\n\n  while (nested != null && ++index < length) {\n    var key = toKey(path[index]),\n        newValue = value;\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return object;\n    }\n\n    if (index != lastIndex) {\n      var objValue = nested[key];\n      newValue = customizer ? customizer(objValue, key, nested) : undefined;\n      if (newValue === undefined) {\n        newValue = isObject(objValue)\n          ? objValue\n          : (isIndex(path[index + 1]) ? [] : {});\n      }\n    }\n    assignValue(nested, key, newValue);\n    nested = nested[key];\n  }\n  return object;\n}\n\nexport default baseSet;\n", "import baseGet from './_baseGet.js';\nimport baseSet from './_baseSet.js';\nimport castPath from './_castPath.js';\n\n/**\n * The base implementation of  `_.pickBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @param {Function} predicate The function invoked per property.\n * @returns {Object} Returns the new object.\n */\nfunction basePickBy(object, paths, predicate) {\n  var index = -1,\n      length = paths.length,\n      result = {};\n\n  while (++index < length) {\n    var path = paths[index],\n        value = baseGet(object, path);\n\n    if (predicate(value, path)) {\n      baseSet(result, castPath(path, object), value);\n    }\n  }\n  return result;\n}\n\nexport default basePickBy;\n", "/**\n * Gets the last element of `array`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @returns {*} Returns the last element of `array`.\n * @example\n *\n * _.last([1, 2, 3]);\n * // => 3\n */\nfunction last(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? array[length - 1] : undefined;\n}\n\nexport default last;\n", "import toFinite from './toFinite.js';\n\n/**\n * Converts `value` to an integer.\n *\n * **Note:** This method is loosely based on\n * [`ToInteger`](http://www.ecma-international.org/ecma-262/7.0/#sec-tointeger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toInteger(3.2);\n * // => 3\n *\n * _.toInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toInteger(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toInteger('3.2');\n * // => 3\n */\nfunction toInteger(value) {\n  var result = toFinite(value),\n      remainder = result % 1;\n\n  return result === result ? (remainder ? result - remainder : result) : 0;\n}\n\nexport default toInteger;\n", "import toString from './toString.js';\n\n/** Used to generate unique IDs. */\nvar idCounter = 0;\n\n/**\n * Generates a unique ID. If `prefix` is given, the ID is appended to it.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {string} [prefix=''] The value to prefix the ID with.\n * @returns {string} Returns the unique ID.\n * @example\n *\n * _.uniqueId('contact_');\n * // => 'contact_104'\n *\n * _.uniqueId();\n * // => '105'\n */\nfunction uniqueId(prefix) {\n  var id = ++idCounter;\n  return toString(prefix) + id;\n}\n\nexport default uniqueId;\n", "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n    nativeMax = Math.max;\n\n/**\n * The base implementation of `_.range` and `_.rangeRight` which doesn't\n * coerce arguments.\n *\n * @private\n * @param {number} start The start of the range.\n * @param {number} end The end of the range.\n * @param {number} step The value to increment or decrement by.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Array} Returns the range of numbers.\n */\nfunction baseRange(start, end, step, fromRight) {\n  var index = -1,\n      length = nativeMax(nativeCeil((end - start) / (step || 1)), 0),\n      result = Array(length);\n\n  while (length--) {\n    result[fromRight ? length : ++index] = start;\n    start += step;\n  }\n  return result;\n}\n\nexport default baseRange;\n", "import createRange from './_createRange.js';\n\n/**\n * Creates an array of numbers (positive and/or negative) progressing from\n * `start` up to, but not including, `end`. A step of `-1` is used if a negative\n * `start` is specified without an `end` or `step`. If `end` is not specified,\n * it's set to `start` with `start` then set to `0`.\n *\n * **Note:** JavaScript follows the IEEE-754 standard for resolving\n * floating-point values which can produce unexpected results.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {number} [start=0] The start of the range.\n * @param {number} end The end of the range.\n * @param {number} [step=1] The value to increment or decrement by.\n * @returns {Array} Returns the range of numbers.\n * @see _.inRange, _.rangeRight\n * @example\n *\n * _.range(4);\n * // => [0, 1, 2, 3]\n *\n * _.range(-4);\n * // => [0, -1, -2, -3]\n *\n * _.range(1, 5);\n * // => [1, 2, 3, 4]\n *\n * _.range(0, 20, 5);\n * // => [0, 5, 10, 15]\n *\n * _.range(0, -4, -1);\n * // => [0, -1, -2, -3]\n *\n * _.range(1, 4, 0);\n * // => [1, 1, 1]\n *\n * _.range(0);\n * // => []\n */\nvar range = createRange();\n\nexport default range;\n", "import baseRange from './_baseRange.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toFinite from './toFinite.js';\n\n/**\n * Creates a `_.range` or `_.rangeRight` function.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new range function.\n */\nfunction createRange(fromRight) {\n  return function(start, end, step) {\n    if (step && typeof step != 'number' && isIterateeCall(start, end, step)) {\n      end = step = undefined;\n    }\n    // Ensure the sign of `-0` is preserved.\n    start = toFinite(start);\n    if (end === undefined) {\n      end = start;\n      start = 0;\n    } else {\n      end = toFinite(end);\n    }\n    step = step === undefined ? (start < end ? 1 : -1) : toFinite(step);\n    return baseRange(start, end, step, fromRight);\n  };\n}\n\nexport default createRange;\n", "/*\n * Simple doubly linked list implementation derived from <PERSON><PERSON><PERSON>, et al.,\n * \"Introduction to Algorithms\".\n */\n\nexport { List };\n\nclass List {\n  constructor() {\n    var sentinel = {};\n    sentinel._next = sentinel._prev = sentinel;\n    this._sentinel = sentinel;\n  }\n  dequeue() {\n    var sentinel = this._sentinel;\n    var entry = sentinel._prev;\n    if (entry !== sentinel) {\n      unlink(entry);\n      return entry;\n    }\n  }\n  enqueue(entry) {\n    var sentinel = this._sentinel;\n    if (entry._prev && entry._next) {\n      unlink(entry);\n    }\n    entry._next = sentinel._next;\n    sentinel._next._prev = entry;\n    sentinel._next = entry;\n    entry._prev = sentinel;\n  }\n  toString() {\n    var strs = [];\n    var sentinel = this._sentinel;\n    var curr = sentinel._prev;\n    while (curr !== sentinel) {\n      strs.push(JSON.stringify(curr, filterOutLinks));\n      curr = curr._prev;\n    }\n    return '[' + strs.join(', ') + ']';\n  }\n}\n\nfunction unlink(entry) {\n  entry._prev._next = entry._next;\n  entry._next._prev = entry._prev;\n  delete entry._next;\n  delete entry._prev;\n}\n\nfunction filterOutLinks(k, v) {\n  if (k !== '_next' && k !== '_prev') {\n    return v;\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { List } from './data/list.js';\n\n/*\n * A greedy heuristic for finding a feedback arc set for a graph. A feedback\n * arc set is a set of edges that can be removed to make a graph acyclic.\n * The algorithm comes from: <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, \"A fast and\n * effective heuristic for the feedback arc set problem.\" This implementation\n * adjusts that from the paper to allow for weighted edges.\n */\nexport { greedyFAS };\n\nvar DEFAULT_WEIGHT_FN = _.constant(1);\n\nfunction greedyFAS(g, weightFn) {\n  if (g.nodeCount() <= 1) {\n    return [];\n  }\n  var state = buildState(g, weightFn || DEFAULT_WEIGHT_FN);\n  var results = doGreedyFAS(state.graph, state.buckets, state.zeroIdx);\n\n  // Expand multi-edges\n  return _.flatten(\n    _.map(results, function (e) {\n      return g.outEdges(e.v, e.w);\n    }),\n  );\n}\n\nfunction doGreedyFAS(g, buckets, zeroIdx) {\n  var results = [];\n  var sources = buckets[buckets.length - 1];\n  var sinks = buckets[0];\n\n  var entry;\n  while (g.nodeCount()) {\n    while ((entry = sinks.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    while ((entry = sources.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    if (g.nodeCount()) {\n      for (var i = buckets.length - 2; i > 0; --i) {\n        entry = buckets[i].dequeue();\n        if (entry) {\n          results = results.concat(removeNode(g, buckets, zeroIdx, entry, true));\n          break;\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\nfunction removeNode(g, buckets, zeroIdx, entry, collectPredecessors) {\n  var results = collectPredecessors ? [] : undefined;\n\n  _.forEach(g.inEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var uEntry = g.node(edge.v);\n\n    if (collectPredecessors) {\n      results.push({ v: edge.v, w: edge.w });\n    }\n\n    uEntry.out -= weight;\n    assignBucket(buckets, zeroIdx, uEntry);\n  });\n\n  _.forEach(g.outEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var w = edge.w;\n    var wEntry = g.node(w);\n    wEntry['in'] -= weight;\n    assignBucket(buckets, zeroIdx, wEntry);\n  });\n\n  g.removeNode(entry.v);\n\n  return results;\n}\n\nfunction buildState(g, weightFn) {\n  var fasGraph = new Graph();\n  var maxIn = 0;\n  var maxOut = 0;\n\n  _.forEach(g.nodes(), function (v) {\n    fasGraph.setNode(v, { v: v, in: 0, out: 0 });\n  });\n\n  // Aggregate weights on nodes, but also sum the weights across multi-edges\n  // into a single edge for the fasGraph.\n  _.forEach(g.edges(), function (e) {\n    var prevWeight = fasGraph.edge(e.v, e.w) || 0;\n    var weight = weightFn(e);\n    var edgeWeight = prevWeight + weight;\n    fasGraph.setEdge(e.v, e.w, edgeWeight);\n    maxOut = Math.max(maxOut, (fasGraph.node(e.v).out += weight));\n    maxIn = Math.max(maxIn, (fasGraph.node(e.w)['in'] += weight));\n  });\n\n  var buckets = _.range(maxOut + maxIn + 3).map(function () {\n    return new List();\n  });\n  var zeroIdx = maxIn + 1;\n\n  _.forEach(fasGraph.nodes(), function (v) {\n    assignBucket(buckets, zeroIdx, fasGraph.node(v));\n  });\n\n  return { graph: fasGraph, buckets: buckets, zeroIdx: zeroIdx };\n}\n\nfunction assignBucket(buckets, zeroIdx, entry) {\n  if (!entry.out) {\n    buckets[0].enqueue(entry);\n  } else if (!entry['in']) {\n    buckets[buckets.length - 1].enqueue(entry);\n  } else {\n    buckets[entry.out - entry['in'] + zeroIdx].enqueue(entry);\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { greedyFAS } from './greedy-fas.js';\n\nexport { run, undo };\n\nfunction run(g) {\n  var fas = g.graph().acyclicer === 'greedy' ? greedyFAS(g, weightFn(g)) : dfsFAS(g);\n  _.forEach(fas, function (e) {\n    var label = g.edge(e);\n    g.removeEdge(e);\n    label.forwardName = e.name;\n    label.reversed = true;\n    g.setEdge(e.w, e.v, label, _.uniqueId('rev'));\n  });\n\n  function weightFn(g) {\n    return function (e) {\n      return g.edge(e).weight;\n    };\n  }\n}\n\nfunction dfsFAS(g) {\n  var fas = [];\n  var stack = {};\n  var visited = {};\n\n  function dfs(v) {\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    _.forEach(g.outEdges(v), function (e) {\n      if (Object.prototype.hasOwnProperty.call(stack, e.w)) {\n        fas.push(e);\n      } else {\n        dfs(e.w);\n      }\n    });\n    delete stack[v];\n  }\n\n  _.forEach(g.nodes(), dfs);\n  return fas;\n}\n\nfunction undo(g) {\n  _.forEach(g.edges(), function (e) {\n    var label = g.edge(e);\n    if (label.reversed) {\n      g.removeEdge(e);\n\n      var forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.setEdge(e.w, e.v, label, forwardName);\n    }\n  });\n}\n", "import basePickBy from './_basePickBy.js';\nimport hasIn from './hasIn.js';\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function(value, path) {\n    return hasIn(object, path);\n  });\n}\n\nexport default basePick;\n", "import basePick from './_basePick.js';\nimport flatRest from './_flatRest.js';\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = flatRest(function(object, paths) {\n  return object == null ? {} : basePick(object, paths);\n});\n\nexport default pick;\n", "import flatten from './flatten.js';\nimport overRest from './_overRest.js';\nimport setToString from './_setToString.js';\n\n/**\n * A specialized version of `baseRest` which flattens the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @returns {Function} Returns the new function.\n */\nfunction flatRest(func) {\n  return setToString(overRest(func, undefined, flatten), func + '');\n}\n\nexport default flatRest;\n", "/**\n * The base implementation of `_.gt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is greater than `other`,\n *  else `false`.\n */\nfunction baseGt(value, other) {\n  return value > other;\n}\n\nexport default baseGt;\n", "import baseExtremum from './_baseExtremum.js';\nimport baseGt from './_baseGt.js';\nimport identity from './identity.js';\n\n/**\n * Computes the maximum value of `array`. If `array` is empty or falsey,\n * `undefined` is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Math\n * @param {Array} array The array to iterate over.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * _.max([4, 2, 8, 6]);\n * // => 8\n *\n * _.max([]);\n * // => undefined\n */\nfunction max(array) {\n  return (array && array.length)\n    ? baseExtremum(array, identity, baseGt)\n    : undefined;\n}\n\nexport default max;\n", "import baseAssignValue from './_baseAssignValue.js';\nimport baseForOwn from './_baseForOwn.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * Creates an object with the same keys as `object` and values generated\n * by running each own enumerable string keyed property of `object` thru\n * `iteratee`. The iteratee is invoked with three arguments:\n * (value, key, object).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns the new mapped object.\n * @see _.mapKeys\n * @example\n *\n * var users = {\n *   'fred':    { 'user': 'fred',    'age': 40 },\n *   'pebbles': { 'user': 'pebbles', 'age': 1 }\n * };\n *\n * _.mapValues(users, function(o) { return o.age; });\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n *\n * // The `_.property` iteratee shorthand.\n * _.mapValues(users, 'age');\n * // => { 'fred': 40, 'pebbles': 1 } (iteration order is not guaranteed)\n */\nfunction mapValues(object, iteratee) {\n  var result = {};\n  iteratee = baseIteratee(iteratee, 3);\n\n  baseForOwn(object, function(value, key, object) {\n    baseAssignValue(result, key, iteratee(value, key, object));\n  });\n  return result;\n}\n\nexport default mapValues;\n", "import root from './_root.js';\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\nexport default now;\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\n\nexport {\n  addDummyNode,\n  simplify,\n  asNonCompoundGraph,\n  successorWeights,\n  predecessorWeights,\n  intersectRect,\n  buildLayerMatrix,\n  normalizeRanks,\n  removeEmptyRanks,\n  addBorderNode,\n  maxRank,\n  partition,\n  time,\n  notime,\n};\n\n/*\n * Adds a dummy node to the graph and return v.\n */\nfunction addDummyNode(g, type, attrs, name) {\n  var v;\n  do {\n    v = _.uniqueId(name);\n  } while (g.hasNode(v));\n\n  attrs.dummy = type;\n  g.setNode(v, attrs);\n  return v;\n}\n\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nfunction simplify(g) {\n  var simplified = new Graph().setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    simplified.setNode(v, g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var simpleLabel = simplified.edge(e.v, e.w) || { weight: 0, minlen: 1 };\n    var label = g.edge(e);\n    simplified.setEdge(e.v, e.w, {\n      weight: simpleLabel.weight + label.weight,\n      minlen: Math.max(simpleLabel.minlen, label.minlen),\n    });\n  });\n  return simplified;\n}\n\nfunction asNonCompoundGraph(g) {\n  var simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    if (!g.children(v).length) {\n      simplified.setNode(v, g.node(v));\n    }\n  });\n  _.forEach(g.edges(), function (e) {\n    simplified.setEdge(e, g.edge(e));\n  });\n  return simplified;\n}\n\nfunction successorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var sucs = {};\n    _.forEach(g.outEdges(v), function (e) {\n      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;\n    });\n    return sucs;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\nfunction predecessorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var preds = {};\n    _.forEach(g.inEdges(v), function (e) {\n      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;\n    });\n    return preds;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nfunction intersectRect(rect, point) {\n  var x = rect.x;\n  var y = rect.y;\n\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = rect.width / 2;\n  var h = rect.height / 2;\n\n  if (!dx && !dy) {\n    throw new Error('Not possible to find intersection inside of the rectangle');\n  }\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = (h * dx) / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = (w * dy) / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n}\n\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * function will produce a matrix with the ids of each node.\n */\nfunction buildLayerMatrix(g) {\n  var layering = _.map(_.range(maxRank(g) + 1), function () {\n    return [];\n  });\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    var rank = node.rank;\n    if (!_.isUndefined(rank)) {\n      layering[rank][node.order] = v;\n    }\n  });\n  return layering;\n}\n\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nfunction normalizeRanks(g) {\n  var min = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (_.has(node, 'rank')) {\n      node.rank -= min;\n    }\n  });\n}\n\nfunction removeEmptyRanks(g) {\n  // Ranks may not start at 0, so we need to offset them\n  var offset = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n\n  var layers = [];\n  _.forEach(g.nodes(), function (v) {\n    var rank = g.node(v).rank - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v);\n  });\n\n  var delta = 0;\n  var nodeRankFactor = g.graph().nodeRankFactor;\n  _.forEach(layers, function (vs, i) {\n    if (_.isUndefined(vs) && i % nodeRankFactor !== 0) {\n      --delta;\n    } else if (delta) {\n      _.forEach(vs, function (v) {\n        g.node(v).rank += delta;\n      });\n    }\n  });\n}\n\nfunction addBorderNode(g, prefix, rank, order) {\n  var node = {\n    width: 0,\n    height: 0,\n  };\n  if (arguments.length >= 4) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, 'border', node, prefix);\n}\n\nfunction maxRank(g) {\n  return _.max(\n    _.map(g.nodes(), function (v) {\n      var rank = g.node(v).rank;\n      if (!_.isUndefined(rank)) {\n        return rank;\n      }\n    }),\n  );\n}\n\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * function returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nfunction partition(collection, fn) {\n  var result = { lhs: [], rhs: [] };\n  _.forEach(collection, function (value) {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n}\n\n/*\n * Returns a new function that wraps `fn` with a timer. The wrapper logs the\n * time it takes to execute the function.\n */\nfunction time(name, fn) {\n  var start = _.now();\n  try {\n    return fn();\n  } finally {\n    console.log(name + ' time: ' + (_.now() - start) + 'ms');\n  }\n}\n\nfunction notime(name, fn) {\n  return fn();\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { addBorderSegments };\n\nfunction addBorderSegments(g) {\n  function dfs(v) {\n    var children = g.children(v);\n    var node = g.node(v);\n    if (children.length) {\n      _.forEach(children, dfs);\n    }\n\n    if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n      node.borderLeft = [];\n      node.borderRight = [];\n      for (var rank = node.minRank, maxRank = node.maxRank + 1; rank < maxRank; ++rank) {\n        addBorderNode(g, 'borderLeft', '_bl', v, node, rank);\n        addBorderNode(g, 'borderRight', '_br', v, node, rank);\n      }\n    }\n  }\n\n  _.forEach(g.children(), dfs);\n}\n\nfunction addBorderNode(g, prop, prefix, sg, sgNode, rank) {\n  var label = { width: 0, height: 0, rank: rank, borderType: prop };\n  var prev = sgNode[prop][rank - 1];\n  var curr = util.addDummyNode(g, 'border', label, prefix);\n  sgNode[prop][rank] = curr;\n  g.setParent(curr, sg);\n  if (prev) {\n    g.setEdge(prev, curr, { weight: 1 });\n  }\n}\n", "import * as _ from 'lodash-es';\n\nexport { adjust, undo };\n\nfunction adjust(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapWidthHeight(g);\n  }\n}\n\nfunction undo(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'bt' || rankDir === 'rl') {\n    reverseY(g);\n  }\n\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapXY(g);\n    swapWidthHeight(g);\n  }\n}\n\nfunction swapWidthHeight(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapWidthHeightOne(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    swapWidthHeightOne(g.edge(e));\n  });\n}\n\nfunction swapWidthHeightOne(attrs) {\n  var w = attrs.width;\n  attrs.width = attrs.height;\n  attrs.height = w;\n}\n\nfunction reverseY(g) {\n  _.forEach(g.nodes(), function (v) {\n    reverseYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, reverseYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      reverseYOne(edge);\n    }\n  });\n}\n\nfunction reverseYOne(attrs) {\n  attrs.y = -attrs.y;\n}\n\nfunction swapXY(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapXYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, swapXYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      swapXYOne(edge);\n    }\n  });\n}\n\nfunction swapXYOne(attrs) {\n  var x = attrs.x;\n  attrs.x = attrs.y;\n  attrs.y = x;\n}\n", "/**\n * TypeScript type imports:\n *\n * @import { Graph } from '../graphlib/graph.js';\n */\nimport * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, undo };\n\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nfunction run(g) {\n  g.graph().dummyChains = [];\n  _.forEach(g.edges(), function (edge) {\n    normalizeEdge(g, edge);\n  });\n}\n\n/**\n * @param {Graph} g\n */\nfunction normalizeEdge(g, e) {\n  var v = e.v;\n  var vRank = g.node(v).rank;\n  var w = e.w;\n  var wRank = g.node(w).rank;\n  var name = e.name;\n  var edgeLabel = g.edge(e);\n  var labelRank = edgeLabel.labelRank;\n\n  if (wRank === vRank + 1) return;\n\n  g.removeEdge(e);\n\n  /**\n   * @typedef {Object} Attrs\n   * @property {number} width\n   * @property {number} height\n   * @property {ReturnType<Graph[\"node\"]>} edgeLabel\n   * @property {any} edgeObj\n   * @property {ReturnType<Graph[\"node\"]>[\"rank\"]} rank\n   * @property {string} [dummy]\n   * @property {ReturnType<Graph[\"node\"]>[\"labelpos\"]} [labelpos]\n   */\n\n  /** @type {Attrs | undefined} */\n  var attrs = undefined;\n  var dummy, i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    edgeLabel.points = [];\n    attrs = {\n      width: 0,\n      height: 0,\n      edgeLabel: edgeLabel,\n      edgeObj: e,\n      rank: vRank,\n    };\n    dummy = util.addDummyNode(g, 'edge', attrs, '_d');\n    if (vRank === labelRank) {\n      attrs.width = edgeLabel.width;\n      attrs.height = edgeLabel.height;\n      attrs.dummy = 'edge-label';\n      attrs.labelpos = edgeLabel.labelpos;\n    }\n    g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);\n    if (i === 0) {\n      g.graph().dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n\n  g.setEdge(v, w, { weight: edgeLabel.weight }, name);\n}\n\nfunction undo(g) {\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var origLabel = node.edgeLabel;\n    var w;\n    g.setEdge(node.edgeObj, origLabel);\n    while (node.dummy) {\n      w = g.successors(v)[0];\n      g.removeNode(v);\n      origLabel.points.push({ x: node.x, y: node.y });\n      if (node.dummy === 'edge-label') {\n        origLabel.x = node.x;\n        origLabel.y = node.y;\n        origLabel.width = node.width;\n        origLabel.height = node.height;\n      }\n      v = w;\n      node = g.node(v);\n    }\n  });\n}\n", "import baseExtremum from './_baseExtremum.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseLt from './_baseLt.js';\n\n/**\n * This method is like `_.min` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the minimum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.minBy(objects, function(o) { return o.n; });\n * // => { 'n': 1 }\n *\n * // The `_.property` iteratee shorthand.\n * _.minBy(objects, 'n');\n * // => { 'n': 1 }\n */\nfunction minBy(array, iteratee) {\n  return (array && array.length)\n    ? baseExtremum(array, baseIteratee(iteratee, 2), baseLt)\n    : undefined;\n}\n\nexport default minBy;\n", "import * as _ from 'lodash-es';\n\nexport { longestPath, slack };\n\n/*\n * Initializes ranks for the input graph using the longest path algorithm. This\n * algorithm scales well and is fast in practice, it yields rather poor\n * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom\n * ranks wide and leaving edges longer than necessary. However, due to its\n * speed, this algorithm is good for getting an initial ranking that can be fed\n * into other algorithms.\n *\n * This algorithm does not normalize layers because it will be used by other\n * algorithms in most cases. If using this algorithm directly, be sure to\n * run normalize at the end.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG.\n *    2. Input graph node labels can be assigned properties.\n *\n * Post-conditions:\n *\n *    1. Each node will be assign an (unnormalized) \"rank\" property.\n */\nfunction longestPath(g) {\n  var visited = {};\n\n  function dfs(v) {\n    var label = g.node(v);\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return label.rank;\n    }\n    visited[v] = true;\n\n    var rank = _.min(\n      _.map(g.outEdges(v), function (e) {\n        return dfs(e.w) - g.edge(e).minlen;\n      }),\n    );\n\n    if (\n      rank === Number.POSITIVE_INFINITY || // return value of _.map([]) for Lodash 3\n      rank === undefined || // return value of _.map([]) for Lodash 4\n      rank === null\n    ) {\n      // return value of _.map([null])\n      rank = 0;\n    }\n\n    return (label.rank = rank);\n  }\n\n  _.forEach(g.sources(), dfs);\n}\n\n/*\n * Returns the amount of slack for the given edge. The slack is defined as the\n * difference between the length of the edge and its minimum length.\n */\nfunction slack(g, e) {\n  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport { slack } from './util.js';\n\nexport { feasibleTree };\n\n/*\n * Constructs a spanning tree with tight edges and adjusted the input node's\n * ranks to achieve this. A tight edge is one that is has a length that matches\n * its \"minlen\" attribute.\n *\n * The basic structure for this function is derived from <PERSON><PERSON><PERSON>, et al., \"A\n * Technique for Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a DAG.\n *    2. Graph must be connected.\n *    3. Graph must have at least one node.\n *    5. Graph nodes must have been previously assigned a \"rank\" property that\n *       respects the \"minlen\" property of incident edges.\n *    6. Graph edges must have a \"minlen\" property.\n *\n * Post-conditions:\n *\n *    - Graph nodes will have their rank adjusted to ensure that all edges are\n *      tight.\n *\n * Returns a tree (undirected graph) that is constructed using only \"tight\"\n * edges.\n */\nfunction feasibleTree(g) {\n  var t = new Graph({ directed: false });\n\n  // Choose arbitrary node from which to start our tree\n  var start = g.nodes()[0];\n  var size = g.nodeCount();\n  t.setNode(start, {});\n\n  var edge, delta;\n  while (tightTree(t, g) < size) {\n    edge = findMinSlackEdge(t, g);\n    delta = t.hasNode(edge.v) ? slack(g, edge) : -slack(g, edge);\n    shiftRanks(t, g, delta);\n  }\n\n  return t;\n}\n\n/*\n * Finds a maximal tree of tight edges and returns the number of nodes in the\n * tree.\n */\nfunction tightTree(t, g) {\n  function dfs(v) {\n    _.forEach(g.nodeEdges(v), function (e) {\n      var edgeV = e.v,\n        w = v === edgeV ? e.w : edgeV;\n      if (!t.hasNode(w) && !slack(g, e)) {\n        t.setNode(w, {});\n        t.setEdge(v, w, {});\n        dfs(w);\n      }\n    });\n  }\n\n  _.forEach(t.nodes(), dfs);\n  return t.nodeCount();\n}\n\n/*\n * Finds the edge with the smallest slack that is incident on tree and returns\n * it.\n */\nfunction findMinSlackEdge(t, g) {\n  return _.minBy(g.edges(), function (e) {\n    if (t.hasNode(e.v) !== t.hasNode(e.w)) {\n      return slack(g, e);\n    }\n  });\n}\n\nfunction shiftRanks(t, g, delta) {\n  _.forEach(t.nodes(), function (v) {\n    g.node(v).rank += delta;\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { PriorityQueue } from '../data/priority-queue.js';\n\nexport { dijkstra };\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction dijkstra(g, source, weightFn, edgeFn) {\n  return runDijkstra(\n    g,\n    String(source),\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn ||\n      function (v) {\n        return g.outEdges(v);\n      },\n  );\n}\n\nfunction runDijkstra(g, source, weightFn, edgeFn) {\n  var results = {};\n  var pq = new PriorityQueue();\n  var v, vEntry;\n\n  var updateNeighbors = function (edge) {\n    var w = edge.v !== v ? edge.v : edge.w;\n    var wEntry = results[w];\n    var weight = weightFn(edge);\n    var distance = vEntry.distance + weight;\n\n    if (weight < 0) {\n      throw new Error(\n        'dijkstra does not allow negative edge weights. ' +\n          'Bad edge: ' +\n          edge +\n          ' Weight: ' +\n          weight,\n      );\n    }\n\n    if (distance < wEntry.distance) {\n      wEntry.distance = distance;\n      wEntry.predecessor = v;\n      pq.decrease(w, distance);\n    }\n  };\n\n  g.nodes().forEach(function (v) {\n    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;\n    results[v] = { distance: distance };\n    pq.add(v, distance);\n  });\n\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    vEntry = results[v];\n    if (vEntry.distance === Number.POSITIVE_INFINITY) {\n      break;\n    }\n\n    edgeFn(v).forEach(updateNeighbors);\n  }\n\n  return results;\n}\n", "import * as _ from 'lodash-es';\n\nexport { floyd<PERSON>ars<PERSON> };\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction floydWarshall(g, weightFn, edgeFn) {\n  return runFloydWarshall(\n    g,\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn ||\n      function (v) {\n        return g.outEdges(v);\n      },\n  );\n}\n\nfunction runFloydWarshall(g, weightFn, edgeFn) {\n  var results = {};\n  var nodes = g.nodes();\n\n  nodes.forEach(function (v) {\n    results[v] = {};\n    results[v][v] = { distance: 0 };\n    nodes.forEach(function (w) {\n      if (v !== w) {\n        results[v][w] = { distance: Number.POSITIVE_INFINITY };\n      }\n    });\n    edgeFn(v).forEach(function (edge) {\n      var w = edge.v === v ? edge.w : edge.v;\n      var d = weightFn(edge);\n      results[v][w] = { distance: d, predecessor: v };\n    });\n  });\n\n  nodes.forEach(function (k) {\n    var rowK = results[k];\n    nodes.forEach(function (i) {\n      var rowI = results[i];\n      nodes.forEach(function (j) {\n        var ik = rowI[k];\n        var kj = rowK[j];\n        var ij = rowI[j];\n        var altDistance = ik.distance + kj.distance;\n        if (altDistance < ij.distance) {\n          ij.distance = altDistance;\n          ij.predecessor = kj.predecessor;\n        }\n      });\n    });\n  });\n\n  return results;\n}\n", "import baseProperty from './_baseProperty.js';\n\n/**\n * Gets the size of an ASCII `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nvar asciiSize = baseProperty('length');\n\nexport default asciiSize;\n", "/** Used to compose unicode character classes. */\nvar rsAstralRange = '\\\\ud800-\\\\udfff',\n    rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,\n    rsVarRange = '\\\\ufe0e\\\\ufe0f';\n\n/** Used to compose unicode capture groups. */\nvar rsZWJ = '\\\\u200d';\n\n/** Used to detect strings with [zero-width joiners or code points from the astral planes](http://eev.ee/blog/2015/09/12/dark-corners-of-unicode/). */\nvar reHasUnicode = RegExp('[' + rsZWJ + rsAstralRange  + rsComboRange + rsVarRange + ']');\n\n/**\n * Checks if `string` contains Unicode symbols.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {boolean} Returns `true` if a symbol is found, else `false`.\n */\nfunction hasUnicode(string) {\n  return reHasUnicode.test(string);\n}\n\nexport default hasUnicode;\n", "/** Used to compose unicode character classes. */\nvar rsAstralRange = '\\\\ud800-\\\\udfff',\n    rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,\n    rsVarRange = '\\\\ufe0e\\\\ufe0f';\n\n/** Used to compose unicode capture groups. */\nvar rsAstral = '[' + rsAstralRange + ']',\n    rsCombo = '[' + rsComboRange + ']',\n    rsFitz = '\\\\ud83c[\\\\udffb-\\\\udfff]',\n    rsModifier = '(?:' + rsCombo + '|' + rsFitz + ')',\n    rsNonAstral = '[^' + rsAstralRange + ']',\n    rsRegional = '(?:\\\\ud83c[\\\\udde6-\\\\uddff]){2}',\n    rsSurrPair = '[\\\\ud800-\\\\udbff][\\\\udc00-\\\\udfff]',\n    rsZWJ = '\\\\u200d';\n\n/** Used to compose unicode regexes. */\nvar reOptMod = rsModifier + '?',\n    rsOptVar = '[' + rsVarRange + ']?',\n    rsOptJoin = '(?:' + rsZWJ + '(?:' + [rsNonAstral, rsRegional, rsSurrPair].join('|') + ')' + rsOptVar + reOptMod + ')*',\n    rsSeq = rsOptVar + reOptMod + rsOptJoin,\n    rsSymbol = '(?:' + [rsNonAstral + rsCombo + '?', rsCombo, rsRegional, rsSurrPair, rsAstral].join('|') + ')';\n\n/** Used to match [string symbols](https://mathiasbynens.be/notes/javascript-unicode). */\nvar reUnicode = RegExp(rsFitz + '(?=' + rsFitz + ')|' + rsSymbol + rsSeq, 'g');\n\n/**\n * Gets the size of a Unicode `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nfunction unicodeSize(string) {\n  var result = reUnicode.lastIndex = 0;\n  while (reUnicode.test(string)) {\n    ++result;\n  }\n  return result;\n}\n\nexport default unicodeSize;\n", "import * as _ from 'lodash-es';\n\nexport { topsort, CycleException };\n\ntopsort.CycleException = CycleException;\n\nfunction topsort(g) {\n  var visited = {};\n  var stack = {};\n  var results = [];\n\n  function visit(node) {\n    if (Object.prototype.hasOwnProperty.call(stack, node)) {\n      throw new CycleException();\n    }\n\n    if (!Object.prototype.hasOwnProperty.call(visited, node)) {\n      stack[node] = true;\n      visited[node] = true;\n      _.each(g.predecessors(node), visit);\n      delete stack[node];\n      results.push(node);\n    }\n  }\n\n  _.each(g.sinks(), visit);\n\n  if (_.size(visited) !== g.nodeCount()) {\n    throw new CycleException();\n  }\n\n  return results;\n}\n\nfunction CycleException() {}\nCycleException.prototype = new Error(); // must be an instance of Error to pass testing\n", "import * as _ from 'lodash-es';\n\nexport { dfs };\n\n/*\n * A helper that preforms a pre- or post-order traversal on the input graph\n * and returns the nodes in the order they were visited. If the graph is\n * undirected then this algorithm will navigate using neighbors. If the graph\n * is directed then this algorithm will navigate using successors.\n *\n * Order must be one of \"pre\" or \"post\".\n */\nfunction dfs(g, vs, order) {\n  if (!_.isArray(vs)) {\n    vs = [vs];\n  }\n\n  var navigation = (g.isDirected() ? g.successors : g.neighbors).bind(g);\n\n  var acc = [];\n  var visited = {};\n  _.each(vs, function (v) {\n    if (!g.hasNode(v)) {\n      throw new Error('Graph does not have node: ' + v);\n    }\n\n    doDfs(g, v, order === 'post', visited, navigation, acc);\n  });\n  return acc;\n}\n\nfunction doDfs(g, v, postorder, visited, navigation, acc) {\n  if (!Object.prototype.hasOwnProperty.call(visited, v)) {\n    visited[v] = true;\n\n    if (!postorder) {\n      acc.push(v);\n    }\n    _.each(navigation(v), function (w) {\n      doDfs(g, w, postorder, visited, navigation, acc);\n    });\n    if (postorder) {\n      acc.push(v);\n    }\n  }\n}\n", "import * as _ from 'lodash-es';\nimport * as alg from '../../graphlib/alg/index.js';\nimport { simplify } from '../util.js';\nimport { feasibleTree } from './feasible-tree.js';\nimport { longestPath, slack } from './util.js';\n\nexport { networkSimplex };\n\n// Expose some internals for testing purposes\nnetworkSimplex.initLowLimValues = initLowLimValues;\nnetworkSimplex.initCutValues = initCutValues;\nnetworkSimplex.calcCutValue = calcCutValue;\nnetworkSimplex.leaveEdge = leaveEdge;\nnetworkSimplex.enterEdge = enterEdge;\nnetworkSimplex.exchangeEdges = exchangeEdges;\n\n/*\n * The network simplex algorithm assigns ranks to each node in the input graph\n * and iteratively improves the ranking to reduce the length of edges.\n *\n * Preconditions:\n *\n *    1. The input graph must be a DAG.\n *    2. All nodes in the graph must have an object value.\n *    3. All edges in the graph must have \"minlen\" and \"weight\" attributes.\n *\n * Postconditions:\n *\n *    1. All nodes in the graph will have an assigned \"rank\" attribute that has\n *       been optimized by the network simplex algorithm. Ranks start at 0.\n *\n *\n * A rough sketch of the algorithm is as follows:\n *\n *    1. Assign initial ranks to each node. We use the longest path algorithm,\n *       which assigns ranks to the lowest position possible. In general this\n *       leads to very wide bottom ranks and unnecessarily long edges.\n *    2. Construct a feasible tight tree. A tight tree is one such that all\n *       edges in the tree have no slack (difference between length of edge\n *       and minlen for the edge). This by itself greatly improves the assigned\n *       rankings by shorting edges.\n *    3. Iteratively find edges that have negative cut values. Generally a\n *       negative cut value indicates that the edge could be removed and a new\n *       tree edge could be added to produce a more compact graph.\n *\n * Much of the algorithms here are derived from Gansner, et al., \"A Technique\n * for Drawing Directed Graphs.\" The structure of the file roughly follows the\n * structure of the overall algorithm.\n */\nfunction networkSimplex(g) {\n  g = simplify(g);\n  longestPath(g);\n  var t = feasibleTree(g);\n  initLowLimValues(t);\n  initCutValues(t, g);\n\n  var e, f;\n  while ((e = leaveEdge(t))) {\n    f = enterEdge(t, g, e);\n    exchangeEdges(t, g, e, f);\n  }\n}\n\n/*\n * Initializes cut values for all edges in the tree.\n */\nfunction initCutValues(t, g) {\n  var vs = alg.postorder(t, t.nodes());\n  vs = vs.slice(0, vs.length - 1);\n  _.forEach(vs, function (v) {\n    assignCutValue(t, g, v);\n  });\n}\n\nfunction assignCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  t.edge(child, parent).cutvalue = calcCutValue(t, g, child);\n}\n\n/*\n * Given the tight tree, its graph, and a child in the graph calculate and\n * return the cut value for the edge between the child and its parent.\n */\nfunction calcCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  // True if the child is on the tail end of the edge in the directed graph\n  var childIsTail = true;\n  // The graph's view of the tree edge we're inspecting\n  var graphEdge = g.edge(child, parent);\n  // The accumulated cut value for the edge between this node and its parent\n  var cutValue = 0;\n\n  if (!graphEdge) {\n    childIsTail = false;\n    graphEdge = g.edge(parent, child);\n  }\n\n  cutValue = graphEdge.weight;\n\n  _.forEach(g.nodeEdges(child), function (e) {\n    var isOutEdge = e.v === child,\n      other = isOutEdge ? e.w : e.v;\n\n    if (other !== parent) {\n      var pointsToHead = isOutEdge === childIsTail,\n        otherWeight = g.edge(e).weight;\n\n      cutValue += pointsToHead ? otherWeight : -otherWeight;\n      if (isTreeEdge(t, child, other)) {\n        var otherCutValue = t.edge(child, other).cutvalue;\n        cutValue += pointsToHead ? -otherCutValue : otherCutValue;\n      }\n    }\n  });\n\n  return cutValue;\n}\n\nfunction initLowLimValues(tree, root) {\n  if (arguments.length < 2) {\n    root = tree.nodes()[0];\n  }\n  dfsAssignLowLim(tree, {}, 1, root);\n}\n\nfunction dfsAssignLowLim(tree, visited, nextLim, v, parent) {\n  var low = nextLim;\n  var label = tree.node(v);\n\n  visited[v] = true;\n  _.forEach(tree.neighbors(v), function (w) {\n    if (!Object.prototype.hasOwnProperty.call(visited, w)) {\n      nextLim = dfsAssignLowLim(tree, visited, nextLim, w, v);\n    }\n  });\n\n  label.low = low;\n  label.lim = nextLim++;\n  if (parent) {\n    label.parent = parent;\n  } else {\n    // TODO should be able to remove this when we incrementally update low lim\n    delete label.parent;\n  }\n\n  return nextLim;\n}\n\nfunction leaveEdge(tree) {\n  return _.find(tree.edges(), function (e) {\n    return tree.edge(e).cutvalue < 0;\n  });\n}\n\nfunction enterEdge(t, g, edge) {\n  var v = edge.v;\n  var w = edge.w;\n\n  // For the rest of this function we assume that v is the tail and w is the\n  // head, so if we don't have this edge in the graph we should flip it to\n  // match the correct orientation.\n  if (!g.hasEdge(v, w)) {\n    v = edge.w;\n    w = edge.v;\n  }\n\n  var vLabel = t.node(v);\n  var wLabel = t.node(w);\n  var tailLabel = vLabel;\n  var flip = false;\n\n  // If the root is in the tail of the edge then we need to flip the logic that\n  // checks for the head and tail nodes in the candidates function below.\n  if (vLabel.lim > wLabel.lim) {\n    tailLabel = wLabel;\n    flip = true;\n  }\n\n  var candidates = _.filter(g.edges(), function (edge) {\n    return (\n      flip === isDescendant(t, t.node(edge.v), tailLabel) &&\n      flip !== isDescendant(t, t.node(edge.w), tailLabel)\n    );\n  });\n\n  return _.minBy(candidates, function (edge) {\n    return slack(g, edge);\n  });\n}\n\nfunction exchangeEdges(t, g, e, f) {\n  var v = e.v;\n  var w = e.w;\n  t.removeEdge(v, w);\n  t.setEdge(f.v, f.w, {});\n  initLowLimValues(t);\n  initCutValues(t, g);\n  updateRanks(t, g);\n}\n\nfunction updateRanks(t, g) {\n  var root = _.find(t.nodes(), function (v) {\n    return !g.node(v).parent;\n  });\n  var vs = alg.preorder(t, root);\n  vs = vs.slice(1);\n  _.forEach(vs, function (v) {\n    var parent = t.node(v).parent,\n      edge = g.edge(v, parent),\n      flipped = false;\n\n    if (!edge) {\n      edge = g.edge(parent, v);\n      flipped = true;\n    }\n\n    g.node(v).rank = g.node(parent).rank + (flipped ? edge.minlen : -edge.minlen);\n  });\n}\n\n/*\n * Returns true if the edge is in the tree.\n */\nfunction isTreeEdge(tree, u, v) {\n  return tree.hasEdge(u, v);\n}\n\n/*\n * Returns true if the specified node is descendant of the root node per the\n * assigned low and lim attributes in the tree.\n */\nfunction isDescendant(tree, vLabel, rootLabel) {\n  return rootLabel.low <= vLabel.lim && vLabel.lim <= rootLabel.lim;\n}\n", "import { dfs } from './dfs.js';\n\nexport { postorder };\n\nfunction postorder(g, vs) {\n  return dfs(g, vs, 'post');\n}\n", "import { dfs } from './dfs.js';\n\nexport { preorder };\n\nfunction preorder(g, vs) {\n  return dfs(g, vs, 'pre');\n}\n", "import { feasibleTree } from './feasible-tree.js';\nimport { networkSimplex } from './network-simplex.js';\nimport { longestPath } from './util.js';\n\nexport { rank };\n\n/*\n * Assigns a rank to each node in the input graph that respects the \"minlen\"\n * constraint specified on edges between nodes.\n *\n * This basic structure is derived from <PERSON><PERSON><PERSON>, et al., \"A Technique for\n * Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a connected DAG\n *    2. Graph nodes must be objects\n *    3. Graph edges must have \"weight\" and \"minlen\" attributes\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have a \"rank\" attribute based on the results of the\n *       algorithm. Ranks can start at any index (including negative), we'll\n *       fix them up later.\n */\nfunction rank(g) {\n  switch (g.graph().ranker) {\n    case 'network-simplex':\n      networkSimplexRanker(g);\n      break;\n    case 'tight-tree':\n      tightTreeRanker(g);\n      break;\n    case 'longest-path':\n      longestPathRanker(g);\n      break;\n    default:\n      networkSimplexRanker(g);\n  }\n}\n\n// A fast and simple ranker, but results are far from optimal.\nvar longestPathRanker = longestPath;\n\nfunction tightTreeRanker(g) {\n  longestPath(g);\n  feasibleTree(g);\n}\n\nfunction networkSimplexRanker(g) {\n  networkSimplex(g);\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, cleanup };\n\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nfunction run(g) {\n  var root = util.addDummyNode(g, 'root', {}, '_root');\n  var depths = treeDepths(g);\n  var height = _.max(_.values(depths)) - 1; // Note: depths is an Object not an array\n  var nodeSep = 2 * height + 1;\n\n  g.graph().nestingRoot = root;\n\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  _.forEach(g.edges(), function (e) {\n    g.edge(e).minlen *= nodeSep;\n  });\n\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  var weight = sumWeights(g) + 1;\n\n  // Create border nodes and link them up\n  _.forEach(g.children(), function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n  });\n\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  g.graph().nodeRankFactor = nodeSep;\n}\n\nfunction dfs(g, root, nodeSep, weight, height, depths, v) {\n  var children = g.children(v);\n  if (!children.length) {\n    if (v !== root) {\n      g.setEdge(root, v, { weight: 0, minlen: nodeSep });\n    }\n    return;\n  }\n\n  var top = util.addBorderNode(g, '_bt');\n  var bottom = util.addBorderNode(g, '_bb');\n  var label = g.node(v);\n\n  g.setParent(top, v);\n  label.borderTop = top;\n  g.setParent(bottom, v);\n  label.borderBottom = bottom;\n\n  _.forEach(children, function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n\n    var childNode = g.node(child);\n    var childTop = childNode.borderTop ? childNode.borderTop : child;\n    var childBottom = childNode.borderBottom ? childNode.borderBottom : child;\n    var thisWeight = childNode.borderTop ? weight : 2 * weight;\n    var minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n\n    g.setEdge(top, childTop, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n\n    g.setEdge(childBottom, bottom, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n  });\n\n  if (!g.parent(v)) {\n    g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });\n  }\n}\n\nfunction treeDepths(g) {\n  var depths = {};\n  function dfs(v, depth) {\n    var children = g.children(v);\n    if (children && children.length) {\n      _.forEach(children, function (child) {\n        dfs(child, depth + 1);\n      });\n    }\n    depths[v] = depth;\n  }\n  _.forEach(g.children(), function (v) {\n    dfs(v, 1);\n  });\n  return depths;\n}\n\nfunction sumWeights(g) {\n  return _.reduce(\n    g.edges(),\n    function (acc, e) {\n      return acc + g.edge(e).weight;\n    },\n    0,\n  );\n}\n\nfunction cleanup(g) {\n  var graphLabel = g.graph();\n  g.removeNode(graphLabel.nestingRoot);\n  delete graphLabel.nestingRoot;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.nestingEdge) {\n      g.removeEdge(e);\n    }\n  });\n}\n", "import baseClone from './_baseClone.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * This method is like `_.clone` except that it recursively clones `value`.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Lang\n * @param {*} value The value to recursively clone.\n * @returns {*} Returns the deep cloned value.\n * @see _.clone\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var deep = _.cloneDeep(objects);\n * console.log(deep[0] === objects[0]);\n * // => false\n */\nfunction cloneDeep(value) {\n  return baseClone(value, CLONE_DEEP_FLAG | CLONE_SYMBOLS_FLAG);\n}\n\nexport default cloneDeep;\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\n\nexport { buildLayerGraph };\n\n/*\n * Constructs a graph that can be used to sort a layer of nodes. The graph will\n * contain all base and subgraph nodes from the request layer in their original\n * hierarchy and any edges that are incident on these nodes and are of the type\n * requested by the \"relationship\" parameter.\n *\n * Nodes from the requested rank that do not have parents are assigned a root\n * node in the output graph, which is set in the root graph attribute. This\n * makes it easy to walk the hierarchy of movable nodes during ordering.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG\n *    2. Base nodes in the input graph have a rank attribute\n *    3. Subgraph nodes in the input graph has minRank and maxRank attributes\n *    4. Edges have an assigned weight\n *\n * Post-conditions:\n *\n *    1. Output graph has all nodes in the movable rank with preserved\n *       hierarchy.\n *    2. Root nodes in the movable layer are made children of the node\n *       indicated by the root attribute of the graph.\n *    3. Non-movable nodes incident on movable nodes, selected by the\n *       relationship parameter, are included in the graph (without hierarchy).\n *    4. Edges incident on movable nodes, selected by the relationship\n *       parameter, are added to the output graph.\n *    5. The weights for copied edges are aggregated as need, since the output\n *       graph is not a multi-graph.\n */\nfunction buildLayerGraph(g, rank, relationship) {\n  var root = createRootNode(g),\n    result = new Graph({ compound: true })\n      .setGraph({ root: root })\n      .setDefaultNodeLabel(function (v) {\n        return g.node(v);\n      });\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v),\n      parent = g.parent(v);\n\n    if (node.rank === rank || (node.minRank <= rank && rank <= node.maxRank)) {\n      result.setNode(v);\n      result.setParent(v, parent || root);\n\n      // This assumes we have only short edges!\n      _.forEach(g[relationship](v), function (e) {\n        var u = e.v === v ? e.w : e.v,\n          edge = result.edge(u, v),\n          weight = !_.isUndefined(edge) ? edge.weight : 0;\n        result.setEdge(u, v, { weight: g.edge(e).weight + weight });\n      });\n\n      if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n        result.setNode(v, {\n          borderLeft: node.borderLeft[rank],\n          borderRight: node.borderRight[rank],\n        });\n      }\n    }\n  });\n\n  return result;\n}\n\nfunction createRootNode(g) {\n  var v;\n  while (g.hasNode((v = _.uniqueId('_root'))));\n  return v;\n}\n", "/**\n * This base implementation of `_.zipObject` which assigns values using `assignFunc`.\n *\n * @private\n * @param {Array} props The property identifiers.\n * @param {Array} values The property values.\n * @param {Function} assignFunc The function to assign values.\n * @returns {Object} Returns the new object.\n */\nfunction baseZipObject(props, values, assignFunc) {\n  var index = -1,\n      length = props.length,\n      valsLength = values.length,\n      result = {};\n\n  while (++index < length) {\n    var value = index < valsLength ? values[index] : undefined;\n    assignFunc(result, props[index], value);\n  }\n  return result;\n}\n\nexport default baseZipObject;\n", "import assignValue from './_assignValue.js';\nimport baseZipObject from './_baseZipObject.js';\n\n/**\n * This method is like `_.fromPairs` except that it accepts two arrays,\n * one of property identifiers and one of corresponding values.\n *\n * @static\n * @memberOf _\n * @since 0.4.0\n * @category Array\n * @param {Array} [props=[]] The property identifiers.\n * @param {Array} [values=[]] The property values.\n * @returns {Object} Returns the new object.\n * @example\n *\n * _.zipObject(['a', 'b'], [1, 2]);\n * // => { 'a': 1, 'b': 2 }\n */\nfunction zipObject(props, values) {\n  return baseZipObject(props || [], values || [], assignValue);\n}\n\nexport default zipObject;\n", "/**\n * The base implementation of `_.sortBy` which uses `comparer` to define the\n * sort order of `array` and replaces criteria objects with their corresponding\n * values.\n *\n * @private\n * @param {Array} array The array to sort.\n * @param {Function} comparer The function to define sort order.\n * @returns {Array} Returns `array`.\n */\nfunction baseSortBy(array, comparer) {\n  var length = array.length;\n\n  array.sort(comparer);\n  while (length--) {\n    array[length] = array[length].value;\n  }\n  return array;\n}\n\nexport default baseSortBy;\n", "import isSymbol from './isSymbol.js';\n\n/**\n * Compares values to sort them in ascending order.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {number} Returns the sort order indicator for `value`.\n */\nfunction compareAscending(value, other) {\n  if (value !== other) {\n    var valIsDefined = value !== undefined,\n        valIsNull = value === null,\n        valIsReflexive = value === value,\n        valIsSymbol = isSymbol(value);\n\n    var othIsDefined = other !== undefined,\n        othIsNull = other === null,\n        othIsReflexive = other === other,\n        othIsSymbol = isSymbol(other);\n\n    if ((!othIsNull && !othIsSymbol && !valIsSymbol && value > other) ||\n        (valIsSymbol && othIsDefined && othIsReflexive && !othIsNull && !othIsSymbol) ||\n        (valIsNull && othIsDefined && othIsReflexive) ||\n        (!valIsDefined && othIsReflexive) ||\n        !valIsReflexive) {\n      return 1;\n    }\n    if ((!valIsNull && !valIsSymbol && !othIsSymbol && value < other) ||\n        (othIsSymbol && valIsDefined && valIsReflexive && !valIsNull && !valIsSymbol) ||\n        (othIsNull && valIsDefined && valIsReflexive) ||\n        (!othIsDefined && valIsReflexive) ||\n        !othIsReflexive) {\n      return -1;\n    }\n  }\n  return 0;\n}\n\nexport default compareAscending;\n", "import compareAscending from './_compareAscending.js';\n\n/**\n * Used by `_.orderBy` to compare multiple properties of a value to another\n * and stable sort them.\n *\n * If `orders` is unspecified, all values are sorted in ascending order. Otherwise,\n * specify an order of \"desc\" for descending or \"asc\" for ascending sort order\n * of corresponding values.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {boolean[]|string[]} orders The order to sort by for each property.\n * @returns {number} Returns the sort order indicator for `object`.\n */\nfunction compareMultiple(object, other, orders) {\n  var index = -1,\n      objCriteria = object.criteria,\n      othCriteria = other.criteria,\n      length = objCriteria.length,\n      ordersLength = orders.length;\n\n  while (++index < length) {\n    var result = compareAscending(objCriteria[index], othCriteria[index]);\n    if (result) {\n      if (index >= ordersLength) {\n        return result;\n      }\n      var order = orders[index];\n      return result * (order == 'desc' ? -1 : 1);\n    }\n  }\n  // Fixes an `Array#sort` bug in the JS engine embedded in Adobe applications\n  // that causes it, under certain circumstances, to provide the same value for\n  // `object` and `other`. See https://github.com/jashkenas/underscore/pull/1247\n  // for more details.\n  //\n  // This also ensures a stable sort in V8 and other engines.\n  // See https://bugs.chromium.org/p/v8/issues/detail?id=90 for more details.\n  return object.index - other.index;\n}\n\nexport default compareMultiple;\n", "import arrayMap from './_arrayMap.js';\nimport baseGet from './_baseGet.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseMap from './_baseMap.js';\nimport baseSortBy from './_baseSortBy.js';\nimport baseUnary from './_baseUnary.js';\nimport compareMultiple from './_compareMultiple.js';\nimport identity from './identity.js';\nimport isArray from './isArray.js';\n\n/**\n * The base implementation of `_.orderBy` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.\n * @param {string[]} orders The sort orders of `iteratees`.\n * @returns {Array} Returns the new sorted array.\n */\nfunction baseOrderBy(collection, iteratees, orders) {\n  if (iteratees.length) {\n    iteratees = arrayMap(iteratees, function(iteratee) {\n      if (isArray(iteratee)) {\n        return function(value) {\n          return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);\n        }\n      }\n      return iteratee;\n    });\n  } else {\n    iteratees = [identity];\n  }\n\n  var index = -1;\n  iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n\n  var result = baseMap(collection, function(value, key, collection) {\n    var criteria = arrayMap(iteratees, function(iteratee) {\n      return iteratee(value);\n    });\n    return { 'criteria': criteria, 'index': ++index, 'value': value };\n  });\n\n  return baseSortBy(result, function(object, other) {\n    return compareMultiple(object, other, orders);\n  });\n}\n\nexport default baseOrderBy;\n", "import baseFlatten from './_baseFlatten.js';\nimport baseOrderBy from './_baseOrderBy.js';\nimport baseRest from './_baseRest.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Creates an array of elements, sorted in ascending order by the results of\n * running each element in a collection thru each iteratee. This method\n * performs a stable sort, that is, it preserves the original sort order of\n * equal elements. The iteratees are invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {...(Function|Function[])} [iteratees=[_.identity]]\n *  The iteratees to sort by.\n * @returns {Array} Returns the new sorted array.\n * @example\n *\n * var users = [\n *   { 'user': 'fred',   'age': 48 },\n *   { 'user': 'barney', 'age': 36 },\n *   { 'user': 'fred',   'age': 30 },\n *   { 'user': 'barney', 'age': 34 }\n * ];\n *\n * _.sortBy(users, [function(o) { return o.user; }]);\n * // => objects for [['barney', 36], ['barney', 34], ['fred', 48], ['fred', 30]]\n *\n * _.sortBy(users, ['user', 'age']);\n * // => objects for [['barney', 34], ['barney', 36], ['fred', 30], ['fred', 48]]\n */\nvar sortBy = baseRest(function(collection, iteratees) {\n  if (collection == null) {\n    return [];\n  }\n  var length = iteratees.length;\n  if (length > 1 && isIterateeCall(collection, iteratees[0], iteratees[1])) {\n    iteratees = [];\n  } else if (length > 2 && isIterateeCall(iteratees[0], iteratees[1], iteratees[2])) {\n    iteratees = [iteratees[0]];\n  }\n  return baseOrderBy(collection, baseFlatten(iteratees, 1), []);\n});\n\nexport default sortBy;\n", "import * as _ from 'lodash-es';\n\nexport { crossCount };\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i - 1], layering[i]);\n  }\n  return cc;\n}\n\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(\n    southLayer,\n    _.map(southLayer, function (v, i) {\n      return i;\n    }),\n  );\n  var southEntries = _.flatten(\n    _.map(northLayer, function (v) {\n      return _.sortBy(\n        _.map(g.outEdges(v), function (e) {\n          return { pos: southPos[e.w], weight: g.edge(e).weight };\n        }),\n        'pos',\n      );\n    }),\n  );\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function () {\n    return 0;\n  });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(\n    // @ts-expect-error\n    southEntries.forEach(function (entry) {\n      var index = entry.pos + firstIndex;\n      tree[index] += entry.weight;\n      var weightSum = 0;\n      // @ts-expect-error\n      while (index > 0) {\n        // @ts-expect-error\n        if (index % 2) {\n          weightSum += tree[index + 1];\n        }\n        // @ts-expect-error\n        index = (index - 1) >> 1;\n        tree[index] += entry.weight;\n      }\n      cc += entry.weight * weightSum;\n    }),\n  );\n\n  return cc;\n}\n", "import * as _ from 'lodash-es';\n\nexport { resolveConflicts };\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function (entry, i) {\n    var tmp = (mappedEntries[entry.v] = {\n      indegree: 0,\n      in: [],\n      out: [],\n      vs: [entry.v],\n      i: i,\n    });\n    if (!_.isUndefined(entry.barycenter)) {\n      // @ts-expect-error\n      tmp.barycenter = entry.barycenter;\n      // @ts-expect-error\n      tmp.weight = entry.weight;\n    }\n  });\n\n  _.forEach(cg.edges(), function (e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n\n  var sourceSet = _.filter(mappedEntries, function (entry) {\n    // @ts-expect-error\n    return !entry.indegree;\n  });\n\n  return doResolveConflicts(sourceSet);\n}\n\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n\n  function handleIn(vEntry) {\n    return function (uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (\n        _.isUndefined(uEntry.barycenter) ||\n        _.isUndefined(vEntry.barycenter) ||\n        uEntry.barycenter >= vEntry.barycenter\n      ) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n\n  function handleOut(vEntry) {\n    return function (wEntry) {\n      wEntry['in'].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry['in'].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n\n  return _.map(\n    _.filter(entries, function (entry) {\n      return !entry.merged;\n    }),\n    function (entry) {\n      return _.pick(entry, ['vs', 'i', 'barycenter', 'weight']);\n    },\n  );\n}\n\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\n\nexport { sort };\n\nfunction sort(entries, biasRight) {\n  var parts = util.partition(entries, function (entry) {\n    return Object.prototype.hasOwnProperty.call(entry, 'barycenter');\n  });\n  var sortable = parts.lhs,\n    unsortable = _.sortBy(parts.rhs, function (entry) {\n      return -entry.i;\n    }),\n    vs = [],\n    sum = 0,\n    weight = 0,\n    vsIndex = 0;\n\n  sortable.sort(compareWithBias(!!biasRight));\n\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n\n  _.forEach(sortable, function (entry) {\n    vsIndex += entry.vs.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n\n  var result = { vs: _.flatten(vs) };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n}\n\nfunction consumeUnsortable(vs, unsortable, index) {\n  var last;\n  while (unsortable.length && (last = _.last(unsortable)).i <= index) {\n    unsortable.pop();\n    vs.push(last.vs);\n    index++;\n  }\n  return index;\n}\n\nfunction compareWithBias(bias) {\n  return function (entryV, entryW) {\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    } else if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n}\n", "import * as _ from 'lodash-es';\nimport { barycenter } from './barycenter.js';\nimport { resolveConflicts } from './resolve-conflicts.js';\nimport { sort } from './sort.js';\n\nexport { sortSubgraph };\n\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight : undefined;\n  var subgraphs = {};\n\n  if (bl) {\n    movable = _.filter(movable, function (w) {\n      return w !== bl && w !== br;\n    });\n  }\n\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function (entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (Object.prototype.hasOwnProperty.call(subgraphResult, 'barycenter')) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n\n  var result = sort(entries, biasRight);\n\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br]);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!Object.prototype.hasOwnProperty.call(result, 'barycenter')) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter =\n        (result.barycenter * result.weight + blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n\n  return result;\n}\n\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function (entry) {\n    entry.vs = _.flatten(\n      entry.vs.map(function (v) {\n        if (subgraphs[v]) {\n          return subgraphs[v].vs;\n        }\n        return v;\n      }),\n    );\n  });\n}\n\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter =\n      (target.barycenter * target.weight + other.barycenter * other.weight) /\n      (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}\n", "import * as _ from 'lodash-es';\n\nexport { barycenter };\n\nfunction barycenter(g, movable) {\n  return _.map(movable, function (v) {\n    var inV = g.inEdges(v);\n    if (!inV.length) {\n      return { v: v };\n    } else {\n      var result = _.reduce(\n        inV,\n        function (acc, e) {\n          var edge = g.edge(e),\n            nodeU = g.node(e.v);\n          return {\n            sum: acc.sum + edge.weight * nodeU.order,\n            weight: acc.weight + edge.weight,\n          };\n        },\n        { sum: 0, weight: 0 },\n      );\n\n      return {\n        v: v,\n        barycenter: result.sum / result.weight,\n        weight: result.weight,\n      };\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\nimport { addSubgraphConstraints } from './add-subgraph-constraints.js';\nimport { buildLayerGraph } from './build-layer-graph.js';\nimport { crossCount } from './cross-count.js';\nimport { initOrder } from './init-order.js';\nimport { sortSubgraph } from './sort-subgraph.js';\n\nexport { order };\n\n/*\n * Applies heuristics to minimize edge crossings in the graph and sets the best\n * order solution as an order attribute on each node.\n *\n * Pre-conditions:\n *\n *    1. Graph must be DAG\n *    2. Graph nodes must be objects with a \"rank\" attribute\n *    3. Graph edges must have the \"weight\" attribute\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have an \"order\" attribute based on the results of the\n *       algorithm.\n */\nfunction order(g) {\n  var maxRank = util.maxRank(g),\n    downLayerGraphs = buildLayerGraphs(g, _.range(1, maxRank + 1), 'inEdges'),\n    upLayerGraphs = buildLayerGraphs(g, _.range(maxRank - 1, -1, -1), 'outEdges');\n\n  var layering = initOrder(g);\n  assignOrder(g, layering);\n\n  var bestCC = Number.POSITIVE_INFINITY,\n    best;\n\n  for (var i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {\n    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2);\n\n    layering = util.buildLayerMatrix(g);\n    var cc = crossCount(g, layering);\n    if (cc < bestCC) {\n      lastBest = 0;\n      best = _.cloneDeep(layering);\n      bestCC = cc;\n    }\n  }\n\n  assignOrder(g, best);\n}\n\nfunction buildLayerGraphs(g, ranks, relationship) {\n  return _.map(ranks, function (rank) {\n    return buildLayerGraph(g, rank, relationship);\n  });\n}\n\nfunction sweepLayerGraphs(layerGraphs, biasRight) {\n  var cg = new Graph();\n  _.forEach(layerGraphs, function (lg) {\n    var root = lg.graph().root;\n    var sorted = sortSubgraph(lg, root, cg, biasRight);\n    _.forEach(sorted.vs, function (v, i) {\n      lg.node(v).order = i;\n    });\n    addSubgraphConstraints(lg, cg, sorted.vs);\n  });\n}\n\nfunction assignOrder(g, layering) {\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, i) {\n      g.node(v).order = i;\n    });\n  });\n}\n", "import * as _ from 'lodash-es';\n\n/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nexport function initOrder(g) {\n  var visited = {};\n  var simpleNodes = _.filter(g.nodes(), function (v) {\n    return !g.children(v).length;\n  });\n  var maxRank = _.max(\n    _.map(simpleNodes, function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  var layers = _.map(_.range(maxRank + 1), function () {\n    return [];\n  });\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    var node = g.node(v);\n    layers[node.rank].push(v);\n    _.forEach(g.successors(v), dfs);\n  }\n\n  var orderedVs = _.sortBy(simpleNodes, function (v) {\n    return g.node(v).rank;\n  });\n  _.forEach(orderedVs, dfs);\n\n  return layers;\n}\n", "import * as _ from 'lodash-es';\n\nexport { addSubgraphConstraints };\n\nfunction addSubgraphConstraints(g, cg, vs) {\n  var prev = {},\n    rootPrev;\n\n  _.forEach(vs, function (v) {\n    var child = g.parent(v),\n      parent,\n      prevChild;\n    while (child) {\n      parent = g.parent(child);\n      if (parent) {\n        prevChild = prev[parent];\n        prev[parent] = child;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child;\n      }\n      if (prevChild && prevChild !== child) {\n        cg.setEdge(prevChild, child);\n        return;\n      }\n      child = parent;\n    }\n  });\n\n  /*\n  function dfs(v) {\n    var children = v ? g.children(v) : g.children();\n    if (children.length) {\n      var min = Number.POSITIVE_INFINITY,\n          subgraphs = [];\n      _.each(children, function(child) {\n        var childMin = dfs(child);\n        if (g.children(child).length) {\n          subgraphs.push({ v: child, order: childMin });\n        }\n        min = Math.min(min, childMin);\n      });\n      _.reduce(_.sortBy(subgraphs, \"order\"), function(prev, curr) {\n        cg.setEdge(prev.v, curr.v);\n        return curr;\n      });\n      return min;\n    }\n    return g.node(v).order;\n  }\n  dfs(undefined);\n  */\n}\n", "import * as _ from 'lodash-es';\n\nexport { parentDummyChains };\n\nfunction parentDummyChains(g) {\n  var postorderNums = postorder(g);\n\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var edgeObj = node.edgeObj;\n    var pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);\n    var path = pathData.path;\n    var lca = pathData.lca;\n    var pathIdx = 0;\n    var pathV = path[pathIdx];\n    var ascending = true;\n\n    while (v !== edgeObj.w) {\n      node = g.node(v);\n\n      if (ascending) {\n        while ((pathV = path[pathIdx]) !== lca && g.node(pathV).maxRank < node.rank) {\n          pathIdx++;\n        }\n\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n\n      if (!ascending) {\n        while (\n          pathIdx < path.length - 1 &&\n          g.node((pathV = path[pathIdx + 1])).minRank <= node.rank\n        ) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n\n      g.setParent(v, pathV);\n      v = g.successors(v)[0];\n    }\n  });\n}\n\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nfunction findPath(g, postorderNums, v, w) {\n  var vPath = [];\n  var wPath = [];\n  var low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  var lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  var parent;\n  var lca;\n\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = g.parent(parent);\n    vPath.push(parent);\n  } while (parent && (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n\n  // Traverse from w to LCA\n  parent = w;\n  while ((parent = g.parent(parent)) !== lca) {\n    wPath.push(parent);\n  }\n\n  return { path: vPath.concat(wPath.reverse()), lca: lca };\n}\n\nfunction postorder(g) {\n  var result = {};\n  var lim = 0;\n\n  function dfs(v) {\n    var low = lim;\n    _.forEach(g.children(v), dfs);\n    result[v] = { low: low, lim: lim++ };\n  }\n  _.forEach(g.children(), dfs);\n\n  return result;\n}\n", "import baseForOwn from './_baseForOwn.js';\nimport castFunction from './_castFunction.js';\n\n/**\n * Iterates over own enumerable string keyed properties of an object and\n * invokes `iteratee` for each property. The iteratee is invoked with three\n * arguments: (value, key, object). Iteratee functions may exit iteration\n * early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 0.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forOwnRight\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forOwn(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a' then 'b' (iteration order is not guaranteed).\n */\nfunction forOwn(object, iteratee) {\n  return object && baseForOwn(object, castFunction(iteratee));\n}\n\nexport default forOwn;\n", "import baseFor from './_baseFor.js';\nimport castFunction from './_castFunction.js';\nimport keysIn from './keysIn.js';\n\n/**\n * Iterates over own and inherited enumerable string keyed properties of an\n * object and invokes `iteratee` for each property. The iteratee is invoked\n * with three arguments: (value, key, object). Iteratee functions may exit\n * iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 0.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forInRight\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forIn(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a', 'b', then 'c' (iteration order is not guaranteed).\n */\nfunction forIn(object, iteratee) {\n  return object == null\n    ? object\n    : baseFor(object, castFunction(iteratee), keysIn);\n}\n\nexport default forIn;\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\n\n/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\n\nexport {\n  positionX,\n  findType1Conflicts,\n  findType2Conflicts,\n  addConflict,\n  hasConflict,\n  verticalAlignment,\n  horizontalCompaction,\n  alignCoordinates,\n  findSmallestWidthAlignment,\n  balance,\n};\n\n/*\n * Marks all edges in the graph with a type-1 conflict with the \"type1Conflict\"\n * property. A type-1 conflict is one where a non-inner segment crosses an\n * inner segment. An inner segment is an edge with both incident nodes marked\n * with the \"dummy\" property.\n *\n * This algorithm scans layer by layer, starting with the second, for type-1\n * conflicts between the current layer and the previous layer. For each layer\n * it scans the nodes from left to right until it reaches one that is incident\n * on an inner segment. It then scans predecessors to determine if they have\n * edges that cross that inner segment. At the end a final scan is done for all\n * nodes on the current rank to see if they cross the last visited inner\n * segment.\n *\n * This algorithm (safely) assumes that a dummy node will only be incident on a\n * single node in the layers being scanned.\n */\nfunction findType1Conflicts(g, layering) {\n  var conflicts = {};\n\n  function visitLayer(prevLayer, layer) {\n    var // last visited node in the previous layer that is incident on an inner\n      // segment.\n      k0 = 0,\n      // Tracks the last node in this layer scanned for crossings with a type-1\n      // segment.\n      scanPos = 0,\n      prevLayerLength = prevLayer.length,\n      lastNode = _.last(layer);\n\n    _.forEach(layer, function (v, i) {\n      var w = findOtherInnerSegmentNode(g, v),\n        k1 = w ? g.node(w).order : prevLayerLength;\n\n      if (w || v === lastNode) {\n        _.forEach(layer.slice(scanPos, i + 1), function (scanNode) {\n          _.forEach(g.predecessors(scanNode), function (u) {\n            var uLabel = g.node(u),\n              uPos = uLabel.order;\n            if ((uPos < k0 || k1 < uPos) && !(uLabel.dummy && g.node(scanNode).dummy)) {\n              addConflict(conflicts, u, scanNode);\n            }\n          });\n        });\n        // @ts-expect-error\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n\n    return layer;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findType2Conflicts(g, layering) {\n  var conflicts = {};\n\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    var v;\n    _.forEach(_.range(southPos, southEnd), function (i) {\n      v = south[i];\n      if (g.node(v).dummy) {\n        _.forEach(g.predecessors(v), function (u) {\n          var uNode = g.node(u);\n          if (uNode.dummy && (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {\n            addConflict(conflicts, u, v);\n          }\n        });\n      }\n    });\n  }\n\n  function visitLayer(north, south) {\n    var prevNorthPos = -1,\n      nextNorthPos,\n      southPos = 0;\n\n    _.forEach(south, function (v, southLookahead) {\n      if (g.node(v).dummy === 'border') {\n        var predecessors = g.predecessors(v);\n        if (predecessors.length) {\n          nextNorthPos = g.node(predecessors[0]).order;\n          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);\n          // @ts-expect-error\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scan(south, southPos, south.length, nextNorthPos, north.length);\n    });\n\n    return south;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findOtherInnerSegmentNode(g, v) {\n  if (g.node(v).dummy) {\n    return _.find(g.predecessors(v), function (u) {\n      return g.node(u).dummy;\n    });\n  }\n}\n\nfunction addConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n\n  var conflictsV = conflicts[v];\n  if (!conflictsV) {\n    conflicts[v] = conflictsV = {};\n  }\n  conflictsV[w] = true;\n}\n\nfunction hasConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return !!conflicts[v] && Object.prototype.hasOwnProperty.call(conflicts[v], w);\n}\n\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nfunction verticalAlignment(g, layering, conflicts, neighborFn) {\n  var root = {},\n    align = {},\n    pos = {};\n\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, order) {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n\n  _.forEach(layering, function (layer) {\n    var prevIdx = -1;\n    _.forEach(layer, function (v) {\n      var ws = neighborFn(v);\n      if (ws.length) {\n        ws = _.sortBy(ws, function (w) {\n          return pos[w];\n        });\n        var mp = (ws.length - 1) / 2;\n        for (var i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          var w = ws[i];\n          if (align[v] === v && prevIdx < pos[w] && !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n\n  return { root: root, align: align };\n}\n\nfunction horizontalCompaction(g, layering, root, align, reverseSep) {\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  var xs = {},\n    blockG = buildBlockGraph(g, layering, root, reverseSep),\n    borderType = reverseSep ? 'borderLeft' : 'borderRight';\n\n  function iterate(setXsFunc, nextNodesFunc) {\n    var stack = blockG.nodes();\n    var elem = stack.pop();\n    var visited = {};\n    while (elem) {\n      if (visited[elem]) {\n        setXsFunc(elem);\n      } else {\n        visited[elem] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem));\n      }\n\n      elem = stack.pop();\n    }\n  }\n\n  // First pass, assign smallest coordinates\n  function pass1(elem) {\n    xs[elem] = blockG.inEdges(elem).reduce(function (acc, e) {\n      return Math.max(acc, xs[e.v] + blockG.edge(e));\n    }, 0);\n  }\n\n  // Second pass, assign greatest coordinates\n  function pass2(elem) {\n    var min = blockG.outEdges(elem).reduce(function (acc, e) {\n      return Math.min(acc, xs[e.w] - blockG.edge(e));\n    }, Number.POSITIVE_INFINITY);\n\n    var node = g.node(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  }\n\n  iterate(pass1, blockG.predecessors.bind(blockG));\n  iterate(pass2, blockG.successors.bind(blockG));\n\n  // Assign x coordinates to all nodes\n  _.forEach(align, function (v) {\n    xs[v] = xs[root[v]];\n  });\n\n  return xs;\n}\n\nfunction buildBlockGraph(g, layering, root, reverseSep) {\n  var blockGraph = new Graph(),\n    graphLabel = g.graph(),\n    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);\n\n  _.forEach(layering, function (layer) {\n    var u;\n    _.forEach(layer, function (v) {\n      var vRoot = root[v];\n      blockGraph.setNode(vRoot);\n      if (u) {\n        var uRoot = root[u],\n          prevMax = blockGraph.edge(uRoot, vRoot);\n        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));\n      }\n      u = v;\n    });\n  });\n\n  return blockGraph;\n}\n\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nfunction findSmallestWidthAlignment(g, xss) {\n  return _.minBy(_.values(xss), function (xs) {\n    var max = Number.NEGATIVE_INFINITY;\n    var min = Number.POSITIVE_INFINITY;\n\n    _.forIn(xs, function (x, v) {\n      var halfWidth = width(g, v) / 2;\n\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n\n    return max - min;\n  });\n}\n\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nfunction alignCoordinates(xss, alignTo) {\n  var alignToVals = _.values(alignTo),\n    alignToMin = _.min(alignToVals),\n    alignToMax = _.max(alignToVals);\n\n  _.forEach(['u', 'd'], function (vert) {\n    _.forEach(['l', 'r'], function (horiz) {\n      var alignment = vert + horiz,\n        xs = xss[alignment],\n        delta;\n      if (xs === alignTo) return;\n\n      var xsVals = _.values(xs);\n      delta = horiz === 'l' ? alignToMin - _.min(xsVals) : alignToMax - _.max(xsVals);\n\n      if (delta) {\n        xss[alignment] = _.mapValues(xs, function (x) {\n          return x + delta;\n        });\n      }\n    });\n  });\n}\n\nfunction balance(xss, align) {\n  return _.mapValues(xss.ul, function (ignore, v) {\n    if (align) {\n      return xss[align.toLowerCase()][v];\n    } else {\n      var xs = _.sortBy(_.map(xss, v));\n      return (xs[1] + xs[2]) / 2;\n    }\n  });\n}\n\nfunction positionX(g) {\n  var layering = util.buildLayerMatrix(g);\n  var conflicts = _.merge(findType1Conflicts(g, layering), findType2Conflicts(g, layering));\n\n  var xss = {};\n  var adjustedLayering;\n  _.forEach(['u', 'd'], function (vert) {\n    adjustedLayering = vert === 'u' ? layering : _.values(layering).reverse();\n    _.forEach(['l', 'r'], function (horiz) {\n      if (horiz === 'r') {\n        adjustedLayering = _.map(adjustedLayering, function (inner) {\n          return _.values(inner).reverse();\n        });\n      }\n\n      var neighborFn = (vert === 'u' ? g.predecessors : g.successors).bind(g);\n      var align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      var xs = horizontalCompaction(g, adjustedLayering, align.root, align.align, horiz === 'r');\n      if (horiz === 'r') {\n        xs = _.mapValues(xs, function (x) {\n          return -x;\n        });\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n\n  var smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, g.graph().align);\n}\n\nfunction sep(nodeSep, edgeSep, reverseSep) {\n  return function (g, v, w) {\n    var vLabel = g.node(v);\n    var wLabel = g.node(w);\n    var sum = 0;\n    var delta;\n\n    sum += vLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(vLabel, 'labelpos')) {\n      switch (vLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = -vLabel.width / 2;\n          break;\n        case 'r':\n          delta = vLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;\n\n    sum += wLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(wLabel, 'labelpos')) {\n      switch (wLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = wLabel.width / 2;\n          break;\n        case 'r':\n          delta = -wLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    return sum;\n  };\n}\n\nfunction width(g, v) {\n  return g.node(v).width;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\nimport { positionX } from './bk.js';\n\nexport { position };\n\nfunction position(g) {\n  g = util.asNonCompoundGraph(g);\n\n  positionY(g);\n  _.forOwn(positionX(g), function (x, v) {\n    g.node(v).x = x;\n  });\n}\n\nfunction positionY(g) {\n  var layering = util.buildLayerMatrix(g);\n  var rankSep = g.graph().ranksep;\n  var prevY = 0;\n  _.forEach(layering, function (layer) {\n    var maxHeight = _.max(\n      _.map(layer, function (v) {\n        return g.node(v).height;\n      }),\n    );\n    _.forEach(layer, function (v) {\n      g.node(v).y = prevY + maxHeight / 2;\n    });\n    prevY += maxHeight + rankSep;\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { addBorderSegments } from './add-border-segments.js';\nimport * as coordinateSystem from './coordinate-system.js';\nimport * as acyclic from './acyclic.js';\nimport * as normalize from './normalize.js';\nimport { rank } from './rank/index.js';\nimport * as nestingGraph from './nesting-graph.js';\nimport { order } from './order/index.js';\nimport { parentDummyChains } from './parent-dummy-chains.js';\nimport { position } from './position/index.js';\nimport * as util from './util.js';\n\nexport { layout };\n\nfunction layout(g, opts) {\n  var time = opts && opts.debugTiming ? util.time : util.notime;\n  time('layout', () => {\n    var layoutGraph = time('  buildLayoutGraph', () => buildLayoutGraph(g));\n    time('  runLayout', () => runLayout(layoutGraph, time));\n    time('  updateInputGraph', () => updateInputGraph(g, layoutGraph));\n  });\n}\n\nfunction runLayout(g, time) {\n  time('    makeSpaceForEdgeLabels', () => makeSpaceForEdgeLabels(g));\n  time('    removeSelfEdges', () => removeSelfEdges(g));\n  time('    acyclic', () => acyclic.run(g));\n  time('    nestingGraph.run', () => nestingGraph.run(g));\n  time('    rank', () => rank(util.asNonCompoundGraph(g)));\n  time('    injectEdgeLabelProxies', () => injectEdgeLabelProxies(g));\n  time('    removeEmptyRanks', () => util.removeEmptyRanks(g));\n  time('    nestingGraph.cleanup', () => nestingGraph.cleanup(g));\n  time('    normalizeRanks', () => util.normalizeRanks(g));\n  time('    assignRankMinMax', () => assignRankMinMax(g));\n  time('    removeEdgeLabelProxies', () => removeEdgeLabelProxies(g));\n  time('    normalize.run', () => normalize.run(g));\n  time('    parentDummyChains', () => parentDummyChains(g));\n  time('    addBorderSegments', () => addBorderSegments(g));\n  time('    order', () => order(g));\n  time('    insertSelfEdges', () => insertSelfEdges(g));\n  time('    adjustCoordinateSystem', () => coordinateSystem.adjust(g));\n  time('    position', () => position(g));\n  time('    positionSelfEdges', () => positionSelfEdges(g));\n  time('    removeBorderNodes', () => removeBorderNodes(g));\n  time('    normalize.undo', () => normalize.undo(g));\n  time('    fixupEdgeLabelCoords', () => fixupEdgeLabelCoords(g));\n  time('    undoCoordinateSystem', () => coordinateSystem.undo(g));\n  time('    translateGraph', () => translateGraph(g));\n  time('    assignNodeIntersects', () => assignNodeIntersects(g));\n  time('    reversePoints', () => reversePointsForReversedEdges(g));\n  time('    acyclic.undo', () => acyclic.undo(g));\n}\n\n/*\n * Copies final layout information from the layout graph back to the input\n * graph. This process only copies whitelisted attributes from the layout graph\n * to the input graph, so it serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction updateInputGraph(inputGraph, layoutGraph) {\n  _.forEach(inputGraph.nodes(), function (v) {\n    var inputLabel = inputGraph.node(v);\n    var layoutLabel = layoutGraph.node(v);\n\n    if (inputLabel) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n\n      if (layoutGraph.children(v).length) {\n        inputLabel.width = layoutLabel.width;\n        inputLabel.height = layoutLabel.height;\n      }\n    }\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var inputLabel = inputGraph.edge(e);\n    var layoutLabel = layoutGraph.edge(e);\n\n    inputLabel.points = layoutLabel.points;\n    if (Object.prototype.hasOwnProperty.call(layoutLabel, 'x')) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n    }\n  });\n\n  inputGraph.graph().width = layoutGraph.graph().width;\n  inputGraph.graph().height = layoutGraph.graph().height;\n}\n\nvar graphNumAttrs = ['nodesep', 'edgesep', 'ranksep', 'marginx', 'marginy'];\nvar graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: 'tb' };\nvar graphAttrs = ['acyclicer', 'ranker', 'rankdir', 'align'];\nvar nodeNumAttrs = ['width', 'height'];\nvar nodeDefaults = { width: 0, height: 0 };\nvar edgeNumAttrs = ['minlen', 'weight', 'width', 'height', 'labeloffset'];\nvar edgeDefaults = {\n  minlen: 1,\n  weight: 1,\n  width: 0,\n  height: 0,\n  labeloffset: 10,\n  labelpos: 'r',\n};\nvar edgeAttrs = ['labelpos'];\n\n/*\n * Constructs a new graph from the input graph, which can be used for layout.\n * This process copies only whitelisted attributes from the input graph to the\n * layout graph. Thus this function serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction buildLayoutGraph(inputGraph) {\n  var g = new Graph({ multigraph: true, compound: true });\n  var graph = canonicalize(inputGraph.graph());\n\n  g.setGraph(\n    _.merge({}, graphDefaults, selectNumberAttrs(graph, graphNumAttrs), _.pick(graph, graphAttrs)),\n  );\n\n  _.forEach(inputGraph.nodes(), function (v) {\n    var node = canonicalize(inputGraph.node(v));\n    g.setNode(v, _.defaults(selectNumberAttrs(node, nodeNumAttrs), nodeDefaults));\n    g.setParent(v, inputGraph.parent(v));\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var edge = canonicalize(inputGraph.edge(e));\n    g.setEdge(\n      e,\n      _.merge({}, edgeDefaults, selectNumberAttrs(edge, edgeNumAttrs), _.pick(edge, edgeAttrs)),\n    );\n  });\n\n  return g;\n}\n\n/*\n * This idea comes from the Gansner paper: to account for edge labels in our\n * layout we split each rank in half by doubling minlen and halving ranksep.\n * Then we can place labels at these mid-points between nodes.\n *\n * We also add some minimal padding to the width to push the label for the edge\n * away from the edge itself a bit.\n */\nfunction makeSpaceForEdgeLabels(g) {\n  var graph = g.graph();\n  graph.ranksep /= 2;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    edge.minlen *= 2;\n    if (edge.labelpos.toLowerCase() !== 'c') {\n      if (graph.rankdir === 'TB' || graph.rankdir === 'BT') {\n        edge.width += edge.labeloffset;\n      } else {\n        edge.height += edge.labeloffset;\n      }\n    }\n  });\n}\n\n/*\n * Creates temporary dummy nodes that capture the rank in which each edge's\n * label is going to, if it has one of non-zero width and height. We do this\n * so that we can safely remove empty ranks while preserving balance for the\n * label's position.\n */\nfunction injectEdgeLabelProxies(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.width && edge.height) {\n      var v = g.node(e.v);\n      var w = g.node(e.w);\n      var label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e };\n      util.addDummyNode(g, 'edge-proxy', label, '_ep');\n    }\n  });\n}\n\nfunction assignRankMinMax(g) {\n  var maxRank = 0;\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.borderTop) {\n      node.minRank = g.node(node.borderTop).rank;\n      node.maxRank = g.node(node.borderBottom).rank;\n      // @ts-expect-error\n      maxRank = _.max(maxRank, node.maxRank);\n    }\n  });\n  g.graph().maxRank = maxRank;\n}\n\nfunction removeEdgeLabelProxies(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'edge-proxy') {\n      g.edge(node.e).labelRank = node.rank;\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction translateGraph(g) {\n  var minX = Number.POSITIVE_INFINITY;\n  var maxX = 0;\n  var minY = Number.POSITIVE_INFINITY;\n  var maxY = 0;\n  var graphLabel = g.graph();\n  var marginX = graphLabel.marginx || 0;\n  var marginY = graphLabel.marginy || 0;\n\n  function getExtremes(attrs) {\n    var x = attrs.x;\n    var y = attrs.y;\n    var w = attrs.width;\n    var h = attrs.height;\n    minX = Math.min(minX, x - w / 2);\n    maxX = Math.max(maxX, x + w / 2);\n    minY = Math.min(minY, y - h / 2);\n    maxY = Math.max(maxY, y + h / 2);\n  }\n\n  _.forEach(g.nodes(), function (v) {\n    getExtremes(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      getExtremes(edge);\n    }\n  });\n\n  minX -= marginX;\n  minY -= marginY;\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    node.x -= minX;\n    node.y -= minY;\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, function (p) {\n      p.x -= minX;\n      p.y -= minY;\n    });\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      edge.x -= minX;\n    }\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      edge.y -= minY;\n    }\n  });\n\n  graphLabel.width = maxX - minX + marginX;\n  graphLabel.height = maxY - minY + marginY;\n}\n\nfunction assignNodeIntersects(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    var nodeV = g.node(e.v);\n    var nodeW = g.node(e.w);\n    var p1, p2;\n    if (!edge.points) {\n      edge.points = [];\n      p1 = nodeW;\n      p2 = nodeV;\n    } else {\n      p1 = edge.points[0];\n      p2 = edge.points[edge.points.length - 1];\n    }\n    edge.points.unshift(util.intersectRect(nodeV, p1));\n    edge.points.push(util.intersectRect(nodeW, p2));\n  });\n}\n\nfunction fixupEdgeLabelCoords(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      if (edge.labelpos === 'l' || edge.labelpos === 'r') {\n        edge.width -= edge.labeloffset;\n      }\n      switch (edge.labelpos) {\n        case 'l':\n          edge.x -= edge.width / 2 + edge.labeloffset;\n          break;\n        case 'r':\n          edge.x += edge.width / 2 + edge.labeloffset;\n          break;\n      }\n    }\n  });\n}\n\nfunction reversePointsForReversedEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.reversed) {\n      edge.points.reverse();\n    }\n  });\n}\n\nfunction removeBorderNodes(g) {\n  _.forEach(g.nodes(), function (v) {\n    if (g.children(v).length) {\n      var node = g.node(v);\n      var t = g.node(node.borderTop);\n      var b = g.node(node.borderBottom);\n      var l = g.node(_.last(node.borderLeft));\n      var r = g.node(_.last(node.borderRight));\n\n      node.width = Math.abs(r.x - l.x);\n      node.height = Math.abs(b.y - t.y);\n      node.x = l.x + node.width / 2;\n      node.y = t.y + node.height / 2;\n    }\n  });\n\n  _.forEach(g.nodes(), function (v) {\n    if (g.node(v).dummy === 'border') {\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction removeSelfEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    if (e.v === e.w) {\n      var node = g.node(e.v);\n      if (!node.selfEdges) {\n        node.selfEdges = [];\n      }\n      node.selfEdges.push({ e: e, label: g.edge(e) });\n      g.removeEdge(e);\n    }\n  });\n}\n\nfunction insertSelfEdges(g) {\n  var layers = util.buildLayerMatrix(g);\n  _.forEach(layers, function (layer) {\n    var orderShift = 0;\n    _.forEach(layer, function (v, i) {\n      var node = g.node(v);\n      node.order = i + orderShift;\n      _.forEach(node.selfEdges, function (selfEdge) {\n        util.addDummyNode(\n          g,\n          'selfedge',\n          {\n            width: selfEdge.label.width,\n            height: selfEdge.label.height,\n            rank: node.rank,\n            order: i + ++orderShift,\n            e: selfEdge.e,\n            label: selfEdge.label,\n          },\n          '_se',\n        );\n      });\n      delete node.selfEdges;\n    });\n  });\n}\n\nfunction positionSelfEdges(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'selfedge') {\n      var selfNode = g.node(node.e.v);\n      var x = selfNode.x + selfNode.width / 2;\n      var y = selfNode.y;\n      var dx = node.x - x;\n      var dy = selfNode.height / 2;\n      g.setEdge(node.e, node.label);\n      g.removeNode(v);\n      node.label.points = [\n        { x: x + (2 * dx) / 3, y: y - dy },\n        { x: x + (5 * dx) / 6, y: y - dy },\n        { x: x + dx, y: y },\n        { x: x + (5 * dx) / 6, y: y + dy },\n        { x: x + (2 * dx) / 3, y: y + dy },\n      ];\n      node.label.x = node.x;\n      node.label.y = node.y;\n    }\n  });\n}\n\nfunction selectNumberAttrs(obj, attrs) {\n  return _.mapValues(_.pick(obj, attrs), Number);\n}\n\nfunction canonicalize(attrs) {\n  var newAttrs = {};\n  _.forEach(attrs, function (v, k) {\n    newAttrs[k.toLowerCase()] = v;\n  });\n  return newAttrs;\n}\n", "import baseRest from './_baseRest.js';\nimport eq from './eq.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport keysIn from './keysIn.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns own and inherited enumerable string keyed properties of source\n * objects to the destination object for all destination properties that\n * resolve to `undefined`. Source objects are applied from left to right.\n * Once a property is set, additional values of the same property are ignored.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @see _.defaultsDeep\n * @example\n *\n * _.defaults({ 'a': 1 }, { 'b': 2 }, { 'a': 3 });\n * // => { 'a': 1, 'b': 2 }\n */\nvar defaults = baseRest(function(object, sources) {\n  object = Object(object);\n\n  var index = -1;\n  var length = sources.length;\n  var guard = length > 2 ? sources[2] : undefined;\n\n  if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n    length = 1;\n  }\n\n  while (++index < length) {\n    var source = sources[index];\n    var props = keysIn(source);\n    var propsIndex = -1;\n    var propsLength = props.length;\n\n    while (++propsIndex < propsLength) {\n      var key = props[propsIndex];\n      var value = object[key];\n\n      if (value === undefined ||\n          (eq(value, objectProto[key]) && !hasOwnProperty.call(object, key))) {\n        object[key] = source[key];\n      }\n    }\n  }\n\n  return object;\n});\n\nexport default defaults;\n", "import arrayMap from './_arrayMap.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseMap from './_baseMap.js';\nimport isArray from './isArray.js';\n\n/**\n * Creates an array of values by running each element in `collection` thru\n * `iteratee`. The iteratee is invoked with three arguments:\n * (value, index|key, collection).\n *\n * Many lodash methods are guarded to work as iteratees for methods like\n * `_.every`, `_.filter`, `_.map`, `_.mapValues`, `_.reject`, and `_.some`.\n *\n * The guarded methods are:\n * `ary`, `chunk`, `curry`, `curryRight`, `drop`, `dropRight`, `every`,\n * `fill`, `invert`, `parseInt`, `random`, `range`, `rangeRight`, `repeat`,\n * `sampleSize`, `slice`, `some`, `sortBy`, `split`, `take`, `takeRight`,\n * `template`, `trim`, `trimEnd`, `trimStart`, and `words`\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n * @example\n *\n * function square(n) {\n *   return n * n;\n * }\n *\n * _.map([4, 8], square);\n * // => [16, 64]\n *\n * _.map({ 'a': 4, 'b': 8 }, square);\n * // => [16, 64] (iteration order is not guaranteed)\n *\n * var users = [\n *   { 'user': 'barney' },\n *   { 'user': 'fred' }\n * ];\n *\n * // The `_.property` iteratee shorthand.\n * _.map(users, 'user');\n * // => ['barney', 'fred']\n */\nfunction map(collection, iteratee) {\n  var func = isArray(collection) ? arrayMap : baseMap;\n  return func(collection, baseIteratee(iteratee, 3));\n}\n\nexport default map;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.has` without support for deep paths.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {Array|string} key The key to check.\n * @returns {boolean} Returns `true` if `key` exists, else `false`.\n */\nfunction baseHas(object, key) {\n  return object != null && hasOwnProperty.call(object, key);\n}\n\nexport default baseHas;\n", "import baseHas from './_baseHas.js';\nimport hasPath from './_hasPath.js';\n\n/**\n * Checks if `path` is a direct property of `object`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n * @example\n *\n * var object = { 'a': { 'b': 2 } };\n * var other = _.create({ 'a': _.create({ 'b': 2 }) });\n *\n * _.has(object, 'a');\n * // => true\n *\n * _.has(object, 'a.b');\n * // => true\n *\n * _.has(object, ['a', 'b']);\n * // => true\n *\n * _.has(other, 'a');\n * // => false\n */\nfunction has(object, path) {\n  return object != null && hasPath(object, path, baseHas);\n}\n\nexport default has;\n"], "names": ["findIndexFunc", "collection", "predicate", "fromIndex", "iterable", "Object", "isArrayLike", "iteratee", "baseIteratee", "keys", "key", "index", "undefined", "nativeMax", "Math", "max", "createFind", "array", "length", "toInteger", "baseFindIndex", "baseExtremum", "identity", "baseLt", "result", "Array", "baseEach", "value", "baseFlatten", "isArray", "isObjectLike", "baseGetTag", "comparator", "current", "computed", "isSymbol", "baseRest", "arrays", "baseUniq", "isArrayLikeObject", "GRAPH_NODE", "Graph", "constructor", "opts", "arguments", "this", "_isDirected", "prototype", "hasOwnProperty", "call", "directed", "_isMultigraph", "multigraph", "_isCompound", "compound", "_label", "_defaultNodeLabelFn", "_", "_defaultEdgeLabelFn", "_nodes", "_parent", "_children", "_in", "_preds", "_out", "_sucs", "_edgeObjs", "_edgeLabels", "isDirected", "isMultigraph", "isCompound", "setGraph", "label", "graph", "setDefaultNodeLabel", "newDefault", "nodeCount", "_nodeCount", "nodes", "sources", "self", "v", "sinks", "setNodes", "vs", "args", "setNode", "node", "hasNode", "removeNode", "removeEdge", "e", "_removeFromParentsChildList", "children", "child", "setParent", "parent", "Error", "ancestor", "predecessors", "predsV", "successors", "sucsV", "neighbors", "preds", "<PERSON><PERSON><PERSON><PERSON>", "filterNodes", "filter", "copy", "w", "setEdge", "edge", "parents", "findParent", "setDefaultEdgeLabel", "edgeCount", "_edgeCount", "edges", "set<PERSON>ath", "name", "valueSpecified", "arg0", "edgeArgsToId", "edgeObj", "v_", "w_", "tmp", "edgeArgsToObj", "freeze", "incrementOrInitEntry", "edgeObjToId", "hasEdge", "decrementOrRemoveEntry", "inEdges", "u", "inV", "outEdges", "outV", "nodeEdges", "concat", "map", "k", "reWhitespace", "string", "test", "char<PERSON>t", "reTrimStart", "slice", "trimmedEndIndex", "replace", "reIsBadHex", "reIsBinary", "reIsOctal", "freeParseInt", "parseInt", "isObject", "other", "valueOf", "baseTrim", "isBinary", "INFINITY", "toNumber", "object", "path", "customizer", "<PERSON><PERSON><PERSON>", "lastIndex", "nested", "to<PERSON><PERSON>", "newValue", "objValue", "isIndex", "assignValue", "paths", "baseGet", "baseSet", "toFinite", "remainder", "idCounter", "prefix", "id", "toString", "nativeCeil", "ceil", "start", "end", "step", "fromRight", "isIterateeCall", "baseRange", "createRange", "List", "sentinel", "_next", "_prev", "_sentinel", "dequeue", "entry", "unlink", "enqueue", "strs", "curr", "push", "JSON", "stringify", "filterOutLinks", "join", "DEFAULT_WEIGHT_FN", "greedyFAS", "g", "weightFn", "state", "fasGraph", "maxIn", "maxOut", "in", "out", "prevWeight", "weight", "edgeWeight", "buckets", "zeroIdx", "assignBucket", "buildState", "results", "i", "doGreedyFAS", "collectPredecessors", "uEntry", "wEntry", "run", "fas", "acyclicer", "stack", "visited", "dfs", "dfsFAS", "<PERSON><PERSON><PERSON>", "reversed", "basePickBy", "hasIn", "func", "setToString", "overRest", "flatten", "flatRest", "base<PERSON>ick", "baseGt", "baseForOwn", "baseAssignValue", "root", "Date", "now", "addDummyNode", "type", "attrs", "dummy", "asNonCompoundGraph", "simplified", "intersectRect", "rect", "point", "sx", "sy", "x", "y", "dx", "dy", "width", "h", "height", "abs", "buildLayerMatrix", "layering", "maxRank", "rank", "order", "addBorderNode", "time", "fn", "console", "log", "notime", "prop", "sg", "sgNode", "borderType", "prev", "util", "undo", "rankDir", "rankdir", "toLowerCase", "reverseYOne", "points", "reverseY", "swapXYOne", "swapXY", "swapWidthHeight", "swapWidthHeightOne", "dummy<PERSON><PERSON><PERSON>", "vRank", "wRank", "edgeLabel", "labelRank", "labelpos", "normalizeEdge", "longestPath", "minlen", "Number", "POSITIVE_INFINITY", "slack", "feasibleTree", "delta", "t", "size", "tightTree", "find<PERSON>in<PERSON>lack<PERSON>dge", "shiftRanks", "edgeV", "baseProperty", "RegExp", "rsAstralRange", "rsAstral", "rsCombo", "rsFitz", "rsNonAstral", "rsRegional", "rsSurrPair", "reOptMod", "rsOptVar", "rsSeq", "rsSymbol", "CycleException", "navigation", "bind", "acc", "doDfs", "postorder", "networkSimplex", "simpleLabel", "simplify", "initLowLimValues", "initCutValues", "leaveEdge", "exchangeEdges", "enterEdge", "alg", "childLab", "cutvalue", "calcCutValue", "assignCutValue", "childIsTail", "graphEdge", "cutValue", "isOutEdge", "pointsToHead", "otherWeight", "otherCutValue", "tree", "dfsAssignLowLim", "<PERSON><PERSON><PERSON>", "low", "lim", "vLabel", "w<PERSON><PERSON><PERSON>", "tailLabel", "flip", "candidates", "isDescendant", "f", "flipped", "updateRanks", "rootLabel", "ranker", "networkSimplexRanker", "tightTreeRanker", "longestPathRanker", "depths", "depth", "treeDepths", "nodeSep", "nestingRoot", "sumWeights", "nodeRankFactor", "top", "bottom", "borderTop", "borderBottom", "childNode", "childTop", "childBottom", "thisWeight", "nestingEdge", "baseClone", "CLONE_DEEP_FLAG", "buildLayerGraph", "relationship", "createRootNode", "minRank", "borderLeft", "borderRight", "props", "values", "assignFunc", "vals<PERSON><PERSON><PERSON>", "baseZipObject", "comparer", "sort", "valIsDefined", "valIsNull", "valIsReflexive", "valIsSymbol", "othIsDefined", "othIsNull", "othIsReflexive", "othIsSymbol", "orders", "objCriteria", "criteria", "othCriteria", "ordersLength", "compareAscending", "iteratees", "arrayMap", "baseUnary", "baseMap", "baseSortBy", "compareMultiple", "baseOrderBy", "crossCount", "cc", "twoLayerCrossCount", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "southPos", "southEntries", "pos", "firstIndex", "treeSize", "for<PERSON>ach", "weightSum", "resolveConflicts", "entries", "cg", "mappedEntries", "indegree", "barycenter", "entryV", "entryW", "sourceSet", "handleIn", "vEntry", "merged", "target", "source", "sum", "min", "mergeEntries", "handleOut", "pop", "reverse", "doResolveConflicts", "biasRight", "bias", "parts", "lhs", "rhs", "sortable", "unsortable", "vsIndex", "consumeUnsortable", "last", "sortSubgraph", "movable", "bl", "br", "subgraphs", "barycenters", "nodeU", "subgraphResult", "expandSubgraphs", "blPred", "br<PERSON><PERSON>", "downLayerGraphs", "buildLayerGraphs", "upLayerGraphs", "simpleNodes", "layers", "orderedVs", "initOrder", "assignOrder", "best", "bestCC", "lastBest", "sweepLayerGraphs", "ranks", "layerGraphs", "lg", "sorted", "rootPrev", "prev<PERSON><PERSON><PERSON>", "addSubgraphConstraints", "layer", "parent<PERSON>ummy<PERSON><PERSON><PERSON>", "postorderNums", "pathData", "lca", "vPath", "wPath", "<PERSON><PERSON><PERSON>", "pathIdx", "pathV", "ascending", "castFunction", "baseFor", "keysIn", "findType1Conflicts", "conflicts", "prevLayer", "k0", "scanPos", "prevL<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastNode", "findOtherInnerSegmentNode", "k1", "scanNode", "uLabel", "uPos", "addConflict", "conflictsV", "hasConflict", "horizontalCompaction", "align", "reverseSep", "xs", "blockG", "blockGraph", "graphLabel", "sepFn", "edgeSep", "sep", "nodesep", "edgesep", "vRoot", "uRoot", "prevMax", "buildBlockGraph", "iterate", "setXsFunc", "nextNodesFunc", "elem", "reduce", "positionX", "adjustedLayering", "scan", "south", "southEnd", "prevNorthBorder", "nextNorthBorder", "uNode", "north", "nextNorthPos", "prevNorthPos", "southLookahead", "findType2Conflicts", "xss", "vert", "horiz", "inner", "neighborFn", "prevIdx", "ws", "mp", "floor", "il", "verticalAlignment", "smallestWidth", "NEGATIVE_INFINITY", "halfWidth", "findSmallestWidthAlignment", "alignTo", "alignToVals", "alignToMin", "alignToMax", "alignment", "xsVals", "alignCoordinates", "ul", "ignore", "balance", "position", "rankSep", "ranksep", "prevY", "maxHeight", "positionY", "layout", "debugTiming", "layoutGraph", "inputGraph", "canonicalize", "graphDefaults", "selectNumberAttrs", "graphNumAttrs", "graphAttrs", "nodeNumAttrs", "nodeDefaults", "edgeDefaults", "edgeNumAttrs", "edgeAttrs", "buildLayoutGraph", "labeloffset", "makeSpaceForEdgeLabels", "selfEdges", "removeSelfEdges", "acyclic", "nestingGraph", "injectEdgeLabelProxies", "offset", "assignRankMinMax", "removeEdgeLabelProxies", "normalize", "addBorderSegments", "orderShift", "selfEdge", "insertSelf<PERSON>dges", "coordinateSystem", "selfNode", "position<PERSON><PERSON><PERSON><PERSON>", "b", "l", "r", "removeBorderNodes", "origLabel", "fixupEdgeLabelCoords", "minX", "maxX", "minY", "maxY", "marginX", "marginx", "marginY", "marginy", "getExtremes", "p", "translateGraph", "p1", "p2", "nodeV", "nodeW", "unshift", "assignNodeIntersects", "reversePointsForReversedEdges", "runLayout", "inputLabel", "layoutLabel", "updateInputGraph", "obj", "newAttrs", "objectProto", "guard", "propsIndex", "props<PERSON><PERSON>th", "eq", "<PERSON><PERSON><PERSON>", "baseHas"], "sourceRoot": ""}