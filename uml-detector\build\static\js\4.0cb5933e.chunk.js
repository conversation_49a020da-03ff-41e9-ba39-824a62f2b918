"use strict";(self.webpackChunkuml_detector=self.webpackChunkuml_detector||[]).push([[4],{5004:(e,t,s)=>{s.d(t,{diagram:()=>m});var i=s(7731),n=s(89),r=(s(5616),s(594),s(8546),s(4190),s(590),s(1984),s(6102)),a=s(7551),l=function(){var e=(0,a.K2)((function(e,t,s,i){for(s=s||{},i=e.length;i--;s[e[i]]=t);return s}),"o"),t=[1,3],s=[1,4],i=[1,5],n=[1,6],r=[5,6,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],l=[1,22],c=[2,7],o=[1,26],h=[1,27],u=[1,28],y=[1,29],m=[1,33],d=[1,34],E=[1,35],p=[1,36],R=[1,37],f=[1,38],_=[1,24],g=[1,31],S=[1,32],I=[1,30],b=[1,39],T=[1,40],k=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],N=[1,61],q=[89,90],A=[5,8,9,11,13,21,22,23,24,27,29,41,42,43,44,45,46,54,61,63,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],C=[27,29],v=[1,70],w=[1,71],x=[1,72],L=[1,73],D=[1,74],O=[1,75],$=[1,76],M=[1,83],F=[1,80],K=[1,84],P=[1,85],V=[1,86],U=[1,87],Y=[1,88],B=[1,89],Q=[1,90],H=[1,91],W=[1,92],j=[5,8,9,11,13,21,22,23,24,27,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],G=[63,64],z=[1,101],X=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,76,77,89,90],J=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],Z=[1,110],ee=[1,106],te=[1,107],se=[1,108],ie=[1,109],ne=[1,111],re=[1,116],ae=[1,117],le=[1,114],ce=[1,115],oe={trace:(0,a.K2)((function(){}),"trace"),yy:{},symbols_:{error:2,start:3,directive:4,NEWLINE:5,RD:6,diagram:7,EOF:8,acc_title:9,acc_title_value:10,acc_descr:11,acc_descr_value:12,acc_descr_multiline_value:13,requirementDef:14,elementDef:15,relationshipDef:16,direction:17,styleStatement:18,classDefStatement:19,classStatement:20,direction_tb:21,direction_bt:22,direction_rl:23,direction_lr:24,requirementType:25,requirementName:26,STRUCT_START:27,requirementBody:28,STYLE_SEPARATOR:29,idList:30,ID:31,COLONSEP:32,id:33,TEXT:34,text:35,RISK:36,riskLevel:37,VERIFYMTHD:38,verifyType:39,STRUCT_STOP:40,REQUIREMENT:41,FUNCTIONAL_REQUIREMENT:42,INTERFACE_REQUIREMENT:43,PERFORMANCE_REQUIREMENT:44,PHYSICAL_REQUIREMENT:45,DESIGN_CONSTRAINT:46,LOW_RISK:47,MED_RISK:48,HIGH_RISK:49,VERIFY_ANALYSIS:50,VERIFY_DEMONSTRATION:51,VERIFY_INSPECTION:52,VERIFY_TEST:53,ELEMENT:54,elementName:55,elementBody:56,TYPE:57,type:58,DOCREF:59,ref:60,END_ARROW_L:61,relationship:62,LINE:63,END_ARROW_R:64,CONTAINS:65,COPIES:66,DERIVES:67,SATISFIES:68,VERIFIES:69,REFINES:70,TRACES:71,CLASSDEF:72,stylesOpt:73,CLASS:74,ALPHA:75,COMMA:76,STYLE:77,style:78,styleComponent:79,NUM:80,COLON:81,UNIT:82,SPACE:83,BRKT:84,PCT:85,MINUS:86,LABEL:87,SEMICOLON:88,unqString:89,qString:90,$accept:0,$end:1},terminals_:{2:"error",5:"NEWLINE",6:"RD",8:"EOF",9:"acc_title",10:"acc_title_value",11:"acc_descr",12:"acc_descr_value",13:"acc_descr_multiline_value",21:"direction_tb",22:"direction_bt",23:"direction_rl",24:"direction_lr",27:"STRUCT_START",29:"STYLE_SEPARATOR",31:"ID",32:"COLONSEP",34:"TEXT",36:"RISK",38:"VERIFYMTHD",40:"STRUCT_STOP",41:"REQUIREMENT",42:"FUNCTIONAL_REQUIREMENT",43:"INTERFACE_REQUIREMENT",44:"PERFORMANCE_REQUIREMENT",45:"PHYSICAL_REQUIREMENT",46:"DESIGN_CONSTRAINT",47:"LOW_RISK",48:"MED_RISK",49:"HIGH_RISK",50:"VERIFY_ANALYSIS",51:"VERIFY_DEMONSTRATION",52:"VERIFY_INSPECTION",53:"VERIFY_TEST",54:"ELEMENT",57:"TYPE",59:"DOCREF",61:"END_ARROW_L",63:"LINE",64:"END_ARROW_R",65:"CONTAINS",66:"COPIES",67:"DERIVES",68:"SATISFIES",69:"VERIFIES",70:"REFINES",71:"TRACES",72:"CLASSDEF",74:"CLASS",75:"ALPHA",76:"COMMA",77:"STYLE",80:"NUM",81:"COLON",82:"UNIT",83:"SPACE",84:"BRKT",85:"PCT",86:"MINUS",87:"LABEL",88:"SEMICOLON",89:"unqString",90:"qString"},productions_:[0,[3,3],[3,2],[3,4],[4,2],[4,2],[4,1],[7,0],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[17,1],[17,1],[17,1],[17,1],[14,5],[14,7],[28,5],[28,5],[28,5],[28,5],[28,2],[28,1],[25,1],[25,1],[25,1],[25,1],[25,1],[25,1],[37,1],[37,1],[37,1],[39,1],[39,1],[39,1],[39,1],[15,5],[15,7],[56,5],[56,5],[56,2],[56,1],[16,5],[16,5],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[19,3],[20,3],[20,3],[30,1],[30,3],[30,1],[30,3],[18,3],[73,1],[73,3],[78,1],[78,2],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[26,1],[26,1],[33,1],[33,1],[35,1],[35,1],[55,1],[55,1],[58,1],[58,1],[60,1],[60,1]],performAction:(0,a.K2)((function(e,t,s,i,n,r,a){var l=r.length-1;switch(n){case 4:this.$=r[l].trim(),i.setAccTitle(this.$);break;case 5:case 6:this.$=r[l].trim(),i.setAccDescription(this.$);break;case 7:this.$=[];break;case 17:i.setDirection("TB");break;case 18:i.setDirection("BT");break;case 19:i.setDirection("RL");break;case 20:i.setDirection("LR");break;case 21:i.addRequirement(r[l-3],r[l-4]);break;case 22:i.addRequirement(r[l-5],r[l-6]),i.setClass([r[l-5]],r[l-3]);break;case 23:i.setNewReqId(r[l-2]);break;case 24:i.setNewReqText(r[l-2]);break;case 25:i.setNewReqRisk(r[l-2]);break;case 26:i.setNewReqVerifyMethod(r[l-2]);break;case 29:this.$=i.RequirementType.REQUIREMENT;break;case 30:this.$=i.RequirementType.FUNCTIONAL_REQUIREMENT;break;case 31:this.$=i.RequirementType.INTERFACE_REQUIREMENT;break;case 32:this.$=i.RequirementType.PERFORMANCE_REQUIREMENT;break;case 33:this.$=i.RequirementType.PHYSICAL_REQUIREMENT;break;case 34:this.$=i.RequirementType.DESIGN_CONSTRAINT;break;case 35:this.$=i.RiskLevel.LOW_RISK;break;case 36:this.$=i.RiskLevel.MED_RISK;break;case 37:this.$=i.RiskLevel.HIGH_RISK;break;case 38:this.$=i.VerifyType.VERIFY_ANALYSIS;break;case 39:this.$=i.VerifyType.VERIFY_DEMONSTRATION;break;case 40:this.$=i.VerifyType.VERIFY_INSPECTION;break;case 41:this.$=i.VerifyType.VERIFY_TEST;break;case 42:i.addElement(r[l-3]);break;case 43:i.addElement(r[l-5]),i.setClass([r[l-5]],r[l-3]);break;case 44:i.setNewElementType(r[l-2]);break;case 45:i.setNewElementDocRef(r[l-2]);break;case 48:i.addRelationship(r[l-2],r[l],r[l-4]);break;case 49:i.addRelationship(r[l-2],r[l-4],r[l]);break;case 50:this.$=i.Relationships.CONTAINS;break;case 51:this.$=i.Relationships.COPIES;break;case 52:this.$=i.Relationships.DERIVES;break;case 53:this.$=i.Relationships.SATISFIES;break;case 54:this.$=i.Relationships.VERIFIES;break;case 55:this.$=i.Relationships.REFINES;break;case 56:this.$=i.Relationships.TRACES;break;case 57:this.$=r[l-2],i.defineClass(r[l-1],r[l]);break;case 58:i.setClass(r[l-1],r[l]);break;case 59:i.setClass([r[l-2]],r[l]);break;case 60:case 62:case 65:this.$=[r[l]];break;case 61:case 63:this.$=r[l-2].concat([r[l]]);break;case 64:this.$=r[l-2],i.setCssStyle(r[l-1],r[l]);break;case 66:r[l-2].push(r[l]),this.$=r[l-2];break;case 68:this.$=r[l-1]+r[l]}}),"anonymous"),table:[{3:1,4:2,6:t,9:s,11:i,13:n},{1:[3]},{3:8,4:2,5:[1,7],6:t,9:s,11:i,13:n},{5:[1,9]},{10:[1,10]},{12:[1,11]},e(r,[2,6]),{3:12,4:2,6:t,9:s,11:i,13:n},{1:[2,2]},{4:17,5:l,7:13,8:c,9:s,11:i,13:n,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:o,22:h,23:u,24:y,25:23,33:25,41:m,42:d,43:E,44:p,45:R,46:f,54:_,72:g,74:S,77:I,89:b,90:T},e(r,[2,4]),e(r,[2,5]),{1:[2,1]},{8:[1,41]},{4:17,5:l,7:42,8:c,9:s,11:i,13:n,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:o,22:h,23:u,24:y,25:23,33:25,41:m,42:d,43:E,44:p,45:R,46:f,54:_,72:g,74:S,77:I,89:b,90:T},{4:17,5:l,7:43,8:c,9:s,11:i,13:n,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:o,22:h,23:u,24:y,25:23,33:25,41:m,42:d,43:E,44:p,45:R,46:f,54:_,72:g,74:S,77:I,89:b,90:T},{4:17,5:l,7:44,8:c,9:s,11:i,13:n,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:o,22:h,23:u,24:y,25:23,33:25,41:m,42:d,43:E,44:p,45:R,46:f,54:_,72:g,74:S,77:I,89:b,90:T},{4:17,5:l,7:45,8:c,9:s,11:i,13:n,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:o,22:h,23:u,24:y,25:23,33:25,41:m,42:d,43:E,44:p,45:R,46:f,54:_,72:g,74:S,77:I,89:b,90:T},{4:17,5:l,7:46,8:c,9:s,11:i,13:n,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:o,22:h,23:u,24:y,25:23,33:25,41:m,42:d,43:E,44:p,45:R,46:f,54:_,72:g,74:S,77:I,89:b,90:T},{4:17,5:l,7:47,8:c,9:s,11:i,13:n,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:o,22:h,23:u,24:y,25:23,33:25,41:m,42:d,43:E,44:p,45:R,46:f,54:_,72:g,74:S,77:I,89:b,90:T},{4:17,5:l,7:48,8:c,9:s,11:i,13:n,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:o,22:h,23:u,24:y,25:23,33:25,41:m,42:d,43:E,44:p,45:R,46:f,54:_,72:g,74:S,77:I,89:b,90:T},{4:17,5:l,7:49,8:c,9:s,11:i,13:n,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:o,22:h,23:u,24:y,25:23,33:25,41:m,42:d,43:E,44:p,45:R,46:f,54:_,72:g,74:S,77:I,89:b,90:T},{4:17,5:l,7:50,8:c,9:s,11:i,13:n,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:o,22:h,23:u,24:y,25:23,33:25,41:m,42:d,43:E,44:p,45:R,46:f,54:_,72:g,74:S,77:I,89:b,90:T},{26:51,89:[1,52],90:[1,53]},{55:54,89:[1,55],90:[1,56]},{29:[1,59],61:[1,57],63:[1,58]},e(k,[2,17]),e(k,[2,18]),e(k,[2,19]),e(k,[2,20]),{30:60,33:62,75:N,89:b,90:T},{30:63,33:62,75:N,89:b,90:T},{30:64,33:62,75:N,89:b,90:T},e(q,[2,29]),e(q,[2,30]),e(q,[2,31]),e(q,[2,32]),e(q,[2,33]),e(q,[2,34]),e(A,[2,81]),e(A,[2,82]),{1:[2,3]},{8:[2,8]},{8:[2,9]},{8:[2,10]},{8:[2,11]},{8:[2,12]},{8:[2,13]},{8:[2,14]},{8:[2,15]},{8:[2,16]},{27:[1,65],29:[1,66]},e(C,[2,79]),e(C,[2,80]),{27:[1,67],29:[1,68]},e(C,[2,85]),e(C,[2,86]),{62:69,65:v,66:w,67:x,68:L,69:D,70:O,71:$},{62:77,65:v,66:w,67:x,68:L,69:D,70:O,71:$},{30:78,33:62,75:N,89:b,90:T},{73:79,75:M,76:F,78:81,79:82,80:K,81:P,82:V,83:U,84:Y,85:B,86:Q,87:H,88:W},e(j,[2,60]),e(j,[2,62]),{73:93,75:M,76:F,78:81,79:82,80:K,81:P,82:V,83:U,84:Y,85:B,86:Q,87:H,88:W},{30:94,33:62,75:N,76:F,89:b,90:T},{5:[1,95]},{30:96,33:62,75:N,89:b,90:T},{5:[1,97]},{30:98,33:62,75:N,89:b,90:T},{63:[1,99]},e(G,[2,50]),e(G,[2,51]),e(G,[2,52]),e(G,[2,53]),e(G,[2,54]),e(G,[2,55]),e(G,[2,56]),{64:[1,100]},e(k,[2,59],{76:F}),e(k,[2,64],{76:z}),{33:103,75:[1,102],89:b,90:T},e(X,[2,65],{79:104,75:M,80:K,81:P,82:V,83:U,84:Y,85:B,86:Q,87:H,88:W}),e(J,[2,67]),e(J,[2,69]),e(J,[2,70]),e(J,[2,71]),e(J,[2,72]),e(J,[2,73]),e(J,[2,74]),e(J,[2,75]),e(J,[2,76]),e(J,[2,77]),e(J,[2,78]),e(k,[2,57],{76:z}),e(k,[2,58],{76:F}),{5:Z,28:105,31:ee,34:te,36:se,38:ie,40:ne},{27:[1,112],76:F},{5:re,40:ae,56:113,57:le,59:ce},{27:[1,118],76:F},{33:119,89:b,90:T},{33:120,89:b,90:T},{75:M,78:121,79:82,80:K,81:P,82:V,83:U,84:Y,85:B,86:Q,87:H,88:W},e(j,[2,61]),e(j,[2,63]),e(J,[2,68]),e(k,[2,21]),{32:[1,122]},{32:[1,123]},{32:[1,124]},{32:[1,125]},{5:Z,28:126,31:ee,34:te,36:se,38:ie,40:ne},e(k,[2,28]),{5:[1,127]},e(k,[2,42]),{32:[1,128]},{32:[1,129]},{5:re,40:ae,56:130,57:le,59:ce},e(k,[2,47]),{5:[1,131]},e(k,[2,48]),e(k,[2,49]),e(X,[2,66],{79:104,75:M,80:K,81:P,82:V,83:U,84:Y,85:B,86:Q,87:H,88:W}),{33:132,89:b,90:T},{35:133,89:[1,134],90:[1,135]},{37:136,47:[1,137],48:[1,138],49:[1,139]},{39:140,50:[1,141],51:[1,142],52:[1,143],53:[1,144]},e(k,[2,27]),{5:Z,28:145,31:ee,34:te,36:se,38:ie,40:ne},{58:146,89:[1,147],90:[1,148]},{60:149,89:[1,150],90:[1,151]},e(k,[2,46]),{5:re,40:ae,56:152,57:le,59:ce},{5:[1,153]},{5:[1,154]},{5:[2,83]},{5:[2,84]},{5:[1,155]},{5:[2,35]},{5:[2,36]},{5:[2,37]},{5:[1,156]},{5:[2,38]},{5:[2,39]},{5:[2,40]},{5:[2,41]},e(k,[2,22]),{5:[1,157]},{5:[2,87]},{5:[2,88]},{5:[1,158]},{5:[2,89]},{5:[2,90]},e(k,[2,43]),{5:Z,28:159,31:ee,34:te,36:se,38:ie,40:ne},{5:Z,28:160,31:ee,34:te,36:se,38:ie,40:ne},{5:Z,28:161,31:ee,34:te,36:se,38:ie,40:ne},{5:Z,28:162,31:ee,34:te,36:se,38:ie,40:ne},{5:re,40:ae,56:163,57:le,59:ce},{5:re,40:ae,56:164,57:le,59:ce},e(k,[2,23]),e(k,[2,24]),e(k,[2,25]),e(k,[2,26]),e(k,[2,44]),e(k,[2,45])],defaultActions:{8:[2,2],12:[2,1],41:[2,3],42:[2,8],43:[2,9],44:[2,10],45:[2,11],46:[2,12],47:[2,13],48:[2,14],49:[2,15],50:[2,16],134:[2,83],135:[2,84],137:[2,35],138:[2,36],139:[2,37],141:[2,38],142:[2,39],143:[2,40],144:[2,41],147:[2,87],148:[2,88],150:[2,89],151:[2,90]},parseError:(0,a.K2)((function(e,t){if(!t.recoverable){var s=new Error(e);throw s.hash=t,s}this.trace(e)}),"parseError"),parse:(0,a.K2)((function(e){var t=this,s=[0],i=[],n=[null],r=[],l=this.table,c="",o=0,h=0,u=0,y=r.slice.call(arguments,1),m=Object.create(this.lexer),d={yy:{}};for(var E in this.yy)Object.prototype.hasOwnProperty.call(this.yy,E)&&(d.yy[E]=this.yy[E]);m.setInput(e,d.yy),d.yy.lexer=m,d.yy.parser=this,"undefined"==typeof m.yylloc&&(m.yylloc={});var p=m.yylloc;r.push(p);var R=m.options&&m.options.ranges;function f(){var e;return"number"!==typeof(e=i.pop()||m.lex()||1)&&(e instanceof Array&&(e=(i=e).pop()),e=t.symbols_[e]||e),e}"function"===typeof d.yy.parseError?this.parseError=d.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,(0,a.K2)((function(e){s.length=s.length-2*e,n.length=n.length-e,r.length=r.length-e}),"popStack"),(0,a.K2)(f,"lex");for(var _,g,S,I,b,T,k,N,q,A={};;){if(S=s[s.length-1],this.defaultActions[S]?I=this.defaultActions[S]:(null!==_&&"undefined"!=typeof _||(_=f()),I=l[S]&&l[S][_]),"undefined"===typeof I||!I.length||!I[0]){var C="";for(T in q=[],l[S])this.terminals_[T]&&T>2&&q.push("'"+this.terminals_[T]+"'");C=m.showPosition?"Parse error on line "+(o+1)+":\n"+m.showPosition()+"\nExpecting "+q.join(", ")+", got '"+(this.terminals_[_]||_)+"'":"Parse error on line "+(o+1)+": Unexpected "+(1==_?"end of input":"'"+(this.terminals_[_]||_)+"'"),this.parseError(C,{text:m.match,token:this.terminals_[_]||_,line:m.yylineno,loc:p,expected:q})}if(I[0]instanceof Array&&I.length>1)throw new Error("Parse Error: multiple actions possible at state: "+S+", token: "+_);switch(I[0]){case 1:s.push(_),n.push(m.yytext),r.push(m.yylloc),s.push(I[1]),_=null,g?(_=g,g=null):(h=m.yyleng,c=m.yytext,o=m.yylineno,p=m.yylloc,u>0&&u--);break;case 2:if(k=this.productions_[I[1]][1],A.$=n[n.length-k],A._$={first_line:r[r.length-(k||1)].first_line,last_line:r[r.length-1].last_line,first_column:r[r.length-(k||1)].first_column,last_column:r[r.length-1].last_column},R&&(A._$.range=[r[r.length-(k||1)].range[0],r[r.length-1].range[1]]),"undefined"!==typeof(b=this.performAction.apply(A,[c,h,o,d.yy,I[1],n,r].concat(y))))return b;k&&(s=s.slice(0,-1*k*2),n=n.slice(0,-1*k),r=r.slice(0,-1*k)),s.push(this.productions_[I[1]][0]),n.push(A.$),r.push(A._$),N=l[s[s.length-2]][s[s.length-1]],s.push(N);break;case 3:return!0}}return!0}),"parse")},he=function(){return{EOF:1,parseError:(0,a.K2)((function(e,t){if(!this.yy.parser)throw new Error(e);this.yy.parser.parseError(e,t)}),"parseError"),setInput:(0,a.K2)((function(e,t){return this.yy=t||this.yy||{},this._input=e,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this}),"setInput"),input:(0,a.K2)((function(){var e=this._input[0];return this.yytext+=e,this.yyleng++,this.offset++,this.match+=e,this.matched+=e,e.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),e}),"input"),unput:(0,a.K2)((function(e){var t=e.length,s=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-t),this.offset-=t;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),s.length-1&&(this.yylineno-=s.length-1);var n=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===i.length?this.yylloc.first_column:0)+i[i.length-s.length].length-s[0].length:this.yylloc.first_column-t},this.options.ranges&&(this.yylloc.range=[n[0],n[0]+this.yyleng-t]),this.yyleng=this.yytext.length,this}),"unput"),more:(0,a.K2)((function(){return this._more=!0,this}),"more"),reject:(0,a.K2)((function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}),"reject"),less:(0,a.K2)((function(e){this.unput(this.match.slice(e))}),"less"),pastInput:(0,a.K2)((function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,a.K2)((function(){var e=this.match;return e.length<20&&(e+=this._input.substr(0,20-e.length)),(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,a.K2)((function(){var e=this.pastInput(),t=new Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"}),"showPosition"),test_match:(0,a.K2)((function(e,t){var s,i,n;if(this.options.backtrack_lexer&&(n={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(n.yylloc.range=this.yylloc.range.slice(0))),(i=e[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=i.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length},this.yytext+=e[0],this.match+=e[0],this.matches=e,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(e[0].length),this.matched+=e[0],s=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),s)return s;if(this._backtrack){for(var r in n)this[r]=n[r];return!1}return!1}),"test_match"),next:(0,a.K2)((function(){if(this.done)return this.EOF;var e,t,s,i;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var n=this._currentRules(),r=0;r<n.length;r++)if((s=this._input.match(this.rules[n[r]]))&&(!t||s[0].length>t[0].length)){if(t=s,i=r,this.options.backtrack_lexer){if(!1!==(e=this.test_match(s,n[r])))return e;if(this._backtrack){t=!1;continue}return!1}if(!this.options.flex)break}return t?!1!==(e=this.test_match(t,n[i]))&&e:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}),"next"),lex:(0,a.K2)((function(){var e=this.next();return e||this.lex()}),"lex"),begin:(0,a.K2)((function(e){this.conditionStack.push(e)}),"begin"),popState:(0,a.K2)((function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]}),"popState"),_currentRules:(0,a.K2)((function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules}),"_currentRules"),topState:(0,a.K2)((function(e){return(e=this.conditionStack.length-1-Math.abs(e||0))>=0?this.conditionStack[e]:"INITIAL"}),"topState"),pushState:(0,a.K2)((function(e){this.begin(e)}),"pushState"),stateStackSize:(0,a.K2)((function(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,a.K2)((function(e,t,s,i){switch(s){case 0:return"title";case 1:return this.begin("acc_title"),9;case 2:return this.popState(),"acc_title_value";case 3:return this.begin("acc_descr"),11;case 4:return this.popState(),"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:case 58:case 65:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:return 21;case 9:return 22;case 10:return 23;case 11:return 24;case 12:return 5;case 13:case 14:case 15:case 56:break;case 16:return 8;case 17:return 6;case 18:return 27;case 19:return 40;case 20:return 29;case 21:return 32;case 22:return 31;case 23:return 34;case 24:return 36;case 25:return 38;case 26:return 41;case 27:return 42;case 28:return 43;case 29:return 44;case 30:return 45;case 31:return 46;case 32:return 47;case 33:return 48;case 34:return 49;case 35:return 50;case 36:return 51;case 37:return 52;case 38:return 53;case 39:return 54;case 40:return 65;case 41:return 66;case 42:return 67;case 43:return 68;case 44:return 69;case 45:return 70;case 46:return 71;case 47:return 57;case 48:return 59;case 49:return this.begin("style"),77;case 50:case 68:return 75;case 51:return 81;case 52:return 88;case 53:return"PERCENT";case 54:return 86;case 55:return 84;case 57:case 64:this.begin("string");break;case 59:return this.begin("style"),72;case 60:return this.begin("style"),74;case 61:return 61;case 62:return 64;case 63:return 63;case 66:return"qString";case 67:return t.yytext=t.yytext.trim(),89;case 69:return 80;case 70:return 76}}),"anonymous"),rules:[/^(?:title\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:(\r?\n)+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:%[^\n]*)/i,/^(?:$)/i,/^(?:requirementDiagram\b)/i,/^(?:\{)/i,/^(?:\})/i,/^(?::{3})/i,/^(?::)/i,/^(?:id\b)/i,/^(?:text\b)/i,/^(?:risk\b)/i,/^(?:verifyMethod\b)/i,/^(?:requirement\b)/i,/^(?:functionalRequirement\b)/i,/^(?:interfaceRequirement\b)/i,/^(?:performanceRequirement\b)/i,/^(?:physicalRequirement\b)/i,/^(?:designConstraint\b)/i,/^(?:low\b)/i,/^(?:medium\b)/i,/^(?:high\b)/i,/^(?:analysis\b)/i,/^(?:demonstration\b)/i,/^(?:inspection\b)/i,/^(?:test\b)/i,/^(?:element\b)/i,/^(?:contains\b)/i,/^(?:copies\b)/i,/^(?:derives\b)/i,/^(?:satisfies\b)/i,/^(?:verifies\b)/i,/^(?:refines\b)/i,/^(?:traces\b)/i,/^(?:type\b)/i,/^(?:docref\b)/i,/^(?:style\b)/i,/^(?:\w+)/i,/^(?::)/i,/^(?:;)/i,/^(?:%)/i,/^(?:-)/i,/^(?:#)/i,/^(?: )/i,/^(?:["])/i,/^(?:\n)/i,/^(?:classDef\b)/i,/^(?:class\b)/i,/^(?:<-)/i,/^(?:->)/i,/^(?:-)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[\w][^:,\r\n\{\<\>\-\=]*)/i,/^(?:\w+)/i,/^(?:[0-9]+)/i,/^(?:,)/i],conditions:{acc_descr_multiline:{rules:[6,7,68,69,70],inclusive:!1},acc_descr:{rules:[4,68,69,70],inclusive:!1},acc_title:{rules:[2,68,69,70],inclusive:!1},style:{rules:[50,51,52,53,54,55,56,57,58,68,69,70],inclusive:!1},unqString:{rules:[68,69,70],inclusive:!1},token:{rules:[68,69,70],inclusive:!1},string:{rules:[65,66,68,69,70],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,67,68,69,70],inclusive:!0}}}}();function ue(){this.yy={}}return oe.lexer=he,(0,a.K2)(ue,"Parser"),ue.prototype=oe,oe.Parser=ue,new ue}();l.parser=l;var c=l,o=class{constructor(){this.relations=[],this.latestRequirement=this.getInitialRequirement(),this.requirements=new Map,this.latestElement=this.getInitialElement(),this.elements=new Map,this.classes=new Map,this.direction="TB",this.RequirementType={REQUIREMENT:"Requirement",FUNCTIONAL_REQUIREMENT:"Functional Requirement",INTERFACE_REQUIREMENT:"Interface Requirement",PERFORMANCE_REQUIREMENT:"Performance Requirement",PHYSICAL_REQUIREMENT:"Physical Requirement",DESIGN_CONSTRAINT:"Design Constraint"},this.RiskLevel={LOW_RISK:"Low",MED_RISK:"Medium",HIGH_RISK:"High"},this.VerifyType={VERIFY_ANALYSIS:"Analysis",VERIFY_DEMONSTRATION:"Demonstration",VERIFY_INSPECTION:"Inspection",VERIFY_TEST:"Test"},this.Relationships={CONTAINS:"contains",COPIES:"copies",DERIVES:"derives",SATISFIES:"satisfies",VERIFIES:"verifies",REFINES:"refines",TRACES:"traces"},this.setAccTitle=a.SV,this.getAccTitle=a.iN,this.setAccDescription=a.EI,this.getAccDescription=a.m7,this.setDiagramTitle=a.ke,this.getDiagramTitle=a.ab,this.getConfig=(0,a.K2)((()=>(0,a.D7)().requirement),"getConfig"),this.clear(),this.setDirection=this.setDirection.bind(this),this.addRequirement=this.addRequirement.bind(this),this.setNewReqId=this.setNewReqId.bind(this),this.setNewReqRisk=this.setNewReqRisk.bind(this),this.setNewReqText=this.setNewReqText.bind(this),this.setNewReqVerifyMethod=this.setNewReqVerifyMethod.bind(this),this.addElement=this.addElement.bind(this),this.setNewElementType=this.setNewElementType.bind(this),this.setNewElementDocRef=this.setNewElementDocRef.bind(this),this.addRelationship=this.addRelationship.bind(this),this.setCssStyle=this.setCssStyle.bind(this),this.setClass=this.setClass.bind(this),this.defineClass=this.defineClass.bind(this),this.setAccTitle=this.setAccTitle.bind(this),this.setAccDescription=this.setAccDescription.bind(this)}static#e=(()=>(0,a.K2)(this,"RequirementDB"))();getDirection(){return this.direction}setDirection(e){this.direction=e}resetLatestRequirement(){this.latestRequirement=this.getInitialRequirement()}resetLatestElement(){this.latestElement=this.getInitialElement()}getInitialRequirement(){return{requirementId:"",text:"",risk:"",verifyMethod:"",name:"",type:"",cssStyles:[],classes:["default"]}}getInitialElement(){return{name:"",type:"",docRef:"",cssStyles:[],classes:["default"]}}addRequirement(e,t){return this.requirements.has(e)||this.requirements.set(e,{name:e,type:t,requirementId:this.latestRequirement.requirementId,text:this.latestRequirement.text,risk:this.latestRequirement.risk,verifyMethod:this.latestRequirement.verifyMethod,cssStyles:[],classes:["default"]}),this.resetLatestRequirement(),this.requirements.get(e)}getRequirements(){return this.requirements}setNewReqId(e){void 0!==this.latestRequirement&&(this.latestRequirement.requirementId=e)}setNewReqText(e){void 0!==this.latestRequirement&&(this.latestRequirement.text=e)}setNewReqRisk(e){void 0!==this.latestRequirement&&(this.latestRequirement.risk=e)}setNewReqVerifyMethod(e){void 0!==this.latestRequirement&&(this.latestRequirement.verifyMethod=e)}addElement(e){return this.elements.has(e)||(this.elements.set(e,{name:e,type:this.latestElement.type,docRef:this.latestElement.docRef,cssStyles:[],classes:["default"]}),a.Rm.info("Added new element: ",e)),this.resetLatestElement(),this.elements.get(e)}getElements(){return this.elements}setNewElementType(e){void 0!==this.latestElement&&(this.latestElement.type=e)}setNewElementDocRef(e){void 0!==this.latestElement&&(this.latestElement.docRef=e)}addRelationship(e,t,s){this.relations.push({type:e,src:t,dst:s})}getRelationships(){return this.relations}clear(){this.relations=[],this.resetLatestRequirement(),this.requirements=new Map,this.resetLatestElement(),this.elements=new Map,this.classes=new Map,(0,a.IU)()}setCssStyle(e,t){for(const s of e){const e=this.requirements.get(s)??this.elements.get(s);if(!t||!e)return;for(const s of t)s.includes(",")?e.cssStyles.push(...s.split(",")):e.cssStyles.push(s)}}setClass(e,t){for(const s of e){const e=this.requirements.get(s)??this.elements.get(s);if(e)for(const s of t){e.classes.push(s);const t=this.classes.get(s)?.styles;t&&e.cssStyles.push(...t)}}}defineClass(e,t){for(const s of e){let e=this.classes.get(s);void 0===e&&(e={id:s,styles:[],textStyles:[]},this.classes.set(s,e)),t&&t.forEach((function(t){if(/color/.exec(t)){const s=t.replace("fill","bgFill");e.textStyles.push(s)}e.styles.push(t)})),this.requirements.forEach((e=>{e.classes.includes(s)&&e.cssStyles.push(...t.flatMap((e=>e.split(","))))})),this.elements.forEach((e=>{e.classes.includes(s)&&e.cssStyles.push(...t.flatMap((e=>e.split(","))))}))}}getClasses(){return this.classes}getData(){const e=(0,a.D7)(),t=[],s=[];for(const i of this.requirements.values()){const s=i;s.id=i.name,s.cssStyles=i.cssStyles,s.cssClasses=i.classes.join(" "),s.shape="requirementBox",s.look=e.look,t.push(s)}for(const i of this.elements.values()){const s=i;s.shape="requirementBox",s.look=e.look,s.id=i.name,s.cssStyles=i.cssStyles,s.cssClasses=i.classes.join(" "),t.push(s)}for(const i of this.relations){let t=0;const n=i.type===this.Relationships.CONTAINS,r={id:`${i.src}-${i.dst}-${t}`,start:this.requirements.get(i.src)?.name??this.elements.get(i.src)?.name,end:this.requirements.get(i.dst)?.name??this.elements.get(i.dst)?.name,label:`&lt;&lt;${i.type}&gt;&gt;`,classes:"relationshipLine",style:["fill:none",n?"":"stroke-dasharray: 10,7"],labelpos:"c",thickness:"normal",type:"normal",pattern:n?"normal":"dashed",arrowTypeStart:n?"requirement_contains":"",arrowTypeEnd:n?"":"requirement_arrow",look:e.look};s.push(r),t++}return{nodes:t,edges:s,other:{},config:e,direction:this.getDirection()}}},h=(0,a.K2)((e=>`\n\n  marker {\n    fill: ${e.relationColor};\n    stroke: ${e.relationColor};\n  }\n\n  marker.cross {\n    stroke: ${e.lineColor};\n  }\n\n  svg {\n    font-family: ${e.fontFamily};\n    font-size: ${e.fontSize};\n  }\n\n  .reqBox {\n    fill: ${e.requirementBackground};\n    fill-opacity: 1.0;\n    stroke: ${e.requirementBorderColor};\n    stroke-width: ${e.requirementBorderSize};\n  }\n  \n  .reqTitle, .reqLabel{\n    fill:  ${e.requirementTextColor};\n  }\n  .reqLabelBox {\n    fill: ${e.relationLabelBackground};\n    fill-opacity: 1.0;\n  }\n\n  .req-title-line {\n    stroke: ${e.requirementBorderColor};\n    stroke-width: ${e.requirementBorderSize};\n  }\n  .relationshipLine {\n    stroke: ${e.relationColor};\n    stroke-width: 1;\n  }\n  .relationshipLabel {\n    fill: ${e.relationLabelColor};\n  }\n  .divider {\n    stroke: ${e.nodeBorder};\n    stroke-width: 1;\n  }\n  .label {\n    font-family: ${e.fontFamily};\n    color: ${e.nodeTextColor||e.textColor};\n  }\n  .label text,span {\n    fill: ${e.nodeTextColor||e.textColor};\n    color: ${e.nodeTextColor||e.textColor};\n  }\n  .labelBkg {\n    background-color: ${e.edgeLabelBackground};\n  }\n\n`),"getStyles"),u={};(0,a.VA)(u,{draw:()=>y});var y=(0,a.K2)((async function(e,t,s,l){a.Rm.info("REF0:"),a.Rm.info("Drawing requirement diagram (unified)",t);const{securityLevel:c,state:o,layout:h}=(0,a.D7)(),u=l.db.getData(),y=(0,i.A)(t,c);u.type=l.type,u.layoutAlgorithm=(0,n.q7)(h),u.nodeSpacing=o?.nodeSpacing??50,u.rankSpacing=o?.rankSpacing??50,u.markers=["requirement_contains","requirement_arrow"],u.diagramId=t,await(0,n.XX)(u,y);r._K.insertTitle(y,"requirementDiagramTitleText",o?.titleTopMargin??25,l.db.getDiagramTitle()),(0,i.P)(y,8,"requirementDiagram",o?.useMaxWidth??!0)}),"draw"),m={parser:c,get db(){return new o},renderer:u,styles:h}},7731:(e,t,s)=>{s.d(t,{A:()=>r,P:()=>a});var i=s(7551),n=s(1804),r=(0,i.K2)(((e,t)=>{let s;"sandbox"===t&&(s=(0,n.Ltv)("#i"+e));return("sandbox"===t?(0,n.Ltv)(s.nodes()[0].contentDocument.body):(0,n.Ltv)("body")).select(`[id="${e}"]`)}),"getDiagramElement"),a=(0,i.K2)(((e,t,s,n)=>{e.attr("class",s);const{width:r,height:a,x:o,y:h}=l(e,t);(0,i.a$)(e,a,r,n);const u=c(o,h,r,a,t);e.attr("viewBox",u),i.Rm.debug(`viewBox configured: ${u} with padding: ${t}`)}),"setupViewPortForSVG"),l=(0,i.K2)(((e,t)=>{const s=e.node()?.getBBox()||{width:0,height:0,x:0,y:0};return{width:s.width+2*t,height:s.height+2*t,x:s.x,y:s.y}}),"calculateDimensionsWithPadding"),c=(0,i.K2)(((e,t,s,i,n)=>`${e-n} ${t-n} ${s} ${i}`),"createViewBox")}}]);
//# sourceMappingURL=4.0cb5933e.chunk.js.map