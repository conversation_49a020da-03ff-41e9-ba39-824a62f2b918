class 1:
NOM_CLASSE: Commande
ATTRIBUTS: 
- numBonCommande: int
- dateCommande: Date
- dateReglement: Date
- moyenPaiement: String
- totalCommande: float = 0.00
- paiementValide: boolean
- etatCde: string
- statutCde: String
- idClient: int
MÉTHODES:

class 2:
NOM_CLASSE: Client
ATTRIBUTS: 
-idClient: int
-nomClient: String
-prenomClient: String
-login: String
-password: String
-rue: String
-codePostal: string
-ville: string
-pays: String
-tel: String
-eMail: String
MÉTHODES:

class 3:
NOM_CLASSE: Produit
ATTRIBUTS: 
-idProd: int
-refProd: string
-nomProd: String
-prixUnitaireHT: float = 0.00
-qteStock: int = 0
-genre: string
-couleur: string
-taille: String
-poids: float
-infosArt: String
+TVA: const = 0.2
-prixTTC: float = 0.00
-idCatProd: int
-idMarque: int
-idActivite: int
MÉTHODES:

class 4:
NOM_CLASSE: Marque
ATTRIBUTS: 
-idMarque: int
-nomMarque: string
MÉTHODES:

class 5:
NOM_CLASSE: Categorie_produit
ATTRIBUTS: 
-idCatProd: int
-nomCategorie: String
MÉTHODES:

class 6:
NOM_CLASSE: ActiviteSportive
ATTRIBUTS: 
-idActivite: int
-nomActivite: String
MÉTHODES:

class 7:
NOM_CLASSE: Constituer
ATTRIBUTS: 
-qte: int = 0
MÉTHODES:



----- RÉSUMÉ DES RELATIONS -----
• 2 relation(s) de type one-way-association
• 1 relation(s) de type composition
• 3 relation(s) de type endpoint

----- RELATIONS DÉTECTÉES -----
• Categorie_produit est associé à Produit de façon unidirectionnelle (association unidirectionnelle)
• Client contient Commande comme partie intégrante (composition (partie intégrante))
• Il y a une relation de association entre Commande et Produit
• Il y a une relation de association entre Produit et Marque
• Produit est associé à ActiviteSportive de façon unidirectionnelle (association unidirectionnelle)
• Il y a une relation de association entre Constituer et Produit