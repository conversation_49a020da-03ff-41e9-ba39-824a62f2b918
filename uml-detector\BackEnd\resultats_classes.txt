class 1:

NOM_CLASSE: Client

ATTRIBUTS: 




-idClient: int
-nomClient: String
-prenomClient: String
-login: String
-password: String
-rue: String
-codePostal: string
-ville: string
-pays: String
-tel: String
+ id : integer
identifier : StringValue
address : StringValue
nom
prenom
email
-eMail: String
+ Name : string
+ Address : string
MÉTHODES:








void ajouter()
void supprimer()
void modifier()

----- RELATIONS DÉTECTÉES -----

• Aucune relation entre classes n'a été détectée sur ce diagramme.  