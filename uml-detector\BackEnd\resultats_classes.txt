class 1:

NOM_CLASSE: Client

ATTRIBUTS: 



-prenomClient: String
-idClient: int
-nomClient: String
-prenomClient: String
-login: String
-password: String
-rue: String
-codePostal: string
-ville: string
-pays: String
-tel: String
-eMail: String
+ Name : string
+ Address : string
+ id : integer
identifier : StringValue
address : StringValue
nom
prenom
email
MÉTHODES:








void ajouter()
void supprimer()
void modifier()
----- RELATIONS DÉTECTÉES -----

• Aucune relation entre classes n'a été détectée sur ce diagramme.

----- RELATIONS DÉTECTÉES -----

• Aucune relation entre classes n'a été détectée sur ce diagramme. 