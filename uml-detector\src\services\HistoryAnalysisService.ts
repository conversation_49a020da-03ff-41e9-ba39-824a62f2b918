// HistoryAnalysisService.ts - Service pour analyser l'historique et proposer des imports de classes
import { HistoryItem } from '../components/types/HistoryTypes';

export interface ClassData {
  name: string;
  attributes: string[];
  methods: string[];
}

export interface HistoryMatch {
  historyItem: HistoryItem;
  matchingClasses: ClassData[];
  similarity: number;
  isShared: boolean;
  hasAccess: boolean;
  previewData?: {
    thumbnailUrl?: string;
    classContext: string;
    relatedClasses: string[];
  };
}

export interface SortOption {
  key: 'date' | 'similarity' | 'name';
  label: string;
  direction: 'asc' | 'desc';
}

export class HistoryAnalysisService {
  
  /**
   * Recherche dans l'historique les diagrammes contenant une classe du même nom
   */
  static findMatchingDiagrams(
    targetClassName: string,
    historyItems: HistoryItem[],
    currentUserId: string,
    currentDiagramText?: string
  ): HistoryMatch[] {
    const matches: HistoryMatch[] = [];

    historyItems.forEach(item => {
      // Exclure le diagramme actuellement ouvert en comparant le contenu
      if (currentDiagramText && item.extractedText === currentDiagramText) {
        return;
      }

      const extractedClasses = this.extractClassesFromText(item.extractedText);
      const matchingClasses = extractedClasses.filter(cls =>
        cls.name.toLowerCase() === targetClassName.toLowerCase()
      );

      if (matchingClasses.length > 0) {
        const similarity = this.calculateSimilarity(targetClassName, matchingClasses[0]);
        const previewData = this.generatePreviewData(item, matchingClasses[0], extractedClasses);

        matches.push({
          historyItem: item,
          matchingClasses,
          similarity,
          isShared: item.userId !== currentUserId,
          hasAccess: this.checkAccess(item, currentUserId),
          previewData
        });
      }
    });

    return matches;
  }
  
  /**
   * Extrait les classes depuis le texte d'analyse
   */
  static extractClassesFromText(extractedText: string): ClassData[] {
    const classes: ClassData[] = [];

    if (!extractedText) return classes;

    // Diviser le texte par sections de classe
    const classSections = extractedText.split(/class \d+:/g).filter(section => section.trim());

    classSections.forEach(section => {
      const classNameMatch = section.match(/NOM_CLASSE:\s*(.+)/);
      if (!classNameMatch) return;

      const className = classNameMatch[1].trim();

      // Nettoyer la section en supprimant les parties de relations
      let cleanedSection = section;

      // Supprimer tout ce qui vient après les sections de résumé des relations
      const relationsSummaryIndex = cleanedSection.indexOf('----- RÉSUMÉ DES RELATIONS -----');
      if (relationsSummaryIndex !== -1) {
        cleanedSection = cleanedSection.substring(0, relationsSummaryIndex);
      }

      const relationsDetectedIndex = cleanedSection.indexOf('----- RELATIONS DÉTECTÉES -----');
      if (relationsDetectedIndex !== -1) {
        cleanedSection = cleanedSection.substring(0, relationsDetectedIndex);
      }

      // Extraire les attributs
      const attributesSection = cleanedSection.split('ATTRIBUTS:')[1]?.split('MÉTHODES:')[0] || '';
      const attributes = attributesSection
        .split('\n')
        .map(attr => attr.trim())
        .filter(attr =>
          attr &&
          !attr.includes('NOM_CLASSE:') &&
          !attr.startsWith('-----') &&
          !attr.includes('RÉSUMÉ DES RELATIONS') &&
          !attr.includes('RELATIONS DÉTECTÉES')
        );

      // Extraire les méthodes
      const methodsSection = cleanedSection.split('MÉTHODES:')[1] || '';
      const methods = methodsSection
        .split('\n')
        .map(method => method.trim())
        .filter(method =>
          method &&
          !method.includes('class ') &&
          !method.includes('RELATIONS') &&
          !method.startsWith('-----') &&
          !method.startsWith('•') && // Exclure les puces des relations
          !method.includes('RÉSUMÉ DES RELATIONS') &&
          !method.includes('RELATIONS DÉTECTÉES')
        );

      // Exclure les classes vides (sans attributs ni méthodes)
      if (attributes.length > 0 || methods.length > 0) {
        classes.push({
          name: className,
          attributes,
          methods
        });
      }
    });

    return classes;
  }
  
  /**
   * Calcule la similarité entre deux classes
   */
  static calculateSimilarity(targetClassName: string, classData: ClassData): number {
    // Similarité basée sur le nom (exact = 100%)
    let similarity = targetClassName.toLowerCase() === classData.name.toLowerCase() ? 100 : 0;
    
    // Bonus pour le nombre d'attributs et méthodes (plus il y en a, plus c'est intéressant)
    const contentScore = Math.min((classData.attributes.length + classData.methods.length) * 5, 50);
    similarity += contentScore;
    
    return Math.min(similarity, 100);
  }
  
  /**
   * Vérifie les droits d'accès à un diagramme
   */
  static checkAccess(item: HistoryItem, currentUserId: string): boolean {
    // Pour l'instant, accès uniquement aux diagrammes de l'utilisateur
    // TODO: Implémenter la logique de partage/collaboration
    return item.userId === currentUserId;
  }
  
  /**
   * Trie les résultats selon les critères spécifiés
   */
  static sortMatches(matches: HistoryMatch[], sortOption: SortOption): HistoryMatch[] {
    return [...matches].sort((a, b) => {
      let comparison = 0;
      
      switch (sortOption.key) {
        case 'date':
          comparison = a.historyItem.createdAt.getTime() - b.historyItem.createdAt.getTime();
          break;
        case 'similarity':
          comparison = a.similarity - b.similarity;
          break;
        case 'name':
          comparison = a.historyItem.title.localeCompare(b.historyItem.title);
          break;
      }
      
      return sortOption.direction === 'desc' ? -comparison : comparison;
    });
  }
  
  /**
   * Fusionne les attributs et méthodes de plusieurs classes
   */
  static mergeClassData(
    currentClass: ClassData, 
    importedClasses: ClassData[], 
    conflictResolution: 'replace' | 'merge' | 'skip' = 'merge'
  ): ClassData {
    const mergedAttributes = [...currentClass.attributes];
    const mergedMethods = [...currentClass.methods];
    
    importedClasses.forEach(importedClass => {
      // Fusionner les attributs
      importedClass.attributes.forEach(attr => {
        const exists = mergedAttributes.some(existing => 
          this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)
        );
        
        if (!exists) {
          mergedAttributes.push(attr);
        } else if (conflictResolution === 'replace') {
          const index = mergedAttributes.findIndex(existing => 
            this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)
          );
          if (index !== -1) {
            mergedAttributes[index] = attr;
          }
        }
      });
      
      // Fusionner les méthodes
      importedClass.methods.forEach(method => {
        const exists = mergedMethods.some(existing => 
          this.normalizeMethodName(existing) === this.normalizeMethodName(method)
        );
        
        if (!exists) {
          mergedMethods.push(method);
        } else if (conflictResolution === 'replace') {
          const index = mergedMethods.findIndex(existing => 
            this.normalizeMethodName(existing) === this.normalizeMethodName(method)
          );
          if (index !== -1) {
            mergedMethods[index] = method;
          }
        }
      });
    });
    
    return {
      name: currentClass.name,
      attributes: mergedAttributes,
      methods: mergedMethods
    };
  }
  
  /**
   * Normalise le nom d'un attribut pour la comparaison
   */
  static normalizeAttributeName(attribute: string): string {
    return attribute.toLowerCase().replace(/[^a-z0-9]/g, '');
  }
  
  /**
   * Normalise le nom d'une méthode pour la comparaison
   */
  static normalizeMethodName(method: string): string {
    return method.toLowerCase().replace(/[^a-z0-9]/g, '');
  }
  
  /**
   * Détecte les conflits potentiels lors de la fusion
   */
  static detectConflicts(
    currentClass: ClassData, 
    importedClasses: ClassData[]
  ): { attributes: string[], methods: string[] } {
    const conflictingAttributes: string[] = [];
    const conflictingMethods: string[] = [];
    
    importedClasses.forEach(importedClass => {
      importedClass.attributes.forEach(attr => {
        const exists = currentClass.attributes.some(existing => 
          this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)
        );
        if (exists && !conflictingAttributes.includes(attr)) {
          conflictingAttributes.push(attr);
        }
      });
      
      importedClass.methods.forEach(method => {
        const exists = currentClass.methods.some(existing => 
          this.normalizeMethodName(existing) === this.normalizeMethodName(method)
        );
        if (exists && !conflictingMethods.includes(method)) {
          conflictingMethods.push(method);
        }
      });
    });
    
    return { attributes: conflictingAttributes, methods: conflictingMethods };
  }
  
  /**
   * Génère les données d'aperçu pour une correspondance
   */
  static generatePreviewData(
    historyItem: HistoryItem,
    targetClass: ClassData,
    allClasses: ClassData[]
  ): { thumbnailUrl?: string; classContext: string; relatedClasses: string[] } {
    // Générer le contexte de la classe
    const classContext = `${targetClass.name}\n` +
      `Attributs: ${targetClass.attributes.length}\n` +
      `Méthodes: ${targetClass.methods.length}`;

    // Trouver les classes liées (mentionnées dans les relations ou ayant des attributs similaires)
    const relatedClasses = allClasses
      .filter(cls => cls.name !== targetClass.name)
      .map(cls => cls.name)
      .slice(0, 3); // Limiter à 3 classes liées

    return {
      thumbnailUrl: historyItem.thumbnailUrl,
      classContext,
      relatedClasses
    };
  }

  /**
   * Vérifie si l'utilisateur a accès à un diagramme partagé
   */
  static hasSharedAccess(item: HistoryItem, currentUserId: string): boolean {
    // TODO: Implémenter la logique de vérification des permissions partagées
    // Pour l'instant, retourner false pour les diagrammes d'autres utilisateurs
    return item.userId === currentUserId;
  }

  /**
   * Marque les diagrammes partagés/collectifs
   */
  static getAccessLevel(item: HistoryItem, currentUserId: string): 'owner' | 'shared' | 'public' | 'restricted' {
    if (item.userId === currentUserId) {
      return 'owner';
    }

    // TODO: Implémenter la logique de partage
    // Vérifier si le diagramme est partagé avec l'utilisateur
    if (this.hasSharedAccess(item, currentUserId)) {
      return 'shared';
    }

    return 'restricted';
  }

  /**
   * Options de tri disponibles
   */
  static getSortOptions(): SortOption[] {
    return [
      { key: 'similarity', label: 'Similarité', direction: 'desc' },
      { key: 'date', label: 'Date (récent)', direction: 'desc' },
      { key: 'date', label: 'Date (ancien)', direction: 'asc' },
      { key: 'name', label: 'Nom A-Z', direction: 'asc' },
      { key: 'name', label: 'Nom Z-A', direction: 'desc' }
    ];
  }
}
