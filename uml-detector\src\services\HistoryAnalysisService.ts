// HistoryAnalysisService.ts - Service pour analyser l'historique et proposer des imports de classes
import { HistoryItem } from '../components/types/HistoryTypes';

export interface ClassData {
  name: string;
  attributes: string[];
  methods: string[];
}

export interface HistoryMatch {
  historyItem: HistoryItem;
  matchingClasses: ClassData[];
  similarity: number;
  isShared: boolean;
  hasAccess: boolean;
}

export interface SortOption {
  key: 'date' | 'similarity' | 'name';
  label: string;
  direction: 'asc' | 'desc';
}

export class HistoryAnalysisService {
  
  /**
   * Recherche dans l'historique les diagrammes contenant une classe du même nom
   */
  static findMatchingDiagrams(
    targetClassName: string, 
    historyItems: HistoryItem[], 
    currentUserId: string
  ): HistoryMatch[] {
    const matches: HistoryMatch[] = [];
    
    historyItems.forEach(item => {
      const extractedClasses = this.extractClassesFromText(item.extractedText);
      const matchingClasses = extractedClasses.filter(cls => 
        cls.name.toLowerCase() === targetClassName.toLowerCase()
      );
      
      if (matchingClasses.length > 0) {
        const similarity = this.calculateSimilarity(targetClassName, matchingClasses[0]);
        
        matches.push({
          historyItem: item,
          matchingClasses,
          similarity,
          isShared: item.userId !== currentUserId,
          hasAccess: this.checkAccess(item, currentUserId)
        });
      }
    });
    
    return matches;
  }
  
  /**
   * Extrait les classes depuis le texte d'analyse
   */
  static extractClassesFromText(extractedText: string): ClassData[] {
    const classes: ClassData[] = [];
    
    if (!extractedText) return classes;
    
    // Diviser le texte par sections de classe
    const classSections = extractedText.split(/class \d+:/g).filter(section => section.trim());
    
    classSections.forEach(section => {
      const classNameMatch = section.match(/NOM_CLASSE:\s*(.+)/);
      if (!classNameMatch) return;
      
      const className = classNameMatch[1].trim();
      
      // Extraire les attributs
      const attributesSection = section.split('ATTRIBUTS:')[1]?.split('MÉTHODES:')[0] || '';
      const attributes = attributesSection
        .split('\n')
        .map(attr => attr.trim())
        .filter(attr => attr && !attr.includes('NOM_CLASSE:'));
      
      // Extraire les méthodes
      const methodsSection = section.split('MÉTHODES:')[1] || '';
      const methods = methodsSection
        .split('\n')
        .map(method => method.trim())
        .filter(method => method && !method.includes('class ') && !method.includes('RELATIONS'));
      
      classes.push({
        name: className,
        attributes,
        methods
      });
    });
    
    return classes;
  }
  
  /**
   * Calcule la similarité entre deux classes
   */
  static calculateSimilarity(targetClassName: string, classData: ClassData): number {
    // Similarité basée sur le nom (exact = 100%)
    let similarity = targetClassName.toLowerCase() === classData.name.toLowerCase() ? 100 : 0;
    
    // Bonus pour le nombre d'attributs et méthodes (plus il y en a, plus c'est intéressant)
    const contentScore = Math.min((classData.attributes.length + classData.methods.length) * 5, 50);
    similarity += contentScore;
    
    return Math.min(similarity, 100);
  }
  
  /**
   * Vérifie les droits d'accès à un diagramme
   */
  static checkAccess(item: HistoryItem, currentUserId: string): boolean {
    // Pour l'instant, accès uniquement aux diagrammes de l'utilisateur
    // TODO: Implémenter la logique de partage/collaboration
    return item.userId === currentUserId;
  }
  
  /**
   * Trie les résultats selon les critères spécifiés
   */
  static sortMatches(matches: HistoryMatch[], sortOption: SortOption): HistoryMatch[] {
    return [...matches].sort((a, b) => {
      let comparison = 0;
      
      switch (sortOption.key) {
        case 'date':
          comparison = a.historyItem.createdAt.getTime() - b.historyItem.createdAt.getTime();
          break;
        case 'similarity':
          comparison = a.similarity - b.similarity;
          break;
        case 'name':
          comparison = a.historyItem.title.localeCompare(b.historyItem.title);
          break;
      }
      
      return sortOption.direction === 'desc' ? -comparison : comparison;
    });
  }
  
  /**
   * Fusionne les attributs et méthodes de plusieurs classes
   */
  static mergeClassData(
    currentClass: ClassData, 
    importedClasses: ClassData[], 
    conflictResolution: 'replace' | 'merge' | 'skip' = 'merge'
  ): ClassData {
    const mergedAttributes = [...currentClass.attributes];
    const mergedMethods = [...currentClass.methods];
    
    importedClasses.forEach(importedClass => {
      // Fusionner les attributs
      importedClass.attributes.forEach(attr => {
        const exists = mergedAttributes.some(existing => 
          this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)
        );
        
        if (!exists) {
          mergedAttributes.push(attr);
        } else if (conflictResolution === 'replace') {
          const index = mergedAttributes.findIndex(existing => 
            this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)
          );
          if (index !== -1) {
            mergedAttributes[index] = attr;
          }
        }
      });
      
      // Fusionner les méthodes
      importedClass.methods.forEach(method => {
        const exists = mergedMethods.some(existing => 
          this.normalizeMethodName(existing) === this.normalizeMethodName(method)
        );
        
        if (!exists) {
          mergedMethods.push(method);
        } else if (conflictResolution === 'replace') {
          const index = mergedMethods.findIndex(existing => 
            this.normalizeMethodName(existing) === this.normalizeMethodName(method)
          );
          if (index !== -1) {
            mergedMethods[index] = method;
          }
        }
      });
    });
    
    return {
      name: currentClass.name,
      attributes: mergedAttributes,
      methods: mergedMethods
    };
  }
  
  /**
   * Normalise le nom d'un attribut pour la comparaison
   */
  static normalizeAttributeName(attribute: string): string {
    return attribute.toLowerCase().replace(/[^a-z0-9]/g, '');
  }
  
  /**
   * Normalise le nom d'une méthode pour la comparaison
   */
  static normalizeMethodName(method: string): string {
    return method.toLowerCase().replace(/[^a-z0-9]/g, '');
  }
  
  /**
   * Détecte les conflits potentiels lors de la fusion
   */
  static detectConflicts(
    currentClass: ClassData, 
    importedClasses: ClassData[]
  ): { attributes: string[], methods: string[] } {
    const conflictingAttributes: string[] = [];
    const conflictingMethods: string[] = [];
    
    importedClasses.forEach(importedClass => {
      importedClass.attributes.forEach(attr => {
        const exists = currentClass.attributes.some(existing => 
          this.normalizeAttributeName(existing) === this.normalizeAttributeName(attr)
        );
        if (exists && !conflictingAttributes.includes(attr)) {
          conflictingAttributes.push(attr);
        }
      });
      
      importedClass.methods.forEach(method => {
        const exists = currentClass.methods.some(existing => 
          this.normalizeMethodName(existing) === this.normalizeMethodName(method)
        );
        if (exists && !conflictingMethods.includes(method)) {
          conflictingMethods.push(method);
        }
      });
    });
    
    return { attributes: conflictingAttributes, methods: conflictingMethods };
  }
  
  /**
   * Options de tri disponibles
   */
  static getSortOptions(): SortOption[] {
    return [
      { key: 'similarity', label: 'Similarité', direction: 'desc' },
      { key: 'date', label: 'Date (récent)', direction: 'desc' },
      { key: 'date', label: 'Date (ancien)', direction: 'asc' },
      { key: 'name', label: 'Nom A-Z', direction: 'asc' },
      { key: 'name', label: 'Nom Z-A', direction: 'desc' }
    ];
  }
}
